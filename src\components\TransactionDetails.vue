<template>
<div>
  <div v-if="isLoading">
    <CanPayLoader/>
  </div>
  <div class="container">
    <div  class="row btn-gap">
      <div class="col-12 d-flex flex-column" style="margin: auto">
        <button
          type="button"
          class="btn-black btn-verify mb-0"
          @click="viewHistoricalTransaction()"
        >
          View Historical Transaction
        </button>
      </div>
    </div>
    <div class="row botton-gap btn-gap" v-if="terminalTypes.length">
      <div class="col-12 d-flex flex-column" style="margin: auto">
        <button
          type="button"
          class="btn-black btn-verify"
          v-on:click="transactionExport()"
        >
          Download Transaction Report
        </button>
      </div>
    </div>

    <div
    class="btn-gap pb-4"
    v-if="terminalTypes.length"
    >
      <div
      v-for="(terminals, index) in terminalTypes"
      :key="index"
      >
        <transaction-detail-card
          v-bind:terminal-types="terminalTypes"
          v-bind:terminals="terminals"
          v-bind:index="index"
          v-bind:show_tip_button="true"
        />
      </div>
    </div>

    <div
      class="one mx-auto history-div"
      v-if="!terminalTypes.length && this.showloader"
    >
      <DefaultLoader :color="'#fffff'" />
      <br />
      <h3 style="color: #ffffff">Loading</h3>
    </div>
    <div
      class="one mx-auto history-div"
      v-if="!terminalTypes.length && !this.showloader"
    >
      <span class="success-text">No transactions to display.</span>
      <br />
      <span class="success-sub-text">
        Your transaction history will show here once you make a purchase with CanPay.
      </span>
    </div>


    <!-- Leave a tip modal start -->
    <tip-modals 
      ref="tipModal"
      :selected-transaction="selectedTransaction"
      :transaction-amount="transactionAmount"
      @make-tip-transaction="makeTransactionForTip"
    />
    <!-- Leave a tip modal end -->

  </div>
</div>
</template>
<script>
import api from "../api/transactiondetails.js";
import pay from "./../api/payment.js";
import { DefaultLoader } from "vue-spinners-css";
// Removed: import Tip from "./Payment/Tip.vue";
import TransactionDetailCard from "./TransactionDetailCard.vue";
import moment from "moment";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue";
import TipModals from "./Modals/TipModals.vue";
import { saveAs } from "file-saver";
import Loading from "vue-loading-overlay";
export default {
  name: "TransactionDetails",
  data() {
    return {
      showloader: false,
      selectedTransaction: {},
      terminalTypes: [],
      transactionAmount: "",
      isLoading: false,
      fullPage: true
    };
  },
  watch: {
    // Removed unused watch handlers for tipamount and customTipAmount
  },
  created() {
    this.transactionHistory();
  },
  components: {
    DefaultLoader,
    // Removed: "enter-tip": Tip,
    Loading,
    'transaction-detail-card': TransactionDetailCard,
    'tip-modals': TipModals,
    CanPayLoader
  },

  methods: {
    transactionHistory: function () {
      this.showloader = true;
      var self = this;
      api
        .transactionHistory()
        .then(function (response) {
          if (response.code == 200) {
            self.terminalTypes = response.data;
            self.showloader = false;
          } else {
            if(response.message){
              alert(response.message);
            }
            self.showloader = false;
          }
        })
        .catch(function (error) {
          self.showloader = false;
        });
    },
    searchedTransactionHistory(data) {
      let self = this;
      var header = {
        "Content-type": "application/json",
      };
      var request = {
        search_keyword: data,
      };
      api
        .searchtransactionHistory(request)
        .then((response) => {
          if (response.code == 200) {
            self.terminalTypes = response.data;
            self.$forceUpdate();
            self.showloader = false;
          } else {
            self.showloader = false;
          }
        })
        .catch(function (error) {
          self.showloader = false;
        });
    },
    transactionExport() {
      this.showloader = true;
      var self = this;
      console.log(self.terminalTypes);
      var request = {
        report: self.terminalTypes,
      };
      api
        .exportTransaction(request)
        .then((response) => {
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") +
              "_Transaction__Details.xlsx"
          );
          self.loading = false;
        })
        .catch(function (error) {
          self.showloader = false;
        });
    },
    viewHistoricalTransaction() {
      this.$router.push("/historicaltransactiondetails").catch((err) => {});
    },
    showModal() {
      this.$refs.tipModal.showModal();
    },
    makeTransactionForTip(tipAmount) {
      let self = this;
      var request = {
        transaction_id: this.selectedTransaction.id,
        tip_amount: tipAmount,
      };
      pay
        .insertTip(request)
        .then((response) => {
          self.$refs.tipModal.showTransactionErrorModal(response.message);
          if (response.code == 200) {
            self.$refs.tipModal.hideModal();
            self.transactionHistory();
          }
        })
        .catch((err) => {
          self.$refs.tipModal.showTipErrorModal(err.response.data.message);
        });
    },
  },
  mounted() {
    var self = this;
    var element = document.getElementsByClassName("content-wrap");
    if (element[0]) {
      element[0].style.setProperty("background-color", "#149240");
      element[0].style.height = "114vh";
      if(window.innerWidth>1200){
        element[0].style.height = "121vh";
      }
    }
    
    // Add event listener for the show-tip-modal event
    this.$root.$on('show-tip-modal', (terminals) => {
      // Show the Tip component modal
      this.selectedTransaction = terminals;
      this.transactionAmount = Number(this.selectedTransaction.amount).toFixed(2);
      this.showModal();
    });
    

    self.$root.$on("search-keyword", function (data) {
      self.showloader = true;
      if (data == "") {
        self.showloader = true;
        setTimeout(function () {
          self.transactionHistory();
        }, 3000);
      } else {
        setTimeout(function () {
          self.searchedTransactionHistory(data);
        }, 3000);
      }
    });

    self.$root.$emit("changeWhiteBackground", [
      false,
      false,
      "TransactionHistoryHeader",
    ]);

    
  },
  beforeDestroy() {
    this.$root.$off("search-keyword");
    this.$root.$off("show-tip-modal");
  },
};
</script>
<style>
  #tip-not-allowed-modal___BV_modal_body_ {
    background-color: #ffffff;
    border-radius: 12px;
    margin: 10px;
  }

.min-h{
    min-height: calc(100vh - 115px)!important;
}

.tran-card-link, .tran-card-link:hover{
    text-decoration: none;
    color: unset;
}
.tran-card{
    background: white;
    border-radius: 7px;
    text-align: left;
}

.tran-card-header{
    position: relative;
    padding: 15px 18px;
    border-radius: 7px;
}
.tran-store-logo{
    position: absolute;
    width: 80px;
    top: 10px;
    right: 10px;
}
.tran-title{
    font-weight: 600;
    font-size: 25px;
}
.tran-store-name{
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 15px;
}

.tran-point-card{
    padding: 7px 0;
}
.tran-point-card .title{
    margin: 0!important;
    padding: 0;
}
.tran-point-card .title span{
   font-size: 22px;
  font-weight: 800;
}
.tran-point-card .value{
  font-size: 14px;
  font-weight: 800;
}

.tran-timing{
    list-style: none;
    margin: 0;
    padding: 0;
    color: #9b9595;
    font-size: 13px;
    font-weight: 600;
}
.tran-timing li{
    display: flex;
    align-items: center;
}

.tran-card-body{
  padding: 10px 15px;
}

.tran-detail{
    align-items: center;
    margin: 0;
}
.tran-detail .title{
    font-weight: 700;
    font-size: 17px;
    margin: 0;
}
.tran-detail .value{
    font-weight: 700;
    font-size: 18px;
    margin: 0;
}

.tran-detail-point{
    align-items: center;
    margin: 0;
    color: #149240;
}
.tran-detail-point .title{
    font-weight: 700;
    font-size: 16px;
    margin: 0;
}
.tran-detail-point .value{
    font-weight: 700;
    font-size: 18px;
    margin: 0;
}

.tran-detail-total{
    align-items: center;
    background: #e3e3e3;
    padding: 13px 15px;
}
.tran-detail-total .title{
  font-weight: 700;
    font-size: 22px;
    margin: 0;
}
.tran-detail-total .value{
    font-weight: 700;
    font-size: 23px;
    margin: 0;
}
.tran-detail-icon-avatar{
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background-color: #118037 !important;
    display: flex;
    justify-content: center;
    align-items: center;
}
.tran-detail-store{
  font-size: 13px;
    font-weight: 600;
}
.active-bg-card{
    background-color: #e3e3e3 !important;
    border-radius: 7px 7px 0 0;
}
</style>
