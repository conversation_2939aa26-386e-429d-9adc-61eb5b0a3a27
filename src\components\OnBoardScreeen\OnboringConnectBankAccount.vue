<template>
  <div>
  <div v-if="isLoading">
    <CanPayLoader/>
  </div>
    <!-- this div is visible when finicity portal is connected -->
    <div
      id="connect-container"
      v-if="showFloatDiv && !isLoading"
      class="d-flex justify-content-start iframe-header"
    >
      <label
        >Having trouble?<button
          @click="showManual()"
          type="button"
          class="ml-2 align-self-center btn-manual-link"
        >
          Enter Banking Manually
        </button>
      </label>
    </div>
    <div class="container splash">
      <cp-onboardingheader></cp-onboardingheader>
      <div v-if="!isLoading">
        <div class="row">
          <div class="col-12 text-center">
            <img
              class="bank-phone-image"
              src="../../assets/images/bank-acount.png"
            />
          </div>
        </div>
        <!-- connect to your bank text -->
        <div class="row">
          <div class="col-12">
            <span class="col-12 register-phone-number-text"
              >Connect Your Bank Account</span
            >
          </div>
        </div>
        <div>
          <!-- link checking account (finicity bank linking) button row -->
          <div class="row btn-row-space padding" >
            <div class="col-12">
              <button
                type="button"
                class="btn-login btn-width"
                @click="bankLinkFinicity"
              >
                Link Checking Account
                <br />
                <label>(recommended)</label>
              </button>
            </div>
          </div>

          <!-- text section 1 -->
          <div class="row row-space">
            <div class="col-12">
              <span class="success-description-label"
                >If your bank is supported, linking your checking account
                through your bank sign-in allows you to:</span
              >
            </div>
          </div>
          <!-- text section 2 -->
          <div class="row row-space">
            <div class="col-12">
              <span class="success-description-label">
                * Better manage your transactions
                <br />* Gives you customized spending limits
              </span>
            </div>
          </div>
          <!-- manual bank linking button row -->
          <div  class="row padding margin-button" v-if="show_manual_link_option">
            <div class="col-12">
              <button
                style="margin-top: 0.2rem !important"
                type="button"
                class="
                  btn-login
                  btn-next-onboarding
                  btn-next-onboarding-manual-banking
                "
                @click="bankLinkManual"
              >
                I'd rather type my account number
              </button>
            </div>
          </div>
          <!-- text section 3 -->
          <div  class="row row-space" v-if="show_manual_link_option">
            <div class="col-12">
              <span class="success-description-label"
                >Manually adding your checking account gives you:</span
              >
            </div>
          </div>
          <!-- text section 4 -->
          <div  class="row row-space" v-if="show_manual_link_option">
            <div class="col-12">
              <span class="success-description-label">
                * Lower default spending limits
                <br />* Fewer available daily transactions
              </span>
            </div>
          </div>
        </div>
      </div>
      <!---- MODAL FOR 0 Error Modal ---->

      <div>
        <b-modal
          ref="zero-pp-modal"
          hide-footer
          v-b-modal.modal-center
          modal-backdrop
          hide-header
          id="zero-pp-modal"
          centered
        >
          <div class="color">
            <div class="purchaserpower-modal-text">
              <div class="col-12 text-center">
                <svg
                  version="1.1"
                  id="Layer_1"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  x="0px"
                  y="0px"
                  width="120"
                  height="120"
                  viewBox="0 0 100 125"
                  style="enable-background: new 0 0 100 125"
                  xml:space="preserve"
                  fill="#e14343"
                  class="on-boarding-upgrade-alert-icon"
                >
                  <path
                    d="M96.2,47.5l-22-38c-0.4-0.6-1-1-1.7-1h-44c-0.7,0-1.4,0.4-1.7,1l-22,37.9c-0.4,0.6-0.4,1.4,0,2l22,38.1c0.4,0.6,1,1,1.7,1
	h44c0.7,0,1.4-0.4,1.7-1l22-38C96.6,48.9,96.6,48.1,96.2,47.5z M71.3,84.5H29.7L8.8,48.4l20.8-35.9h41.7l20.8,36L71.3,84.5z
	 M50.5,60.5c1.1,0,2-0.9,2-2v-30c0-1.1-0.9-2-2-2c-1.1,0-2,0.9-2,2v30C48.5,59.6,49.4,60.5,50.5,60.5z M48.4,66.4
	c-0.6,0.6-0.9,1.3-0.9,2.1c0,0.8,0.3,1.6,0.9,2.1s1.3,0.9,2.1,0.9c0.8,0,1.6-0.3,2.1-0.9c0.6-0.6,0.9-1.3,0.9-2.1
	c0-0.8-0.3-1.6-0.9-2.1C51.5,65.3,49.5,65.3,48.4,66.4z"
                  />
                </svg>
              </div>
              <div class="d-block text-center">
                <label class="purchasepower-def-label">
                  {{ pp_alert_message }}
                </label>
              </div>
              <br />
              <br />
              <div class="text-center">
                <button
                  type="button"
                  class="mx-auto col-10 offset-1 btn-black"
                  style="height: 60px"
                  @click="hideModal('zero-pp-modal')"
                >
                  <label class="purchasepower-modal-ok-label">OK</label>
                </button>
              </div>
            </div>
          </div>
        </b-modal>
      </div>
    </div>
  </div>
</template>

<script>
import OnboardingHeader from "./OnboardingHeader.vue";
import api from "../../api/onboarding.js";
import constants from "../Common/constant.js";
import Loading from "vue-loading-overlay";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  /**
   * write a component's description
   * this component is use to Upgrade to the new canpay the user's email as a consumer
   */
  name: "BankAccountRegistration",
  /**
   * @description-

   * @returns {any}
   */

  data() {
    let self = this;
    return {
      bankAccount: "",
      success: false,
      pp_alert_message: "",
      isLoading: false,
      fullPage: true,
      userStatus: "",
      showFloatDiv: false,
      sessionId:null,
      show_manual_link_option:true
    };
  },
  components: {
    "cp-onboardingheader": OnboardingHeader,
    Loading,
    CanPayLoader
  },
  destroyed() {
    document
      .getElementById("app")
      .style.setProperty("background-color", "#149240");
  },
  created() {
    
  },
  mounted() {
    this.$root.$emit("show_header", false);
    this.$root.$emit("changeWhiteBackground", [true, false, ""]);
    document
      .getElementById("app")
      .style.setProperty("background-color", "#ffffff");

    var element = document.getElementsByClassName("wrapper");
    element[0].style.setProperty("background-color", "#ffffff");

    var elementHtml = document.getElementsByTagName("html")[0];
    elementHtml.style.setProperty("background-color", "#ffffff");
    this.sessionId = atob(window.location.href.split("/").splice(-2, 1)[0]);
    if(localStorage.getItem("microbilt_error_need_bank_link") != null){
      this.show_manual_link_option = false;
    }
  },
  methods: {
    showModal(modal) {
      this.$refs[modal].show();
    },
    hideModal(modal) {
      this.$refs[modal].hide();
    },
    bankLinkFinicity() {
      let self = this;
      this.isLoading = true;
      var request = {
        session_id:self.sessionId,
      };
      api
        .ConnectBank(request)
        .then((response) => {
          if (response.code == 200) {
            const finicityConnectUrl = response.data.link;
            window.finicityConnect.launch(finicityConnectUrl, {
              selector: "#connect-container",
              overlay: "rgba(255,255,255, 0)",
              success: function (data) {
                console.log("Yay! We got data", data);
                if (data.code == 200) {
                  self.showFloatDiv = false;
                  self.redirectToSuccess();
                }
              },
              cancel: function () {
                self.isLoading = false;
                self.showFloatDiv = false;
                console.log("The user cancelled the iframe");
              },
              error: function (err) {
                self.isLoading = false;
                self.showFloatDiv = false;
                console.error(
                  "Some runtime error was generated during Finicity Connect",
                  err
                );
              },
              loaded: function () {
                self.isLoading = false;
                self.showFloatDiv = false;
                console.log(
                  "This gets called only once after the iframe has finished loading"
                );
              },
              route: function (event) {
                
                  self.isLoading = false;
                  self.showFloatDiv = true;
                  document.getElementById("finicityConnectIframe").style.top =
                    "65px";
                  document.getElementById(
                    "finicityConnectIframe"
                  ).style.height = "calc(100vh - 65px)";
                
                console.log(
                  "This is called as the user progresses through Connect"
                );
              },
            });
          } else {
            self.isLoading = false;
            self.pp_alert_message = response.message;
            this.showModal("zero-pp-modal");
          }
        })
        .catch((err) => {
          self.isLoading = false;
          console.log(err);
        });
    },
    bankLinkManual() {
      this.$router.push("/onboardingmanualbankaccount/"+btoa(this.sessionId)+"/onboardingmanualbankaccount");
    },
    showManual() {
      this.showFloatDiv = false;
      window.finicityConnect.destroy();
      this.$router.push("/onboardingmanualbankaccount/"+btoa(this.sessionId)+"/onboardingmanualbankaccount");
    },
    redirectToSuccess() {
      this.isLoading = true;
      var request = {
        session_id: this.sessionId,
      };
      api
        .Finalonboarding(request)
        .then((response) => {
          self.isLoading = true;
          if (response.code == 200) {
            this.$router.push("/registrationsuccess");
          }
        })
        .catch((err) => {
          this.isLoading = false;
          this.pp_alert_message = err.response.data.message;
          if(err.response.data.code!= 597){
            this.showModal("zero-pp-modal");
          }
          if (err.response.data.code == 401) {
            this.pp_alert_message = err.response.data.message;
            this.showModal("zero-pp-modal");
          }else if(err.response.data.code == 597){
            self.generateConnectFix(
              err.response.data.data.institutionId
            );
          }
        });
    },
    generateConnectFix(id) {
      let self = this;
      this.isLoading = true;
      var request = {
        institution_id: id,
      };
      api
        .generateConnectFix(request)
        .then((response) => {
          if (response.code == 200) {
            const finicityConnectUrl = response.data.link;
            window.finicityConnect.launch(finicityConnectUrl, {
              selector: "#connect-container",
              overlay: "rgba(255,255,255, 0)",
              success: function (data) {
                console.log("Yay! We got data", data);
                if (data.code == 200) {
                  //now store the details at canpay end
                  self.bankLinkFinicity();
                }
              },
              cancel: function () {
                self.isLoading = false;
                console.log("The user cancelled the iframe");
              },
              error: function (err) {
                console.log(err);
                self.isLoading = false;
                console.error(
                  "Some runtime error was generated during Finicity Connect",
                  err
                );
              },
              loaded: function () {
                self.isLoading = false;
                console.log(
                  "This gets called only once after the iframe has finished loading"
                );
              },
              route: function (event) {
                self.isLoading = false;
                console.log(
                  "This is called as the user progresses through Connect"
                );
              },
              user: function (event) {
                if (event.data.errorCode) {
                  setTimeout(() => {
                    window.finicityConnect.destroy();
                    //if error code is present then call the connect fix api
                    self.generateConnectFix(event.data.data.institutionId);
                  }, 2000);
                }
              },
            });
          } else {
            self.isLoading = false;
          }
        })
        .catch((err) => {
          self.isLoading = false;
          console.log(err);
        });
    }
  },
};
</script>
<style lang="scss">
#zero-pp-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
</style>

