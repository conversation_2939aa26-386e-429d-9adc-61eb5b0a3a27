<!DOCTYPE html>
<html lang="en">

<head>
    <meta name="robots" content="noindex" charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <link rel="stylesheet" type="text/css" href="//fonts.googleapis.com/css?family=Open+Sans:400,600,700,300,800" />
    <link href='https://fonts.googleapis.com/css?family=Montserrat:200,300,400,500,600,700,800' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css?family=Montserrat' rel='stylesheet'>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/css/datepicker.css" rel="stylesheet" type="text/css" />
    <script src="https://code.jquery.com/jquery-3.5.1.js" integrity="sha256-QWo7LDvxbWT2tbbQ97B53yJnYU3WhH/C8ycbRAkjPDc=" crossorigin="anonymous"></script>
    <script src="https://connect2.finicity.com/assets/sdk/finicity-connect.min.js"></script>
    <script src="https://www.gstatic.com/firebasejs/5.5.3/firebase-app.js"></script>
    <script src="<%= BASE_URL %>js/YouShallPass.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.3.0/js/bootstrap-datepicker.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" />
    <script src="js/Winwheel.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/latest/TweenMax.min.js"></script>
    <!-- Lastly add this package -->
    <script src="<%= BASE_URL %>js/mx-connect.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue-loading-overlay@3"></script>
    <link href="https://cdn.jsdelivr.net/npm/vue-loading-overlay@3/dist/vue-loading.css" rel="stylesheet">
    <!-- Init the plugin and component-->
<!-- Calendly Widget Script & CSS -->
    <script src="https://assets.calendly.com/assets/external/widget.js" type="text/javascript"></script>
    <link href="https://assets.calendly.com/assets/external/widget.css" rel="stylesheet">

    <title>CanPay</title>
</head>

<body>
    <noscript>
      <strong
        >We're sorry but CanPay doesn't work properly without JavaScript
        enabled. Please enable it to continue.</strong
      >
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
</body>

</html>


<script>
    var RequiredField = "Please complete all required fields.";
    var VaildPhoneNumber = "Please enter a valid 10-digit phone number.";
    var ValidEmail = "Please enter a valid email.";
    var VaildStore = "Please enter store access for your user.";
    var SelectUserType = "Please select User type";
    var pinNotMatched = "Quick Access PINs do not match.";
</script>
