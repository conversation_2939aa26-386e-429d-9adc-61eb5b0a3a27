<template>
<div>
    <div v-if="isLoading">
      <CanPayLoader/>
    </div>
  <div class="container splash mt-4">
    <div class="row m-5">
      <div class="col-12 remove-padding">
        <span class="bl-heading"
          >Change Funding Account<b /> For Return repayment</span
        >
      </div>
    </div>
    <div class="bank-list" v-if="!isLoading">
      <div v-if="balance_checking == 1">
        <div class="accordian-main-div-class">
    <!-- Active Bank Accordion -->
    <div class="accordion-item" v-if="bank_list.active_bank">
      <div class="accordion-header" :class="{ 'active-accordion': activeAccordion === 'active_bank' }" @click="toggleAccordion('active_bank','')">
       {{currentBankName}} ({{ bank_list.active_bank_account_count }})
        <span :class="{ 'rotate-arrow': activeAccordion === 'active_bank', 'rotate': activeAccordion === 'active_bank' }">&#9660;</span>
      </div>
      <div v-if="activeAccordion === 'active_bank'" class="accordion-content">
        <div v-for="(bank, bankName) in bank_list.active_bank" :key="bankName">
          <div v-for="(accounts, accountType) in bank" :key="accountType">
            <div class="row m-2 pl-2">
              <div class="col-4 remove-padding">
        <span class="float-left">{{ ucfirst(accountType) }}</span>
      </div>
      <div class="col-8 pl-2 remove-padding">
        <span class="float-left font-weight-bold">PurchPower</span>

        <div
          @click="showModal('purchase-power-modal')"
          style="margin-right: 4.5rem"
        >
          <svg
            version="1.1"
            id="Layer_1"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            x="0px"
            y="0px"
            width="16"
            height="16"
            fill="#000000"
            viewBox="0 0 90 90"
            style="enable-background: new 0 0 90 90"
            xml:space="preserve"
          >
            <path
              d="M45,0C20.1,0,0,20.1,0,45s20.1,45,45,45s45-20.1,45-45C90,20.2,69.8,0,45,0z M45,82C24.6,82,8,65.4,8,45S24.6,8,45,8
s37,16.6,37,37S65.4,82,45,82z M40,20c0-2.8,2.2-5,5-5s5,2.2,5,5s-2.2,5-5,5S40,22.8,40,20z M56,65.4V68c0,1.1-0.9,2-2,2H36
c-1.1,0-2-0.9-2-2v-2.6c0-0.9,0.6-1.6,1.4-1.9l4.1-1.3c0.3-0.1,0.5-0.4,0.5-0.7V41h-3.1c-1.6,0-2.9-1.3-2.9-2.9c0-1.2,0.8-2.3,2-2.7
l11.4-3.6c1.1-0.3,2.2,0.3,2.5,1.3c0.1,0.2,0.1,0.4,0.1,0.6v27.8c0,0.3,0.2,0.6,0.5,0.7l4.1,1.3C55.4,63.7,56,64.5,56,65.4z"
            />
          </svg>
        </div>
      </div>
              </div>
              <div class="row" v-for="(account, index) in accounts" :key="index">
      <div class="col-12">
        <div class="row bank-name-list m-2">
          <div class="col-4 align-self-center bl-f-weight">
            <span>x{{ account.account_no }}</span>
          </div>
          <div class="col-4 align-self-center bl-f-weight">
            <span v-if="account.purchase_power!=blacklistedAccountNumber && account.purchase_power!=blockedRoutingNumber">$</span><span>{{ account.purchase_power }}</span>
          </div>
          <div class="col-2 align-self-center">
            <div class="text-center" v-if="account.status == 0 || account.blocked_account_number == 1">
              <a href="javascript:void(0);" @click="confirmDelete(account)" title="Disable Bank Account"> 
                <label
                  class="delete-bank-label float-right"
                ></label>
              </a>
            </div>
          </div>
          <div class="col-2 align-self-center">
            <div class="text-center">
              <input
                :id="account.id"
                class="radio-custom"
                name="radio-group"
                type="radio"
                :ischecked="account.status"
                :checked="account.status"
                @click="selectBank(account)"
                v-if="account.blocked_account_number == 0 && account.is_disable == 0"
              />
              <label
                :for="account.id"
                class="radio-custom-label float-right"
              ></label>
            </div>
          </div>
        </div>
      </div>
      </div>
          </div>
        </div>
      </div>
    </div>



    <!-- Other Banks Accordions -->
<div v-for="(otherBank, index) in bank_list.other_banks" :key="index" class="accordion-item">
  <div class="accordion-header" :class="{ 'active-accordion': activeAccordion === index }" @click="toggleAccordion(index,otherBank.institution_id)">
    {{ otherBank.name }} ({{ otherBank.count }})
    <span :class="{ 'rotate-arrow': activeAccordion === index, 'rotate': activeAccordion === index }">&#9660;</span>
  </div>
  <div v-if="activeAccordion === index" class="accordion-content">
    <div v-for="(acc, accType) in otherBank.accounts[0]" :key="accType">
      <div v-for="(accounts, accountType) in acc" :key="accountType">
        <div class="row m-2 pl-2">
        <div class="col-4 remove-padding">
    <span class="float-left">{{ ucfirst(accountType) }}</span>
    </div>
    <div class="col-8 pl-2 remove-padding">
    <span class="float-left font-weight-bold">PurchPower</span>

    <div
    @click="showModal('purchase-power-modal')"
    style="margin-right: 4.5rem"
    >
    <svg
      version="1.1"
      id="Layer_1"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      x="0px"
      y="0px"
      width="16"
      height="16"
      fill="#000000"
      viewBox="0 0 90 90"
      style="enable-background: new 0 0 90 90"
      xml:space="preserve"
    >
      <path
        d="M45,0C20.1,0,0,20.1,0,45s20.1,45,45,45s45-20.1,45-45C90,20.2,69.8,0,45,0z M45,82C24.6,82,8,65.4,8,45S24.6,8,45,8
    s37,16.6,37,37S65.4,82,45,82z M40,20c0-2.8,2.2-5,5-5s5,2.2,5,5s-2.2,5-5,5S40,22.8,40,20z M56,65.4V68c0,1.1-0.9,2-2,2H36
    c-1.1,0-2-0.9-2-2v-2.6c0-0.9,0.6-1.6,1.4-1.9l4.1-1.3c0.3-0.1,0.5-0.4,0.5-0.7V41h-3.1c-1.6,0-2.9-1.3-2.9-2.9c0-1.2,0.8-2.3,2-2.7
    l11.4-3.6c1.1-0.3,2.2,0.3,2.5,1.3c0.1,0.2,0.1,0.4,0.1,0.6v27.8c0,0.3,0.2,0.6,0.5,0.7l4.1,1.3C55.4,63.7,56,64.5,56,65.4z"
      />
    </svg>
    </div>
    </div>
        </div>
        <div class="row" v-for="(account, index) in accounts" :key="index">
    <div class="col-12">
    <div class="row bank-name-list m-2">
    <div class="col-4 align-self-center bl-f-weight">
      <span>x{{ account.account_no }}</span>
    </div>
    <div class="col-4 align-self-center bl-f-weight">
      <span v-if="account.purchase_power!=blacklistedAccountNumber && account.purchase_power!=blockedRoutingNumber">$</span><span>{{ account.purchase_power }}</span>
    </div>
    <div class="col-2 align-self-center">
      <div class="text-center" v-if="account.status == 0 || account.blocked_account_number == 1">
        <a href="javascript:void(0);" @click="confirmDelete(account)" title="Disable Bank Account"> 
          <label
            class="delete-bank-label float-right"
          ></label>
        </a>
      </div>
    </div>
    <div class="col-2 align-self-center">
      <div class="text-center">
        <input
          :id="account.id"
          class="radio-custom"
          name="radio-group"
          type="radio"
          :ischecked="account.status"
          :checked="account.status"
          @click="selectBank(account)"
          v-if="account.blocked_account_number == 0 && account.is_disable == 0"
        />
        <label
          :for="account.id"
          class="radio-custom-label float-right"
        ></label>
      </div>
    </div>
    </div>
    </div>
    </div>
      </div>
      
    </div>
   
  </div>
</div>
  </div>
      </div>
      <div v-else>
        <div class="row m-2">
          <div class="col-12 remove-padding">
            <span class="float-left bl-bank-name bl-f-weight"
              >Manual Bank List</span
            >
          </div>
        </div>
        <div class="accordian-main-div-class">
          <div v-for="(all_bank, index) in bank_list" :key="index" class="accordion-item">
            <div class="accordion-header" :class="{ 'active-accordion': activeManulaBankAccordion === index }" @click="toggleManualBankAccordion(index)">
              {{ index }} ({{ getAccountCount(all_bank) }}) <span :class="{ 'rotate-arrow': activeManulaBankAccordion === index, 'rotate': activeManulaBankAccordion === index }">&#9660;</span>
            </div>
            <div v-if="activeManulaBankAccordion === index" class="accordion-content">
              <div v-for="(accounts, accountType) in all_bank" :key="accountType">
                <!-- <div v-for="(accounts, accountType) in bank" :key="accountType"> -->
                  <div class="row m-2 pl-2">
                    <div class="col-4 remove-padding">
                      <span class="float-left">{{ ucfirst(accountType) }}</span>
                    </div>
                  </div>
                  <div class="row" v-for="(account, index) in accounts" :key="index">
                    <div class="col-12">
                      <div class="row bank-name-list m-2">
                        <div class="col-4 align-self-center bl-f-weight">
                          <span>x{{ account.account_no }}</span>
                        </div>
                        <div class="col-6 align-self-center">
                        </div>
                        <div class="col-2 align-self-center">
                          <div class="text-center">
                            <input :id="account.id" class="radio-custom" name="radio-group" type="radio" :ischecked="account.status" :checked="account.status" @click="selectBank(account)" v-if="account.is_disable == 0" />
                            <label :for="account.id" class="radio-custom-label float-right"></label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                <!-- </div> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mt-5">
        <div class="col-6">
        <button type="button" class="btn-signup-splash" v-on:click="checkRealAccNo();">
           Re-Pay Now
        </button>
      </div>
      <div class="col-6">
        <button type="button" class="btn-login-splash" v-on:click="clickClose">
          Close
        </button>
      </div>

    </div>
    <div class="row mt-2">
      <div class="col-12">
        <span class="bl-footer-text">
          Don’t see the account you want?<br />
          <span class="bl-f-color" @click="gotoBankLink()"
            ><u>Add new Bank Account </u></span
          ></span
        >
      </div>
    </div>
    <b-modal
      ref="update-bank-modal"
      hide-footer
      no-close-on-backdrop
      modal-backdrop
      hide-header
      id="update-bank-modal"
      centered
    >
      <div class="color">
        <div class="col-12 text-center">
          <svg
            version="1.1"
            id="Layer_1"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            x="0px"
            y="0px"
            width="120"
            height="120"
            viewBox="0 0 100 125"
            style="enable-background: new 0 0 100 125"
            xml:space="preserve"
            fill="#e14343"
          >
            <path
              d="M96.2,47.5l-22-38c-0.4-0.6-1-1-1.7-1h-44c-0.7,0-1.4,0.4-1.7,1l-22,37.9c-0.4,0.6-0.4,1.4,0,2l22,38.1c0.4,0.6,1,1,1.7,1
    h44c0.7,0,1.4-0.4,1.7-1l22-38C96.6,48.9,96.6,48.1,96.2,47.5z M71.3,84.5H29.7L8.8,48.4l20.8-35.9h41.7l20.8,36L71.3,84.5z
     M50.5,60.5c1.1,0,2-0.9,2-2v-30c0-1.1-0.9-2-2-2c-1.1,0-2,0.9-2,2v30C48.5,59.6,49.4,60.5,50.5,60.5z M48.4,66.4
    c-0.6,0.6-0.9,1.3-0.9,2.1c0,0.8,0.3,1.6,0.9,2.1s1.3,0.9,2.1,0.9c0.8,0,1.6-0.3,2.1-0.9c0.6-0.6,0.9-1.3,0.9-2.1
    c0-0.8-0.3-1.6-0.9-2.1C51.5,65.3,49.5,65.3,48.4,66.4z"
            />
          </svg>
        </div>
        <div class="purchaserpower-modal-text">
          <div class="d-block text-center">
            <label class="purchasepower-def-label">
              There was some problem trying to link your bank account. Please
              Update your banking details.
            </label>
          </div>
          <br />
          <br />
          <div class="row">
            <div class="col-12 text-center">
              <button
                type="button"
                class="mx-auto col-10 offset-1 btn-black"
                style="height: 60px"
                @click="gotoBankLink()"
              >
                <span class="purchasepower-modal-ok-label"
                  >Add new bank account
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </b-modal>
    <b-modal
      ref="purchase-power-modal"
      hide-footer
      v-b-modal.modal-center
      modal-backdrop
      hide-header
      id="pay-modal-center"
      centered
      title="BootstrapVue"
    >
      <div class="color">
        <div class="purchaserpower-modal-text">
          <div class="col-12 text-center mt-5 mb-5">
            <svg
              version="1.1"
              id="Layer_1"
              xmlns:cc="http://creativecommons.org/ns#"
              xmlns:dc="http://purl.org/dc/elements/1.1/"
              xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
              xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
              xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
              xmlns:svg="http://www.w3.org/2000/svg"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              x="0px"
              y="0px"
              viewBox="0 0 26.5 33.1"
              style="enable-background: new 0 0 26.5 33.1"
              xml:space="preserve"
              width="75"
              height="75"
              fill="#149240"
            >
              <g transform="translate(0,-270.54165)">
                <path
                  d="M17.1,272.1c-0.3,0-0.6,0.1-0.8,0.2l-5.6,3.2H3.4c-1,0-1.8,0.8-1.8,1.8v16.3c0,1,0.8,1.8,1.8,1.8h18.8c1,0,1.8-0.8,1.8-1.8
        v-4.8h0.4c0.3,0,0.5-0.2,0.5-0.5v-4.7c0-0.3-0.2-0.5-0.5-0.5h-0.4v-5.7c0-1-0.8-1.8-1.8-1.8h-2l-1.5-2.6
        C18.3,272.4,17.7,272.1,17.1,272.1L17.1,272.1z M17.2,273.2c0.2,0,0.4,0.1,0.6,0.4l2.5,4.4H8.7l8-4.6
        C16.9,273.2,17,273.2,17.2,273.2L17.2,273.2z M3.4,276.6h5.4l-2.2,1.2H4.1c-0.7,0-0.7,1,0,1h18.8v4.2h-3.3c-1.6,0-2.9,1.3-2.9,2.9
        s1.3,2.9,2.9,2.9h3.3v4.8c0,0.4-0.3,0.7-0.7,0.7H3.4c-0.4,0-0.7-0.3-0.7-0.7v-16.3C2.6,277,2.9,276.6,3.4,276.6L3.4,276.6z
         M20.8,276.6h1.4c0.4,0,0.7,0.3,0.7,0.7v0.5h-1.4L20.8,276.6z M19.6,284.1h4.3v3.6h-4.3c-1,0-1.8-0.8-1.8-1.8
        C17.7,284.9,18.5,284.1,19.6,284.1z M19.8,284.9c-0.6,0-1.1,0.5-1.1,1c0,0.6,0.5,1,1.1,1s1.1-0.5,1.1-1
        C20.9,285.4,20.4,284.9,19.8,284.9z"
                />
              </g>
            </svg>
          </div>
          <div class="d-block text-center">
            <label class="purchasepower-def-label">
              <b>What is my Purchase Power?</b>
            </label>
          </div>
          <br />

          <h3 class="purchasepower-modal-text text-justify">
            Purchase Power is your available spending with CanPay. Your Purchase
            Power is impacted by many factors including recent purchases
            through CanPay, your total CanPay spending history, and how long
            you've had a CanPay account. Negative items, like NSF payment
            returns, can also impact your Purchase Power.
          </h3>

          <div class="text-center mt-5">
            <button
              type="button"
              class="mx-auto col-10 offset-1 btn-black"
              style="height: 60px"
              @click="hideModal('purchase-power-modal')"
            >
              <label class="purchasepower-modal-ok-label">OK</label>
            </button>
          </div>
        </div>
      </div>
    </b-modal>
    <b-modal
      ref="delink-modal"
      hide-footer
      no-close-on-backdrop
      modal-backdrop
      hide-header
      id="delink-modal"
      centered
    >
      <div class="color">
        <div class="col-12 text-center">
          <svg
            version="1.1"
            id="Layer_1"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            x="0px"
            y="0px"
            width="120"
            height="120"
            viewBox="0 0 100 125"
            style="enable-background: new 0 0 100 125"
            xml:space="preserve"
            fill="#e14343"
          >
            <path
              d="M96.2,47.5l-22-38c-0.4-0.6-1-1-1.7-1h-44c-0.7,0-1.4,0.4-1.7,1l-22,37.9c-0.4,0.6-0.4,1.4,0,2l22,38.1c0.4,0.6,1,1,1.7,1
    h44c0.7,0,1.4-0.4,1.7-1l22-38C96.6,48.9,96.6,48.1,96.2,47.5z M71.3,84.5H29.7L8.8,48.4l20.8-35.9h41.7l20.8,36L71.3,84.5z
     M50.5,60.5c1.1,0,2-0.9,2-2v-30c0-1.1-0.9-2-2-2c-1.1,0-2,0.9-2,2v30C48.5,59.6,49.4,60.5,50.5,60.5z M48.4,66.4
    c-0.6,0.6-0.9,1.3-0.9,2.1c0,0.8,0.3,1.6,0.9,2.1s1.3,0.9,2.1,0.9c0.8,0,1.6-0.3,2.1-0.9c0.6-0.6,0.9-1.3,0.9-2.1
    c0-0.8-0.3-1.6-0.9-2.1C51.5,65.3,49.5,65.3,48.4,66.4z"
            />
          </svg>
        </div>
        <div class="purchaserpower-modal-text">
          <div class="d-block text-center">
            <label class="purchasepower-def-label">
              The connection to your bank/credit union has disconnected. Please relink your banking to continue.
            </label>
          </div>
          <br />
          <br />
          <div class="row">
            <div class="col-12 text-center">
              <button
                type="button"
                class="mx-auto col-10 offset-1 btn-black"
                style="height: 60px"
                @click="gotoBankLink()"
              >
                <span class="purchasepower-modal-ok-label"
                  >Add Primary Account</span
                >
              </button>
            </div>
          </div>
        </div>
      </div>
    </b-modal>

    <b-modal
      ref="disable-bank-success-modal"
      hide-footer
      no-close-on-backdrop
      modal-backdrop
      hide-header
      id="disable-bank-success-modal"
      centered
    >
      <div class="color">
        <div class="col-12 text-center"></div>
        <div class="purchaserpower-modal-text">
          <div class="d-block text-center">
            <label class="purchasepower-def-label">
              Bank Account Disabled successfully
            </label>
          </div>
          <br />
          <br />
          <div class="row">
            <div class="col-12 text-center">
              <button
                type="button"
                class="mx-auto col-10 offset-1 btn-black"
                style="height: 60px"
                @click="closeRemoveBankModal()"
              >
                <span class="purchasepower-modal-ok-label">OK</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </b-modal>
    <b-modal
      ref="confirm-modal"
      hide-footer
      v-b-modal.modal-center
      modal-backdrop
      hide-header
      id="confirm-modal"
      centered
    >
      <div class="color">
        <div class="col-12 text-center">
          <svg
            version="1.1"
            id="Layer_1"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            x="0px"
            y="0px"
            width="120"
            height="120"
            viewBox="0 0 100 125"
            style="enable-background: new 0 0 100 125"
            xml:space="preserve"
            fill="#e14343"
          >
            <path
              d="M96.2,47.5l-22-38c-0.4-0.6-1-1-1.7-1h-44c-0.7,0-1.4,0.4-1.7,1l-22,37.9c-0.4,0.6-0.4,1.4,0,2l22,38.1c0.4,0.6,1,1,1.7,1
    h44c0.7,0,1.4-0.4,1.7-1l22-38C96.6,48.9,96.6,48.1,96.2,47.5z M71.3,84.5H29.7L8.8,48.4l20.8-35.9h41.7l20.8,36L71.3,84.5z
     M50.5,60.5c1.1,0,2-0.9,2-2v-30c0-1.1-0.9-2-2-2c-1.1,0-2,0.9-2,2v30C48.5,59.6,49.4,60.5,50.5,60.5z M48.4,66.4
    c-0.6,0.6-0.9,1.3-0.9,2.1c0,0.8,0.3,1.6,0.9,2.1s1.3,0.9,2.1,0.9c0.8,0,1.6-0.3,2.1-0.9c0.6-0.6,0.9-1.3,0.9-2.1
    c0-0.8-0.3-1.6-0.9-2.1C51.5,65.3,49.5,65.3,48.4,66.4z"
            />
          </svg>
        </div>
        <div class="purchaserpower-modal-text">
          <div class="d-block text-center">
            <label class="purchasepower-def-label">
              Do you wish to disable this account from CanPay? Once you disable
              this account, it can no longer be used for future purchases unless
              you enable it.
            </label>
          </div>
          <br />
          <br />
          <div class="row">
            <div class="col-12 text-center">
              <button
                @click="hideModal('confirm-modal')"
                class="btn btn-danger btn-md center-block tip-btn"
              >
                <label class="forgetpassword-ok-label">Cancel</label>
              </button>
              <button
                @click="disableBank()"
                class="btn btn-danger btn-md center-block tip-ok-btn"
              >
                <label class="forgetpassword-ok-label">Confirm</label>
              </button>
            </div>
          </div>
        </div>
      </div>
    </b-modal>
    <b-modal
      ref="warning-insufficient-fund"
      no-close-on-backdrop
      hide-footer
      v-b-modal.modal-center
      modal-backdrop
      hide-header
      id="warning-insufficient-fund"
      centered
    >
      <div class="color">
        <div class="row mt-4">
          <div class="col-12 text-center">
            <svg
              version="1.1"
              class="align-self-center"
              id="Layer_1"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              x="0px"
              y="0px"
              width="100"
              height="100"
              viewBox="0 0 100 125"
              style="enable-background: new 0 0 100 125"
              xml:space="preserve"
              fill="#e24141"
            >
              <path
                d="M96.2,47.5l-22-38c-0.4-0.6-1-1-1.7-1h-44c-0.7,0-1.4,0.4-1.7,1l-22,37.9c-0.4,0.6-0.4,1.4,0,2l22,38.1c0.4,0.6,1,1,1.7,1
    h44c0.7,0,1.4-0.4,1.7-1l22-38C96.6,48.9,96.6,48.1,96.2,47.5z M71.3,84.5H29.7L8.8,48.4l20.8-35.9h41.7l20.8,36L71.3,84.5z
     M50.5,60.5c1.1,0,2-0.9,2-2v-30c0-1.1-0.9-2-2-2c-1.1,0-2,0.9-2,2v30C48.5,59.6,49.4,60.5,50.5,60.5z M48.4,66.4
    c-0.6,0.6-0.9,1.3-0.9,2.1c0,0.8,0.3,1.6,0.9,2.1s1.3,0.9,2.1,0.9c0.8,0,1.6-0.3,2.1-0.9c0.6-0.6,0.9-1.3,0.9-2.1
    c0-0.8-0.3-1.6-0.9-2.1C51.5,65.3,49.5,65.3,48.4,66.4z"
              />
            </svg>
          </div>
        </div>
        <div class="d-block text-center m-3">
          <span style="color: black; font-size: 1.5rem; font-weight: 600"
            >Warning!</span
          >
        </div>
        <div class="d-block text-center">
          <span style="color: #5b5a5a">
            Your bank is reporting insufficient balance to pay for this
            purchase. Please deposit additional funds.
            <br /><br />
            If you recently deposited funds, it can take up to 12 hours for your
            bank to report the new balance.
          </span>
        </div>
        <div class="row mt-5">
          <div class="col-12 text-center">
            <button
              type="button"
              class="btn-black"
              style="
                height: 60px;
                background: #149240 !important;
                border: none !important;
              "
              @click="repayDebit(0)"
            >
              <span class="purchasepower-modal-ok-label"
                >Continue and Re-Pay</span
              >
            </button>
          </div>
        </div>
        <div class="row">
          <div class="col-12 text-center">
            <button
              type="button"
              class="btn-black"
              style="
                height: 60px;
                background: #000000 !important;
                border: none !important;
              "
              @click="updateBankAccount()"
            >
              <span class="purchasepower-modal-ok-label">Change Bank</span>
            </button>
          </div>
        </div>
      </div>
    </b-modal>

    <b-modal
      ref="represent-success"
      no-close-on-backdrop
      hide-footer
      v-b-modal.modal-center
      modal-backdrop
      hide-header
      id="represent-success"
      centered
    >
      <div class="color">
        <div class="row mt-4">
          <div class="col-12 text-center">
            <svg
              version="1.1"
              id="Layer_1"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              x="0px"
              y="0px"
              width="70"
              height="70"
              viewBox="0 0 63.9 64.4"
              style="enable-background: new 0 0 63.9 64.4"
              xml:space="preserve"
              fill="#149240"
            >
              <g>
                <g>
                  <g>
                    <path
                      d="M56.9,33.2c-0.4,0-0.9,0-1.3,0c-0.9,0-1.9,0.8-1.8,1.8c0,1,0.8,1.8,1.8,1.8c0.1,0,0.3,0,0.4,0c0.3,0-0.2,0-0.2,0
                c0.1,0,0.1,0,0.2,0c0.1,0,0.3,0.1,0.4,0.1c0.1,0,0.1,0,0.2,0.1c0.2,0.1-0.4-0.2-0.1-0.1c0.2,0.1,0.4,0.2,0.7,0.4
                c0.2,0.2-0.1-0.1-0.1-0.1c0,0,0.1,0.1,0.1,0.1c0.1,0.1,0.2,0.2,0.3,0.3c0,0,0.1,0.1,0.1,0.1c0.2,0.2-0.1-0.1-0.1-0.1
                c0,0.1,0.2,0.2,0.2,0.3c0.1,0.1,0.1,0.2,0.2,0.3c0,0.1,0.1,0.1,0.1,0.2c-0.2-0.4-0.1-0.2,0-0.1c0,0.1,0.1,0.2,0.1,0.4
                c0,0.1,0,0.1,0,0.2c0.1,0.3,0-0.2,0-0.2c0,0.3,0,0.5,0,0.8c0,0.1,0,0.1,0,0.2c0,0,0.1-0.5,0-0.2c0,0.1-0.1,0.3-0.1,0.4
                c0,0.1-0.1,0.2-0.1,0.4C58,40,58.1,40,58,40.2c0,0.1-0.1,0.2-0.1,0.3c-0.1,0.1-0.1,0.2-0.2,0.3c0,0-0.1,0.1-0.1,0.2
                c0,0,0.3-0.3,0.1-0.1c-0.2,0.2-0.4,0.4-0.6,0.6c0,0,0.4-0.3,0.1-0.1c-0.1,0-0.1,0.1-0.2,0.1c-0.1,0.1-0.2,0.1-0.3,0.2
                c-0.1,0-0.1,0.1-0.2,0.1c-0.2,0.1-0.2,0,0.2-0.1c-0.1,0-0.3,0.1-0.4,0.1c-0.1,0-0.3,0.1-0.4,0.1c0,0,0.5,0,0.2,0
                c-0.1,0-0.1,0-0.2,0c-0.1,0-0.2,0-0.3,0c-0.7,0-1.3,0-2,0c-0.9,0-1.9,0.8-1.8,1.8c0,1,0.8,1.8,1.8,1.8c0.1,0,0.3,0,0.4,0
                c0.3,0-0.2,0-0.2,0c0.1,0,0.1,0,0.2,0c0.1,0,0.3,0.1,0.4,0.1c0.1,0,0.1,0,0.2,0.1c0.2,0.1-0.4-0.2-0.1-0.1
                c0.2,0.1,0.4,0.2,0.7,0.4c0.2,0.2-0.1-0.1-0.1-0.1c0,0,0.1,0.1,0.1,0.1c0.1,0.1,0.2,0.2,0.3,0.3c0,0,0.1,0.1,0.1,0.1
                c0.2,0.2-0.1-0.1-0.1-0.1c0,0.1,0.2,0.2,0.2,0.3c0.1,0.1,0.1,0.2,0.2,0.3c0,0.1,0.1,0.1,0.1,0.2c-0.2-0.4-0.1-0.2,0-0.1
                c0,0.1,0.1,0.2,0.1,0.4c0,0.1,0,0.1,0,0.2c0.1,0.3,0-0.2,0-0.2c0,0.3,0,0.5,0,0.8c0,0.1,0,0.1,0,0.2c0,0,0.1-0.5,0-0.2
                c0,0.1-0.1,0.3-0.1,0.4c0,0.1-0.1,0.2-0.1,0.4c0.1-0.3,0.1-0.3,0.1-0.2c0,0.1-0.1,0.2-0.1,0.3c-0.1,0.1-0.1,0.2-0.2,0.3
                c0,0-0.1,0.1-0.1,0.2c0,0,0.3-0.3,0.1-0.1c-0.2,0.2-0.4,0.4-0.6,0.6c0,0,0.4-0.3,0.1-0.1c-0.1,0-0.1,0.1-0.2,0.1
                c-0.1,0.1-0.2,0.1-0.3,0.2c-0.1,0-0.1,0.1-0.2,0.1c-0.2,0.1-0.2,0,0.2-0.1c-0.1,0-0.3,0.1-0.4,0.1c-0.1,0-0.3,0.1-0.4,0.1
                c0,0,0.5,0,0.2,0c-0.1,0-0.1,0-0.2,0c-0.1,0-0.2,0-0.3,0c-0.7,0-1.3,0-2,0c-0.9,0-1.9,0.8-1.8,1.8c0,1,0.8,1.8,1.8,1.8
                c0.2,0,0.3,0,0.5,0c0,0-0.5-0.1-0.2,0c0.1,0,0.2,0,0.2,0.1c0.1,0,0.3,0.1,0.4,0.1c0.3,0.1-0.1-0.1-0.2-0.1
                c0.1,0.1,0.2,0.1,0.3,0.1c0.1,0.1,0.3,0.2,0.4,0.2c0.1,0,0.1,0.1,0.2,0.1c-0.4-0.2-0.2-0.2-0.1-0.1c0.1,0.1,0.2,0.2,0.3,0.3
                c0,0,0.1,0.1,0.1,0.1c0,0.1,0.1,0.1,0.1,0.1c-0.1-0.1-0.1-0.1-0.2-0.2c0.1,0.1,0.2,0.3,0.3,0.4c0.1,0.1,0.1,0.3,0.2,0.4
                c0.1,0.2-0.2-0.4-0.1-0.1c0,0.1,0,0.1,0.1,0.2c0.1,0.2,0.1,0.3,0.1,0.5c0.1,0.3,0-0.4,0-0.1c0,0.1,0,0.2,0,0.2c0,0.2,0,0.3,0,0.5
                c0,0,0,0,0,0.1c0,0.2,0,0.2,0-0.1c0,0,0,0.2,0,0.2c0,0.2-0.1,0.4-0.2,0.5c-0.1,0.4,0.2-0.4,0,0c0,0.1-0.1,0.1-0.1,0.2
                c-0.1,0.1-0.2,0.3-0.3,0.4c-0.1,0.2,0.3-0.3,0-0.1c-0.1,0.1-0.1,0.1-0.1,0.2c-0.1,0.1-0.1,0.1-0.2,0.2c0,0-0.4,0.3-0.2,0.1
                c0.2-0.2-0.1,0-0.1,0.1c-0.1,0-0.1,0.1-0.2,0.1c-0.1,0-0.1,0.1-0.2,0.1c0,0-0.4,0.2-0.1,0.1c0.3-0.1-0.1,0-0.1,0
                c-0.1,0-0.1,0-0.2,0.1s-0.1,0-0.2,0.1c-0.1,0-0.2,0-0.2,0c0.3,0,0.3,0,0.2,0c-0.2,0-0.5,0-0.7,0c-2.3,0-4.6,0-6.8,0
                c-3.6,0-7.1,0-10.7,0c-2.6,0-5.1,0-7.7,0c-0.2,0-0.4,0-0.7,0c-0.2,0-0.3,0-0.5,0c-0.4,0-0.8,0-1.2-0.1c0.4,0.1,0.1,0,0,0
                c-0.1,0-0.2,0-0.3-0.1c-0.2,0-0.4-0.1-0.7-0.1c-0.9-0.2-1.7-0.5-2.5-0.7c-0.8-0.2-1.6-0.4-2.4-0.6c-0.2,0-0.3-0.1-0.5-0.1
                c0.4,0.6,0.9,1.2,1.3,1.7c0-1,0-1.9,0-2.9c0-2.3,0-4.6,0-6.9c0-2.8,0-5.5,0-8.3c0-2.4,0-4.8,0-7.2c0-1.2,0-2.3,0-3.5
                c0,0,0,0,0-0.1c-0.6,0.6-1.2,1.2-1.8,1.8c0.8,0,1.6,0,2.4,0c0.6,0,1.2,0,1.8-0.2c1.1-0.4,2-1,2.6-2c0.3-0.4,0.5-0.8,0.7-1.2
                c0.4-0.7,0.7-1.5,1.1-2.2c1.3-2.7,2.7-5.4,4-8.1c0.4-0.9,0.9-1.8,1.3-2.7c0.1-0.2,0.2-0.4,0.3-0.6c0.5-1,0.8-2.1,0.8-3.3
                c0-1.6,0-3.1,0-4.7c0-0.7,0-1.5,0-2.2c0-0.1,0-0.3,0,0.1c0,0.2,0,0.3,0,0.2c-0.2,0.6-0.2,0.5-0.1,0.3c0.1-0.1,0.1-0.2,0.2-0.3
                c0-0.1,0.1-0.1,0.1-0.2c0,0,0.1-0.1,0.1-0.1c0.2-0.3-0.3,0.4-0.1,0.2c0.1-0.2,0.3-0.3,0.4-0.5c0.1-0.1,0.5-0.4,0,0
                c0.1-0.1,0.2-0.2,0.4-0.3c0.1-0.1,0.3-0.2,0.4-0.2c0.2-0.1-0.2,0.1-0.2,0.1c0.1,0,0.2-0.1,0.3-0.1c0.2-0.1,0.3-0.1,0.5-0.1
                c0.1,0,0.2,0,0.2-0.1c0.5-0.1-0.3,0,0,0c0.2,0,0.4,0,0.6,0c0.1,0,0.3,0,0.4,0c-0.1,0-0.2,0-0.2,0c0.1,0,0.2,0,0.3,0.1
                c0.2,0.1,0.4,0.1,0.6,0.2c0.4,0.1-0.3-0.2,0.1,0C35.8,3.9,35.9,4,36,4c0.2,0.1,0.5,0.2,0.6,0.4c-0.2-0.3-0.2-0.1-0.1,0
                c0.1,0.1,0.1,0.1,0.2,0.2C37,4.8,37.2,5,37.4,5.3c0.3,0.4-0.2-0.3,0,0.1c0.1,0.1,0.2,0.3,0.3,0.4c0.2,0.3,0.3,0.6,0.5,0.9
                c0,0.1,0.1,0.2,0.1,0.3c-0.2-0.5-0.1-0.2,0-0.1c0.1,0.2,0.1,0.3,0.2,0.5c0.1,0.4,0.2,0.8,0.3,1.2c0,0.2,0.1,0.4,0.1,0.5
                c0.1,0.4,0-0.4,0,0c0,0.1,0,0.2,0,0.3c0,0.4,0.1,0.9,0.1,1.3c0,0.8-0.1,1.6-0.2,2.4c-0.1,0.8-0.2,1.5-0.3,2.2
                c0,0.2-0.1,0.4-0.1,0.7c0,0.1-0.1,0.4,0,0.1c0-0.3,0,0,0,0.1c-0.1,0.4-0.1,0.9-0.2,1.3c-0.2,1.6-0.5,3.2-0.7,4.8
                c-0.1,0.7-0.2,1.3-0.3,2c-0.1,0.6-0.1,1.1,0.3,1.6c0.3,0.4,0.8,0.7,1.4,0.7c1.1,0,2.2,0,3.3,0c2.3,0,4.7,0,7,0c2.1,0,4.1,0,6.2,0
                c0.5,0,0.9,0,1.4,0c0,0,0.1,0,0.1,0c0.2,0,0.3,0,0.5,0c0.4,0-0.3-0.1,0,0c0.1,0,0.2,0,0.3,0.1c0.2,0.1,0.4,0.1,0.6,0.2
                c0,0,0.1,0,0.1,0.1c0,0-0.4-0.2-0.2-0.1c0.1,0.1,0.2,0.1,0.3,0.2c0.2,0.1,0.3,0.2,0.5,0.3c0.2,0.1-0.2-0.2-0.2-0.1
                c0,0,0.1,0.1,0.1,0.1c0.1,0.1,0.2,0.1,0.2,0.2c0.1,0.1,0.3,0.3,0.4,0.4c0.1,0.1-0.1-0.2-0.1-0.2c0,0,0.1,0.1,0.1,0.1
                c0.1,0.1,0.1,0.2,0.2,0.3c0.1,0.2,0.2,0.3,0.3,0.5c0.1,0.3-0.2-0.4,0,0c0,0.1,0.1,0.2,0.1,0.3c0.1,0.2,0.1,0.4,0.1,0.6
                c0.1,0.3,0-0.4,0-0.1c0,0.1,0,0.3,0,0.4c0,0.2,0,0.4,0,0.6c0,0,0.1-0.4,0-0.2c0,0.1,0,0.1,0,0.1c0,0.1-0.1,0.2-0.1,0.3
                c-0.1,0.2-0.1,0.4-0.2,0.6c0,0,0.2-0.4,0.1-0.2c0,0,0,0.1-0.1,0.1c-0.1,0.1-0.1,0.2-0.2,0.3c-0.1,0.1-0.1,0.2-0.2,0.2
                c0,0.1-0.2,0.3-0.1,0.1c0.2-0.2,0,0.1-0.1,0.1c-0.1,0.1-0.2,0.2-0.2,0.2c-0.1,0.1-0.1,0.1-0.2,0.2c-0.1,0.1-0.3,0.3-0.1,0.1
                c0.2-0.2,0,0-0.1,0.1c-0.1,0.1-0.2,0.1-0.3,0.2c-0.1,0.1-0.2,0.1-0.3,0.1c-0.1,0-0.4,0.2-0.1,0.1c0.3-0.1,0,0-0.1,0
                c-0.1,0-0.2,0.1-0.3,0.1c-0.1,0-0.2,0.1-0.3,0.1c-0.1,0-0.5,0.1-0.2,0c0.3,0,0,0-0.1,0C57.2,33.2,57,33.2,56.9,33.2
                c-0.9,0-1.9,0.8-1.8,1.8c0,1,0.8,1.8,1.8,1.8c2.3,0,4.4-1.1,5.7-3c1.3-1.7,1.6-4.1,0.8-6.1c-0.8-2.1-2.4-3.7-4.6-4.4
                c-0.8-0.2-1.6-0.3-2.4-0.3c-1.9,0-3.7,0-5.5,0c-2.5,0-5,0-7.5,0c-1.4,0-2.9-0.1-4.4,0c0,0,0,0-0.1,0c0.6,0.8,1.2,1.5,1.7,2.3
                c0.5-3.3,1.1-6.6,1.5-9.9c0.2-1.5,0.4-3.1,0.4-4.7c0-1.9-0.3-3.8-1.1-5.6c-0.7-1.5-1.7-2.8-3.1-3.8c-1.8-1.3-4.2-1.7-6.3-1
                c-1.4,0.5-2.6,1.4-3.4,2.6c-0.4,0.5-0.6,1.1-0.6,1.8c0,1.8,0,3.7,0,5.5c0,0.3,0,0.7,0,1c0,0.3,0.1,0.7,0,0.9c0-0.1,0-0.2,0-0.2
                c0,0.1,0,0.2-0.1,0.3c0,0.1-0.1,0.3-0.1,0.4c0,0.1-0.1,0.2-0.1,0.2c0,0,0,0.1,0,0.1c-0.1,0.2-0.1,0.2,0-0.1
                c-0.1,0.1-0.1,0.2-0.1,0.3c-0.2,0.3-0.3,0.7-0.5,1c-1.4,2.7-2.7,5.5-4.1,8.2c-0.8,1.6-1.6,3.1-2.3,4.7c-0.1,0.1-0.1,0.2-0.2,0.4
                c0,0.1-0.1,0.1-0.1,0.2c0,0.1-0.1,0.2-0.1,0.2c-0.2,0.3,0.3-0.3,0,0c-0.1,0.1-0.3,0.2-0.4,0.4c0.1-0.1,0.1-0.1,0.2-0.2
                c-0.1,0-0.1,0.1-0.2,0.1c-0.1,0.1-0.3,0.1-0.4,0.2c0.1,0,0.2-0.1,0.2-0.1c-0.1,0-0.1,0-0.2,0.1c-0.1,0-0.1,0-0.2,0.1
                c0,0-0.1,0-0.1,0c-0.2,0-0.1,0,0.2,0c-0.1,0-0.2,0-0.3,0c-0.1,0-0.2,0-0.3,0c-0.5,0-1,0-1.5,0c-0.3,0-0.6,0-0.9,0
                c-1,0-1.8,0.8-1.8,1.8c0,1,0,1.9,0,2.9c0,2.3,0,4.6,0,6.9c0,2.8,0,5.5,0,8.3c0,2.4,0,4.8,0,7.2c0,1.2,0,2.3,0,3.5c0,0,0,0,0,0.1
                c0,0.8,0.6,1.6,1.3,1.7c1.3,0.3,2.5,0.5,3.8,0.9c0.7,0.2,1.4,0.5,2.2,0.6c1,0.2,2,0.3,3,0.3c0.2,0,0.3,0,0.5,0c2.5,0,4.9,0,7.4,0
                c3.7,0,7.3,0,11,0c2.4,0,4.8,0,7.2,0c0.3,0,0.6,0,0.9,0c1.9-0.2,3.7-1.3,4.7-3c1.8-3,0.5-7.2-2.7-8.6c-0.8-0.4-1.6-0.6-2.5-0.6
                c0,1.2,0,2.4,0,3.6c0.7,0,1.3,0,2,0c1.5,0,2.8-0.5,4-1.4c1.3-1,2-2.6,2.2-4.2c0.2-1.6-0.4-3.2-1.4-4.4c-1.1-1.4-2.9-2.2-4.7-2.2
                c0,1.2,0,2.4,0,3.6c0.7,0,1.3,0,2,0c1.5,0,2.8-0.5,4-1.4c1.3-1,2-2.6,2.2-4.2c0.2-1.6-0.4-3.2-1.4-4.4c-1.1-1.4-2.9-2.2-4.7-2.2
                c0,1.2,0,2.4,0,3.6c0.4,0,0.9,0,1.3,0c0.9,0,1.9-0.8,1.8-1.8C58.7,34,57.9,33.2,56.9,33.2z"
                    />
                  </g>
                </g>
                <g>
                  <g>
                    <path
                      d="M14.9,60.8c-3.4,0-6.7,0-10.1,0c-0.4,0-0.8,0-1.2,0c-0.3,0,0.5,0.1,0,0c-0.3-0.1,0.4,0.2,0.1,0c0,0-0.1-0.1-0.2-0.1
                c0.1,0.1,0.1,0.1,0.2,0.2c0,0-0.1-0.1-0.1-0.1c-0.2-0.2,0.2,0.4,0.1,0.1c0,0,0-0.1,0-0.1c-0.1-0.2-0.1-0.1,0.1,0.2
                c0,0-0.1-0.5-0.1-0.1c0,0.3,0,0,0-0.1c0-0.8,0-1.6,0-2.4c0-3.7,0-7.4,0-11.1c0-4.2,0-8.5,0-12.7c0-1.5,0-2.9,0-4.4
                c0-0.4,0-0.8,0-1.2c0-0.2,0-0.4,0-0.5c0.1,0.3-0.1,0.4,0,0.2c0,0,0-0.1,0-0.1c0.1-0.2,0-0.1-0.1,0.2c0,0,0.1-0.2,0.1-0.2
                c-0.1,0.1-0.1,0.1-0.2,0.2c0,0,0.1-0.1,0.1-0.1c0.2-0.2-0.4,0.2-0.1,0.1c0,0,0.1-0.1,0.2-0.1c-0.1,0-0.2,0.1-0.3,0.1
                c0.1,0,0.1,0,0.2-0.1c0.3-0.1-0.5,0-0.1,0c0.1,0,0.2,0,0.3,0c0.7,0,1.4,0,2.1,0c2.1,0,4.2,0,6.4,0c0.7,0,1.4,0,2.1,0
                c0.1,0,0.2,0,0.4,0c0.1,0,0.2,0,0,0c-0.3-0.1,0,0,0,0c0.3,0.1-0.4-0.2-0.1,0c0,0,0.1,0,0.1,0c0.2,0.1,0.1,0.1-0.1-0.1
                c0,0,0.2,0.2,0.2,0.2c-0.3-0.2-0.2-0.3-0.1-0.1c0,0,0,0.1,0,0.1c0.1,0.2,0.1,0.1-0.1-0.2c0,0.1,0,0.1,0.1,0.2c0,0.2,0,0.1,0-0.2
                c0,0.2,0,0.4,0,0.5c0,0.4,0,0.8,0,1.2c0,3.5,0,6.9,0,10.4c0,4.3,0,8.7,0,13C14.8,55.9,14.8,58.3,14.9,60.8
                C14.8,60.7,14.8,60.8,14.9,60.8C14.8,61.2,14.9,60.4,14.9,60.8C14.8,60.7,14.8,60.8,14.9,60.8c-0.1,0.2-0.1,0.2,0-0.1
                c0,0-0.1,0.2-0.1,0.2C14.7,61,15.1,60.5,14.9,60.8C14.8,60.8,14.8,60.8,14.9,60.8C14.6,60.9,14.7,60.9,14.9,60.8
                c0-0.1-0.1,0-0.2,0.1C14.8,60.8,14.9,60.8,14.9,60.8C14.9,60.7,14.8,60.8,14.9,60.8C14.4,60.9,15.2,60.7,14.9,60.8
                c-0.9,0-1.9,0.8-1.8,1.8c0,1,0.8,1.8,1.8,1.8c2-0.1,3.5-1.6,3.6-3.6c0-0.2,0-0.5,0-0.7c0-1.2,0-2.4,0-3.7c0-4.2,0-8.4,0-12.5
                c0-4,0-8,0-12.1c0-1,0-2.1,0-3.1c0-0.5,0-1-0.2-1.5c-0.4-1.2-1.4-2.1-2.7-2.3c-0.3-0.1-0.6-0.1-0.9-0.1c-3.6,0-7.2,0-10.8,0
                c-0.1,0-0.3,0-0.4,0c-1.3,0.1-2.5,0.8-3,1.9C0.1,27.4,0,28,0,28.7c0,1,0,2,0,3c0,4,0,8,0,12c0,4.2,0,8.4,0,12.6
                c0,1.2,0,2.5,0,3.7c0,0.3,0,0.5,0,0.8c0,1.9,1.5,3.4,3.4,3.6c0.4,0,0.9,0,1.3,0c0.9,0,1.9,0,2.8,0c2.1,0,4.2,0,6.3,0
                c0.4,0,0.8,0,1.1,0c0.9,0,1.9-0.8,1.8-1.8C16.6,61.6,15.9,60.8,14.9,60.8z"
                    />
                  </g>
                </g>
              </g>
            </svg>
          </div>
        </div>
        <div class="d-block text-center m-3">
          <span style="color: black; font-size: 1.5rem; font-weight: 600"
            >Thanks for initiating your repayment!</span
          >
        </div>
        <div class="d-block text-center">
          <span style="color: #5b5a5a">
            CanPay has initiated a debit for
            <span style="color: black !important"
              ><b>{{ debit_amount }}</b></span
            >
            from your account number
            <span style="color: black !important"
              ><u
                ><b>xxxx{{ return_initiated_account }}</b></u
              ></span
            >. <br /><br />
            <span style="color: black !important"
              ><b>This will take at least 2 business days,</b></span
            >
            please make sure sufficient funds remain available in your account.
          </span>
        </div>
        <div class="row mt-5">
          <div class="col-12 text-center">
            <button
              type="button"
              class="btn-black"
              style="
                height: 60px;
                background: #149240 !important;
                border: none !important;
              "
              @click="hideModal('represent-success')"
            >
              <span class="purchasepower-modal-ok-label">OK</span>
            </button>
          </div>
        </div>
      </div>
    </b-modal>
    <b-modal
      ref="validation-modal"
      no-close-on-backdrop
      hide-footer
      v-b-modal.modal-center
      modal-backdrop
      hide-header
      id="validation-modal"
      centered
    >
      <div class="color">
        <div class="purchaserpower-modal-text mt-4">
          <div class="d-flex justify-content-center mb-3">
            <svg v-if="alertType == 'success'" fill="#0aa942" width="78" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
            viewBox="0 0 52 52" style="enable-background:new 0 0 52 52;" xml:space="preserve">
            <g><path d="M26,0C11.664,0,0,11.663,0,26s11.664,26,26,26s26-11.663,26-26S40.336,0,26,0z M26,50C12.767,50,2,39.233,2,26S12.767,2,26,2s24,10.767,24,24S39.233,50,26,50z"/><path d="M38.252,15.336l-15.369,17.29l-9.259-7.407c-0.43-0.345-1.061-0.274-1.405,0.156c-0.345,0.432-0.275,1.061,0.156,1.406l10,8C22.559,34.928,22.78,35,23,35c0.276,0,0.551-0.114,0.748-0.336l16-18c0.367-0.412,0.33-1.045-0.083-1.411C39.251,14.885,38.62,14.922,38.252,15.336z"/></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g>
            </svg>
          
            <svg v-if="alertType == 'error'" width="80" fill="#f05f5f" viewBox="0 0 500 500" xmlns="http://www.w3.org/2000/svg"><g transform="matrix(0.962608, 0, 0, 0.962608, 4.347751, 3.048281)" style="">  <g>    <path d="M437.018,74.987c-99.968-99.977-262.067-99.977-362.035,0c-99.977,99.959-99.977,262.059,0,362.027 c99.968,99.977,262.067,99.977,362.035,0C536.994,337.054,536.994,174.955,437.018,74.987z M418.918,418.914 c-89.984,89.967-235.853,89.967-325.837,0c-89.967-89.975-89.967-235.844,0-325.828c89.984-89.967,235.853-89.967,325.837,0 C508.885,183.07,508.885,328.939,418.918,418.914z"/>  </g></g><g transform="matrix(0.962608, 0, 0, 0.962608, 4.347751, 3.048281)" style="">  <g>    <path d="M 270.314 259.421 L 341.727 188.008 C 346.111 183.632 346.111 176.518 341.727 172.141 C 337.35 167.758 330.236 167.758 325.86 172.141 L 254.447 243.554 L 183.033 172.141 C 178.657 167.75 171.543 167.75 167.167 172.141 C 162.783 176.518 162.783 183.632 167.167 188.008 L 238.58 259.421 L 167.167 330.835 C 162.783 335.211 162.783 342.325 167.167 346.702 C 171.543 351.086 178.657 351.086 183.033 346.702 L 254.447 275.288 L 325.86 346.702 C 330.236 351.085 337.35 351.085 341.727 346.702 C 346.111 342.325 346.111 335.211 341.727 330.835 L 270.314 259.421 Z" style=""/>  </g></g><g transform="matrix(0.962608, 0, 0, 0.962608, 4.347751, 3.048281)" style=""/><g transform="matrix(0.962608, 0, 0, 0.962608, 4.347751, 3.048281)" style=""/><g transform="matrix(0.962608, 0, 0, 0.962608, 4.347751, 3.048281)" style=""/><g transform="matrix(0.962608, 0, 0, 0.962608, 4.347751, 3.048281)" style=""/><g transform="matrix(0.962608, 0, 0, 0.962608, 4.347751, 3.048281)" style=""/><g transform="matrix(0.962608, 0, 0, 0.962608, 4.347751, 3.048281)" style=""/><g transform="matrix(0.962608, 0, 0, 0.962608, 4.347751, 3.048281)" style=""/><g transform="matrix(0.962608, 0, 0, 0.962608, 4.347751, 3.048281)" style=""/><g transform="matrix(0.962608, 0, 0, 0.962608, 4.347751, 3.048281)" style=""/><g transform="matrix(0.962608, 0, 0, 0.962608, 4.347751, 3.048281)" style=""/><g transform="matrix(0.962608, 0, 0, 0.962608, 4.347751, 3.048281)" style=""/><g transform="matrix(0.962608, 0, 0, 0.962608, 4.347751, 3.048281)" style=""/><g transform="matrix(0.962608, 0, 0, 0.962608, 4.347751, 3.048281)" style=""/><g transform="matrix(0.962608, 0, 0, 0.962608, 4.347751, 3.048281)" style=""/><g transform="matrix(0.962608, 0, 0, 0.962608, 4.347751, 3.048281)" style=""/>
            </svg>
          </div>
          <div class="d-block text-center">
            <label class="purchasepower-def-label">
              {{ error_message }}
            </label>
          </div>
          <div class="row text-center mt-4">
            <div class="col-12">
              <button
                type="button"
                class="btn-black"
                style="height: 60px"
                @click="hideModal('validation-modal')"
              >
                <span class="purchasepower-modal-ok-label">OK</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </b-modal>
    <!-- change bank account details modal -->
    <b-modal
      ref="change-bank-modal"
      id="change-bank-modal"
      hide-footer
      v-b-modal.modal-center
      no-close-on-backdrop
      hide-header
      centered
    >
      <div class="row mt-3">
        <div class="col-12 text-center">
          <svg
            version="1.1"
            id="Layer_1"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            x="0px"
            y="0px"
            width="120"
            height="120"
            viewBox="0 0 100 125"
            style="enable-background: new 0 0 100 125"
            xml:space="preserve"
            fill="#e24141"
          >
            <path
              d="M96.2,47.5l-22-38c-0.4-0.6-1-1-1.7-1h-44c-0.7,0-1.4,0.4-1.7,1l-22,37.9c-0.4,0.6-0.4,1.4,0,2l22,38.1c0.4,0.6,1,1,1.7,1
    h44c0.7,0,1.4-0.4,1.7-1l22-38C96.6,48.9,96.6,48.1,96.2,47.5z M71.3,84.5H29.7L8.8,48.4l20.8-35.9h41.7l20.8,36L71.3,84.5z
     M50.5,60.5c1.1,0,2-0.9,2-2v-30c0-1.1-0.9-2-2-2c-1.1,0-2,0.9-2,2v30C48.5,59.6,49.4,60.5,50.5,60.5z M48.4,66.4
    c-0.6,0.6-0.9,1.3-0.9,2.1c0,0.8,0.3,1.6,0.9,2.1s1.3,0.9,2.1,0.9c0.8,0,1.6-0.3,2.1-0.9c0.6-0.6,0.9-1.3,0.9-2.1
    c0-0.8-0.3-1.6-0.9-2.1C51.5,65.3,49.5,65.3,48.4,66.4z"
            />
          </svg>
        </div>
      </div>
      <div class="row">
        <div class="col-12 text-center">
          You currently have unpaid purchases. Your bank informed us that they
          could not complete your payment. In order to pay for your purchase and
          continue using CanPay, you must update your banking details.
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-12 text-center">
          <button @click="changeBank" type="button" class="btn-signup-splash">
            <span class="forgetpassword-ok-label">Add new bank account</span>
          </button>
        </div>
      </div>
    </b-modal>
        <b-modal
      ref="confirm-bank-account"
      hide-footer
      v-b-modal.modal-center
      modal-backdrop
      hide-header
      id="confirm-bank-account"
      centered
    >
      <div class="color">
        <div class="purchaserpower-modal-text mt-4">
          <div class="d-block text-center">
            <label class="purchasepower-def-label">
              Your account will be debited against XXXX{{bank_id}}
            </label>
          </div>
          <div class="row">
            <div class="col-12 text-center">

              <button
                class="btn btn-danger btn-md center-block tip-ok-btn"
                @click="confirmBankAccount()"
              >
                <span class="forgetpassword-ok-label">Confirm</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </b-modal>
        <b-modal
      ref="confirm-repayment-schedule"
      hide-footer
      v-b-modal.modal-center
      modal-backdrop
      hide-header
      id="confirm-repayment-schedule"
      centered
    >
      <div class="color">
        <div class="purchaserpower-modal-text mt-4">
          <div class="d-block text-center">
            <label class="purchasepower-def-label">
              Please confirm funds<br/> will be available<br/> in your account.
            </label>
              <br/>
              <br/>
            <span>CanPay will debit your account on<br/> <b>{{reschedule_date_text}}</b>. Once you agree, you<br/> will not be able to change this date. </span>
          </div>
          <div class="row">
            <div class="col-12 text-center">
              <button
                class="btn btn-login-signup btn-md center-block tip-ok-btn pad-0 mt-6"
                @click="confirmRepayment()"
              >
                <span class="forgetpassword-ok-label">Agree, funds will be available</span>
              </button>
              <button
                class="btn btn-login btn-md center-block tip-btn mt-3"
                @click="cancelRepaymentSchedule()"
              >
                <span class="forgetpassword-ok-label">Cancel Schedule</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </b-modal>
    <real-account-number-modal page_name="return_banklist" ref="RealAccountNumberModal"></real-account-number-modal>
  </div>
</div>
</template>
<script>
import api from "../../api/account.js";
import Loading from "vue-loading-overlay";
import constants from "../Common/constant.js";
import moment from "moment";
import RealAccountNumberModal from './RealAccountNumberModal.vue';
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  name: "BankList",
  /**
   * @description -
   * @returns {any}
   * qr_url => this will store the qr-url from generate qrcode api
   * qr_code => this will store the qr code in base64 format
   * payment_pin => this will store the payment pin
   */

  data() {
    let self = this;
    return {
      isLoading: false,
      fullPage: true,
      currentUser: null,
      bank_changed: false,
      account_no: "",
      bank_id: "",
      repayment_bank_id: "",
      banking_solution: "",
      bank_list: [],
      transaction: {},
      show_refresh_btn: false,
      account_id: "",
      return_initiated_account: "",
      debit_amount: 0.0,
      error_message: null,
      changeBankAccount: null,
      returnAccountId: null,
      accountCount: 0,
      balance_checking: localStorage.getItem("balance_checking"),
      blacklistedAccountNumber: constants.blacklistedAccountNumber,
      blockedRoutingNumber: constants.blockedRoutingNumber,
      reschedule_date_text:null,
      returnAccountNumber:null,
      returnRoutingNumber:null,
      activeBankAccountNumber:null,
      activeBankRoutingNumber:null,
      find: false,
      alertType: "",
      activeAccordion: 'active_bank', // Set the active accordion to the active bank by default
      activeManulaBankAccordion: '', // Set the active accordion to the active bank by default
      currentBankName: '',
      isOpen: null // Use a single variable to store the index of the open accordion
    };
  },
  created() {
    if (localStorage.getItem("changeBankAccount")) {
      this.changeBankAccount = localStorage.getItem("changeBankAccount")==1 ? localStorage.getItem("changeBankAccount") : null;
      this.returnAccountId = localStorage.getItem("account_id");
      this.returnAccountNumber= localStorage.getItem("account_no_original");

      this.returnRoutingNumber = localStorage.getItem("routing_no_original");
    }

  },

  mounted() {
    this.$root.$emit("changeWhiteBackground", [true, false, ""]);
    var app = document.getElementById("app");
    if (app) {
      app.style.setProperty("background-color", "#ffffff");
    }
    var element = document.getElementsByClassName("content-wrap");
    if (element[0]) {
      element[0].style.setProperty("background-color", "#ffffff");
      element[0].style.height = "114vh";
      if(window.innerWidth>1200){
        element[0].style.height = "121vh";
      }
    }
    this.currentUser = localStorage.getItem("consumer_login_response")
      ? JSON.parse(localStorage.getItem("consumer_login_response"))
      : null;
    if (this.balance_checking == 1) {
      this.getBankAccountList();
      setTimeout(() => {
        this.$refs.RealAccountNumberModal.checkForRealAccountNumber();
      }, 2000);
    } else {
      this.getManualBankAccountList();
    }
  },
  components: {
    Loading,
    RealAccountNumberModal,
    CanPayLoader
  },
  watch: {
    bank_id: function (newval, oldval) {
      if (this.balance_checking == 1) {
        this.bank_changed = false;
        if (newval != oldval) {
          this.bank_changed = true;
        }
      }
    },
  },
  methods: {
    toggleAccordion(target,institution_id) {
      // Toggle the open/close state for the clicked accordion
      this.isOpen = this.isOpen === target ? null : target;
      console.log(this.isOpen,target);
      this.activeAccordion = this.activeAccordion === target ? null : target;
      if (target === 'active_bank') {
        // Set currentBankName only if the active bank accordion is clicked
        this.currentBankName = Object.keys(this.bank_list.active_bank)[0] || '';
      }else if(this.isOpen >= 0){
        this.getOtherBanks(target,institution_id);
      }
    }, // Method to get the account count per bank
    getAccountCount(all_bank) {
      let count = 0;
      for (const accountType in all_bank) {
        count += all_bank[accountType].length; // Sum the number of accounts in each accountType
      }
      return count;
    },
    toggleManualBankAccordion(target) {
      if (this.activeManulaBankAccordion === target) {
        this.activeManulaBankAccordion = '';
      } else {
        this.activeManulaBankAccordion = target;
      }
    },
    ucfirst(str) {
      return str.charAt(0).toUpperCase() + str.slice(1);
    },
    getOtherBanks(index,institution_id) {
      var self = this;
      self.isLoading = true;
      var request = {
        institution_id: institution_id,
        type: 'others',
      };
      api
        .getBankAccountList(request)
        .then((response) => {
          let newDataArray = response.data;
          if (self.bank_list.other_banks[index] && self.bank_list.other_banks[index].hasOwnProperty('accounts')) {
            self.bank_list.other_banks[index].accounts.push(newDataArray);
          }
          self.isLoading = false;
        })
        .catch(function (error) {
          self.isLoading = false;
        });
    },
    // represent unpaid debit for manually linked bank account consumers
    returnRepresentTransaction() {
      var self = this;
      self.$refs["warning-insufficient-fund"].hide();
      self.repayment_bank_id = self.bank_id;
      self.isLoading = true;
        if(self.changeBankAccount)
        {
        if (self.returnAccountNumber == self.activeBankAccountNumber && self.returnRoutingNumber == self.activeBankRoutingNumber && self.account_no ==  self.returnAccountNumber) {
          console.log("1",self.returnAccountNumber);
          console.log("2",self.activeBankAccountNumber);
          console.log("3",self.account_no)
          self.isLoading = false;
          self.error_message = "Please change the bank account before repayment as the selected account seems to be invalid and has an active return";
          self.alertType = 'error'
          self.$refs["validation-modal"].show();
          return false;
        }
        }
        
      if(localStorage.getItem("repaymentSchedule"))
      {
        self.confirmBankAccount();
        return false;
      }
      var request = {
        id: localStorage.getItem("transaction_id"),
        balance_checking: this.balance_checking,
        bank_account_id: self.repayment_bank_id,
      };

      api
        .representReturnTransaction(request)
        .then((response) => {
          self.isLoading = false;
          self.debit_amount = localStorage.getItem("debit_amount");
          self.transaction = {};
          self.return_initiated_account = response.data;
          self.$refs["represent-success"].show();
        })
        .catch(function (error) {
          self.isLoading = false;
          if (error.response.data.code == 598) {
            self.$refs["warning-insufficient-fund"].show();
          } else {
            self.error_message = error.response.data.message;
            self.alertType = 'error'
            self.$refs["validation-modal"].show();
          }
          self.isLoading = false;
        });
    },
    getBankAccountList() {
      var self = this;
      self.isLoading = true;
      var request = {};
      api
        .getBankAccountList(request)
        .then((response) => {
          self.bank_list = response.data.data;
          // Initialize currentBankName with the first bank name
          const activeBankName = Object.keys(this.bank_list.active_bank)[0];
          self.currentBankName = activeBankName || '';
          this.show_refresh_btn = response.data.refresh;
          self.findObjectByLabel(self.bank_list, "status");
          self.isLoading = false;
          if (self.changeBankAccount && self.accountCount == 1 && this.returnAccountNumber == this.activeBankAccountNumber) {
            self.$bvModal.show("change-bank-modal");
          }
        })
        .catch(function (error) {
          self.isLoading = false;
        });
    },
    getManualBankAccountList() {
      var self = this;
      self.isLoading = true;
      let request = {
        params: {
          is_return_page: 1
        }
      };
      api
        .getManualBankAccountList(request)
        .then((response) => {
          self.bank_list = response.data.data;
          this.show_refresh_btn = response.data.refresh;
          self.findObjectByLabel(self.bank_list, "status");
          self.isLoading = false;
          var not_disabled_account = 0;
          for (const bank in self.bank_list) {
            const accounts = self.bank_list[bank];
            for (const accountType in accounts) {
              const accountList = accounts[accountType];
              const matchingAccount = accountList.find(account => account.status === 1);
              if (matchingAccount) {
                self.activeManulaBankAccordion = matchingAccount.bank_name;
              }
              // Find a non-disabled account (is_disable === 0)
              const notDisabledAccount = accountList.find(account => account.is_disable === 0);
              if (notDisabledAccount) {
                // You can perform an action here, such as storing or logging the not disabled account
                not_disabled_account = 1;
              }
            }
          }
          if (self.changeBankAccount && not_disabled_account == 0) {
            self.$bvModal.show("change-bank-modal");
          }
        })
        .catch(function (error) {
          self.isLoading = false;
        });
    },
    findObjectByLabel(obj, label) {
    
      for (var elements in obj) {
        if (elements === label) {
          if (obj[elements] == 1) {
            this.find =true;
            this.bank_id = obj["id"];
            this.activeBankAccountNumber = obj["account_no_original"];
            this.account_no = obj["account_no_original"];
            this.activeBankRoutingNumber = obj["routing_no_original"];            
          }
          this.accountCount++;
        }
        if (typeof obj[elements] === "object") {

           this.findObjectByLabel(obj[elements], "status");
         
          
        }
      }
    },
        formatDate(data) {
      return moment.utc(data).local().format(" MMM DD, YYYY");
    },
    save() {
      var self = this;
      self.isLoading = true;
      var request = {
        id: self.bank_id,
        banking_solution: self.banking_solution,
      };
      api
        .saveBankDetailsFromList(request)
        .then((response) => {
          self.currentUser.account_no = self.account_no.slice(-4);
          localStorage.setItem(
            "consumer_login_response",
            JSON.stringify(self.currentUser)
          );
          self.isLoading = false;
          if (localStorage.getItem("redirect-return") != null) {
            localStorage.removeItem("redirect-return");
            self.$router.push("/returns");
          } else {
            self.$router.push("/pay");
          }
        })
        .catch(function (error) {
          self.isLoading = false;
          if (error.response.data.data == 1) {
            self.$refs["update-bank-modal"].show();
          } else if (error.response.data.code == 597) {
            self.generateConnectFix(error.response.data.data.institutionId);
          }
        });
    },
    selectBank(account) {
      var target = document.getElementById(account.id);
      target.checked = true;
      this.bank_id = account.id;
      this.banking_solution = account.banking_solution_name;
      if (target.getAttribute("ischecked") == 0) {
        this.account_no = account.account_no;
      }
    },
    clickClose() {
      var self = this;
      if (localStorage.getItem("redirect-return") != null) {
        localStorage.removeItem("redirect-return");
        self.$router.push("/returns");
      } else {
        self.$router.push("/pay");
      }
    },
    //calls the api to change bank account through finicity portal
    gotoBankLink() {
      let self = this;
      self.$router.push("/banklinking");
    },
    clickUpdateBanking() {
      var self = this;
      self.isLoading = true;
      self.$refs["delink-modal"].hide();
      self.$refs["update-bank-modal"].hide();
      if (self.balance_checking == 0 || localStorage.getItem("can_choose_manual_direct_bank") != null) {
        localStorage.setItem("redirect-return", 1);
        self.$router.push("/banklinking");
        return false;
      } else {
        api
          .updateLinkGenerate()
          .then((response) => {
            if (response.code == 200) {
              const finicityConnectUrl = response.data.link;
              window.finicityConnect.launch(finicityConnectUrl, {
                selector: "#connect-container",
                overlay: "rgba(255,255,255, 0)",
                success: function (data) {
                  console.log("Yay! We got data", data);
                  if (data.code == 200) {
                    //now store the details at canpay end
                    self.storeBankDetails();
                    setTimeout(() => {
                      this.$refs.RealAccountNumberModal.checkForRealAccountNumber();
                    }, 2000);
                  }
                },
                cancel: function () {
                  self.isLoading = false;
                  console.log("The user cancelled the iframe");
                },
                error: function (err) {
                  self.isLoading = false;
                  console.error(
                    "Some runtime error was generated during Finicity Connect",
                    err
                  );
                },
                loaded: function () {
                  self.isLoading = false;
                  console.log(
                    "This gets called only once after the iframe has finished loading"
                  );
                },
                route: function (event) {
                  self.isLoading = false;
                },
                user: function (event) {
                  if (event.data.errorCode) {
                    console.log(event.data.data.institutionId);
                    setTimeout(() => {
                      window.finicityConnect.destroy();
                      //if error code is present then call the connect fix api
                      self.generateConnectFix(event.data.data.institutionId);
                    }, 2000);
                  }
                },
              });
            } else {
              self.isLoading = false;
            }
          })
          .catch((err) => {
            self.isLoading = false;
            console.log(err);
          });
      }
    },
    generateConnectFix(id) {
      let self = this;
      this.isLoading = true;
      var request = {
        institution_id: id,
      };
      api
        .generateConnectFix(request)
        .then((response) => {
          if (response.code == 200) {
            const finicityConnectUrl = response.data.link;
            window.finicityConnect.launch(finicityConnectUrl, {
              selector: "#connect-container",
              overlay: "rgba(255,255,255, 0)",
              success: function (data) {
                console.log("Yay! We got data", data);
                if (data.code == 200) {
                  //now store the details at canpay end
                  self.storeBankDetails();
                }
              },
              cancel: function () {
                self.isLoading = false;
                console.log("The user cancelled the iframe");
              },
              error: function (err) {
                console.log(err);
                self.isLoading = false;
                console.error(
                  "Some runtime error was generated during Finicity Connect",
                  err
                );
              },
              loaded: function () {
                self.isLoading = false;
                console.log(
                  "This gets called only once after the iframe has finished loading"
                );
              },
              route: function (event) {
                self.isLoading = false;
                console.log(
                  "This is called as the user progresses through Connect"
                );
              },
              user: function (event) {
                if (event.data.errorCode) {
                  console.log(event.data.data.institutionId);
                  setTimeout(() => {
                    window.finicityConnect.destroy();
                    //if error code is present then call the connect fix api
                    self.generateConnectFix(event.data.data.institutionId);
                  }, 2000);
                }
              },
            });
          } else {
            self.isLoading = false;
          }
        })
        .catch((err) => {
          self.isLoading = false;
          console.log(err);
        });
    },
    //stores the bank details into canpay end
    storeBankDetails() {
      var self = this;
      this.isLoading = true;
      var request = {
        event: "return_repayment",
      };
      api
        .updateBank(request)
        .then((response) => {
          if (response.code == 200) {
            localStorage.setItem(
              "consumer_login_response",
              JSON.stringify(response.data)
            );
            this.getBankAccountList();
            setTimeout(() => {
              this.$refs.RealAccountNumberModal.checkForRealAccountNumber();
            }, 2000);
          }
        })
        .catch(function (error) {
          self.isLoading = false;
          if (error.response.data.code == 598) {
            self.$refs["delink-modal"].show();
          } else if (error.response.data.code == 597) {
            self.generateConnectFix(error.response.data.data.institutionId);
          } else {
            self.error_message = error.response.data.message;
            self.alertType = 'error'
            self.$refs["validation-modal"].show();
          }
        });
    },
    showModal(modal) {
      this.$refs[modal].show();
    },
    hideModal(modal) {
      this.alertType = ''
      if (modal == "represent-success") {
        this.$router.push("/returns").catch((err) => {});
        this.$refs[modal].hide();
      } else {
        this.$refs[modal].hide();
      }
    },

    disableBank() {
      var self = this;
      self.$refs["confirm-modal"].hide();
      var request = {
        id: self.account_id,
      };
      self.isLoading = true;
      api
        .disableBankAccount(request)
        .then((response) => {
          self.$refs["disable-bank-success-modal"].show();
          self.getBankAccountList();
        })
        .catch(function (error) {
          self.isLoading = false;
        });
    },
    confirmDelete(account) {
      var self = this;
      self.account_id = account.id;
      self.$refs["confirm-modal"].show();
    },
    closeRemoveBankModal() {
      var self = this;
      self.$refs["disable-bank-success-modal"].hide();
    },
    changeBank() {
      let self = this;
      if (self.balance_checking) {
        self.$refs["change-bank-modal"].hide();
        self.gotoBankLink();
      }
    },
      confirmBankAccount(){
      var self = this;
      self.$refs["confirm-bank-account"].hide();
      self.$refs["confirm-repayment-schedule"].show();
      var repaymentDate = localStorage.getItem('repaymentDate');
      self.reschedule_date_text = self.formatDate(parseInt(repaymentDate.split('-')[2])+'-'+parseInt(repaymentDate.split('-')[0])+'-'+parseInt(repaymentDate.split('-')[1]));
    },
    confirmRepayment(){
      var self = this;

      var request = {
        id: localStorage.getItem('return_transaction_id'),
        account_id: self.bank_id,
        repayment_offset_post_date: localStorage.getItem('repaymentDate')
      };
      api
        .addReturnTransactionPaymentSchedule(request)
        .then((response) => {
          if(response.code == 200){
            self.$router.push("/returns");
            self.$refs["confirm-repayment-schedule"].hide();
            
          }
        })
        .catch(function (error) {

        });
    },
    cancelRepaymentSchedule(){
      var self = this;
      self.return_transaction_id = "";
      self.show_return_list = true;
      self.$refs["confirm-repayment-schedule"].hide();
    },
    
    updateBankAccount() {
      this.$refs["warning-insufficient-fund"].hide();
      this.$refs["confirm-bank-account"].hide();
      localStorage.setItem("redirect-return", true);
      this.$router.push("/banklinking");
    }, 
    repayDebit(check_balance) {
      var self = this;
      self.$refs["warning-insufficient-fund"].hide();
      self.isLoading = true;
      var request = {
        id: localStorage.getItem("transaction_id"),
        balance_checking: check_balance,
        bank_account_id: self.repayment_bank_id,
      };
      api
        .representReturnTransaction(request)
        .then((response) => {
          self.isLoading = false;
          self.debit_amount = localStorage.getItem("debit_amount");
          self.transaction = {};
          self.return_initiated_account = response.data;
          self.$refs["represent-success"].show();
        })
        .catch(function (error) {
          if (error.response.data.code == 598) {
            self.$refs["warning-insufficient-fund"].show();
          } else {
            self.error_message = error.response.data.message;
            self.$refs["validation-modal"].show();
          }
          self.isLoading = false;
        });
    },
    checkRealAccNo(){
      var self = this;
      self.$refs.RealAccountNumberModal.checkForRealAccountNumber(1,self.bank_id);
    }
  },
};
</script>
<style lang="scss">
#pay-modal-center___BV_modal_content_ {
  border-radius: 10px;
  margin: 10px;
  background-color: #ffffff;
}
#pay-modal-center___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#delink-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#refresh-balance-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#disable-bank-success-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#confirm-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#warning-insufficient-fund___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#represent-success___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#change-bank-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
}
#confirm-bank-account___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#confirm-repayment-schedule___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}

.bank-list::-webkit-scrollbar {
  width: 10px;
}

.bank-list::-webkit-scrollbar-track {
  background: #ffffff; 
}
 
.bank-list::-webkit-scrollbar-thumb {
  background: #dfdfdf; 
}

.bank-list::-webkit-scrollbar-thumb:hover {
  background: #dfdfdf; 
}

.accordion-item {
  border: 1px solid #ccc;
  border-radius: 5px;
  margin-bottom: 10px;
  margin-top: 10px;
}

.accordion-header {
  background-color: #f0f0f0;
  padding: 10px;
  cursor: pointer;
  border-bottom: 1px solid #ccc;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.active-accordion {
  background-color: #149240;
  color: #ffffff; /* Added line to set text color to white */
}

.accordion-header h3 {
  margin: 0;
  font-weight: bold;
}

.accordion-content {
  padding: 10px;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

.rotate-arrow {
  transition: transform 0.3s ease;
}

.rotate-arrow.rotate {
  transform: rotate(180deg);
}

ul {
  padding-left: 20px;
  margin: 0;
}

li {
  list-style-type: none;
}
</style>