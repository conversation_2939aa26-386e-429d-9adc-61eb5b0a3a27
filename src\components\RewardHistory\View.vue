<template>
  <div class="container min-h py-3">
    <div class="td-card">
        <div class="td-card-header">
            <div class="td-caption mb-2">$148</div>
            <div class="td-store-title">Store Name Here</div>
            <div class="td-separator"></div>
            <ul class="td-timing">
                <li class="d-flex justify-content-between">
                    <div>
                        <svg
                        class="mr-2"
                        version="1.1"
                        id="Layer_1"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        x="0px"
                        y="0px"
                        viewBox="0 0 90 112.5"
                        xml:space="preserve"
                        fill="#e9e9e9"
                        width="22px"
                        height="24px"
                        >
                        <path
                            d="M63,25h-2v-2c0-1.1-0.9-2-2-2s-2,0.9-2,2v2H33v-2c0-1.1-0.9-2-2-2s-2,0.9-2,2v2h-2c-3.3,0-6,2.7-6,6v32c0,3.3,2.7,6,6,6h36
                        c3.3,0,6-2.7,6-6V31C69,27.7,66.3,25,63,25z M65,63c0,1.1-0.9,2-2,2H27c-1.1,0-2-0.9-2-2V31c0-1.1,0.9-2,2-2h2v2c0,1.1,0.9,2,2,2
                    c1.1,0,2-0.9,2-2v-2h24v2c0,1.1,0.9,2,2,2s2-0.9,2-2v-2h2c1.1,0,2,0.9,2,2V63z"
                        />
                        <path
                            d="M59,39H31c-1.1,0-2,0.9-2,2s0.9,2,2,2h28c1.1,0,2-0.9,2-2S60.1,39,59,39z"
                        />
                        </svg> 
                        <span>22 May, 2022 | 4:16 PM</span>
                    </div>
                </li>
            </ul>
            <!-- Store Logo -->
            <img class="td-store-logo" src="https://www.trulieve.com/files/posts/Trulieve_logo-01.png" alt="">
        </div>
        <div class="td-card-body">
            <div class="row justify-content-between td-detail mb-3">
                <span class="title">Transaction Amount</span>
                <span class="value">$33.00</span>
            </div>
            <div class="row justify-content-between td-detail-point mb-3">
                <span class="title">Reward 200 pts</span>
                <span class="value">-$35.00</span>
            </div>
            <div class="row justify-content-between td-detail-total mb-3">
                <span class="title">TOTAL</span>
                <span class="value">$115.00</span>
            </div>
            <div class="row mx-0">
                <a class="anchor d-flex align-items-center mr-2" href="#">
                    <span class="td-detail-icon-avatar mr-1">
                    <svg
                        version="1.1"
                        id="Layer_1"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        x="0px"
                        y="0px"
                        viewBox="0 0 100 125"
                        xml:space="preserve"
                        width="10"
                        fill="#ffffff"
                    >
                        <path
                        d="M92.8,70.6L81.4,59.1c-2-2.1-4.3-3.1-6.8-3.1s-4.9,1.1-6.9,3.1L61,65.8c-0.6-0.3-1.2-0.6-1.8-0.9c-0.8-0.4-1.5-0.7-2.1-1.1
                        c-6.2-3.9-11.8-9.1-17.2-15.7c-2.7-3.4-4.5-6.2-5.8-9.2c1.8-1.6,3.4-3.3,5-4.9c0.6-0.6,1.2-1.2,1.8-1.8c2.1-2.1,3.2-4.5,3.2-7
                        s-1.1-4.9-3.2-7l-5.7-5.7c-0.7-0.7-1.3-1.3-1.9-2c-1.3-1.3-2.6-2.6-3.9-3.8c-2-2-4.3-3-6.8-3s-4.8,1-6.9,3L8.6,14
                        c-2.6,2.6-4.1,5.8-4.4,9.5c-0.4,4.6,0.5,9.4,2.7,15.3c3.3,9.1,8.4,17.5,15.9,26.5c9.1,10.8,20,19.4,32.5,25.4
                        c4.8,2.3,11.1,4.9,18.2,5.4c0.4,0,0.9,0,1.3,0c4.7,0,8.7-1.7,11.8-5.1l0.1-0.1c1.1-1.3,2.3-2.5,3.7-3.8c0.9-0.9,1.8-1.8,2.7-2.7
                        C97,80.2,97,74.7,92.8,70.6z M89.2,81.1c-0.8,0.9-1.6,1.7-2.5,2.5c-1.3,1.3-2.7,2.6-4,4.1c-2.2,2.3-4.7,3.4-8.1,3.4
                        c-0.3,0-0.7,0-1,0c-6.2-0.4-12-2.8-16.4-4.9C45.4,80.5,35,72.4,26.4,62.1c-7.1-8.5-11.9-16.5-15-25C9.5,31.9,8.7,27.8,9,23.9
                        c0.2-2.5,1.2-4.6,3-6.4l7.1-7.1c1.1-1,2.3-1.6,3.4-1.6s2.3,0.5,3.3,1.6c1.3,1.2,2.5,2.4,3.8,3.7c0.7,0.7,1.3,1.3,2,2l5.7,5.7
                        c1.2,1.2,1.7,2.3,1.7,3.4s-0.6,2.3-1.7,3.4c-0.6,0.6-1.2,1.2-1.8,1.8c-1.8,1.8-3.4,3.5-5.2,5.1l-0.1,0.1c-1.6,1.6-1.4,3.2-1,4.4
                        c0,0.1,0,0.1,0.1,0.2c1.5,3.5,3.5,6.9,6.7,10.9c5.7,7.1,11.8,12.5,18.4,16.8c0.8,0.5,1.7,1,2.5,1.4s1.5,0.7,2.1,1.1
                        c0.1,0,0.1,0.1,0.2,0.1c0.7,0.3,1.3,0.5,1.9,0.5c1.6,0,2.6-1,2.9-1.4l7.1-7.1c1.1-1.1,2.2-1.6,3.4-1.6c1.4,0,2.5,0.9,3.3,1.6
                        L89.3,74C92.1,76.9,90.8,79.4,89.2,81.1z"
                        />
                    </svg>
                    </span>
                    <label class="td-detail-store mb-0">239-317-6906</label>
                </a>
                <a class="anchor d-flex align-items-center" href="#">
                    <span class="td-detail-icon-avatar mr-1">
                    <svg
                        version="1.1"
                        id="Layer_1"
                        xmlns:cc="http://creativecommons.org/ns#"
                        xmlns:dc="http://purl.org/dc/elements/1.1/"
                        xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
                        xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
                        xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
                        xmlns:svg="http://www.w3.org/2000/svg"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        x="0px"
                        y="0px"
                        viewBox="5 5 110 120"
                        xml:space="preserve"
                        width="15"
                        fill="#ffffff"
                    >
                        <g transform="translate(0,-952.36218)">
                        <path
                            d="M25.9,968.2c-1.7,0.1-2.9,1.5-2.8,3.2c0,0,0,0.1,0,0.1l6.2,58.7c0.2,1.6,1.6,2.9,3.3,2.7c0.8-0.1,1.6-0.5,2.1-1.2l8.5-11
                            l8.3,14.3c0.8,1.4,2.7,1.9,4.1,1.1l12.1-7c1.4-0.8,1.9-2.7,1.1-4.1l-8.3-14.3l13.8-1.8c1.6-0.2,2.8-1.7,2.6-3.4
                            c-0.1-0.8-0.5-1.5-1.2-2l-47.8-34.8C27.3,968.3,26.6,968.1,25.9,968.2z M29.8,977.6l36.3,26.4l-10.8,1.4c-1.6,0.2-2.8,1.7-2.6,3.4
                            c0.1,0.4,0.2,0.8,0.4,1.1l9,15.6l-6.9,4l-9-15.6c-0.8-1.4-2.7-1.9-4.1-1.1c-0.3,0.2-0.6,0.5-0.9,0.8l-6.7,8.7L29.8,977.6
                            L29.8,977.6z"
                        />
                        </g>
                    </svg>
                    </span>
                    <label class="td-detail-store mb-0">www.trulieve.com</label>
                </a>
            </div>
        </div>
    </div>
  </div>
</template>
<script>
import moment from "moment";
export default {
    name: "TransactionDetail",
    data() {
        return {
        
        };
    },
    watch: {
        
    },
    created() {
    },
    components: {
        
    },
    methods: {
        
    },
    mounted() {
        // var element = document.getElementsByClassName("content-wrap");
        // if (element[0]) {
        // element[0].style.setProperty("background-color", "#149240");
        // }
        // this.$root.$emit("loginapp", [""]);
        // this.$root.$emit("changeWhiteBackground", [false, true, "TransactionDetailHeader"]);

        var element = document.getElementsByClassName("content-wrap");
        if (element[0]) {
            element[0].style.setProperty("background", "linear-gradient(0deg, #148f3f, #00ae44)");
            element[0].style.height = "114vh";
      if(window.innerWidth>1200){
        element[0].style.height = "121vh";
      }
        }
        this.$root.$emit("loginapp", [""]);
        this.$root.$emit("changeWhiteBackground", [
            false,
            false,
            "TransactionDetailHeader"
        ]);
    },
};
</script>


<style scoped>
.min-h{
    min-height: calc(100vh - 115px)!important;
}

.td-card{
}
.td-card-header{
    padding: 20px 15px;
    background: #000;
    color: #fff;
    text-align: left;
    border-radius: 6px 6px 0 0;
    position: relative;
}
.td-caption{
    font-size: 20px;
}
.td-store-title{
    font-size: 16px;
}
.td-separator{
    height: 1px;
    background: #373737;
    width: 100%;
    margin: 15px 0;
}
.td-timing{
    list-style: none;
    margin: 0;
    padding: 0;
    color: #e9e9e9;
    font-size: 12px;
}
.td-timing li{
    display: flex;
    align-items: center;
}
.td-store-logo{
    position: absolute;
    width: 80px;
    top: 15px;
    right: 15px;
}

.td-card-body{
    padding: 20px 15px;
    border-radius: 0 0 6px 6px;
    background: #ffffff;
}
.td-detail{
    align-items: center;
    margin: 0;
}
.td-detail .title{
    font-weight: bold;
    font-size: 17px;
    margin: 0;
}
.td-detail .value{
    font-weight: 600;
    font-size: 14px;
    margin: 0;
}

.td-detail-point{
    align-items: center;
    margin: 0;
    color: #149240;
}
.td-detail-point .title{
    font-weight: 600;
    font-size: 14px;
    margin: 0;
}
.td-detail-point .value{
    font-weight: 600;
    font-size: 14px;
    margin: 0;
}

.td-detail-total{
    align-items: center;
    background: #e3e3e3;
    padding: 10px 15px;
}
.td-detail-total .title{
    font-weight: bold;
    font-size: 18px;
    margin: 0;
}
.td-detail-total .value{
    font-weight: bold;
    font-size: 18px;
    margin: 0;
}
.td-detail-icon-avatar{
    height: 17px;
    width: 17px;
    border-radius: 50%;
    background-color: #118037 !important;
    display: flex;
    justify-content: center;
    align-items: center;
}
.td-detail-store{
    font-size: 12px;
}
</style>