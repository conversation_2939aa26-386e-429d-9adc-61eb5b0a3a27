const getRedirectUrl = (code) => {
    return new Promise((res, rej) => {
        axios.get(`short-urls/${code}`)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const createShortUrl = (url) => {
    return new Promise((res, rej) => {
        axios.post('short-urls', { url: url })
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            });
    });
};

export default {
    getRedirectUrl,
    createShortUrl

};