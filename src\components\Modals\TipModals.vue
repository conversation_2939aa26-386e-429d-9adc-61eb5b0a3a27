<template>
  <div>
    <!-- Leave a tip modal start -->
    <div>
      <b-modal
        ref="tip-modal-center"
        hide-footer
        v-b-modal.modal-center
        modal-backdrop
        hide-header
        id="tip-modal-center"
        centered
        title="BootstrapVue"
        class="hsf"
      >
        <div class="row cross-btn-div">
          <button @click="hideModal" class="cross-btn">
            <svg
              version="1.1"
              id="Layer_1"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              x="0px"
              y="0px"
              viewBox="0 0 100 125"
              style="enable-background: new 0 0 100 125"
              xml:space="preserve"
              fill="#000000"
              height="30"
              width="30"
            >
              <g>
                <path
                  d="M85.7,14.3c-2.3-2.3-6.1-2.3-8.5,0L50,41.5L22.7,14.3c-2.3-2.3-6.1-2.3-8.5,0c-2.3,2.3-2.3,6.1,0,8.5L41.5,50L14.3,77.3
		c-2.3,2.3-2.3,6.1,0,8.5c1.2,1.2,2.7,1.8,4.2,1.8s3.1-0.6,4.2-1.8L50,58.5l27.3,27.3c1.2,1.2,2.7,1.8,4.2,1.8s3.1-0.6,4.2-1.8
		c2.3-2.3,2.3-6.1,0-8.5L58.5,50l27.3-27.3C88.1,20.4,88.1,16.6,85.7,14.3z"
                />
              </g>
            </svg>
          </button>
        </div>
        <div class="row mt-3 mb-2">
          <svg fil="#000000" fill="#000000" style="enable-background: new 0 0 100 125" class="dollar-icon" width="35" height="35" viewBox="0 0 92 92"  xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H92V92H0V0Z" fill="url(#pattern0_10055_830)"/>
            <defs>
            <pattern id="pattern0_10055_830" patternContentUnits="objectBoundingBox" width="1" height="1">
            <use xlink:href="#image0_10055_830" transform="scale(0.0108696)"/>
            </pattern>
            <image id="image0_10055_830" width="92" height="92" preserveAspectRatio="none" xlink:href="data:image/png;base64,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"/>
            </defs>
          </svg>

          <label class="leave-a-tip-modal-label">Leave A Tip</label>
        </div>
        
        <div class="row">
          <div class="col-12">
            <label class="purchase-power-modal-text"
              >${{ transactionAmount }} Purchase</label
            >
          </div>
        </div>
        <div v-for="(percentage, index) in selectedTransaction.tip_percentage" :key="index" class="row">
          <div class="col-12">
            <div
              class="t-div"
              @click="selectTip(percentage)"
              :class="{ 'folder-selected': selected == index + 1 }"
              data-dismiss="modal"
            >
              <h5 class="h4-style">{{percentage}}%</h5>
              <h5 class="h4-style1">${{ calculateTipAmount(percentage) }}</h5>
            </div>
          </div>
        </div>
        <div class="row custom-tip-div">
          <div class="col-12">
            <div
              class="input-group"
            >
              <input
                class="form-control Custom-Tip"
                :class="{ 'folder-selected': selected == 'custom' }"
                v-model="customTipAmount"
                inputmode="numeric"
                :placeholder="customplaceholder"
                @click="clickCustomTipAmount()"
                style="height:60px; padding-right: 20px !important; border-radius: 5px; background-color: #ECECEC;"
                @keypress="isNumber($event)"
              />
            </div>
          </div>
        </div>
        
        <div class="row mt-2">
           <div class="col-6 text-center">
            <button
                @click="hideModal"
                class="btn btn-md w-100 tip-btn mt-4"
                style="background-color: white !important;"
              >
              <span  style="color: black !important;" class="forgetpassword-ok-label">No Tip</span>
            </button>
          </div>
          <div class="col-6 text-center">
            <button
              @click="clickADD"
              :disabled="disableTipButton == true"
              class="btn btn-md w-100 tip-btn mt-4"
            >
              <span class="forgetpassword-ok-label">Add Tip</span>
            </button>
          </div>
        </div>
      </b-modal>
    </div>
    <!-- MODAL FOR TRANSACTION ERROR MESSAGES -->
    <div>
      <b-modal
        ref="transaction-error-modal"
        hide-footer
        v-b-modal.modal-center
        modal-backdrop
        hide-header
        id="transaction-error-modal"
        centered
      >
        <div class="color">
          <div class="purchaserpower-modal-text">
            <div class="d-block text-center">
              <label class="purchasepower-def-label">
                {{ error_message }}
              </label>
            </div>
            <br />
            <br />
            <div class="text-center">
              <button
                type="button"
                class="mx-auto col-10 offset-1 btn-black"
                style="height: 60px"
                @click="hideTransactionErrorModal"
              >
                <label class="purchasepower-modal-ok-label">OK</label>
              </button>
            </div>
          </div>
        </div>
      </b-modal>
    </div>
    <!-- MODAL FOR TERMINAL TIP NOT ALLOWED -->
    <div>
      <b-modal
        ref="tip-not-allowed-modal"
        hide-footer
        v-b-modal.modal-center
        modal-backdrop
        hide-header
        id="tip-not-allowed-modal"
        centered
      >
        <div class="color">
          <div class="purchaserpower-modal-text">
            <div class="d-block text-center">
              <label class="purchasepower-def-label">
                {{ tip_error_message }}
              </label>
            </div>
            <br />
            <br />
            <div class="text-center">
              <button
                type="button"
                class="mx-auto col-10 offset-1 btn-black"
                style="height: 60px"
                @click="hideTipErrorModal"
              >
                <label class="purchasepower-modal-ok-label">OK</label>
              </button>
            </div>
          </div>
        </div>
      </b-modal>
    </div>
  </div>
</template>

<script>

export default {
  name: "TipModals",
  props: {
    selectedTransaction: {
      type: Object,
      default: () => ({})
    },
    transactionAmount: {
      type: [String, Number],
      default: 0
    }
  },
  data() {
    return {
      selected: -1,
      tipamount: 0,
      customTipAmount: '',
      customplaceholder: "Custom Tip",
      disableTipButton: true,
      error_message: "",
      tip_error_message: ""
    };
  },
  methods: {
    showModal() {
      this.selected = -1;
      this.tipamount = 0;
      this.customTipAmount = '';
      this.$refs["tip-modal-center"].show();
    },
    hideModal() {
      this.$refs["tip-modal-center"].hide();
    },
    showTransactionErrorModal(msg) {
      this.error_message = msg;
      this.$refs["transaction-error-modal"].show();
    },
    hideTransactionErrorModal() {
      this.error_message = "";
      this.$refs["transaction-error-modal"].hide();
    },
    showTipErrorModal(msg) {
      this.tip_error_message = msg;
      this.hideModal();
      this.$refs["tip-not-allowed-modal"].show();
    },
    hideTipErrorModal() {
      this.tip_error_message = "";
      this.$refs["tip-not-allowed-modal"].hide();
    },
    selectTip(percentage) {
      this.customTipAmount = "";
      this.selected = this.selectedTransaction.tip_percentage.indexOf(percentage) + 1;
      this.tipamount = this.calculateTipAmount(percentage);
    },
    calculateTipAmount(percentage) {
      return (parseFloat(this.transactionAmount) * (percentage / 100)).toFixed(2);
    },
    clickCustomTipAmount() {
      this.tipamount = 0;
      this.selected = 'custom';
    },
    isNumber: function (evt) {
      evt = evt ? evt : window.event;
      var charCode = evt.which ? evt.which : evt.keyCode;
      // first number should't be dot
      if(this.customTipAmount.length == 0 && charCode == 46){
        this.customTipAmount = 0.00;
      }
      // only allow positive number
      if ((charCode < 48 || charCode > 57) && (charCode !== 46 || this.customTipAmount.indexOf('.') !== -1)) { // 46 is dot
          evt.preventDefault();
      }
      // restrict to 2 decimal place
      else if (this.customTipAmount !== null && this.customTipAmount.indexOf('.') > -1 && (this.customTipAmount.split('.')[1].length > 1)) {
        evt.preventDefault();
      }
      else {
        return true;
      }
    },
    clickADD() {
      this.$emit('make-tip-transaction', this.tipamount);
    }
  },
  watch: {
    tipamount: function (newval, oldval) {
      if (this.tipamount.length > 0 && parseFloat(this.tipamount) > 0) {
        this.disableTipButton = false;
      } else {
        this.disableTipButton = true;
      }
    },
    customTipAmount: function (newval, oldval) {
      if (this.customTipAmount > 0) {
        this.tipamount = this.customTipAmount;
      } else if (this.selected == 'custom') {
        this.tipamount = 0;
      }
    }
  }
}
</script>

<style>
#transaction-error-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#tip-not-allowed-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
</style>
