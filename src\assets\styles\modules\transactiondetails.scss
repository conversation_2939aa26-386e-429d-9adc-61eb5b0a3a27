.h1 {
    text-align: left;
}

.h4 {
    text-align: left;
    border-bottom: 2px solid rgba(180, 178, 178, 0.322);
}

.h6 {
    text-align: left;
    color: darkgray;
}

.history-div {
    background-color: white;
    border-radius: 5px;
    margin-top: 20px;
    background-color: unset;
    height: 100% !important;
    overflow: hidden !important;
}

.icon {
    color: rgb(26, 134, 26);
}

.anchor {
    color: black;
}

.card-items {
    display: table;
    width: 100%;
    text-align: left;
}

.accordian-style {
    // margin-bottom: 40px;
    margin-bottom: 15px;
}

.transaction-amount-heading {
    font-family: $cp-font-secondary;
    font-size: 18px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    float: left;
    color: #000000;
}

.participant-merchant-style {
    font-family: $cp-font;
    font-size: 14px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #000000;
    margin-top: 30px !important;
}

.store-name-label {
    font-family: $cp-font;
    font-size: 13px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    float: left;
    color: #000000;
    text-align: left !important;
}

.transaction-date {
    font-family: $cp-font;
    font-size: 13px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #7f7f7f;
    // position: absolute;
    margin-left: 10px;
    margin-top: 2px;
    float: left;
}

.return-transaction-date {
    font-family: "Open Sans";
    font-size: 0.8rem;
    color: #7f7f7f;
    font-weight: 600;
}

.return-text {
    font-size: 0.8rem;
    font-family: 'Open Sans';
    font-weight: 600;
}

.solid-block {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background-color: $cp-secondary !important;
    border-color: $cp-secondary !important;
    display: inline-block;
    border: 2px solid #fff;
}

.store-details-label {
    font-family: $cp-font;
    font-size: 13px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #000000;
    margin-top: -5px;
    margin-left: 8px;
    vertical-align: middle;
}

.side-black {
    border-left: 5px solid rgb(0, 0, 0) !important;
}

.side-green {
    border-left: 5px solid #1b9142 !important;
}

.side-yellow {
    border-left: 5px solid #FF9F20 !important;
}


/* return transaction horizontal scroll*/

.div-container {
    overflow-x: scroll;
    overflow-y: hidden;
}

.inner-div {
    height: 100%;
    white-space: nowrap;
}

.floatLeft {
    display: inline-block;
}

.return-font-red {
    color: #f05f5f;
    font-size: 1.2rem;
    font-family: 'Open Sans';
    font-weight: 900;
}

.return-amount {
    font-size: 1.4rem;
    color: #f05f5f;
    font-family: 'Montserrat';
    font-weight: 900;
}

.return-padding {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
}

.return-div-gray {
    background-color: $cp-gray;
}

.return-pl {
    padding-left: 2.2rem !important;
}

.return-pb {
    padding-bottom: 1em !important;
}

.return-pt {
    padding-top: 1em !important;
}

.return-pt-sm {
    padding-top: 0.3em !important;
}

.return-text {
    font-family: "Open Sans";
    font-size: 0.9rem;
    color: #7f7f7f;
}

.return-text-bold {
    font-family: "Open Sans";
    font-size: 0.8rem;
    font-weight: 900;
    float: left;
    color: #000000;
    text-align: left !important;
}

.return-status-text-red {
    color: #f05f5f;
    font-family: "Open Sans";
    font-size: 0.8rem;
    font-weight: 900;
    float: left;
    text-align: left !important;
}