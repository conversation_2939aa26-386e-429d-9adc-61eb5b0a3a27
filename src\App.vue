<template>
  <div id="app" style="overflow-x: hidden;height:100vh;">
    <div class="wrapper" v-if="show == '0'">
          <!-- adding CanPayCrewLandingHeader header  -->
          <div
            class="reward-wheel-nav"
            v-if=" show == '0' && headerType == 'CanPayCrewLandingHeader'"
          >
            <!-- adding the Header -->
            <CanPayCrewLandingHeader></CanPayCrewLandingHeader>
          </div>
      <div v-else-if="show_header">
        <locate-retailer-header v-if="header_retailer"></locate-retailer-header>
        <cp-preheader v-else></cp-preheader>
      </div>
      <router-view  ref="pageComponent"/>
    </div>
    <div class="wrapper">
      <vue-drawer-layout
        id="nav-drawer"
        ref="drawerLayout"
        :drawer-width="300"
        :drawable-distance="200"
        :content-drawable="true"
        :enable="false"
        :animatable="true"
        :z-index="0"
        :backdrop="true"
        @slide-start="handleSlideStart"
        @slide-move="handleSlideMove"
        @slide-end="handleSlideEnd"
        @mask-click="handleMaskClick"
        class="drawer-black-content"
        v-if="show == '1'"
      >
        <div class="drawer-black-content" slot="drawer">
          <div
            class="row white-body"
            style="margin-right: 0 !important; border-bottom-left-radius: 32px; overflow: hidden;"
          >
            <div class="col-12 drawer-white-body d-flex justify-content-start" style="align-content: center">
              <img v-if="consumer_type == 'lite'" src="./assets/images/canpay-side-logo-lite.png" style="margin-top: 5px; height: 140px" />
              <img v-else src="./assets/images/canpay-logo.png" style="margin-top: 5px; height: 140px" />
            </div>
          </div>
          <div class="row">
            <div class="col-1" style="margin-left: 20px">
              <ul class="nav flex-column" style="white-space: nowrap">
                <!-- home start -->
                <li class="nav-item active active">
                  <a
                    class="nav-link"
                    style="
                      color: green;
                      text-align: left;
                      margin-left: -5px;
                      height: 50px !important;
                      margin-top: 10px !important;
                    "
                    v-on:click="clickHome"
                  >
                    <svg
                      xmlns:dc="http://purl.org/dc/elements/1.1/"
                      xmlns:cc="http://creativecommons.org/ns#"
                      xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
                      xmlns:svg="http://www.w3.org/2000/svg"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
                      xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
                      version="1.1"
                      x="0px"
                      y="0px"
                      viewBox="0 0 100 125"
                      width="30"
                      height="30"
                      fill="#149240"
                    >
                      <g transform="translate(0,-952.36218)">
                        <path
                          style="
                            text-indent: 0;
                            text-transform: none;
                            direction: ltr;
                            block-progression: tb;
                            baseline-shift: baseline;
                            color: #000000;
                            enable-background: accumulate;
                          "
                          d="M 48.187519,957.95464 8.1875186,987.95509 c -0.7389,0.5603 -1.1923,1.479 -1.1875,2.4063 l 0,54.00071 c 2e-4,1.5708 1.4292,2.9999 3.0000004,3.0001 l 27,0 c 1.5708,-2e-4 2.9998,-1.4293 3,-3.0001 l 0,-14.9999 c 0,-5.592 4.4081,-10.0002 10,-10.0002 5.5919,0 10,4.4082 10,10.0002 l 0,14.9999 c 2e-4,1.5708 1.4292,2.9999 3,3.0001 l 27,0 c 1.5708,-2e-4 2.9998,-1.4293 3,-3.0001 l 0,-54.00071 c 0,-0.9273 -0.4486,-1.846 -1.1875,-2.4063 l -40,-30.00045 c -1.3099,-0.83182 -2.4021,-0.74694 -3.625,0 z m 1.8125,6.15634 37,27.75041 0,49.50071 -21,0 0,-11.9999 c 0,-8.8123 -7.1879,-16.0002 -16,-16.0002 -8.8121,0 -16,7.1879 -16,16.0002 l 0,11.9999 -21,0 0,-49.50071 z"
                          fill="#149240"
                          fill-opacity="1"
                          stroke="none"
                          marker="none"
                          visibility="visible"
                          display="inline"
                          overflow="visible"
                        />
                      </g>
                    </svg>
                    <span style="margin-left: 25px">
                      <label class="menu-title">Home</label>
                    </span>
                  </a>
                </li>
                <!-- home end -->

                <!-- Transaction History start -->
                <li class="nav-item active">
                  <a
                    class="nav-link"
                    style="color: green; text-align: left; margin-left: -12px"
                    v-on:click="clickTransactionhistory"
                  >
                    <svg
                      version="1.1"
                      id="Layer_1"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                      x="0px"
                      y="0px"
                      viewBox="0 0 64 80"
                      style="enable-background: new 0 0 64 80"
                      xml:space="preserve"
                      width="40"
                      height="40"
                      fill="#149240"
                    >
                      <g>
                        <g>
                          <path
                            d="M36.2,39.3h-11l0-7c0-0.4-0.2-0.7-0.6-0.9c-0.4-0.2-0.8-0.1-1.1,0.1l-14,12c-0.4,0.4-0.4,1.1,0,1.5l14,12
			c0.3,0.3,0.7,0.3,1.1,0.1c0.4-0.2,0.6-0.5,0.6-0.9l0-7h11c0.6,0,1-0.4,1-1v-8C37.2,39.8,36.8,39.3,36.2,39.3z M24.2,41.3h11v6h-11
			c-0.3,0-0.5,0.1-0.7,0.3s-0.3,0.4-0.3,0.7v5.8l-11.5-9.8l11.5-9.8v5.8c0,0.3,0.1,0.5,0.3,0.7S24,41.3,24.2,41.3z"
                          />
                          <path
                            d="M43.8,15.4c-0.4,0.2-0.6,0.5-0.6,0.9l0,7h-11c-0.6,0-1,0.4-1,1v8c0,0.6,0.4,1,1,1h11l0,7c0,0.4,0.2,0.7,0.6,0.9
			c0.4,0.2,0.8,0.1,1.1-0.1l14-12c0.2-0.2,0.3-0.5,0.3-0.8s-0.1-0.6-0.3-0.8l-14-12C44.6,15.3,44.2,15.3,43.8,15.4z M56.7,28.3
			l-11.5,9.8l0-5.8c0-0.3-0.1-0.5-0.3-0.7s-0.4-0.3-0.7-0.3h-11v-6h11c0.3,0,0.5-0.1,0.7-0.3s0.3-0.4,0.3-0.7l0-5.8L56.7,28.3z"
                          />
                        </g>
                      </g>
                    </svg>
                    <span style="margin-left: 30px">
                      <label class="menu-title" style="margin-left: -8px"
                        >Transaction History</label
                      >
                    </span>
                  </a>
                </li>
                <!-- Transaction History end -->

                <!-- CanPay Points Start -->
                <li v-if="rwState == 'allowed' || rwState == 'partially_allowed' || (rwInvited == 1 && rwState == 'not_allowed')" class="nav-item active active">
                  <a
                    class="nav-link"
                    style="color: green; text-align: left; margin-left: -5px"
                    v-on:click="clickRewarPoints"
                  >
                    <svg width="30" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="35" cy="35" r="32" fill="black"/>
                    <path d="M58.0327 44.4525C60.969 38.2154 59.7673 32.2515 59.4272 30.6694C59.0417 28.8825 58.0667 24.66 54.5183 20.8699C52.8282 19.0889 50.821 17.6413 48.6004 16.6018C45.6075 15.2019 43.0567 14.9287 41.6622 14.7921C35.9144 14.223 31.1643 15.9075 28.5114 17.114C29.8031 15.8716 31.2794 14.8387 32.8875 14.0523C34.3086 13.3704 35.8149 12.8842 37.3656 12.6069C39.9046 12.1197 42.5076 12.0697 45.0633 12.4589C46.2423 12.641 51.1626 13.4946 56.0714 17.3188C63.225 22.8844 66.1613 30.6125 64.4947 39.5016C61.9553 53.1026 48.9632 61.0697 35.7897 57.5641C31.9125 56.5284 27.8312 54.0814 26.0173 51.6912C26.108 51.6912 26.2101 51.6685 26.2667 51.7026C26.7202 51.9985 27.1624 52.3172 27.6272 52.6131C34.9962 57.2568 42.5465 57.291 50.0515 53.1367C51.0019 52.6094 51.9047 52.0001 52.7497 51.3156C56.0034 48.6751 57.4885 45.6021 58.0327 44.4525Z" fill="white"/>
                    <path d="M65.1182 45.2722C65.0389 45.7502 64.9028 46.4331 64.6874 47.2412C63.1229 53.023 59.5518 56.6765 58.0213 58.2016C52.4209 63.79 45.9022 65.4062 42.9206 66.1119C36.7873 67.5573 31.8331 66.9427 30.0532 66.6582C23.6479 65.6338 19.2492 63.0388 17.9001 62.1852C15.1477 60.4502 12.6808 58.2961 10.5878 55.8001C8.14327 52.8969 6.22821 49.5834 4.93068 46.012C4.60191 45.1014 3.35485 41.5276 3.03741 36.6335C2.74266 31.9671 3.44554 28.4843 3.6156 27.6762C4.17003 25.0362 5.06567 22.4803 6.27977 20.0733C6.91463 18.8441 10.1456 12.8346 17.2879 8.35027C18.9431 7.30316 23.2738 4.84474 29.407 3.91145C31.0282 3.66105 45.0066 1.72618 55.0171 10.5242C59.6539 14.5988 62.386 20.0505 62.8168 20.9269C63.4801 22.2806 64.0557 23.6759 64.54 25.104C64.3813 24.8877 63.2023 23.226 63.1683 23.1805C58.8149 16.7044 53.7587 13.6541 53.7587 13.6541C51.8468 12.5265 49.7911 11.665 47.6481 11.0932C40.8346 9.24941 35.0981 10.8884 33.7377 11.2981C25.7565 13.6996 21.0291 20.0847 20.757 20.4603C15.9388 27.1299 16.0408 34.0954 16.1315 36.0303C16.4376 42.5406 19.2718 47.1387 20.5642 48.9939C20.6663 49.1191 20.8137 49.3126 20.995 49.5403C21.1311 49.7224 21.2671 49.9045 21.4032 50.0866C24.7816 54.5482 29.2596 57.6553 34.2365 59.2032C37.9454 60.3419 41.8646 60.6138 45.6942 59.9984C49.5239 59.3829 53.1626 57.8962 56.3321 55.6522C59.121 53.6718 60.8102 51.6117 61.8532 50.337C63.2363 48.6525 64.2453 46.9908 64.5741 46.3306C64.6194 46.251 65.0956 45.2722 65.1182 45.2722Z" fill="#007EE5"/>
                    <path d="M28.7061 45.6C22.7202 45.6 19.7273 41.6278 19.7273 35.5387C19.7273 29.2447 22.879 25.4546 29.0462 25.4546C31.427 25.4546 33.3429 26.0237 34.8167 27.2301C36.1998 28.4707 36.9707 30.1552 37.1634 32.2949H34.76C33.8984 32.2949 33.2749 31.8966 32.9008 31.1112C32.2432 29.7113 30.9508 28.9943 29.0576 28.9943C25.3617 28.9943 23.7179 31.612 23.7179 35.5501C23.7179 39.3743 25.2937 42.0717 28.9329 42.0717C31.427 42.0717 32.8554 40.6945 33.2862 38.532H37.1521C36.8007 43.0392 33.6377 45.6 28.7061 45.6Z" fill="white"/>
                    <path d="M49.3373 25.2747H42.5465C41.4015 25.2747 40.4945 26.2762 40.4945 27.4258V45.3063H44.5758V38.17H49.632C53.634 38.17 55.7766 35.6661 55.7766 31.7166C55.7766 27.551 53.5773 25.2747 49.3373 25.2747ZM48.8725 34.5507H44.8932V28.746H49.0765C50.9811 28.746 51.9561 29.7135 51.9561 31.6597C51.9674 33.606 50.9471 34.5848 48.8725 34.5507H48.8725Z" fill="#007EE5"/>
                    </svg>
                      <span style="margin-left: 24px">
                      <label class="menu-title">CanPay Points</label>
                    </span>
                  </a>
                </li>
                <!-- CanPay Points End -->

                <!-- Member Benefit Start -->
                <li class="nav-item active">
                  <a
                    class="nav-link"
                    style="color: green; text-align: left; margin-left: -8px"
                    v-on:click="clickMemeberBenefitAccounts"
                  >

                  <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  width="30" 
                  height="29" 
                  viewBox="0 0 69 70" 
                  fill="none">
                  <path d="M62.8039 13.318H49.5002C51.4273 11.7074 52.5534 9.62158 52.6508 7.31643C52.7732 4.41839 51.1851 1.79259 48.6048 0.627103C47.0326 -0.0832245 42.8136 -1.19395 37.6044 3.81899C35.8136 5.54167 34.259 7.61183 33.054 9.82975C31.8501 7.61225 30.2958 5.54238 28.5046 3.81913C23.295 -1.19395 19.076 -0.0830833 17.5039 0.626962C14.9236 1.79259 13.3355 4.41825 13.458 7.31629C13.5553 9.62158 14.6815 11.7075 16.6083 13.3179H5.44607C2.44306 13.3179 0 15.7643 0 18.7714V29.257C0 31.5137 1.376 33.4546 3.33197 34.2828V63.1142C3.33197 66.911 6.41489 70 10.2042 70H58.0458C61.8353 70 64.9181 66.9111 64.9181 63.1142V34.2828C66.874 33.4546 68.25 31.5137 68.25 29.257V18.7714C68.25 15.7643 65.807 13.318 62.8039 13.318ZM64.0218 18.7716V29.2571C64.0218 29.9296 63.4755 30.4767 62.8039 30.4767H35.1664V18.1623C36.2889 18.8623 37.2729 20.1351 38.5191 21.8269C39.6008 23.2955 40.7193 24.8141 42.1926 25.9861C42.5814 26.2954 43.0454 26.4456 43.5063 26.4456C44.1288 26.4456 44.7456 26.1715 45.1629 25.6454C45.8891 24.73 45.7368 23.3982 44.8227 22.671C43.789 21.8486 42.8819 20.6173 41.9218 19.3137C41.5017 18.7434 41.0594 18.1433 40.5806 17.5522H62.8041C63.4757 17.5522 64.0219 18.0992 64.0219 18.7717L64.0218 18.7716ZM40.5338 6.872C42.3186 5.15468 44.1289 4.2304 45.6168 4.2304C46.0657 4.2304 46.4856 4.31465 46.866 4.48656C47.8629 4.93677 48.4753 5.97736 48.4262 7.13748C48.3487 8.97307 46.7055 10.6332 44.0293 11.579C41.9423 12.3178 39.2077 12.8504 36.1328 13.1286C37.1919 10.8175 38.7376 8.59991 40.5338 6.872ZM19.2426 4.48656C19.6232 4.31465 20.0427 4.2304 20.4917 4.2304C21.9796 4.2304 23.7903 5.15483 25.575 6.87229C27.372 8.6009 28.9171 10.8185 29.9743 13.1289C26.8982 12.8507 24.1639 12.3179 22.0781 11.5787C19.4032 10.6334 17.7599 8.97321 17.6824 7.13762C17.6333 5.9775 18.2458 4.93677 19.2426 4.48656ZM4.22821 18.7716C4.22821 18.0991 4.77449 17.552 5.44607 17.552H25.5277C25.0493 18.1428 24.6073 18.7424 24.1876 19.3124C23.2272 20.6165 22.3201 21.8482 21.2861 22.6708C20.3719 23.3981 20.2196 24.7298 20.9458 25.6452C21.3632 26.1714 21.9798 26.4455 22.6024 26.4455C23.0632 26.4455 23.5273 26.2953 23.9161 25.9859C25.3898 24.8135 26.5086 23.2945 27.5906 21.8253C28.8351 20.1354 29.818 18.8637 30.9382 18.1636V30.4765H5.44607C4.77449 30.4765 4.22821 29.9295 4.22821 29.257V18.7714V18.7716ZM7.56017 63.1144V34.7107H30.9382V65.7661H10.2042C8.74633 65.7661 7.56017 64.5765 7.56017 63.1144ZM58.0458 65.7661H35.1664V34.7107H60.6898V63.1144C60.6898 64.5765 59.5037 65.7661 58.0458 65.7661Z" fill="#1B9142"/>
                  </svg>
                    <span style="margin-left: 28px">
                      <label class="menu-title">Member Benefits</label>
                    </span>      
                  </a>
                </li>
                <!-- Member Benefit End -->

                <!-- Canpay Crew Start -->
                <li class="nav-item active">
                  <a
                    class="nav-link"
                    style="color: green; text-align: left; margin-left: -8px;position:relative;"
                    v-on:click="clickCanPayCrew"
                  >

                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 47 47" fill="none">
                    <path d="M26.2728 12.7778L32.4444 19.4861V32.2222H14.5556V12.7778H26.2728ZM26.2728 10H14.5556C13.15 10 12 11.25 12 12.7778V32.2222C12 33.75 13.15 35 14.5556 35H32.4444C33.85 35 35 33.75 35 32.2222V19.4861C35 18.75 34.7317 18.0417 34.2461 17.5278L28.0744 10.8194C27.6017 10.2917 26.95 10 26.2728 10ZM17.1111 26.6667H29.8889V29.4444H17.1111V26.6667ZM17.1111 21.1111H29.8889V23.8889H17.1111V21.1111ZM17.1111 15.5556H26.0556V18.3333H17.1111V15.5556Z" fill="#149240"/>
                    <path d="M23.165 0C10.3779 0 0 10.3779 0 23.165C0 35.9521 10.3779 46.33 23.165 46.33C35.9521 46.33 46.33 35.9521 46.33 23.165C46.33 10.3779 35.9521 0 23.165 0ZM23.165 41.697C12.9261 41.697 4.633 33.4039 4.633 23.165C4.633 12.9261 12.9261 4.633 23.165 4.633C33.4039 4.633 41.697 12.9261 41.697 23.165C41.697 33.4039 33.4039 41.697 23.165 41.697Z" fill="#149240"/>
                    </svg>
                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="35" viewBox="0 0 162 91" fill="none" style="
                        position: absolute;
                        top: -8px;
                        left: 35px;
                        transform: scale(0.9);
                    ">
                    <rect width="162" height="90.1698" rx="16.0472" fill="#FF0040"></rect>
                    <path d="M57.4511 27.5909V62.0471H51.1588L36.1683 40.3605H35.9159V62.0471H28.631V27.5909H35.0242L49.8969 49.2606H50.1998V27.5909H57.4511ZM63.47 62.0471V27.5909H86.6876V33.5971H70.7549V41.8074H85.493V47.8137H70.7549V56.0408H86.7549V62.0471H63.47ZM100.235 62.0471L90.3763 27.5909H98.3342L104.038 51.5319H104.324L110.616 27.5909H117.43L123.705 51.5824H124.008L129.712 27.5909H137.67L127.81 62.0471H120.711L114.149 39.5193H113.88L107.335 62.0471H100.235Z" fill="white"></path>
                    </svg>
                    <span style="margin-left: 28px">
                      <label class="menu-title">CanPay Crew
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 221 97" fill="none" style="
                            position: absolute;top: -10px;margin-left: -3px;transform: scale(0.9)
                        ">
                        <rect width="221" height="97" rx="48" fill="#095A26"></rect>
                        <path d="M45.934 72V22.9964H63.0662C66.4798 22.9964 69.2953 23.5866 71.5126 24.767C73.7299 25.9315 75.3809 27.5027 76.4656 29.4807C77.5503 31.4428 78.0927 33.6202 78.0927 36.013C78.0927 38.1186 77.7178 39.8573 76.9681 41.2292C76.2343 42.601 75.2612 43.6857 74.0489 44.4833C72.8525 45.2809 71.5525 45.8711 70.1487 46.2539V46.7325C71.6482 46.8282 73.1556 47.3546 74.671 48.3117C76.1864 49.2688 77.4546 50.6407 78.4755 52.4273C79.4964 54.2138 80.0069 56.3992 80.0069 58.9834C80.0069 61.44 79.4486 63.6493 78.3319 65.6113C77.2153 67.5734 75.4527 69.1287 73.044 70.2772C70.6352 71.4257 67.5007 72 63.6404 72H45.934ZM51.8681 66.7359H63.6404C67.5167 66.7359 70.2684 65.9862 71.8954 64.4867C73.5385 62.9713 74.36 61.1369 74.36 58.9834C74.36 57.3244 73.9373 55.7931 73.0918 54.3893C72.2464 52.9696 71.042 51.837 69.4787 50.9916C67.9155 50.1302 66.0651 49.6995 63.9276 49.6995H51.8681V66.7359ZM51.8681 44.5312H62.8747C64.6613 44.5312 66.2725 44.1802 67.7081 43.4784C69.1597 42.7765 70.3082 41.7875 71.1537 40.5113C72.0151 39.2352 72.4458 37.7357 72.4458 36.013C72.4458 33.8595 71.696 32.033 70.1966 30.5335C68.6971 29.0181 66.3203 28.2604 63.0662 28.2604H51.8681V44.5312ZM104.401 72.7657C100.86 72.7657 97.805 71.984 95.2368 70.4208C92.6845 68.8416 90.7145 66.6402 89.3267 63.8168C87.9548 60.9774 87.2689 57.6754 87.2689 53.9108C87.2689 50.1462 87.9548 46.8282 89.3267 43.9569C90.7145 41.0696 92.6446 38.8205 95.1171 37.2093C97.6056 35.5823 100.509 34.7687 103.827 34.7687C105.741 34.7687 107.631 35.0878 109.498 35.7258C111.364 36.3639 113.063 37.4008 114.594 38.8364C116.126 40.2561 117.346 42.1384 118.255 44.4833C119.164 46.8282 119.619 49.7155 119.619 53.1451V55.5378H91.2887V50.6566H113.876C113.876 48.5829 113.462 46.7325 112.632 45.1054C111.819 43.4784 110.654 42.1942 109.139 41.2531C107.639 40.3119 105.869 39.8414 103.827 39.8414C101.578 39.8414 99.6315 40.3997 97.9884 41.5163C96.3614 42.617 95.1091 44.0526 94.2318 45.8233C93.3545 47.5939 92.9158 49.4921 92.9158 51.518V54.7722C92.9158 57.5478 93.3943 59.9006 94.3514 61.8308C95.3245 63.745 96.6724 65.2046 98.3952 66.2095C100.118 67.1985 102.12 67.693 104.401 67.693C105.885 67.693 107.224 67.4857 108.421 67.0709C109.633 66.6402 110.678 66.0022 111.555 65.1567C112.433 64.2953 113.111 63.2266 113.589 61.9504L119.045 63.4818C118.47 65.3322 117.505 66.9593 116.149 68.363C114.794 69.7508 113.119 70.8355 111.125 71.6172C109.131 72.3828 106.889 72.7657 104.401 72.7657ZM144.193 35.2473V40.0328H125.146V35.2473H144.193ZM130.697 26.4419H136.344V61.4719C136.344 63.067 136.576 64.2634 137.038 65.061C137.517 65.8426 138.123 66.369 138.857 66.6402C139.606 66.8955 140.396 67.0231 141.226 67.0231C141.848 67.0231 142.358 66.9912 142.757 66.9274C143.156 66.8476 143.475 66.7838 143.714 66.7359L144.863 71.8086C144.48 71.9521 143.945 72.0957 143.259 72.2393C142.573 72.3988 141.704 72.4786 140.651 72.4786C139.056 72.4786 137.493 72.1356 135.961 71.4497C134.446 70.7637 133.186 69.7189 132.181 68.3152C131.192 66.9114 130.697 65.1408 130.697 63.0032V26.4419ZM163.502 72.8614C161.173 72.8614 159.06 72.4227 157.161 71.5454C155.263 70.6521 153.756 69.368 152.639 67.693C151.522 66.0022 150.964 63.9603 150.964 61.5676C150.964 59.462 151.379 57.7551 152.208 56.4471C153.038 55.1231 154.146 54.0862 155.534 53.3365C156.922 52.5868 158.453 52.0285 160.128 51.6616C161.819 51.2787 163.518 50.9757 165.225 50.7523C167.458 50.4652 169.269 50.2499 170.656 50.1063C172.06 49.9468 173.081 49.6836 173.719 49.3167C174.373 48.9498 174.7 48.3117 174.7 47.4025V47.2111C174.7 44.8502 174.054 43.0158 172.762 41.7077C171.486 40.3997 169.548 39.7457 166.948 39.7457C164.252 39.7457 162.138 40.3359 160.607 41.5163C159.075 42.6967 157.999 43.9569 157.377 45.2968L152.017 43.3826C152.974 41.1494 154.25 39.4107 155.845 38.1664C157.456 36.9063 159.211 36.0289 161.109 35.5344C163.024 35.024 164.906 34.7687 166.756 34.7687C167.937 34.7687 169.293 34.9123 170.824 35.1994C172.371 35.4706 173.863 36.0369 175.298 36.8983C176.75 37.7597 177.954 39.0597 178.911 40.7985C179.869 42.5372 180.347 44.8662 180.347 47.7853V72H174.7V67.0231H174.413C174.03 67.8207 173.392 68.6741 172.499 69.5833C171.606 70.4926 170.417 71.2662 168.934 71.9043C167.45 72.5424 165.64 72.8614 163.502 72.8614ZM164.363 67.7888C166.597 67.7888 168.479 67.3501 170.01 66.4727C171.558 65.5954 172.722 64.4628 173.504 63.075C174.301 61.6872 174.7 60.2276 174.7 58.6963V53.5279C174.461 53.8151 173.934 54.0783 173.121 54.3175C172.323 54.5409 171.398 54.7403 170.345 54.9157C169.308 55.0752 168.296 55.2188 167.307 55.3464C166.334 55.4581 165.544 55.5538 164.938 55.6336C163.47 55.825 162.098 56.136 160.822 56.5667C159.562 56.9815 158.541 57.6116 157.759 58.457C156.994 59.2865 156.611 60.4191 156.611 61.8547C156.611 63.8168 157.337 65.3003 158.788 66.3052C160.256 67.2942 162.114 67.7888 164.363 67.7888Z" fill="white"></path>
                        </svg>
                      </label>
                    </span>      
                  </a>
                </li>
                <!-- Member Benefit End -->

                <!-- Connected Program Start -->
                <li class="nav-item active">
                  <a
                    class="nav-link"
                    style="color: green; text-align: left; margin-left: -8px"
                    v-on:click="clickLinkedAccounts"
                  >

                    <svg 
                      version="1.1"
                      id="Layer_1"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                      x="0px"
                      y="0px"
                      viewBox="0 0 50 80"
                      style="enable-background: new 0 0 64 80"
                      xml:space="preserve"
                      width="30"
                      height="29"
                      fill="#149240"
                      >
                      <path 
                      fill-rule="evenodd" 
                      clip-rule="evenodd" 
                      d="M25.528 14.8828L35.1198 5.29099C46.3839 -5.97216 65.9761 1.89451 65.9761 18.0795C65.9761 22.7133 64.2157 27.349 60.6968 30.8679L51.1049 40.4598L46.8424 36.1973L56.4343 26.6054C63.912 19.1277 58.7384 6.04043 47.9083 6.04043C44.8155 6.04043 41.7247 7.2121 39.3823 9.55349L29.7905 19.1453L25.528 14.8828ZM20.1986 50.0507L50.039 20.2102C52.8448 17.4045 48.5823 13.141 45.7766 15.9477L15.9361 45.7882C13.1304 48.5949 17.3929 52.8574 20.1986 50.0507ZM40.4482 51.1165L36.1857 46.854L26.5938 56.4459C24.2525 58.7873 21.1606 59.9589 18.0679 59.9589C7.23772 59.9589 2.06512 46.8717 9.54188 39.3939L19.1337 29.8021L14.8712 25.5396L5.27939 35.1314C1.76045 38.6504 0 43.2861 0 47.9199C0 64.1058 19.5922 71.9725 30.8563 60.7084L40.4482 51.1165Z" 
                      fill="#1B9142"
                      />
                    </svg>    
                    <span style="margin-left: 28px">
                      <label class="menu-title">Connected Program</label>
                    </span>      
                  </a>
                </li>
                <!-- Canpay Crew End -->

                <!-- Account start -->
                <li class="nav-item active" style="margin-top: -10px;">
                  <a
                    class="nav-link"
                    style="color: green; text-align: left; margin-left: -10px"
                    v-on:click="clickAccount"
                  >
                    <svg
                      version="1.1"
                      id="Layer_1"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                      x="0px"
                      y="0px"
                      viewBox="0 0 50 62.5"
                      style="enable-background: new 0 0 50 62.5"
                      xml:space="preserve"
                      width="40"
                      height="40"
                      fill="#149240"
                    >
                      <path
                        d="M32.3,32.1c0.2-0.8,0.4-1.6,0.4-2.5v-8.9c0-4.8-3.9-8.8-8.8-8.8s-8.8,3.9-8.8,8.8v8.9c0,0.9,0.2,1.7,0.4,2.4
	c-2.3,0.7-4.4,2.2-6.1,4.2c-2.4,3-3.8,7-3.8,11.3c0,0,0,0,0,0.1c0,0,0,0,0,0.1c0,0.7,0.6,1.4,1.4,1.4h33.9c0.7,0,1.4-0.6,1.4-1.4
	c0,0,0,0,0-0.1c0,0,0,0,0-0.1C42.2,40.3,38,33.9,32.3,32.1z M17.8,20.8c0-3.4,2.7-6.1,6.1-6.1c3.4,0,6.1,2.7,6.1,6.1v8.9
	c0,3.4-2.7,6.1-6.1,6.1s-6.1-2.7-6.1-6.1V20.8z M11.5,38c1.4-1.7,3.2-2.9,5.1-3.4c1.6,2.3,4.2,3.8,7.3,3.8c3,0,5.7-1.5,7.3-3.8
	c4.5,1.2,7.8,6,8.2,11.8h-31C8.6,43.2,9.7,40.3,11.5,38z"
                      />
                    </svg>
                    <span style="margin-left: 20px">
                      <label class="menu-title">Account</label>
                    </span>
                  </a>
                </li>
                <!-- Account End -->

                <!-- Settings Start -->
                <li class="nav-item active active">
                  <a
                    class="nav-link"
                    style="color: green; text-align: left; margin-left: -6px"
                    v-on:click="clickSetting"
                  >
                    <svg
                      xmlns:dc="http://purl.org/dc/elements/1.1/"
                      xmlns:cc="http://creativecommons.org/ns#"
                      xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
                      xmlns:svg="http://www.w3.org/2000/svg"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
                      xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
                      version="1.1"
                      class="menu-svg"
                      x="0px"
                      y="0px"
                      viewBox="0 0 100 125"
                      height="30"
                      width="30"
                    >
                      <g transform="translate(0,-952.36218)">
                        <path
                          style="
                            text-indent: 0;
                            text-transform: none;
                            direction: ltr;
                            block-progression: tb;
                            baseline-shift: baseline;
                            color: #000000;
                            enable-background: accumulate;
                          "
                          d="m 43.999968,957.36216 c -3.2894,0 -6,2.7106 -6,6 l 0,5.09375 c -4.3936,1.55574 -8.3008,4.00919 -11.7188,7.03125 l -4.562401,-2.53125 c -2.8787,-1.5965 -6.5616,-0.53141 -8.1563,2.34375 l -5.8124998,10.5 c -1.5885,2.86414 -0.5706,6.55723 2.3124998,8.15624 l 4.5313,2.5 c -0.3189,1.9284 -0.5938,3.8907 -0.5938,5.9063 0,2.0133 0.2756,3.9486 0.5938,5.875 l -4.5313,2.5312 c -2.8830998,1.5991 -3.9009998,5.2921 -2.3124998,8.1563 l 5.8124998,10.5 c 1.5947,2.8751 5.2776,3.9402 8.1563,2.3437 l 4.562401,-2.5312 c 3.4149,3.0161 7.3313,5.4464 11.7188,7 l 0,5.125 c 0,3.2894 2.7106,6 6,6 l 12,0 c 3.2894,0 6,-2.7106 6,-6 l 0,-5.125 c 4.3876,-1.5536 8.3039,-3.9839 11.7188,-7 l 4.5624,2.5312 c 2.8786,1.5965 6.5617,0.5314 8.1563,-2.3437 l 5.8125,-10.5 c 1.5886,-2.8642 0.5707,-6.5572 -2.3125,-8.1563 l -4.5313,-2.5312 c 0.318,-1.9264 0.5938,-3.8617 0.5938,-5.875 0,-2.0156 -0.2751,-3.9779 -0.5938,-5.9063 l 4.5313,-2.5 c 2.8832,-1.599 3.9011,-5.2921 2.3125,-8.15623 l -5.8125,-10.5 c -1.5946,-2.87516 -5.2778,-3.94026 -8.1563,-2.34375 l -4.5624,2.53125 c -3.418,-3.02206 -7.3252,-5.47552 -11.7188,-7.03125 l 0,-5.09375 c 0,-3.2894 -2.7106,-6 -6,-6 l -12,0 z m 0,4 12,0 c 1.1426,0 2,0.85739 2,2 l 0,6.5 c 0,0.84506 0.5993,1.65535 1.4062,1.90625 4.8464,1.48736 9.2121,4.0992 12.7813,7.53125 0.6084,0.58397 1.6036,0.71668 2.3437,0.3125 l 5.6876,-3.15625 c 0.9972,-0.5531 2.1637,-0.21941 2.7187,0.78125 l 5.8125,10.50004 c 0.5611,1.0116 0.2427,2.1681 -0.75,2.7187 l -5.6875,3.1563 c -0.7444,0.40689 -1.1702,1.3251 -1,2.1562 0.4455,2.1263 0.6875,4.3274 0.6875,6.5938 0,2.2663 -0.242,4.4363 -0.6875,6.5625 -0.1846,0.8401 0.2439,1.7774 1,2.1875 l 5.6875,3.1562 c 0.9927,0.5506 1.3111,1.7071 0.75,2.7188 l -5.8125,10.5 c -0.555,1.0006 -1.7215,1.3343 -2.7187,0.7812 l -5.6876,-3.1562 c -0.7337,-0.4122 -1.7282,-0.2928 -2.3437,0.2812 -3.5692,3.432 -7.9349,6.0439 -12.7813,7.5313 -0.817,0.2539 -1.4179,1.082 -1.4062,1.9375 l 0,6.5 c 0,1.1426 -0.8574,2 -2,2 l -12,0 c -1.1426,0 -2,-0.8574 -2,-2 l 0,-6.5 c 0.012,-0.8554 -0.5894,-1.6835 -1.4062,-1.9375 -4.8464,-1.4874 -9.212,-4.0993 -12.7813,-7.5313 -0.6155,-0.574 -1.61,-0.6933 -2.3437,-0.2812 l -5.687601,3.1562 c -0.9972,0.5531 -2.1637,0.2194 -2.7187,-0.7812 l -5.8125,-10.5 c -0.5611,-1.0117 -0.2427,-2.1682 0.75,-2.7188 l 5.6875,-3.1562 c 0.7561,-0.4101 1.1846,-1.3474 1,-2.1875 -0.4455,-2.1262 -0.6875,-4.2962 -0.6875,-6.5625 0,-2.2664 0.242,-4.4675 0.6875,-6.5938 0.1702,-0.8311 -0.2556,-1.7493 -1,-2.1562 l -5.6875,-3.1563 c -0.9927,-0.5506 -1.3111,-1.7071 -0.75,-2.7187 l 5.8125,-10.50003 c 0.555,-1.00066 1.7215,-1.33436 2.7187,-0.78125 l 5.687601,3.15625 c 0.74,0.40412 1.7354,0.27142 2.3437,-0.3125 3.5693,-3.43205 7.9347,-6.0439 12.7813,-7.53125 0.8069,-0.25095 1.4046,-1.06122 1.4062,-1.90625 l 0,-6.5 c 0,-1.14262 0.8574,-2 2,-2 z m 6,21 c -11.022,0 -20,8.97794 -20,20.00004 0,11.0219 8.978,20 20,20 11.022,0 20,-8.9781 20,-20 0,-11.0221 -8.978,-20.00003 -20,-20.00003 z m 0,4 c 8.8603,0 16,7.13964 16,16.00004 0,8.8602 -7.1397,16 -16,16 -8.8602,0 -16,-7.1398 -16,-16 0,-8.8604 7.1398,-16.00003 16,-16.00003 z"
                          fill="#149240"
                          fill-opacity="1"
                          stroke="none"
                          marker="none"
                          visibility="visible"
                          display="inline"
                          overflow="visible"
                        />
                      </g>
                    </svg>
                    <span style="margin-left: 26px">
                      <label class="menu-title">Settings</label>
                    </span>
                  </a>
                </li>
                <!-- Settings End -->

                <!-- Participating Merchants start -->
                <li class="nav-item active align-items-end" style="margin-top: -3px;">
                  <a
                    class="nav-link"
                    style="color: green; text-align: left; margin-left: -15px"
                    v-on:click="clickParticipantMerchant"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                      xml:space="preserve"
                      version="1.1"
                      style="
                        shape-rendering: geometricPrecision;
                        text-rendering: geometricPrecision;
                        image-rendering: optimizeQuality;
                        margin-left: 3px;
                      "
                      viewBox="0 0 100 488.75"
                      x="0px"
                      y="0px"
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      width="30"
                      height="30"
                      fill="#149240"
                    >
                      <g>
                        <path
                          class="fil0"
                          d="M146 72c40,0 73,33 73,74 0,40 -33,73 -73,73 -41,0 -74,-33 -74,-73 0,-41 33,-74 74,-74zm0 21c-29,0 -53,24 -53,53 0,28 24,52 53,52 28,0 52,-24 52,-52 0,-29 -24,-53 -52,-53z"
                        />
                        <path
                          class="fil0"
                          d="M146 21c-69,0 -125,56 -125,125 0,19 7,40 16,57 21,46 48,91 74,134l13 21c10,16 33,16 43,0l11 -19c26,-43 53,-87 74,-131 9,-19 18,-41 18,-62 0,-69 -56,-125 -124,-125zm0 -21c80,0 145,65 145,146 0,24 -9,49 -20,71 -22,45 -48,90 -75,133l-11 19c-18,29 -61,29 -79,0l-13 -21c-26,-44 -54,-90 -76,-136 -9,-20 -17,-44 -17,-66 0,-81 65,-146 146,-146z"
                        />
                      </g>
                    </svg>
                    <span style="margin-left: 20px">
                      <label class="menu-title" style="margin-left: 12px"
                        >Participating Merchants</label
                      >
                    </span>
                  </a>
                </li>
                <!-- Participating Merchants end -->
                
                <!-- Terms and Condition Start -->
                <li class="nav-item active" style="margin-top: -10px;">
                  <a
                    class="nav-link"
                    style="color: green; text-align: left; margin-left: -15px"
                    v-on:click="clickTermsandCondition"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                      version="1.1"
                      x="0px"
                      y="0px"
                      viewBox="0 0 80 125"
                      style="enable-background new 0 0 100 100;margin-left: 1px;"
                      xml:space="preserve"
                      width="40"
                      height="40"
                      fill="#149240"
                    >
                      <g>
                        <path d="M-910,170" />
                        <path
                          d="M-750.746,71.573c-0.605-1.992-1.883-6.135-7.527-9.148c-3.367-1.797-9.262-5.377-13.566-7.99   c-1.723-1.043-3.211-1.947-4.23-2.551c-3.934-2.33-13.098-5.701-19.383-5.701l-11.371,0.008c-0.352-0.027-8.313-0.377-14.887,5.855   c-5.5,5.217-8.289,13.342-8.289,24.146v3.938c0,5.494,4.461,9.775,9.949,9.775c4.773,0,8.762-4.074,9.723-8.074h32.473   c0.957,4,4.949,7.883,9.723,7.883s8.766-3.883,9.723-7.883h8.41v-1.619C-750,79.575-750.02,73.925-750.746,71.573z    M-820.051,86.095c-3.285,0-5.957-2.676-5.957-5.965c0-3.291,2.672-5.969,5.957-5.969s5.957,2.678,5.957,5.969   C-814.094,83.419-816.766,86.095-820.051,86.095z M-768.133,86.095c-3.281,0-5.953-2.676-5.953-5.965   c0-3.291,2.672-5.969,5.953-5.969c3.285,0,5.957,2.678,5.957,5.969C-762.176,83.419-764.848,86.095-768.133,86.095z    M-758.375,77.831c-0.898-4-4.926-8.053-9.758-8.053s-8.859,4.053-9.758,8.053h-32.406c-0.895-4-4.922-7.863-9.754-7.863   c-2.16,0-4.148,0.807-5.781,1.986c0.598-7.545,2.871-13.412,6.82-17.182c5.281-5.053,11.906-4.941,12.078-4.941h11.48   c5.289,0,13.813,3.223,17.348,5.318c1.016,0.6,2.488,1.584,4.195,2.619c4.344,2.637,10.293,6.293,13.758,8.145   c4.191,2.234,5.066,4.92,5.594,6.623c0.305,0.99,0.469,5.295,0.531,5.295H-758.375z M-763.348,38.144   c8.168,0,13.348-7.113,13.348-14.025c0-0.045,0-0.09,0-0.135c0-6.846-5.213-13.889-13.346-13.889   c-7.887,0-13.334,6.576-13.529,6.855c-0.234,0.336-1.125,0.734-1.125,1.145v12.049c0,0.41,0.891,0.809,1.125,1.145   C-776.68,31.565-771.234,38.144-763.348,38.144z M-774,18.786c4-1.262,5.27-4.691,10-4.691c5.645,0,10,5.029,10,9.939   c0,0.033,0,0.068,0,0.102c0,4.918-4.318,10.008-9.998,10.008c-4.73,0-6.002-3.43-10.002-4.693V18.786z M-795.805,29.831H-782v-4   h-12.102l-3.125-4H-802l0,0h20v-4h-24v8h7.07L-795.805,29.831z"
                        />
                        <path
                          d="M-875.56,25.206c-4.211-4.266-10.969-7.215-20.086-7.215h-14.363v28h-5.414l-27.602-27.859l-2.984,1.568v26.291h4V24.528   l20.93,21.463h-4.809c-1.133,0-11.184-0.324-15.16,2.117c-0.254,0.156-0.523,0.234-0.813,0.387   c-4.059,2.188-8.023,5.496-8.023,21.496h0.07c-0.125,0-0.195,1.418-0.195,2.082c0,5.523,4.48,9.936,10.008,9.936   c4.824,0,8.852-4.018,9.797-8.018h32.445c0.949,4,4.977,7.953,9.797,7.953c4.824,0,8.852-3.953,9.797-7.953h8.156V47.763   l-0.004-2.248C-869.927,39.565-869.829,31.022-875.56,25.206z M-940.001,78.22c-3.313,0-6.004-2.691-6.004-6s2.691-6,6.004-6   s6.004,2.691,6.004,6S-936.688,78.22-940.001,78.22z M-887.962,78.22c-3.309,0-6.004-2.691-6.004-6s2.695-6,6.004-6   c3.313,0,6.008,2.691,6.008,6S-884.649,78.22-887.962,78.22z M-874.032,45.71l-0.02,24.281h-4.098c-0.91-4-4.953-8.047-9.813-8.047   s-8.898,4.047-9.813,8.047h-32.414c-0.91-4-4.953-7.91-9.813-7.91c-2.082,0-4.012,0.703-5.609,1.789   c0.848-9.078,3.563-10.541,5.648-11.662c0.355-0.191,0.691-0.512,1.008-0.707c2.215-1.355,9.063-1.51,12.965-1.51h19.98v-28h10.363   c8.008,0,13.805,2.479,17.23,5.955C-873.856,32.567-873.958,40.468-874.032,45.71z"
                        />
                        <path
                          d="M-312,191.845h4v-4h-4V191.845z M-312.051,184.155h4v-24h-4V184.155z M-276.535,193.847l-30-52   c-0.715-1.238-2.035-2.002-3.465-2.002s-2.75,0.764-3.465,2.002l-30,52c-0.715,1.236-0.715,2.918,0,4.152   c0.715,1.238,2.035,2.156,3.465,2.156h60c1.43,0,2.75-0.918,3.465-2.156C-275.82,196.765-275.82,195.083-276.535,193.847z    M-340,196.155l30-52l30,52H-340z"
                        />
                        <g>
                          <path
                            d="M38,210h7.184l11.973-16h25.016c4.402,0,7.828-3.689,7.828-8.115v-48.168c0-4.426-3.426-7.717-7.828-7.717H18.133    c-4.404,0-8.133,3.291-8.133,7.717v48.168c0,4.426,3.729,8.115,8.133,8.115H38V210z M86,185.885c0,2.219-1.621,4.115-3.828,4.115    H55.504L42,205.674V190H18.133c-2.207,0-4.133-1.896-4.133-4.115v-48.168c0-2.219,1.926-3.717,4.133-3.717h64.039    c2.207,0,3.828,1.498,3.828,3.717V185.885z"
                          />
                        </g>
                        <path
                          d="M-71.848,179.672h4v-4h-4V179.672z M-37.828,130h-64.039c-4.406,0-8.133,3.291-8.133,7.717v48.168   c0,4.426,3.727,8.115,8.133,8.115H-82v16h7.184l11.973-16h25.016c4.402,0,7.828-3.689,7.828-8.115v-48.168   C-30,133.291-33.426,130-37.828,130z M-34,185.885c0,2.219-1.625,4.115-3.828,4.115h-26.668L-78,205.674V190h-23.867   c-2.207,0-4.133-1.896-4.133-4.115v-48.168c0-2.219,1.926-3.717,4.133-3.717h64.039c2.203,0,3.828,1.498,3.828,3.717V185.885z    M-68.924,143.672c-7.91,0-9.076,6.541-9.076,10v1.988l3.053,0.012l-0.439-1.988c0.012-1.004,0.125-6.012,5.77-6.012   s5.873,5.008,5.885,5.979c-0.004,0.133,0.754,3.305-2.547,6.607c-4.535,4.533-3.721,9.217-3.721,9.414v1.977l3.029,0.021   l-0.416-1.977c0.004-0.133-0.117-3.305,3.184-6.607c4.535-4.533,4.934-9.217,4.934-9.414   C-59.27,150.213-61.014,143.672-68.924,143.672z"
                        />
                        <path
                          d="M-805.84,142.504v44.152c0-0.293-1.537-0.461-2.514-0.461c-2.875,0-6.104,1.273-8.979,3.723   c-5.016,4.273-6.662,10.512-3.74,13.93c1.242,1.465,3.156,2.176,5.305,2.176c2.875,0,6.225-0.357,9.1-2.807   c3.344-2.855,5.176-8.84,5.184-8.84h-0.355v-28.521l36-6.457v-4.063l-36,6.453v-15.934l40-7.105v39.508   c0-0.293-1.537-0.461-2.514-0.461c-2.875,0-6.104,1.273-8.979,3.723c-5.016,4.273-6.662,10.512-3.74,13.93   c1.242,1.465,3.156,2.176,5.305,2.176c2.875,0,6.225-2.268,9.1-4.717c3.266-2.789,5.105-6.531,5.184-10.531h-0.355v-48.4   L-805.84,142.504z M-805.445,192.543c-0.008,0.57-0.125,1.109-0.211,1.422c-0.492,1.836-1.813,3.766-3.602,5.293   c-2.031,1.73-4.453,2.766-6.484,2.766c-0.594,0-1.664-0.098-2.242-0.773c-0.461-0.539-0.5-1.34-0.453-1.918   c0.125-1.625,1.195-4.148,3.805-6.371c2.023-1.73,4.453-2.766,6.477-2.766c0.594,0,1.672,0.102,2.242,0.77   c0.367,0.43,0.469,1.016,0.469,1.574V192.543z M-761.84,184.18c0,0.559,0.072,1.082-0.014,1.387   c-0.492,1.836-1.713,3.766-3.502,5.293c-2.031,1.73-4.404,2.766-6.436,2.766c-0.594,0-1.641-0.098-2.219-0.773   c-0.461-0.539-0.486-1.34-0.439-1.918c0.125-1.625,1.201-4.148,3.811-6.371c2.023-1.73,4.455-2.766,6.479-2.766   c0.594,0,1.477,0.102,2.047,0.77c0.367,0.43,0.273,1.016,0.273,1.574V184.18z"
                        />
                        <g>
                          <path
                            d="M-670.109,181.529c-3.316,0-6,2.688-6,6s2.684,6,6,6c3.313,0,6-2.688,6-6S-666.797,181.529-670.109,181.529z     M-670.109,189.529c-1.105,0-2-0.896-2-2s0.895-2,2-2c1.102,0,2,0.896,2,2S-669.008,189.529-670.109,189.529z M-650.18,130    h-39.863c-3.879,0-7.957,2.334-7.957,7.529v64c0,3.879,2.762,8.471,7.957,8.471h39.863c3.879,0,8.18-3.275,8.18-8.471v-64    C-642,133.65-644.984,130-650.18,130z M-646,201.529c0,4-4.18,4.471-4.18,4.471s-35.863,0-39.863,0s-3.957-4.471-3.957-4.471    s0-60,0-64s3.957-3.529,3.957-3.529s35.863,0,39.863,0s4.18,3.529,4.18,3.529S-646,197.529-646,201.529z M-670.109,173.529    c-7.734,0-14,6.273-14,14c0,7.734,6.266,14,14,14c7.73,0,14-6.266,14-14C-656.109,179.803-662.379,173.529-670.109,173.529z     M-670.109,197.529c-5.516,0-10-4.486-10-10s4.484-10,10-10c5.512,0,10,4.486,10,10S-664.598,197.529-670.109,197.529z M-690,170    h40v-32h-40V170z M-686,142h32v24h-32V142z"
                          />
                        </g>
                        <path
                          d="M-538.813,182.479c5.313-3.598,8.813-9.684,8.813-16.57c0-11.027-8.969-20-20-20c-11.023,0-20,8.973-20,20   c0,6.887,3.5,12.973,8.813,16.57l-1.234-6.605c-2.203-2.738-3.578-6.176-3.578-9.965c0-8.844,7.164-16,16-16s16,7.156,16,16   c0,3.789-1.375,7.227-3.578,9.965L-538.813,182.479z M-558,178.235l6,32h4l6-32H-558z M-550,129.765c-19.852,0-36,16.148-36,36   c0,17.195,12.125,31.594,28.273,35.141l-0.813-4.328C-572.063,192.835-582,180.476-582,165.765c0-17.676,14.328-32,32-32   s32,14.324,32,32c0,14.711-9.938,27.07-23.453,30.813l-0.813,4.328C-526.125,197.358-514,182.96-514,165.765   C-514,145.913-530.148,129.765-550,129.765z M-556,165.843c0,3.314,2.688,6,6,6s6-2.686,6-6s-2.688-6-6-6S-556,162.528-556,165.843   z"
                        />
                        <path
                          d="M-188.578,26.672h6v-4h-6V26.672z M-194,32.504v-7.176h-4v8h-8v-16h8v4h4v-8h-2.82l0,0h-8l0,0H-210v19.176   c-4,0-4,2.695-4,6v44c0,3.313,1.867,6.824,5.18,6.824h16c3.305,0,6.82-3.512,6.82-6.824v-44C-186,35.199-190,32.504-194,32.504z    M-190,83.328c0,1.105-0.895,2-2,2h-16c-1.105,0-2-0.895-2-2v-44c0-1.105,0.895-2,2-2h16c1.105,0,2,0.895,2,2V83.328z M-178,34.672   l12,4v-4l-12-4V34.672z M-178,14.672v4l12-4v-4L-178,14.672z M-178,25.328h12v-4h-12V25.328z"
                        />
                        <path
                          d="M-281.902,54h12v-4h-12V54z M-298.07,43.459l1.004-0.318l0.215-26.406c0-3.316-2.691-6.734-6.012-6.734h-14.227   c-3.313,0-4.813,4.395-4.813,7.711v24.824l9.176,7.465h-28.207l-8.438,27.813c-0.25,1-1.719,6.352,0.41,9.078   c0.816,1.055,3.059,1.848,3.059,1.895V90h15.051l12.816-36h4.133v14.918c0,2.602-0.805,5.082-5.137,5.082h-2.863v4h2.863   c8.059,0,9.137-5.316,9.137-9.082V54h24v-4h-21.223L-298.07,43.459z M-333.617,86h-11.465c-0.293,0-0.344-0.664-0.395-0.727   c-0.602-0.773-0.488-4.266-0.039-6.094l7.539-25.18h15.656L-333.617,86z M-317.902,17.711c0-1.105-0.293-3.711,0.813-3.711h14.227   c1.109,0,2.004,1.91,2.004,3l-0.102,13h-16.941V17.711z M-309.342,48.121l-8.561-7.488V34h16.91l0.234,7.33L-309.342,48.121z    M-281.902,42.961v4.008l12-4.008v-4.008L-281.902,42.961z M-281.902,62.996l12,4.008v-4.008l-12-4.008V62.996z"
                        />
                        <path
                          d="M-643.703,56.885h-11.508l-5.75,9.996l5.75,10.004h11.508l5.75-10.004L-643.703,56.885z M-646.016,72.885h-6.891   l-3.438-6.004l3.438-5.996h6.891l3.445,5.996L-646.016,72.885z M-678.047,11.178v17.707h-8V11.178c-12,0-16,8-16,16s4,12,12,16   v37.707h0.113c0,4,3.559,7.938,7.938,7.938c4.387,0,7.938-3.938,7.938-7.938h0.012V43.178c8-4,12-8,12-16   S-670.047,11.178-678.047,11.178z M-675.824,39.6l-2.223,1.102v38.477h0.008c0,2.188-1.779,3.969-3.963,3.969   s-4.006-1.781-4.006-3.969h-0.039V40.701l-2.172-1.102c-7.5-3.75-9.809-6.656-9.809-12.422c0-4.641-0.02-10.039,7.98-11.578v17.285   h16V16.076c4,1.922,8.006,6.609,8.006,11.102C-666.041,32.943-668.324,35.85-675.824,39.6z"
                        />
                        <path
                          d="M-192,178h4v-28h-4V178z M-157.828,130h-64.039c-4.404,0-8.133,3.291-8.133,7.717v48.168c0,4.426,3.729,8.115,8.133,8.115   H-202v16h7.186l11.971-16h25.016c4.404,0,7.828-3.689,7.828-8.115v-48.168C-150,133.291-153.424,130-157.828,130z M-154,185.689   c0,2.219-1.623,4.311-3.828,4.311h-26.668L-198,205.674V190h-23.867c-2.205,0-4.133-2.092-4.133-4.311v-47.973   c0-2.219,1.928-3.717,4.133-3.717h64.039c2.205,0,3.828,1.498,3.828,3.717V185.689z M-192,147.773h4v-4h-4V147.773z"
                        />
                        <path
                          d="M-397.527,56h-64.945L-470,60.52v4.352l8.473-4.871H-438v20h4V60h8v20h4V60h23.527l8.473,4.434v-4.461L-397.527,56z    M-453.578,55.273c3.895,0,7.195-3.273,8.125-7.273h25.418c0.926,4,4.184,6.297,8.078,6.297S-404.805,52-403.875,48H-398v-0.051   c0-0.84,0.453-5.133-0.133-7.023c-0.504-1.645-1.313-5.063-5.98-7.555c-2.684-1.43-7.273-4.281-10.711-6.367   c-1.383-0.84-2.525-1.736-3.346-2.221C-421.357,22.9-428.727,20-433.836,20h-9.086c-0.273,0-6.879-0.137-12.266,4.969   C-459.676,29.219-462,35.98-462,44.73v3.148C-462,52.496-458.195,55.273-453.578,55.273z M-411.957,52.246   c-2.41,0-4.371-1.957-4.371-4.367c0-2.406,1.961-4.367,4.371-4.367s4.371,1.961,4.371,4.367   C-407.586,50.289-409.547,52.246-411.957,52.246z M-452.438,27.92c4.109-3.93,9.27-3.92,9.406-3.92h9.195   c4.172,0,10.895,2.572,13.691,4.229c0.813,0.48,1.996,1.283,3.371,2.111c3.477,2.109,8.238,5.035,11.02,6.52   c3.215,1.711,3.855,2.855,4.285,4.246c0.203,0.664,0.332,2.895,0.391,2.895h-2.754c-0.879,0-4.176-6.438-8.125-6.438   S-419.203,44-420.078,44h-25.332c-0.879,0-4.176-5.461-8.121-5.461c-1.5,0-2.891,0.828-4.109,1.523   C-457.07,34.568-455.367,30.717-452.438,27.92z M-453.531,43.512c2.406,0,4.367,1.961,4.367,4.367c0,2.41-1.961,4.367-4.367,4.367   c-2.414,0-4.375-1.957-4.375-4.367C-457.906,45.473-455.945,43.512-453.531,43.512z"
                        />
                        <g>
                          <rect x="-462" y="138" width="12" height="12" />
                          <path
                            d="M-442,130h-28v28h28V130z M-446,138.705v9.836V154h-5.193h-9.836H-466v-5.459v-9.836V134h4.971h9.836H-446V138.705z"
                          />
                          <rect x="-462" y="190" width="12" height="12" />
                          <path
                            d="M-470,210h28v-28h-28V210z M-466,200.541v-9.836V186h4.971h9.836H-446v4.705v9.836V206h-5.193h-9.836H-466V200.541z"
                          />
                          <path
                            d="M-418,130v28h-4v-4h-8.111H-434v4h-4v4h4v8h-4.111H-442v-4.377V162h-4v4h-4.111l0.111-0.377V174h7.889H-438v8h3.889H-430    v-4.377V174h3.887H-422v4h3.889H-414v-4h-4v-4.377v-4V162h4v-4h24v-28H-418z M-394,138.705v9.836V154h-5.193h-9.836H-414v-5.459    v-9.836V134h4.971h9.836H-394V138.705z"
                          />
                          <rect x="-410" y="138" width="12" height="12" />
                          <polygon
                            points="-434,145.623 -434,150 -430.111,150 -426.111,150 -426,149.623 -426,146 -422,146 -422,133.623 -422.111,134     -426.111,134 -434,134 -434,138 -438,138 -438,142 -434,142   "
                          />
                          <polygon
                            points="-454,178 -454,174 -458,174 -458,165.623 -458,162 -462,162 -462,166 -470,166 -470,178 -458.111,178   "
                          />
                          <polygon
                            points="-398,162 -402.111,162 -406.111,162 -406,161.623 -406,170 -398,170   "
                          />
                          <rect x="-414.111" y="165.623" width="4" height="4" />
                          <rect x="-426.111" y="205.623" width="4" height="4" />
                          <rect x="-414.111" y="205.623" width="4" height="4" />
                          <rect x="-438.111" y="193.623" width="4" height="4" />
                          <polygon
                            points="-406,194 -410,194 -410,198 -414,198 -414,194 -410,194 -410,190 -414.111,190 -418.111,190 -422.111,190     -426,190 -426,194 -422,194 -422,198 -426,198 -426,194 -430,194 -430,198 -434,198 -434,202 -430,202 -430,206 -426,206     -426,202 -422.111,202 -418.111,202 -414.111,202 -410,202 -410,206 -406,206 -406,202 -402,202 -402,198 -406,198   "
                          />
                          <rect x="-402.111" y="193.623" width="4" height="4" />
                          <rect x="-410.111" y="185.623" width="4" height="4" />
                          <rect x="-430.111" y="185.623" width="4" height="4" />
                          <polygon
                            points="-402,174 -406,174 -406,177.623 -406.113,178 -410,178 -410,182 -406.113,182 -406.111,182 -402,182     -402,185.623 -402,190 -394,190 -394,178 -402,178   "
                          />
                        </g>
                        <path
                          d="M-42.372,63.533c0.457-0.953,0.512-2.055,0.152-3.055l-13.438-37.676c-0.586-1.637-2.125-2.66-3.766-2.66   c-0.445,0-0.898,0.078-1.344,0.234l-3.77,1.344c-1,0.355-1.816,1.094-2.27,2.055c-0.457,0.957-0.512,2.055-0.156,3.055   l13.441,37.68c0.582,1.633,2.125,2.656,3.77,2.656c0.445,0,0.898-0.078,1.34-0.234l3.77-1.344   C-43.642,65.236-42.829,64.494-42.372,63.533z M-49.751,63.166l-13.445-37.68l3.773-1.34l13.438,37.676L-49.751,63.166z    M-36.853,67.955l-1.414-1.414l-1.414,1.414c-0.281,0.281-6.844,6.93-6.844,13.93c0,6.063,4.938,8.258,8.258,8.258   c3.316,0,8.254-2.195,8.254-8.258C-30.013,74.885-36.575,68.236-36.853,67.955z M-38.274,86.143c-0.477,0-4.25-0.141-4.25-4.258   c0-3.617,2.574-7.398,4.258-9.484c1.68,2.086,4.254,5.867,4.254,9.484C-34.013,85.768-37.267,86.127-38.274,86.143z    M-92.399,73.939c-0.57,0.203-1.086,0.289-1.551,0.289c-2.598,0-3.563-2.719-3.563-2.719s-5.379-15.066-8.066-22.602   c-1.344-3.766,2.039-5.109,2.039-5.109l25.602-9.266v8.555c0,0.063-0.078,0.102-0.305,0.18c-4.152,1.484-6.137,6.066-4.66,10.219   c1.141,3.188,4.26,5.32,7.631,5.32c0.918,0,1.873-0.16,2.74-0.469c2.008-0.715,3.648-2.172,4.563-4.105s0.652-4.105-0.066-6.117   c-0.898-2.527-1.902-4.34-5.902-4.996V33.104l4.215-1.367l-1.535-3.766l-2.68,0.887V12.146c0-1.105-0.133-2.289-1.234-2.289h-8   c-1.109,0-2.766,1.184-2.766,2.289v20.988l-18.945,6.895c-0.43,0.152-1.738,0.762-3.066,2.145c-2.07,2.156-2.553,5.102-1.49,8.078   l8.111,22.602c0.711,2,3.078,5.375,7.355,5.375c0.949,0,1.93-0.18,2.906-0.523l33.811-12.063l-1.338-3.766   C-59.606,62.236-85.017,71.299-92.399,73.939z M-75.173,46.807c1.641,0,3.184,1.016,3.766,2.652   c0.742,2.082-0.344,4.371-2.422,5.113c-0.445,0.156-0.898,0.234-1.348,0.234c-1.645,0-3.18-1.023-3.766-2.656   c-0.738-2.086,0.344-4.371,2.426-5.113C-76.071,46.877-75.618,46.807-75.173,46.807z M-81.938,13.857h4v16.426l-4,1.43V13.857z"
                        />
                        <g>
                          <path
                            d="M-914.082,156.162h-20v24h-16v28h15.676h3.91h11.609h3.902h16.902v-16h-16V156.162z M-934.082,204.162h-12v-20h12V204.162    z M-918.082,204.162h-12v-44h12V204.162z M-914.082,196.162h12v8h-12V196.162z"
                          />
                          <path
                            d="M-869.918,159.771c0-11.416-12.742-25.686-13.285-26.287l-1.488-1.646l-1.484,1.646    c-0.539,0.602-13.289,14.871-13.289,26.287c0,7.287,5.316,13.318,12.266,14.518c-1.145,3.242-4.211,5.586-7.844,5.586    c-1.102,0-2,0.895-2,2s0.898,2,2,2c5.824,0,10.691-4.064,11.984-9.498C-875.684,173.553-869.918,167.357-869.918,159.771z     M-882.082,170.34v-10.178h-4v10.178c-4-0.941-9.078-5.314-9.078-10.568c0-7.771,7.359-17.84,10.621-21.85    c3.266,4.006,11.004,14.066,11.004,21.85C-873.535,165.027-878.082,169.398-882.082,170.34z"
                          />
                        </g>
                        <g>
                          <path
                            d="M170,130c-22.057,0-40,17.941-40,40c0,22.055,17.943,40,40,40s40-17.945,40-40C210,147.941,192.057,130,170,130z M170,206    c-19.85,0-36-16.152-36-36c0-19.852,16.15-36,36-36s36,16.148,36,36C206,189.848,189.85,206,170,206z"
                          />
                          <circle cx="154" cy="170" r="4" />
                          <circle cx="170" cy="170" r="4" />
                          <circle cx="186" cy="170" r="4" />
                        </g>
                        <g>
                          <path
                            d="M-569.605,37.609v24.773l20,12.391l20-12.391V37.609l-20-12.391L-569.605,37.609z M-533.605,60.078l-16,10.078l-16-10.078    v-20.16l16-10.078l16,10.078V60.078z"
                          />
                          <path
                            d="M-562.297,50c0,6.781,5.516,12.297,12.297,12.297s12.297-5.516,12.297-12.297S-543.219,37.703-550,37.703    S-562.297,43.219-562.297,50z M-541.703,50c0,4.57-3.723,8.297-8.297,8.297s-8.297-3.727-8.297-8.297    c0-4.578,3.723-8.297,8.297-8.297S-541.703,45.422-541.703,50z"
                          />
                          <path
                            d="M-550,10c-22.078,0-40,17.906-40,40s17.922,40,40,40s40-17.906,40-40S-527.922,10-550,10z M-550,86    c-19.852,0-36-16.156-36-36c0-19.852,16.148-36,36-36s36,16.148,36,36C-514,69.844-530.148,86-550,86z"
                          />
                        </g>
                        <g>
                          <path
                            d="M71.432,10H17.5v80h65V21L71.432,10z M78,85.5H22v-71h47.432h0.136L78,22.863V85.5z"
                          />
                          <rect x="33.5" y="36" width="19.5" height="4.5" />
                          <rect x="33.5" y="50.5" width="34.5" height="4.5" />
                          <rect x="33.5" y="65.5" width="34.5" height="4.5" />
                        </g>
                      </g>
                    </svg>
                    <span style="margin-left: 21px">
                      <label class="menu-title" style="margin-left: 5px"
                        >Terms and Conditions</label
                      >
                    </span>
                  </a>
                </li>
                <!-- Terms and Condition end -->

                <!-- Reward Wheel Terms and Conditions start -->
                <li class="nav-item active">
                  <a
                    class="nav-link d-flex align-items-start"
                    style="color: green; text-align: left; margin-left: -15px"
                    v-on:click="clickRewardWheelTermsandCondition"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                      version="1.1"
                      x="0px"
                      y="0px"
                      viewBox="0 0 80 125"
                      style="enable-background new 0 0 100 100; margin-left: 8px"
                      xml:space="preserve"
                      width="40"
                      height="40"
                      fill="#149240"
                    >
                      <g>
                        <path d="M-910,170" />
                        <path
                          d="M-750.746,71.573c-0.605-1.992-1.883-6.135-7.527-9.148c-3.367-1.797-9.262-5.377-13.566-7.99   c-1.723-1.043-3.211-1.947-4.23-2.551c-3.934-2.33-13.098-5.701-19.383-5.701l-11.371,0.008c-0.352-0.027-8.313-0.377-14.887,5.855   c-5.5,5.217-8.289,13.342-8.289,24.146v3.938c0,5.494,4.461,9.775,9.949,9.775c4.773,0,8.762-4.074,9.723-8.074h32.473   c0.957,4,4.949,7.883,9.723,7.883s8.766-3.883,9.723-7.883h8.41v-1.619C-750,79.575-750.02,73.925-750.746,71.573z    M-820.051,86.095c-3.285,0-5.957-2.676-5.957-5.965c0-3.291,2.672-5.969,5.957-5.969s5.957,2.678,5.957,5.969   C-814.094,83.419-816.766,86.095-820.051,86.095z M-768.133,86.095c-3.281,0-5.953-2.676-5.953-5.965   c0-3.291,2.672-5.969,5.953-5.969c3.285,0,5.957,2.678,5.957,5.969C-762.176,83.419-764.848,86.095-768.133,86.095z    M-758.375,77.831c-0.898-4-4.926-8.053-9.758-8.053s-8.859,4.053-9.758,8.053h-32.406c-0.895-4-4.922-7.863-9.754-7.863   c-2.16,0-4.148,0.807-5.781,1.986c0.598-7.545,2.871-13.412,6.82-17.182c5.281-5.053,11.906-4.941,12.078-4.941h11.48   c5.289,0,13.813,3.223,17.348,5.318c1.016,0.6,2.488,1.584,4.195,2.619c4.344,2.637,10.293,6.293,13.758,8.145   c4.191,2.234,5.066,4.92,5.594,6.623c0.305,0.99,0.469,5.295,0.531,5.295H-758.375z M-763.348,38.144   c8.168,0,13.348-7.113,13.348-14.025c0-0.045,0-0.09,0-0.135c0-6.846-5.213-13.889-13.346-13.889   c-7.887,0-13.334,6.576-13.529,6.855c-0.234,0.336-1.125,0.734-1.125,1.145v12.049c0,0.41,0.891,0.809,1.125,1.145   C-776.68,31.565-771.234,38.144-763.348,38.144z M-774,18.786c4-1.262,5.27-4.691,10-4.691c5.645,0,10,5.029,10,9.939   c0,0.033,0,0.068,0,0.102c0,4.918-4.318,10.008-9.998,10.008c-4.73,0-6.002-3.43-10.002-4.693V18.786z M-795.805,29.831H-782v-4   h-12.102l-3.125-4H-802l0,0h20v-4h-24v8h7.07L-795.805,29.831z"
                        />
                        <path
                          d="M-875.56,25.206c-4.211-4.266-10.969-7.215-20.086-7.215h-14.363v28h-5.414l-27.602-27.859l-2.984,1.568v26.291h4V24.528   l20.93,21.463h-4.809c-1.133,0-11.184-0.324-15.16,2.117c-0.254,0.156-0.523,0.234-0.813,0.387   c-4.059,2.188-8.023,5.496-8.023,21.496h0.07c-0.125,0-0.195,1.418-0.195,2.082c0,5.523,4.48,9.936,10.008,9.936   c4.824,0,8.852-4.018,9.797-8.018h32.445c0.949,4,4.977,7.953,9.797,7.953c4.824,0,8.852-3.953,9.797-7.953h8.156V47.763   l-0.004-2.248C-869.927,39.565-869.829,31.022-875.56,25.206z M-940.001,78.22c-3.313,0-6.004-2.691-6.004-6s2.691-6,6.004-6   s6.004,2.691,6.004,6S-936.688,78.22-940.001,78.22z M-887.962,78.22c-3.309,0-6.004-2.691-6.004-6s2.695-6,6.004-6   c3.313,0,6.008,2.691,6.008,6S-884.649,78.22-887.962,78.22z M-874.032,45.71l-0.02,24.281h-4.098c-0.91-4-4.953-8.047-9.813-8.047   s-8.898,4.047-9.813,8.047h-32.414c-0.91-4-4.953-7.91-9.813-7.91c-2.082,0-4.012,0.703-5.609,1.789   c0.848-9.078,3.563-10.541,5.648-11.662c0.355-0.191,0.691-0.512,1.008-0.707c2.215-1.355,9.063-1.51,12.965-1.51h19.98v-28h10.363   c8.008,0,13.805,2.479,17.23,5.955C-873.856,32.567-873.958,40.468-874.032,45.71z"
                        />
                        <path
                          d="M-312,191.845h4v-4h-4V191.845z M-312.051,184.155h4v-24h-4V184.155z M-276.535,193.847l-30-52   c-0.715-1.238-2.035-2.002-3.465-2.002s-2.75,0.764-3.465,2.002l-30,52c-0.715,1.236-0.715,2.918,0,4.152   c0.715,1.238,2.035,2.156,3.465,2.156h60c1.43,0,2.75-0.918,3.465-2.156C-275.82,196.765-275.82,195.083-276.535,193.847z    M-340,196.155l30-52l30,52H-340z"
                        />
                        <g>
                          <path
                            d="M38,210h7.184l11.973-16h25.016c4.402,0,7.828-3.689,7.828-8.115v-48.168c0-4.426-3.426-7.717-7.828-7.717H18.133    c-4.404,0-8.133,3.291-8.133,7.717v48.168c0,4.426,3.729,8.115,8.133,8.115H38V210z M86,185.885c0,2.219-1.621,4.115-3.828,4.115    H55.504L42,205.674V190H18.133c-2.207,0-4.133-1.896-4.133-4.115v-48.168c0-2.219,1.926-3.717,4.133-3.717h64.039    c2.207,0,3.828,1.498,3.828,3.717V185.885z"
                          />
                        </g>
                        <path
                          d="M-71.848,179.672h4v-4h-4V179.672z M-37.828,130h-64.039c-4.406,0-8.133,3.291-8.133,7.717v48.168   c0,4.426,3.727,8.115,8.133,8.115H-82v16h7.184l11.973-16h25.016c4.402,0,7.828-3.689,7.828-8.115v-48.168   C-30,133.291-33.426,130-37.828,130z M-34,185.885c0,2.219-1.625,4.115-3.828,4.115h-26.668L-78,205.674V190h-23.867   c-2.207,0-4.133-1.896-4.133-4.115v-48.168c0-2.219,1.926-3.717,4.133-3.717h64.039c2.203,0,3.828,1.498,3.828,3.717V185.885z    M-68.924,143.672c-7.91,0-9.076,6.541-9.076,10v1.988l3.053,0.012l-0.439-1.988c0.012-1.004,0.125-6.012,5.77-6.012   s5.873,5.008,5.885,5.979c-0.004,0.133,0.754,3.305-2.547,6.607c-4.535,4.533-3.721,9.217-3.721,9.414v1.977l3.029,0.021   l-0.416-1.977c0.004-0.133-0.117-3.305,3.184-6.607c4.535-4.533,4.934-9.217,4.934-9.414   C-59.27,150.213-61.014,143.672-68.924,143.672z"
                        />
                        <path
                          d="M-805.84,142.504v44.152c0-0.293-1.537-0.461-2.514-0.461c-2.875,0-6.104,1.273-8.979,3.723   c-5.016,4.273-6.662,10.512-3.74,13.93c1.242,1.465,3.156,2.176,5.305,2.176c2.875,0,6.225-0.357,9.1-2.807   c3.344-2.855,5.176-8.84,5.184-8.84h-0.355v-28.521l36-6.457v-4.063l-36,6.453v-15.934l40-7.105v39.508   c0-0.293-1.537-0.461-2.514-0.461c-2.875,0-6.104,1.273-8.979,3.723c-5.016,4.273-6.662,10.512-3.74,13.93   c1.242,1.465,3.156,2.176,5.305,2.176c2.875,0,6.225-2.268,9.1-4.717c3.266-2.789,5.105-6.531,5.184-10.531h-0.355v-48.4   L-805.84,142.504z M-805.445,192.543c-0.008,0.57-0.125,1.109-0.211,1.422c-0.492,1.836-1.813,3.766-3.602,5.293   c-2.031,1.73-4.453,2.766-6.484,2.766c-0.594,0-1.664-0.098-2.242-0.773c-0.461-0.539-0.5-1.34-0.453-1.918   c0.125-1.625,1.195-4.148,3.805-6.371c2.023-1.73,4.453-2.766,6.477-2.766c0.594,0,1.672,0.102,2.242,0.77   c0.367,0.43,0.469,1.016,0.469,1.574V192.543z M-761.84,184.18c0,0.559,0.072,1.082-0.014,1.387   c-0.492,1.836-1.713,3.766-3.502,5.293c-2.031,1.73-4.404,2.766-6.436,2.766c-0.594,0-1.641-0.098-2.219-0.773   c-0.461-0.539-0.486-1.34-0.439-1.918c0.125-1.625,1.201-4.148,3.811-6.371c2.023-1.73,4.455-2.766,6.479-2.766   c0.594,0,1.477,0.102,2.047,0.77c0.367,0.43,0.273,1.016,0.273,1.574V184.18z"
                        />
                        <g>
                          <path
                            d="M-670.109,181.529c-3.316,0-6,2.688-6,6s2.684,6,6,6c3.313,0,6-2.688,6-6S-666.797,181.529-670.109,181.529z     M-670.109,189.529c-1.105,0-2-0.896-2-2s0.895-2,2-2c1.102,0,2,0.896,2,2S-669.008,189.529-670.109,189.529z M-650.18,130    h-39.863c-3.879,0-7.957,2.334-7.957,7.529v64c0,3.879,2.762,8.471,7.957,8.471h39.863c3.879,0,8.18-3.275,8.18-8.471v-64    C-642,133.65-644.984,130-650.18,130z M-646,201.529c0,4-4.18,4.471-4.18,4.471s-35.863,0-39.863,0s-3.957-4.471-3.957-4.471    s0-60,0-64s3.957-3.529,3.957-3.529s35.863,0,39.863,0s4.18,3.529,4.18,3.529S-646,197.529-646,201.529z M-670.109,173.529    c-7.734,0-14,6.273-14,14c0,7.734,6.266,14,14,14c7.73,0,14-6.266,14-14C-656.109,179.803-662.379,173.529-670.109,173.529z     M-670.109,197.529c-5.516,0-10-4.486-10-10s4.484-10,10-10c5.512,0,10,4.486,10,10S-664.598,197.529-670.109,197.529z M-690,170    h40v-32h-40V170z M-686,142h32v24h-32V142z"
                          />
                        </g>
                        <path
                          d="M-538.813,182.479c5.313-3.598,8.813-9.684,8.813-16.57c0-11.027-8.969-20-20-20c-11.023,0-20,8.973-20,20   c0,6.887,3.5,12.973,8.813,16.57l-1.234-6.605c-2.203-2.738-3.578-6.176-3.578-9.965c0-8.844,7.164-16,16-16s16,7.156,16,16   c0,3.789-1.375,7.227-3.578,9.965L-538.813,182.479z M-558,178.235l6,32h4l6-32H-558z M-550,129.765c-19.852,0-36,16.148-36,36   c0,17.195,12.125,31.594,28.273,35.141l-0.813-4.328C-572.063,192.835-582,180.476-582,165.765c0-17.676,14.328-32,32-32   s32,14.324,32,32c0,14.711-9.938,27.07-23.453,30.813l-0.813,4.328C-526.125,197.358-514,182.96-514,165.765   C-514,145.913-530.148,129.765-550,129.765z M-556,165.843c0,3.314,2.688,6,6,6s6-2.686,6-6s-2.688-6-6-6S-556,162.528-556,165.843   z"
                        />
                        <path
                          d="M-188.578,26.672h6v-4h-6V26.672z M-194,32.504v-7.176h-4v8h-8v-16h8v4h4v-8h-2.82l0,0h-8l0,0H-210v19.176   c-4,0-4,2.695-4,6v44c0,3.313,1.867,6.824,5.18,6.824h16c3.305,0,6.82-3.512,6.82-6.824v-44C-186,35.199-190,32.504-194,32.504z    M-190,83.328c0,1.105-0.895,2-2,2h-16c-1.105,0-2-0.895-2-2v-44c0-1.105,0.895-2,2-2h16c1.105,0,2,0.895,2,2V83.328z M-178,34.672   l12,4v-4l-12-4V34.672z M-178,14.672v4l12-4v-4L-178,14.672z M-178,25.328h12v-4h-12V25.328z"
                        />
                        <path
                          d="M-281.902,54h12v-4h-12V54z M-298.07,43.459l1.004-0.318l0.215-26.406c0-3.316-2.691-6.734-6.012-6.734h-14.227   c-3.313,0-4.813,4.395-4.813,7.711v24.824l9.176,7.465h-28.207l-8.438,27.813c-0.25,1-1.719,6.352,0.41,9.078   c0.816,1.055,3.059,1.848,3.059,1.895V90h15.051l12.816-36h4.133v14.918c0,2.602-0.805,5.082-5.137,5.082h-2.863v4h2.863   c8.059,0,9.137-5.316,9.137-9.082V54h24v-4h-21.223L-298.07,43.459z M-333.617,86h-11.465c-0.293,0-0.344-0.664-0.395-0.727   c-0.602-0.773-0.488-4.266-0.039-6.094l7.539-25.18h15.656L-333.617,86z M-317.902,17.711c0-1.105-0.293-3.711,0.813-3.711h14.227   c1.109,0,2.004,1.91,2.004,3l-0.102,13h-16.941V17.711z M-309.342,48.121l-8.561-7.488V34h16.91l0.234,7.33L-309.342,48.121z    M-281.902,42.961v4.008l12-4.008v-4.008L-281.902,42.961z M-281.902,62.996l12,4.008v-4.008l-12-4.008V62.996z"
                        />
                        <path
                          d="M-643.703,56.885h-11.508l-5.75,9.996l5.75,10.004h11.508l5.75-10.004L-643.703,56.885z M-646.016,72.885h-6.891   l-3.438-6.004l3.438-5.996h6.891l3.445,5.996L-646.016,72.885z M-678.047,11.178v17.707h-8V11.178c-12,0-16,8-16,16s4,12,12,16   v37.707h0.113c0,4,3.559,7.938,7.938,7.938c4.387,0,7.938-3.938,7.938-7.938h0.012V43.178c8-4,12-8,12-16   S-670.047,11.178-678.047,11.178z M-675.824,39.6l-2.223,1.102v38.477h0.008c0,2.188-1.779,3.969-3.963,3.969   s-4.006-1.781-4.006-3.969h-0.039V40.701l-2.172-1.102c-7.5-3.75-9.809-6.656-9.809-12.422c0-4.641-0.02-10.039,7.98-11.578v17.285   h16V16.076c4,1.922,8.006,6.609,8.006,11.102C-666.041,32.943-668.324,35.85-675.824,39.6z"
                        />
                        <path
                          d="M-192,178h4v-28h-4V178z M-157.828,130h-64.039c-4.404,0-8.133,3.291-8.133,7.717v48.168c0,4.426,3.729,8.115,8.133,8.115   H-202v16h7.186l11.971-16h25.016c4.404,0,7.828-3.689,7.828-8.115v-48.168C-150,133.291-153.424,130-157.828,130z M-154,185.689   c0,2.219-1.623,4.311-3.828,4.311h-26.668L-198,205.674V190h-23.867c-2.205,0-4.133-2.092-4.133-4.311v-47.973   c0-2.219,1.928-3.717,4.133-3.717h64.039c2.205,0,3.828,1.498,3.828,3.717V185.689z M-192,147.773h4v-4h-4V147.773z"
                        />
                        <path
                          d="M-397.527,56h-64.945L-470,60.52v4.352l8.473-4.871H-438v20h4V60h8v20h4V60h23.527l8.473,4.434v-4.461L-397.527,56z    M-453.578,55.273c3.895,0,7.195-3.273,8.125-7.273h25.418c0.926,4,4.184,6.297,8.078,6.297S-404.805,52-403.875,48H-398v-0.051   c0-0.84,0.453-5.133-0.133-7.023c-0.504-1.645-1.313-5.063-5.98-7.555c-2.684-1.43-7.273-4.281-10.711-6.367   c-1.383-0.84-2.525-1.736-3.346-2.221C-421.357,22.9-428.727,20-433.836,20h-9.086c-0.273,0-6.879-0.137-12.266,4.969   C-459.676,29.219-462,35.98-462,44.73v3.148C-462,52.496-458.195,55.273-453.578,55.273z M-411.957,52.246   c-2.41,0-4.371-1.957-4.371-4.367c0-2.406,1.961-4.367,4.371-4.367s4.371,1.961,4.371,4.367   C-407.586,50.289-409.547,52.246-411.957,52.246z M-452.438,27.92c4.109-3.93,9.27-3.92,9.406-3.92h9.195   c4.172,0,10.895,2.572,13.691,4.229c0.813,0.48,1.996,1.283,3.371,2.111c3.477,2.109,8.238,5.035,11.02,6.52   c3.215,1.711,3.855,2.855,4.285,4.246c0.203,0.664,0.332,2.895,0.391,2.895h-2.754c-0.879,0-4.176-6.438-8.125-6.438   S-419.203,44-420.078,44h-25.332c-0.879,0-4.176-5.461-8.121-5.461c-1.5,0-2.891,0.828-4.109,1.523   C-457.07,34.568-455.367,30.717-452.438,27.92z M-453.531,43.512c2.406,0,4.367,1.961,4.367,4.367c0,2.41-1.961,4.367-4.367,4.367   c-2.414,0-4.375-1.957-4.375-4.367C-457.906,45.473-455.945,43.512-453.531,43.512z"
                        />
                        <g>
                          <rect x="-462" y="138" width="12" height="12" />
                          <path
                            d="M-442,130h-28v28h28V130z M-446,138.705v9.836V154h-5.193h-9.836H-466v-5.459v-9.836V134h4.971h9.836H-446V138.705z"
                          />
                          <rect x="-462" y="190" width="12" height="12" />
                          <path
                            d="M-470,210h28v-28h-28V210z M-466,200.541v-9.836V186h4.971h9.836H-446v4.705v9.836V206h-5.193h-9.836H-466V200.541z"
                          />
                          <path
                            d="M-418,130v28h-4v-4h-8.111H-434v4h-4v4h4v8h-4.111H-442v-4.377V162h-4v4h-4.111l0.111-0.377V174h7.889H-438v8h3.889H-430    v-4.377V174h3.887H-422v4h3.889H-414v-4h-4v-4.377v-4V162h4v-4h24v-28H-418z M-394,138.705v9.836V154h-5.193h-9.836H-414v-5.459    v-9.836V134h4.971h9.836H-394V138.705z"
                          />
                          <rect x="-410" y="138" width="12" height="12" />
                          <polygon
                            points="-434,145.623 -434,150 -430.111,150 -426.111,150 -426,149.623 -426,146 -422,146 -422,133.623 -422.111,134     -426.111,134 -434,134 -434,138 -438,138 -438,142 -434,142   "
                          />
                          <polygon
                            points="-454,178 -454,174 -458,174 -458,165.623 -458,162 -462,162 -462,166 -470,166 -470,178 -458.111,178   "
                          />
                          <polygon
                            points="-398,162 -402.111,162 -406.111,162 -406,161.623 -406,170 -398,170   "
                          />
                          <rect x="-414.111" y="165.623" width="4" height="4" />
                          <rect x="-426.111" y="205.623" width="4" height="4" />
                          <rect x="-414.111" y="205.623" width="4" height="4" />
                          <rect x="-438.111" y="193.623" width="4" height="4" />
                          <polygon
                            points="-406,194 -410,194 -410,198 -414,198 -414,194 -410,194 -410,190 -414.111,190 -418.111,190 -422.111,190     -426,190 -426,194 -422,194 -422,198 -426,198 -426,194 -430,194 -430,198 -434,198 -434,202 -430,202 -430,206 -426,206     -426,202 -422.111,202 -418.111,202 -414.111,202 -410,202 -410,206 -406,206 -406,202 -402,202 -402,198 -406,198   "
                          />
                          <rect x="-402.111" y="193.623" width="4" height="4" />
                          <rect x="-410.111" y="185.623" width="4" height="4" />
                          <rect x="-430.111" y="185.623" width="4" height="4" />
                          <polygon
                            points="-402,174 -406,174 -406,177.623 -406.113,178 -410,178 -410,182 -406.113,182 -406.111,182 -402,182     -402,185.623 -402,190 -394,190 -394,178 -402,178   "
                          />
                        </g>
                        <path
                          d="M-42.372,63.533c0.457-0.953,0.512-2.055,0.152-3.055l-13.438-37.676c-0.586-1.637-2.125-2.66-3.766-2.66   c-0.445,0-0.898,0.078-1.344,0.234l-3.77,1.344c-1,0.355-1.816,1.094-2.27,2.055c-0.457,0.957-0.512,2.055-0.156,3.055   l13.441,37.68c0.582,1.633,2.125,2.656,3.77,2.656c0.445,0,0.898-0.078,1.34-0.234l3.77-1.344   C-43.642,65.236-42.829,64.494-42.372,63.533z M-49.751,63.166l-13.445-37.68l3.773-1.34l13.438,37.676L-49.751,63.166z    M-36.853,67.955l-1.414-1.414l-1.414,1.414c-0.281,0.281-6.844,6.93-6.844,13.93c0,6.063,4.938,8.258,8.258,8.258   c3.316,0,8.254-2.195,8.254-8.258C-30.013,74.885-36.575,68.236-36.853,67.955z M-38.274,86.143c-0.477,0-4.25-0.141-4.25-4.258   c0-3.617,2.574-7.398,4.258-9.484c1.68,2.086,4.254,5.867,4.254,9.484C-34.013,85.768-37.267,86.127-38.274,86.143z    M-92.399,73.939c-0.57,0.203-1.086,0.289-1.551,0.289c-2.598,0-3.563-2.719-3.563-2.719s-5.379-15.066-8.066-22.602   c-1.344-3.766,2.039-5.109,2.039-5.109l25.602-9.266v8.555c0,0.063-0.078,0.102-0.305,0.18c-4.152,1.484-6.137,6.066-4.66,10.219   c1.141,3.188,4.26,5.32,7.631,5.32c0.918,0,1.873-0.16,2.74-0.469c2.008-0.715,3.648-2.172,4.563-4.105s0.652-4.105-0.066-6.117   c-0.898-2.527-1.902-4.34-5.902-4.996V33.104l4.215-1.367l-1.535-3.766l-2.68,0.887V12.146c0-1.105-0.133-2.289-1.234-2.289h-8   c-1.109,0-2.766,1.184-2.766,2.289v20.988l-18.945,6.895c-0.43,0.152-1.738,0.762-3.066,2.145c-2.07,2.156-2.553,5.102-1.49,8.078   l8.111,22.602c0.711,2,3.078,5.375,7.355,5.375c0.949,0,1.93-0.18,2.906-0.523l33.811-12.063l-1.338-3.766   C-59.606,62.236-85.017,71.299-92.399,73.939z M-75.173,46.807c1.641,0,3.184,1.016,3.766,2.652   c0.742,2.082-0.344,4.371-2.422,5.113c-0.445,0.156-0.898,0.234-1.348,0.234c-1.645,0-3.18-1.023-3.766-2.656   c-0.738-2.086,0.344-4.371,2.426-5.113C-76.071,46.877-75.618,46.807-75.173,46.807z M-81.938,13.857h4v16.426l-4,1.43V13.857z"
                        />
                        <g>
                          <path
                            d="M-914.082,156.162h-20v24h-16v28h15.676h3.91h11.609h3.902h16.902v-16h-16V156.162z M-934.082,204.162h-12v-20h12V204.162    z M-918.082,204.162h-12v-44h12V204.162z M-914.082,196.162h12v8h-12V196.162z"
                          />
                          <path
                            d="M-869.918,159.771c0-11.416-12.742-25.686-13.285-26.287l-1.488-1.646l-1.484,1.646    c-0.539,0.602-13.289,14.871-13.289,26.287c0,7.287,5.316,13.318,12.266,14.518c-1.145,3.242-4.211,5.586-7.844,5.586    c-1.102,0-2,0.895-2,2s0.898,2,2,2c5.824,0,10.691-4.064,11.984-9.498C-875.684,173.553-869.918,167.357-869.918,159.771z     M-882.082,170.34v-10.178h-4v10.178c-4-0.941-9.078-5.314-9.078-10.568c0-7.771,7.359-17.84,10.621-21.85    c3.266,4.006,11.004,14.066,11.004,21.85C-873.535,165.027-878.082,169.398-882.082,170.34z"
                          />
                        </g>
                        <g>
                          <path
                            d="M170,130c-22.057,0-40,17.941-40,40c0,22.055,17.943,40,40,40s40-17.945,40-40C210,147.941,192.057,130,170,130z M170,206    c-19.85,0-36-16.152-36-36c0-19.852,16.15-36,36-36s36,16.148,36,36C206,189.848,189.85,206,170,206z"
                          />
                          <circle cx="154" cy="170" r="4" />
                          <circle cx="170" cy="170" r="4" />
                          <circle cx="186" cy="170" r="4" />
                        </g>
                        <g>
                          <path
                            d="M-569.605,37.609v24.773l20,12.391l20-12.391V37.609l-20-12.391L-569.605,37.609z M-533.605,60.078l-16,10.078l-16-10.078    v-20.16l16-10.078l16,10.078V60.078z"
                          />
                          <path
                            d="M-562.297,50c0,6.781,5.516,12.297,12.297,12.297s12.297-5.516,12.297-12.297S-543.219,37.703-550,37.703    S-562.297,43.219-562.297,50z M-541.703,50c0,4.57-3.723,8.297-8.297,8.297s-8.297-3.727-8.297-8.297    c0-4.578,3.723-8.297,8.297-8.297S-541.703,45.422-541.703,50z"
                          />
                          <path
                            d="M-550,10c-22.078,0-40,17.906-40,40s17.922,40,40,40s40-17.906,40-40S-527.922,10-550,10z M-550,86    c-19.852,0-36-16.156-36-36c0-19.852,16.148-36,36-36s36,16.148,36,36C-514,69.844-530.148,86-550,86z"
                          />
                        </g>
                        <g>
                          <path
                            d="M71.432,10H17.5v80h65V21L71.432,10z M78,85.5H22v-71h47.432h0.136L78,22.863V85.5z"
                          />
                          <rect x="33.5" y="36" width="19.5" height="4.5" />
                          <rect x="33.5" y="50.5" width="34.5" height="4.5" />
                          <rect x="33.5" y="65.5" width="34.5" height="4.5" />
                        </g>
                      </g>
                    </svg>
                    <span style="margin-left: 28px">
                      <label class="menu-title" style="margin-left: 5px; white-space: break-spaces;"
                        >Reward Wheel Terms and Conditions</label
                      >
                    </span>
                  </a>
                </li>
                <!-- Reward Wheel Terms and Conditions end -->

                <!-- Privacy Policy start -->
                <li class="nav-item active">
                  <a
                    class="nav-link"
                    style="color: green; text-align: left; margin-left: -15px"
                    v-on:click="clickPrivacyPolicy"
                  >
                    <svg
                      version="1.1"
                      id="Layer_1"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                      x="0px"
                      y="0px"
                      viewBox="0 0 50 80"
                      style="enable-background: new 0 0 64 80"
                      xml:space="preserve"
                      width="40"
                      height="40"
                      fill="#149240"
                    >
                      <g>
                        <g>
                          <path
                            d="M54,15c-8.7,0-13-2.9-13-9c0-0.6-0.4-1-1-1H24c-0.6,0-1,0.4-1,1c0,6.1-4.3,9-13,9c-0.6,0-1,0.4-1,1v18
			c0,10.4,21.5,24.3,22.5,24.8c0.2,0.1,0.4,0.2,0.5,0.2s0.4-0.1,0.5-0.2C33.5,58.3,55,44.4,55,34V16C55,15.4,54.6,15,54,15z M53,34
			c0,8.4-17.7,20.6-21,22.8C28.7,54.6,11,42.4,11,34V17c8.8-0.2,13.5-3.6,14-10H39c0.4,6.4,5.1,9.8,14,10C53,17,53,34,53,34z"
                          />
                          <path
                            d="M36.1,29.7c1.8-1.3,2.9-3.3,2.9-5.7c0-3.9-3.1-7-7-7c-3.9,0-7,3.1-7,7c0,2.3,1.2,4.4,2.9,5.7C22.7,31.4,19,36.3,19,42
			c0,0.6,0.4,1,1,1s1-0.4,1-1c0-6.1,4.9-11,11-11s11,4.9,11,11c0,0.6,0.4,1,1,1s1-0.4,1-1C45,36.3,41.3,31.4,36.1,29.7z M27,24
			c0-2.8,2.2-5,5-5s5,2.2,5,5s-2.2,5-5,5S27,26.8,27,24z"
                          />
                        </g>
                      </g>
                    </svg>

                    <span style="margin-left: 20px">
                      <label class="menu-title" style="margin-left: 5px"
                        >Privacy Policy</label
                      >
                    </span>
                  </a>
                </li>
                <!-- Privacy Policy end -->

              </ul>
            </div>
          </div>
          <div class="row" >
            <div class="col-1" style="margin-left: 20px; align-content: left">
              <a
                class="nav-link"
                style="color: green; text-align: left"
                v-on:click="clicklogout"
              >
                <span>
                  <label style="color: green; text-align: left">
                    <h6>Logout</h6>
                  </label>
                </span>
              </a>
            </div>
          </div>
        </div>
        <div
          slot="content"
          :content-drawable="true"
          :style="rewardWheelPageStyle"
          id="rewardWheelBody"
          v-bind:class="[
            isWhitebackground ? 'white-body' : isGreybackground ? 'grey-body' : 'drawer-body overflow-auto',
            '',
            $route.name == 'RewardWheel' ? 'reward-wheel-container' : ''
          ]"
        >
          <!-- main-content -->
          <div class="common-nav d-flex align-items-center" v-if="show == '1' && showCommonheader">
            <!-- adding the Header -->
            <cp-header></cp-header>
          </div>
          <div
            class="termsandcondition-nav"
            v-if="
              show == '1' &&
              !showCommonheader &&
              headerType == 'TermsandConditionHeader'
            "
          >
            <!-- adding the Header -->
            <TermsandconditionHeader></TermsandconditionHeader>
          </div>
          <div
          class="common-nav d-flex align-items-center"
            v-if="
              show == '1' &&
              !showCommonheader &&
              headerType == 'BankLinkingHeader'
            "
          >
            <!-- adding the Header -->
            <BankLinkingHeader></BankLinkingHeader>
          </div>
          <div
          class="common-nav d-flex align-items-center"
            v-if="
              show == '1' &&
              !showCommonheader &&
              headerType == 'RegistrationToStandardHeader'
            "
          >
            <!-- adding the Header -->
            <RegistrationToStandardHeader></RegistrationToStandardHeader>
          </div>
          <div
            class="termsandcondition-nav"
            v-if="
              show == '1' && !showCommonheader && headerType == 'PrivacyHeader'
            "
          >
            <!-- adding the Header -->
            <privacy-policy-header></privacy-policy-header>
          </div>
          <div
            class="transactionhistory-nav"
            v-if="
              show == '1' &&
              !showCommonheader &&
              headerType == 'TransactionHistoryHeader'
            "
          >
            <!-- adding the Header -->
            <transaction-history-header></transaction-history-header>
          </div>
          <div
            class="termsandcondition-nav"
            v-if="
              show == '1' &&
              !showCommonheader &&
              headerType == 'SponsorHistoryHeader'
            "
          >
            <!-- adding the Header -->
            <sponsor-history-header></sponsor-history-header>
          </div>
          <div
            class="participant-nav"
            v-if="
              show == '1' &&
              !showCommonheader &&
              headerType == 'ParticipantHeader'
            "
          >
            <!-- adding the Header -->
            <participant-header></participant-header>
          </div>
          <div
            class="merchant-nav"
            v-if="
              show == '1' &&
              !showCommonheader &&
              headerType == 'MerchantDetailsHeader'
            "
          >
            <!-- adding the Header -->
            <merchant-details></merchant-details>
          </div>
          <div
            class="profile-nav"
            v-if="
              show == '1' && !showCommonheader && headerType == 'ProfileHeader'
            "
          >
            <!-- adding the Header -->
            <profile-header></profile-header>
          </div>
          <!-- adding rewardwheel header  -->
          <div
            :style="wheelHeaderStyle"
            class="reward-wheel-nav"
            v-if="
              show == '1' &&
              !showCommonheader &&
              headerType == 'RewardWheelHeader'
            "
          >
            <!-- adding the Header -->
            <RewardWheelHeader></RewardWheelHeader>
          </div>

          <!-- adding rewardpoint header  -->
          <!-- adding CanPayCrewPetitionHeader header  -->
          <div
            class="crew-nav"
            style="background-color:#ffffff!important"
            v-if="
              show == '1' &&
              !showCommonheader &&
              headerType == 'CanPayCrewPetitionHeader'
            "
          >
            <!-- adding the Header -->
            <CanPayCrewPetitionHeader></CanPayCrewPetitionHeader>
          </div>

          <!-- adding CanPayCrewPetitionHeader header  -->
          <!-- adding CanPayCrewLandingHeader header  -->
          <div
            class="reward-wheel-nav"
            v-if="
              show == '1' &&
              !showCommonheader &&
              headerType == 'CanPayCrewLandingHeader'
            "
          >
            <!-- adding the Header -->
            <CanPayCrewLandingHeader></CanPayCrewLandingHeader>
          </div>

          <!-- adding CanPayCrewLandingHeader header  -->
          <div
            class="reward-point-nav"
            v-if="
              show == '1' &&
              !showCommonheader &&
              headerType == 'RewardPointHeader'
            "
          >
            <!-- adding the Header -->
            <RewardPointHeader></RewardPointHeader>
          </div>

          <!-- adding transaction detail header  -->
          <div
            class="transaction-detail-nav"
            v-if="
              show == '1' &&
              !showCommonheader &&
              headerType == 'TransactionDetailHeader'
            "
          >
            <!-- adding the Header -->
            <TransactionDetailHeader></TransactionDetailHeader>
          </div>
          <div class="common-nav" v-else-if="show == '0'">
            <!-- adding the Header -->
            <cp-preheader></cp-preheader>
          </div>
          <div
            class="transactionhistory-nav"
            v-if="
              show == '1' &&
              !showCommonheader &&
              headerType == 'PendingTransactionHistoryHeader'
            "
          >
            <!-- adding the Header -->
            <pending-transaction-history-header></pending-transaction-history-header>
          </div>
          <div
            class="termsandcondition-nav"
            v-if="
              show == '1' &&
              !showCommonheader &&
              headerType == 'LinAnAccountHeader'
            "
          >
            <!-- adding the Header -->
            <LinAnAccountHeader></LinAnAccountHeader>
          </div>
          <div
            class="termsandcondition-nav"
            v-if="
              show == '1' &&
              !showCommonheader &&
              headerType == 'LinkAnMemberBenefit'
            "
          >
            <!-- adding the Header -->
            <LinkAnMemberBenefit></LinkAnMemberBenefit>
          </div>
          <!-- Pay Dashboard Header -->
          <div
            class="canpay-crew-termsandcondition-nav"
            v-if="
              show == '1' &&
              !showCommonheader &&
              headerType == 'CanPayCrew'
            "
          >
            <!-- adding the Header -->
            <CanPayCrewHeader></CanPayCrewHeader>
          </div>
          <div class="pay-dashboard-nav d-flex align-items-center" v-if="
          show == '1' &&
          !showCommonheader &&
          headerType == 'PayDashboardHeader'
          ">
            <!-- adding the Header -->
            <PayDashboardHeader/>
          </div>

          <!-- OFFLINE MESSAGE  -->
          <transition name="fade">
            <div v-if="!$online" class="no-connection-toast">Oops! You are offline</div>
          </transition>
          <router-view  ref="pageComponent" />
        </div>
      </vue-drawer-layout>
    </div>
    <div class="overlay" v-if="snackWithButtons"></div>
    <div class="snackbar col-sm-4-xl-2 mx-auto overlap" v-if="snackWithButtons">
      <h5 class="text-white text-center mb-0"><span class="snackbar-span">CanPay keeps getting better!</span></h5>
      <h5 class="text-white text-center mb-0"><span class="snackbar-span"> Update Required</span></h5>
      <button
        v-on:click="refreshApp"
        class="btn btn-black center-block btn-green snackbar-button"
        style="border-radius: 7px"
      >
        Update Now
      </button>

      <button
        v-on:click="closeUpdateModal"
        class="close-update-modal"
        type="button"
      >
      <svg width="30px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="Menu / Close_SM">
      <path id="Vector" d="M16 16L12 12M12 12L8 8M12 12L16 8M12 12L8 16" stroke="#045420" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </g>
      </svg>
      </button>
    </div>

    <!-- //////////// INVITAION MODAL //////////// -->
    <b-modal
    ref="invitation-modal"
    hide-footer
    v-b-modal.modal-center
    no-close-on-backdrop
    modal-backdrop
    hide-header
    id="invitation-modal"
    centered
    title="BootstrapVue"
    >
      <InvitationComponent :modal="$refs['invitation-modal']">
        <template v-slot:closeModal="">
          <a class="close-modal" @click="hideModal('invitation-modal')" href="javascript:void(0)">
            <svg fill="#000000" width="20px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M4.293,18.293,10.586,12,4.293,5.707A1,1,0,0,1,5.707,4.293L12,10.586l6.293-6.293a1,1,0,1,1,1.414,1.414L13.414,12l6.293,6.293a1,1,0,1,1-1.414,1.414L12,13.414,5.707,19.707a1,1,0,0,1-1.414-1.414Z"/></svg>
          </a>
        </template>
      </InvitationComponent>
    </b-modal>

    <!-- //////////// LOTTERY INVITATION MODAL //////////// -->
    <b-modal
    ref="lottery-notification-modal"
    hide-footer
    v-b-modal.modal-center
    no-close-on-backdrop
    modal-backdrop
    hide-header
    id="lottery-notification-modal"
    centered
    title="BootstrapVue"
    >
      <LotteryNotification :data="lotteryDetails">
        <template v-slot:closeModal="">
          <button
          type="button"
          @click="setLotteryNotification()"
          class="invite-btn accept w-100 h-100 d-flex align-items-center justify-content-center"
          style="height: 100%!important;"
          >
          OK
          </button>
        </template>
      </LotteryNotification>
    </b-modal>
    <no-primary-account-modal ref="NoPrimaryAccountModal"></no-primary-account-modal>

    <!-- //////////// EXCHANGE RATE NOTIFICATION MODAL //////////// -->
    <b-modal
    ref="exchange-rate-notification-modal"
    hide-footer
    v-b-modal.modal-center
    no-close-on-backdrop
    modal-backdrop
    hide-header
    id="exchange-rate-notification-modal"
    centered
    title="BootstrapVue"
    >
      <ExchangeRateNotification>
        <template v-slot:closeModal="">
          <button
          type="button"
          @click="setExchangeRateNotification()"
          class="invite-btn accept w-100 h-100 d-flex align-items-center justify-content-center"
          style="height: 100%!important;"
          >
          OK
          </button>
        </template>
      </ExchangeRateNotification>
    </b-modal>
    <div class="intercom-button-row" v-if="showCustomIntercomButton">
      <button 
          class="canpay-crew-text-font-12 intercom-aligned-button" 
          v-if ="showCustomIntercomButtonType = 'launch_petition'"
          @click="launchPetitionFromCrew"
          >
          Launch a new Petition&nbsp;&nbsp;
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 20 20" fill="none">
          <path d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM15 11H11V15H9V11H5V9H9V5H11V9H15V11Z" fill="white"/>
          </svg>
      </button>
    </div>

    <div class="intercom-button-row" v-if="showScheduleCallButton">
      <button 
          class="canpay-crew-text-font-12 intercom-schedule-call-button" 
          v-if ="showScheduleCallButtonType = 'schedule_call'"
          v-on:click="openCalendly()"
          >
          Click here to schedule a call with CanPay’s CEO, Dustin, and get the details.
          <calendly-widget ref="CalendlyWidget"></calendly-widget>
      </button>
    </div>
  </div>
  
</template>

<style lang="scss">
@import "./assets/styles/main.scss";
</style>

<script>
import CalendlyWidget from "./components/Crew/CalendlyWidget.vue";
import { db } from "./firebaseConfig.js";
import api from "./api/login.js";
import DrawerLayout from "vue-drawer-layout";
import Header from "./components/Layouts/Headers/Header.vue";
import BankLinkingHeader from "./components/Layouts/Headers/BankLinkingHeader.vue";
import RegistrationToStandardHeader from "./components/Layouts/Headers/RegistrationToStandardHeader.vue";
import PreHeader from "./components/Layouts/Headers/PreHeader.vue";
import TermsandconditionHeader from "./components/Layouts/Headers/Terms&ConditionsHeader.vue";
import TransactionHistoryHeader from "./components/Layouts/Headers/TransactionHistoryHeader.vue";
import PrivacyPolicyHeader from "./components/Layouts/Headers/PrivacyPolicyHeader.vue";
import LinAnAccountHeader from "./components/Layouts/Headers/LinAnAccountHeader.vue";
import ParticipantHeader from "./components/Layouts/Headers/ParticipantHeader.vue";
import MerchantDetails from "./components/Layouts/Headers/MerchantdetailsHeader.vue";
import ProfileHeader from "./components/Layouts/Headers/ProfileHeader.vue";
import EditProfileHeader from "./components/Layouts/Headers/EditProfileHeader.vue";
import LocateRetailerHeader from "./components/Layouts/Headers/LocateRetailerHeader.vue";
import HistoricalTransactionHeader from "./components/Layouts/Headers/HistoricalTransactionHeader.vue";
import PendingTransactionHistoryHeader from "./components/Layouts/Headers/PendingTransactionHistoryHeader.vue";
import NoPrimaryAccountModal from './components/Payment/NoPrimaryAccountModal.vue';
import RewardWheelHeader from "./components/Layouts/Headers/RewardWheelHeader.vue";
import RewardPointHeader from "./components/Layouts/Headers/RewardPointHeader.vue";
import TransactionDetailHeader from "./components/Layouts/Headers/TransactionDetailHeader.vue";
import InvitationComponent from './components/InvitationComponent.vue';
import PayDashboardHeader from './components/Layouts/Headers/PayDashboardHeader.vue';
import ExchangeRateNotification from './components/ExchangeRateNotification.vue';
import LotteryNotification from './components/LotteryNotification.vue';
import SponsorHistoryHeader from "./components/Layouts/Headers/SponsorHistoryHeader.vue";
import LinkAnMemberBenefit from "./components/Layouts/Headers/LinkAnMemberBenefit.vue";
import CanPayCrewHeader from "./components/Layouts/Headers/CanPayCrewHeader.vue";
import CanPayCrewPetitionHeader from "./components/Layouts/Headers/CanPayCrewPetitionHeader.vue";
import CanPayCrewLandingHeader from "./components/Layouts/Headers/CanPayCrewLandingHeader.vue";
export default {
  name: "App",
  data() {
    return {
      show: 0,
      enrollment: localStorage.getItem("enrollment"),
      consumer_details: JSON.parse(
        localStorage.getItem("consumer_login_response")
      ),
      show_header: false,
      refreshing: false,
      registration: null,
      snackWithButtons: false,
      isWhitebackground: false,
      isGreybackground: false,
      showCommonheader: true,
      headerType: "",
      showparticipatingmenu: false,
      show_retailer: false,
      header_retailer: false,
      rwState: '',
      rwInvited: 0,
      lotteryDetails: {},
      wheelHeaderStyle: "",
      rewardWheelPageStyle: "",
      showCustomIntercomButton: false,
      showCustomIntercomButtonType: '',
      showScheduleCallButton: false,
      showScheduleCallButtonType: '',
      consumer_type: localStorage.getItem("consumer_login_response")
        ? JSON.parse(localStorage.getItem("consumer_login_response")).consumer_type
        : null,
    };
  },
  components: {
    "cp-header": Header,
    "cp-preheader": PreHeader,
    DrawerLayout,
    TermsandconditionHeader,
    TransactionHistoryHeader,
    PrivacyPolicyHeader,
    ParticipantHeader,
    MerchantDetails,
    ProfileHeader,
    EditProfileHeader,
    LocateRetailerHeader,
    HistoricalTransactionHeader,
    PendingTransactionHistoryHeader,
    NoPrimaryAccountModal,
    RewardWheelHeader,
    RewardPointHeader,
    TransactionDetailHeader,
    InvitationComponent,
    PayDashboardHeader,
    ExchangeRateNotification,
    LotteryNotification,
    BankLinkingHeader,
    RegistrationToStandardHeader,
    LinAnAccountHeader,
    SponsorHistoryHeader,
    LinkAnMemberBenefit,
    CanPayCrewHeader,
    CanPayCrewPetitionHeader,
    CanPayCrewLandingHeader,
    CalendlyWidget
  },
  mounted: function () {
    let self = this;
    document.addEventListener('rwStateCahnged', (event) => {
        self.rwState = event.detail.rw_state
        self.rwInvited = event.detail.rw_invited

        if(event.detail.rw_invited == 1){
          self.$refs['invitation-modal'].show();
        }
    });

    document.addEventListener('exchangeRateNotification', (event) => {
        if(event.detail.modal == true){
          self.$refs['exchange-rate-notification-modal'].show();
        }else{
          self.$refs['exchange-rate-notification-modal'].hide();
        }
    });

    document.addEventListener('lotteryNotification', (event) => {
        if(event.detail.modal == true){
          self.lotteryDetails = event.detail.details
          self.$refs['lottery-notification-modal'].show();
        }else{
          self.lotteryDetails = {}
          self.$refs['lottery-notification-modal'].hide();
        }
    });

    if(self.rwInvited == 1){
      self.$refs['invitation-modal'].show();
    }

    self.$root.$on("rewardWheelHeaderColor", function (data) {
      if(data.color){
        if(data.transparent){
          self.wheelHeaderStyle = 'background-color:'+ data.color + '96!important;'
        }else{
          self.wheelHeaderStyle = 'background-color:'+ data.color + '!important;'
        }
      }else{
        var el = document.getElementsByClassName("reward-wheel-nav")[0];
        el.style.backgroundColor = "";
        self.wheelHeaderStyle = 'background-color: none;'
      }
    });


    self.$root.$on("rewardWheelPageColor", function (data) {
      if((data.color_1 && data.color_2 && data.color_3) || (data.image && data.is_image)){

        if(data.image && data.is_image){
          self.rewardWheelPageStyle = 'background-image: url(' + data.image + '); background-size: cover; background-position: center;'
        }else{
          self.rewardWheelPageStyle = 'background-color: ' + data.color_1 + '; background-image: linear-gradient( to bottom, ' + data.color_1 + ',' + data.color_2 + ',' + data.color_3 + ');'
        }

      }else{
        self.rewardWheelPageStyle = ''
      }

    });

    self.$root.$on("customIntercomButton", function (data) {
      self.showCustomIntercomButton = data.show;
      self.showCustomIntercomButtonType = data.type;
    });

    self.$root.$on("scheduleCallButton", function (data) {
      self.showScheduleCallButton = data.show;
      self.showScheduleCallButtonType = data.type;
    });

    const consumer_login_response = JSON.parse(localStorage.getItem("consumer_login_response"));
    self.rwState = consumer_login_response && consumer_login_response.rw_state ? consumer_login_response.rw_state : ''
    self.rwInvited = consumer_login_response && consumer_login_response.rw_invited ? consumer_login_response.rw_invited : ''
    this.$root.$on("loginapp", function (data) {
      if (localStorage.getItem("consumer_token") !== null) {
        self.show = 1;
      } else {
        self.show = 0;
      }
    });
    //catching the phone otp pin data when emitted

    self.$root.$on("show_header", function (data) {
      if (data == 2) {
        self.show_header = true;
        self.header_retailer = true;
      } else {
        self.show_header = data;
        self.header_retailer = false;
      }
    });
    self.$root.$on("Menu Drawer", function (data) {
      self.handleToggleDrawer();
    });
    this.$root.$on("changeWhiteBackground", function (data) {
      console.log(data);
      self.isWhitebackground = data[0];
      self.showCommonheader = data[1];
      self.headerType = data[2];
      self.isGreybackground = data?.[3] ?? false;
      console.log(self.show);
      console.log(self.headerType);
      console.log(self.show_header);
      if (['LinAnAccountHeader', 'signerspage'].includes(self.headerType)) {
        self.show = 1;
      }
    });
    // Intercom integration
    self.$intercom.boot({
      user_id:
        self.consumer_details != null ? self.consumer_details.user_id : null,
      name:
        self.consumer_details != null
          ? self.consumer_details.first_name +
            " " +
            self.consumer_details.middle_name +
            " " +
            self.consumer_details.last_name
          : null,
      email: self.consumer_details != null ? self.consumer_details.email : null,
      hide_default_launcher: false,
    });
       var page = window.location.href.split("/").pop();

  },
  created() {
    // Listen for swUpdated event and display refresh snackbar as required.
    document.addEventListener("swUpdated", this.showRefreshUI, { once: true });
    // Refresh all open app tabs when a new service worker is installed.
    navigator.serviceWorker.addEventListener("controllerchange", () => {
      if (this.refreshing) return;
      this.refreshing = true;
      window.location.reload();
    });
    if (localStorage.getItem("consumer_token") !== null) {
      this.show = 1;
    } else {
      this.show = 0;
    }
    this.consumer_details = JSON.parse(localStorage.getItem("consumer_login_response"));
    if(this.consumer_details)
        this.getdata();
  },

  methods: {
    openCalendly(){
      this.$refs.CalendlyWidget.openCanpayCalendlyModal();
    },
    launchPetitionFromCrew() {
      const pageComponent = this.$refs.pageComponent;
      if (pageComponent && typeof pageComponent.findStoreToCreatePetition === 'function') {
        pageComponent.findStoreToCreatePetition();
      } else {
        console.warn('launchPetition method not found on current page');
      }
    },
    showRefreshUI(e) {
      // Display a snackbar inviting the user to refresh/reload the app due
      // to an app update being available.
      // The new service worker is installed, but not yet active.
      // Store the ServiceWorkerRegistration instance for later use.
      this.registration = e.detail;
      this.snackWithButtons = true;
    },
    hideModal(modal){
      this.$refs[modal].hide();
    },
    refreshApp() {
      this.snackWithButtons = false;
      // Protect against missing registration.waiting.
      if (!this.registration || !this.registration.waiting) {
        return;
      }
      this.registration.waiting.postMessage("skipWaiting");
    },

    handleToggleDrawer() {
      this.$refs.drawerLayout.toggle();
    },
    handleSlideStart() {},
    handleSlideMove(position) {},
    handleSlideEnd(visible) {
      if (visible == false) {
        this.$root.$emit("Menu Drawer Close", [""]);
      } else {
        this.$root.$emit("Menu Drawer Open", [""]);
      }
    },
    handleMaskClick() {
      this.$refs.drawerLayout.toggle(false);
    },
    clicklogout() {
      var self = this;
      api
        .logout()
        .then((response) => {
          if (response.code == 200) {
            self.handleToggleDrawer();
            self.headerType = "common";
            self.showCommonheader = true;
            self.show = 0;
            self.isWhitebackground = false;
            self.isGreybackground = false;
            localStorage.removeItem("consumer_token");
            localStorage.removeItem("nopurchasepower");
            localStorage.removeItem("purchasepower");
            localStorage.removeItem("consumer_login_response");
            localStorage.removeItem('show_pp_animation');
            localStorage.removeItem("enrollment");
  
            delete axios.defaults.headers.common["Authorization"];
            self.$router.push("/login").catch((err) => {});
          }
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    clickHome() {
      this.handleToggleDrawer();
      this.headerType = "common";
      this.showCommonheader = true;
      this.show = 1;
      this.isWhitebackground = false;
      this.isGreybackground = false;
      this.$root.$emit("showSpinInfo", {show_spin_info: true});
      this.$router.push("/pay").catch((err) => {});
    },
    clickTransactionhistory() {
      this.handleToggleDrawer();
      this.headerType = "TransactionHistoryHeader";
      this.showCommonheader = false;
      this.show = 1;
      this.isWhitebackground = false;
      this.isGreybackground = false;
      this.$root.$emit("TransactionHistory", "");
      this.$router.push("/transactiondetails").catch((err) => {});
    },
    clickAccount() {
      this.handleToggleDrawer();
      if (this.$route.name != "profile") {
        this.headerType = "ProfileHeader";
        this.show = 1;
        this.isWhitebackground = false;
        this.isGreybackground = false;
        this.showCommonheader = true;
        this.$router.push("/profile").catch((err) => {});
      }
    },
    clickTermsandCondition() {
      this.handleToggleDrawer();
      this.headerType = "TermsandConditionHeader";
      this.showCommonheader = false;
      this.isGreybackground = false;
      this.show = 1;
      this.isWhitebackground = true;
      this.$router.push("/termsandconditions").catch((err) => {});
    },
    clickRewardWheelTermsandCondition() {
      this.handleToggleDrawer();
      this.headerType = "TermsandConditionHeader";
      this.showCommonheader = false;
      this.isGreybackground = false;
      this.show = 1;
      this.isWhitebackground = true;
      this.$router.push("/rewardwheeltermsandconditions").catch((err) => {});
    },
    clickPrivacyPolicy() {
      this.handleToggleDrawer();
      this.headerType = "PrivacyHeader";
      this.showCommonheader = false;
      this.isGreybackground = false;
      this.show = 1;
      this.isWhitebackground = true;
      this.$router.push("/privacypolicy").catch((err) => {});
    },
    clickParticipantMerchant() {
      this.handleToggleDrawer();
      this.headerType = "common";
      this.isGreybackground = false;
      this.showCommonheader = true;
      this.show = 1;
      this.isWhitebackground = true;
      this.$router.push("/participatingmerchant").catch((err) => {});
    },

    clickSetting() {
      this.handleToggleDrawer();
      this.headerType = "common";
      this.showCommonheader = true;
      this.show = 1;
      this.isWhitebackground = false;
      this.isGreybackground = false;
      this.$router.push("/setting").catch((err) => {});
    },
    clickLinkedAccounts(){
      var self = this;
      self.handleToggleDrawer();
      self.headerType = "LinAnAccountHeader";
      self.showCommonheader = false;
      self.show = 1;
      self.isWhitebackground = false;
      self.isGreybackground = false;
      self.$router.push("/linkabenefit");
    },
    clickMemeberBenefitAccounts(){
      var self = this;
      self.handleToggleDrawer()
      self.headerType = "LinkAnMemberBenefit";
      self.showCommonheader = false;
      self.show = 1;
      self.isWhitebackground = false;
      self.isGreybackground = false;
      self.$router.push("/memberbenefit");
    },
      clickCanPayCrew(){
      var self = this;
      self.handleToggleDrawer()
      self.headerType = "CanPayCrew";
      self.showCommonheader = false;
      self.show = 1;
      self.isWhitebackground = false;
      self.isGreybackground = false;
      self.$router.push("/canpaycrew");
    },
    setdata(type) {
      var data = {
          all_bank_delinked: null,
        };
      var self = this;
      var washingtonRef = db
        .collection("users")
        .doc(String(this.consumer_details.user_id));
      // Set the "capital" field of the city 'DC'
      return washingtonRef
        .update(data)
        .then(function () {
          console.log("Document successfully updated!");
        })
        .catch(function (error) {
          // The document probably doesn't exist.
          console.error("Error updating document: ", error);
        });
    },
    setExchangeRateNotification(type) {
      var consumer_login_response = JSON.parse(localStorage.getItem("consumer_login_response"));
      var data = {
        exchange_rate_notification: 0
      };
      var firestore = db
      .collection("users")
      .doc(String(consumer_login_response.user_id));
      // Set the "capital" field of the city 'DC'
      return firestore
      .update(data)
      .then(function () {
      })
      .catch(function (error) {
        // The document probably doesn't exist.
        console.error("Error updating exchange rate notification: ", error);
      });
    },
    setLotteryNotification() {
      var consumer_login_response = JSON.parse(localStorage.getItem("consumer_login_response"));
      var data = {
        lottery_winning_details: null
      };
      var firestore = db
      .collection("users")
      .doc(String(consumer_login_response.user_id));
      // Set the "capital" field of the city 'DC'
      return firestore
      .update(data)
      .then(function () {
      })
      .catch(function (error) {
        // The document probably doesn't exist.
        console.error("Error updating exchange rate notification: ", error);
      });
    },
    clickRewarPoints() {

      if(this.rwInvited == 1){
        this.$refs['invitation-modal'].show();
      }else{
        this.handleToggleDrawer();
        this.headerType = "common";
        this.showCommonheader = true;
        this.show = 1;
        this.isWhitebackground = false;
        this.isGreybackground = false;
        this.$router.push("/rewardpoints").catch((err) => {});
      }
    },
    getdata() {
      let self = this;
      let ref = db
        .collection("users")
        .doc(String(String(this.consumer_details.user_id)));
      ref.get().then((snapshot) => {
        if (snapshot.exists) {

          this.users = snapshot.data();
          const containsKey = (obj, key) => Object.keys(obj).includes(key);
          const hasNameStatus = containsKey(this.users, "status");
          const hasAllBankDelinked = containsKey(this.users, "all_bank_delinked");
          const hasRewardWheelInvitation = containsKey(this.users, "reward_wheel_invitation");
          const hasExchangeRateNotification = containsKey(this.users, "exchange_rate_notification");
          const hasRewardWheelLottery = containsKey(this.users, "lottery_winning_details");

          if(hasAllBankDelinked == true && this.users.all_bank_delinked != null && this.consumer_details.user_id!='' && this.$route.name!='login'){
            const hasBanklLinkType = containsKey(this.users, "bank_link_type");
            if(hasBanklLinkType){
              this.consumer_details.account_id = this.users.active_account_id;
              this.consumer_details.account_no = this.users.active_account_no;
              this.consumer_details.bank_link_type = this.users.bank_link_type;
              localStorage.setItem(
                "consumer_login_response",
                JSON.stringify(this.consumer_details)
              );
            }
            this.$bvModal.hide("account-no-modal");
            if (this.users.all_bank_delinked == 1 && localStorage.getItem("bank_link_success") == null) {
              this.$bvModal.hide("delink-modal");
              this.$refs.NoPrimaryAccountModal.showModal();
            } else if (this.users.all_bank_delinked){
              this.$refs.NoPrimaryAccountModal.hideModal();
              localStorage.removeItem("bank_link_success");
              this.setdata();
            }
          }

          if(hasNameStatus == true) {
            var request = {
              status_id: this.users.status,
            };
            api
              .getConsumerStatus(request)
              .then((response) => {
                if (response.code == 200) {
                  if(response.data.id!=self.consumer_details.status){
                    this.clicklogout();
                  }
                }
              })
              .catch(function (error) {
              });
          }

          ////////////////////////////////////////////////////////////////////
          /////////// CODE NEEDED LATER, PLEASE DO NOT REMOVE CODE///////////
          //////////////////////////////////////////////////////////////////
          // if(hasRewardWheelInvitation == true){

          //   var consumer_login_response = JSON.parse(localStorage.getItem("consumer_login_response"));

          //   consumer_login_response.rw_invited = this.users.reward_wheel_invitation

          //   localStorage.setItem(
          //     "consumer_login_response",
          //     JSON.stringify(consumer_login_response)
          //   );

          //   const event = new CustomEvent('rwStateCahnged', {detail:consumer_login_response});
          //   document.dispatchEvent(event);

          //   if(this.users.reward_wheel_invitation == 1){
          //     this.$refs['invitation-modal'].show();
          //   }
          // }

          if(hasExchangeRateNotification == true){
            if(this.users.exchange_rate_notification == 1){
              this.$refs['exchange-rate-notification-modal'].show();
            }else{
              this.$refs['exchange-rate-notification-modal'].hide();
            }
          }

          if(hasRewardWheelLottery == true){
            if(this.users.lottery_winning_details){
              this.lotteryDetails = this.users.lottery_winning_details
              this.$refs['lottery-notification-modal'].show();
            }else{
              this.lotteryDetails = {}
              this.$refs['lottery-notification-modal'].hide();
            }
          }
        }

        ref.onSnapshot((convo) => {
          let source = convo.metadata.hasPendingWrites ? "Local" : "Server";
          // TODO: add messages to store
          let ref = db
            .collection("users")
            .doc(String(this.consumer_details.user_id));
          ref.get().then((snapshot) => {

            if (snapshot.exists) {
              this.users = snapshot.data();
              const containsKey = (obj, key) => Object.keys(obj).includes(key);
              const hasNameStatus = containsKey(this.users, "status");
              const hasAllBankDelinked = containsKey(this.users, "all_bank_delinked");
              const hasRewardWheelInvitation = containsKey(this.users, "reward_wheel_invitation");
              const hasExchangeRateNotification = containsKey(this.users, "exchange_rate_notification");
              const hasRewardWheelLottery = containsKey(this.users, "lottery_winning_details");

              if(hasAllBankDelinked == true && this.users.all_bank_delinked != null  && this.consumer_details.user_id!='' && this.$route.name!='login'){
                const hasBanklLinkType = containsKey(this.users, "bank_link_type");
                if(hasBanklLinkType){
                  this.consumer_details.account_id = this.users.active_account_id;
                  this.consumer_details.account_no = this.users.active_account_no;
                  this.consumer_details.bank_link_type = this.users.bank_link_type;
                  localStorage.setItem(
                    "consumer_login_response",
                    JSON.stringify(this.consumer_details)
                  );
                }
                this.$bvModal.hide("account-no-modal");
                if (this.users.all_bank_delinked == 1 && localStorage.getItem("bank_link_success") == null) {
                  this.$bvModal.hide("delink-modal");
                  this.$refs.NoPrimaryAccountModal.showModal();
                } else if (this.users.all_bank_delinked){
                  this.$refs.NoPrimaryAccountModal.hideModal();
                  localStorage.removeItem("bank_link_success");
                  this.setdata();
                }
              }

              if(hasNameStatus == true) {
                var request = {
                  status_id: this.users.status,
                };
                api
                  .getConsumerStatus(request)
                  .then((response) => {
                    if (response.code == 200) {
                      if(response.data.id!=self.consumer_details.status){
                        this.clicklogout();
                      }
                    }
                  })
                  .catch(function (error) {
                  });
              }


              ////////////////////////////////////////////////////////////////////
              /////////// CODE NEEDED LATER, PLEASE DO NOT REMOVE CODE///////////
              //////////////////////////////////////////////////////////////////
              // if(hasRewardWheelInvitation == true){

              //   var consumer_login_response = JSON.parse(localStorage.getItem("consumer_login_response"));

              //   consumer_login_response.rw_invited = this.users.reward_wheel_invitation

              //   localStorage.setItem(
              //     "consumer_login_response",
              //     JSON.stringify(consumer_login_response)
              //   );

              //   const event = new CustomEvent('rwStateCahnged', {detail:consumer_login_response});
              //   document.dispatchEvent(event);

              //   if(this.users.reward_wheel_invitation == 1){
              //     this.$refs['invitation-modal'].show();
              //   }
              // }

              if(hasExchangeRateNotification == true){
                if(this.users.exchange_rate_notification == 1){
                  this.$refs['exchange-rate-notification-modal'].show();
                }else{
                  this.$refs['exchange-rate-notification-modal'].hide();
                }
              }

              if(hasRewardWheelLottery == true){
                if(this.users.lottery_winning_details){
                  this.lotteryDetails = this.users.lottery_winning_details
                  this.$refs['lottery-notification-modal'].show();
                }else{
                  this.lotteryDetails = {}
                  this.$refs['lottery-notification-modal'].hide();
                }
              }
            }
          });
        });
      });
    },
    closeUpdateModal(){
      console.log("closeUpdateModal")
      this.snackWithButtons = false;
    }
  },
  watch: {
    '$online': {
      handler(val) {
        let self = this;
        if(self.$online){
          setTimeout(function(){
            if(self.$route.name != 'RewardWheel'){
              window.location.reload()
            }
          }, 1000)
        }
      },
      deep: true,
    },
    '$route.path'(newPath) {
      this.showCustomIntercomButton = false;
      this.showScheduleCallButton = false;
    }
  }

};
</script>
<style>
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
  background-color: transparent !important; /* Adjust the opacity as needed */
  z-index: 9999; /* Ensure the overlay is on top of other elements */
}
.intercom-lightweight-app{
  z-index: 9999;
}
.close-update-modal{
  position: absolute;
  top: 0;
  left: 0;
  border: 0;
  border-radius: 100%;
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #045420;
}
#problematic-account-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#problematic-account-modal___BV_modal_content_{
    background-color: #ffffff;

}
.drawer-mask {
  background-color: transparent !important;
}
#intermediate-bank-linking___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#non-actionable-status-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#intermediate-bank-linking___BV_modal_content_{
    background-color: #ffffff;
}
#member-benfit-modal___BV_modal_header_{
  background-color: #ffffff;
  padding:5px;
  border: 0;
}
#member-benfit-modal___BV_modal_content_{
  background-color: transparent!important;
  margin: 0px;
  padding:0px;
  border: 0;
}
#member-benfit-modal___BV_modal_body_ {
  background-color: #ffffff;
  margin: 0px;
  height:82vh;
  overflow-y: hidden;
  padding:0px;
  border: 0;
}

#non-actionable-status-modal___BV_modal_content_{
    background-color: #ffffff;
    margin:10px;

}
#non-actionable-status-resolve-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}

#non-actionable-status-resolve-modal___BV_modal_content_{
    background-color: #ffffff;
    margin:10px;
}
#member-benfit-modal___BV_modal_content_{
    height:90vh;
}
@media only screen and (min-width: 374px) and (max-width:376px){
  #member-benfit-modal___BV_modal_content_{
      height:87vh;
  }
}
@media only screen and (min-width: 359px) and (max-width:361px){
  #member-benfit-modal___BV_modal_content_{
      height:88vh;
}
}
@media only screen and (min-width: 359px) and (max-width:361px){
  #member-benfit-modal___BV_modal_content_{
      height:88vh;
  }
}
@media only screen and (min-width: 539px) and (max-width:541px){
  #member-benfit-modal___BV_modal_content_{
      height:88vh;
  }
}
</style>
