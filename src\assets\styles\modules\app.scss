.style {
    background-color: $cp-primary;
    height: 100%;
    text-align: center;
}

// @media screen and (orientation: landscape) {
//   #app {
//     transform: rotate(-90deg);
//     transform-origin: left top;
//     width: 100vh;
//     height: 100vw;
//     overflow-x: hidden;
//     overflow-y: scroll;
//     position: fixed;
//     top: 100%;
//     left: 0;
//   }
// }
.wrapper {
    background-color: $cp-primary;
}

.wrapper input {
    background-color: white;
}

.green-background {
    background-color: $cp-primary;
}

.drawer-content {
    background-color: $cp-primary;
}

.drawer-black-content {
    background-color: black !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
}

.drawer-body {
    background-color: $cp-primary;
    scroll-behavior: smooth !important;
    // overflow: visible !important;
}

.drawer-white-body {
    background-color: white;
    // height: 150px !important;
    // margin-top: 30px !important;
    scroll-behavior: smooth;
}

.white-body {
    height: 100%;
    overflow-x: hidden;
    background-color: white;
}

.grey-body {
    height: 100%;
    background-color: #ECECEC;
    scroll-behavior: smooth !important;
}

.menu-title {
    cursor: pointer;
    font-weight: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #ffffff;
    font-size: 17px;
    font-family: $cp-font;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #ffffff;
}

.menu-icon {
    color: $cp-primary;
    font-size: 25px;
}

.card-shadow {
    box-shadow: inset -22px 0 0px -6px #5a5c5b;
}

@media screen and (max-width: 2000px) {
    html {
        background-color: $cp-primary;
    }
}

.drawer-shadow {
    -moz-box-shadow: inset 0 0 10px #000000;
    -webkit-box-shadow: inset 0 0 10px #000000;
    box-shadow: inset 0 0 10px #000000;
}

#app {
    font-family: $cp-font;
    color: $cp-black !important;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-align: center;
    background-color: $cp-primary;
}

#header {
    text-align: right;
    color: $cp-primary;
}

#font-color {
    color: $cp-primary;
}

.termsandcondition-nav {
    height: 80px;
    background-color: white;
    box-shadow: -2px 2px 12px 0px #dad5d5;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    padding-left: 10px;
    padding-right: 10px;
}


.merchant-nav {
    height: 80px;
    background-color: white;
    box-shadow: 0px -3px 12px 0px #dad5d5;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
}

.participant-nav {
    height: 80px;
    background-color: white;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    position: relative;
    z-index: 99999999;
}

.profile-nav {
    height: 100px;
    background-color: white;
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
}

.edit-profile-nav {
    height: 100px;
    background-color: $cp-primary;
}

.transactionhistory-nav {
    height: 150px;
    background-color: white;
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
}

#nav a {
    font-weight: bold;
    color: #2c3e50;
}

#nav a.router-link-exact-active {
    color: $cp-primary;
}

@media screen and (max-width: 2000px) {
    html {
        background-color: $cp-primary;
        scroll-behavior: smooth;
    }
}

#container {
    display: block;
    margin-top: 5px;
}

.snackbar {
    width: 367px;
    position: fixed;
    background: #045420;
    padding: 200px 15px;
    border-radius: 5px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.snackbar-span {
    display: inline-flex;
    margin-bottom: 35px;
    font-size: 25px;
}

.snackbar-button {
    height: 80px;
    margin-top: 0px;
    width: 70%;
    font-size: 25px !important;
    font-weight: 700 !important;
    color: #fff !important;
}

.content-wrap {
    overflow: visible !important;
}



@media only screen and ( min-width:270px) and ( max-width:700px) {
    .common-nav {
        height: 80px;
        background-color: white;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .pay-dashboard-nav{
        height: 100%;
        background-color: white;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
        padding-top: 15px;
        padding-bottom: 15px;
    }
    .header-logo-div {
        margin-top: -47px;
        margin-left: 28px;
    }
    .header-logo {
        height: 50px;
    }
    .pay-dashboard-header-logo{
        height: 50px;
    }
    .header-logo-splash {
        height: 100px;
    }
    .pre-header-logo-div {
        margin-top: 24px;
    }
    .header-logo-div i{
        font-size: 20px;
    }
    .drawer-wrap {
        top: auto !important;
        bottom: 0px;
    }
    .transaction-history-lable {
        font-family: $cp-font;
        font-size: 15px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #000000;
        margin-top: 25px;
    }
    .transaction-history-logo {
        margin-top: 20px;
    }
    .search-bar-row {
        margin-top: 15px;
    }
    .search-bar-input-group {
        border-radius: 6px;
        background-color: #ececec;
    }
    .search-bar-group-append {
        width: 35px;
    }
    .search-bar-group-text {
        background-color: transparent !important;
        border-color: transparent !important;
    }
    .search-icon {
        margin-left: -5px;
        height: 23px;
        margin-top: 5px;
    }
    .search-input {
        border-color: #ececec !important;
        background-color: #ececec !important;
        margin-top: 1px;
        padding-left: 5px !important;
    }
    .reward-wheel-nav {
        height: 90px;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .crew-nav {
        height: 90px;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .reward-point-nav {
        height: 180px;
        background-color: #ffffff;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .transaction-detail-nav {
        height: 100px;
        background-color: #ffffff;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
}

@media only screen and ( min-width:350px) and ( max-width:800px) {
    .common-nav {
        height: 100px;
        background-color: white;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .pay-dashboard-nav{
        height: 100%;
        background-color: white;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
        padding-top: 15px;
        padding-bottom: 15px;
    }
    .header-logo-div {
        margin-top: -47px;
        margin-left: 28px;
    }
    .header-logo {
        height: 60px;
    }
    .pay-dashboard-header-logo{
        height: 70px;
    }
    .header-logo-splash {
        height: 100px;
    }
    .pre-header-logo-div {
        margin-top: 24px;
    }
    .header-logo-div i{
        font-size: 27px;
    }
    .drawer-wrap {
        top: auto !important;
        bottom: 0px;
    }
    .transaction-history-lable {
        font-family: $cp-font;
        font-size: 15px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #000000;
        margin-top: 25px;
    }
    .transaction-history-logo {
        margin-top: 20px;
    }
    .search-bar-row {
        margin-top: 15px;
    }
    .search-bar-input-group {
        border-radius: 6px;
        background-color: #ececec;
    }
    .search-bar-group-append {
        width: 35px;
    }
    .search-bar-group-text {
        background-color: transparent !important;
        border-color: transparent !important;
    }
    .search-icon {
        margin-left: -5px;
        height: 23px;
        margin-top: 5px;
    }
    .search-input {
        border-color: #ececec !important;
        background-color: #ececec !important;
        margin-top: 1px;
        padding-left: 5px !important;
    }
    .reward-wheel-nav {
        height: 90px;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .crew-nav {
        height: 98px;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .reward-point-nav {
        height: 180px;
        background-color: #ffffff;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .transaction-detail-nav {
        height: 100px;
        background-color: #ffffff;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
}

@media only screen and ( min-width:360px) and ( max-width:900px) {
    .pre-header-logo-div {
        margin-top: 24px;
    }
    .drawer-wrap {
        bottom: auto !important;
    }
    .transaction-history-lable {
        font-family: $cp-font;
        font-size: 15px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #000000;
        margin-top: 31px;
    }
    .transaction-history-logo {
        margin-top: 17px;
    }
    .search-input {
        border-color: #ececec !important;
        background-color: #ececec !important;
        margin-top: 1px !important;
        padding-left: 2px !important;
    }
}

@media only screen and ( min-width:800px) and ( max-width:1400px) {
    .pre-header-logo-div {
        margin-top: 24px;
    }
    .common-nav {
        height: 110px;
        background-color: white;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .pay-dashboard-nav{
        height: 100%;
        background-color: white;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
        padding-top: 15px;
        padding-bottom: 15px;
    }
    .header-logo-div {
        margin-top: -28px;
        margin-left: 68px;
    }
    .header-logo {
        height: 90px;
    }
    .pay-dashboard-header-logo{
        height: 90px;
    }
    .drawer-wrap {
        bottom: auto !important;
    }
    .transaction-history-lable {
        font-family: $cp-font;
        font-size: 15px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #000000;
        margin-top: 30px;
    }
    .transaction-history-logo {
        margin-top: 16px;
        height: 59px;
    }
    .search-bar-row {
        margin-top: 15px;
    }
    .search-bar-input-group {
        border-radius: 6px;
        background-color: #ececec;
    }
    .search-bar-group-append {
        width: 35px;
    }
    .search-bar-group-text {
        background-color: transparent !important;
        border-color: transparent !important;
    }
    .search-icon {
        margin-left: -5px;
        height: 23px;
        margin-top: 5px;
    }
    .search-input {
        border-color: #ececec !important;
        background-color: #ececec !important;
        margin-top: 1px;
        padding-left: 5px;
    }
    .reward-wheel-nav {
        height: 115px;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .crew-nav {
        height: 115px;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .reward-point-nav {
        height: 180px;
        background-color: #ffff;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .transaction-detail-nav {
        height: 100px;
        background-color: #ffffff;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
}

@media (min-width: 992px) {
    .pre-header-logo-div {
        margin-top: 24px;
    }
    .container,
    .container-lg,
    .container-md,
    .container-sm {
        max-width: 1019px !important;
    }
    .common-nav {
        height: 110px;
        background-color: white;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .pay-dashboard-nav{
        height: 100%;
        background-color: white;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
        padding-top: 15px;
        padding-bottom: 15px;
    }
    .header-logo-div {
        margin-top: -28px;
        margin-left: 68px;
    }
    .header-logo {
        height: 90px;
    }
    .pay-dashboard-header-logo{
        height: 90px;
    }
    .drawer-wrap {
        bottom: auto !important;
    }
    .transaction-history-lable {
        font-family: $cp-font;
        font-size: 15px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #000000;
        margin-top: 30px;
    }
    .transaction-history-logo {
        margin-top: 16px;
        height: 59px;
    }
    .search-bar-row {
        margin-top: 15px;
    }
    .search-bar-input-group {
        border-radius: 6px;
        background-color: #ececec;
    }
    .search-bar-group-append {
        width: 35px;
    }
    .search-bar-group-text {
        background-color: transparent !important;
        border-color: transparent !important;
    }
    .search-icon {
        margin-left: -5px;
        height: 23px;
        margin-top: 5px;
    }
    .search-input {
        border-color: #ececec !important;
        background-color: #ececec !important;
        margin-top: 1px;
        padding-left: 5px;
    }
    .reward-wheel-nav {
        height: 115px;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .crew-nav {
        height: 101px;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .reward-point-nav {
        height: 180px;
        background-color: #ffff;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .transaction-detail-nav {
        height: 100px;
        background-color: #ffffff;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
}

@media only screen and ( min-width:700px) and ( max-width:1200px) {
    .pre-header-logo-div {
        margin-top: 24px;
    }
    .common-nav {
        height: 110px;
        background-color: white;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .pay-dashboard-nav{
        height: 100%;
        background-color: white;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
        padding-top: 15px;
        padding-bottom: 15px;
    }
    .header-logo-div {
        margin-top: -28px;
        margin-left: 68px;
    }
    .header-logo {
        height: 90px;
    }
    .pay-dashboard-header-logo{
        height: 90px;
    }
    .drawer-wrap {
        bottom: auto !important;
    }
    .transaction-history-lable {
        font-family: $cp-font;
        font-size: 15px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #000000;
        margin-top: 30px;
    }
    .transaction-history-logo {
        margin-top: 16px;
        height: 59px;
    }
    .search-bar-row {
        margin-top: 15px;
    }
    .search-bar-input-group {
        border-radius: 6px;
        background-color: #ececec;
    }
    .search-bar-group-append {
        width: 35px;
    }
    .search-bar-group-text {
        background-color: transparent !important;
        border-color: transparent !important;
    }
    .search-icon {
        margin-left: -5px;
        height: 23px;
        margin-top: 5px;
    }
    .search-input {
        border-color: #ececec !important;
        background-color: #ececec !important;
        margin-top: 1px;
        padding-left: 5px;
    }
    .reward-wheel-nav {
        height: 115px;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .crew-nav {
        height: 115px;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .reward-point-nav {
        height: 180px;
        background-color: #ffff;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .transaction-detail-nav {
        height: 100px;
        background-color: #ffffff;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
}

@media (min-width: 992px) {
    .pre-header-logo-div {
        margin-top: 24px;
    }
    .container,
    .container-lg,
    .container-md,
    .container-sm {
        max-width: 1019px !important;
    }
    .common-nav {
        height: 110px;
        background-color: white;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .pay-dashboard-nav{
        height: 100%;
        background-color: white;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
        padding-top: 15px;
        padding-bottom: 15px;
    }
    .header-logo-div {
        margin-top: -28px;
        margin-left: 68px;
    }
    .header-logo {
        height: 90px;
    }
    .pay-dashboard-header-logo{
        height: 90px;
    }
    .drawer-wrap {
        bottom: auto !important;
    }
    .transaction-history-lable {
        font-family: $cp-font;
        font-size: 15px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #000000;
        margin-top: 30px;
    }
    .transaction-history-logo {
        margin-top: 16px;
        height: 59px;
    }
    .search-bar-row {
        margin-top: 15px;
    }
    .search-bar-input-group {
        border-radius: 6px;
        background-color: #ececec;
    }
    .search-bar-group-append {
        width: 35px;
    }
    .search-bar-group-text {
        background-color: transparent !important;
        border-color: transparent !important;
    }
    .search-icon {
        margin-left: -5px;
        height: 23px;
        margin-top: 5px;
    }
    .search-input {
        border-color: #ececec !important;
        background-color: #ececec !important;
        margin-top: 1px;
        padding-left: 5px;
    }
    .reward-wheel-nav {
        height: 80px;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .crew-nav {
        height: 101px;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .reward-point-nav {
        height: 180px;
        background-color: #ffff;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
    .transaction-detail-nav {
        height: 100px;
        background-color: #ffffff;
        border-bottom-left-radius: 15px;
        border-bottom-right-radius: 15px;
    }
}

@media only screen and (width:380px) {
    #suffix-btn.dropdown-toggle::after {
        margin-left: 9px !important;
        margin-right: 12px !important;
        margin-top: 9px !important;
    }
    .text-suffix {
        font-weight: 600 !important;
        margin-left: -5px !important;
    }
    .svg-icon-for_text {
        margin-top: -50px !important;
    }
    .suffix-div {
        max-width: 70px !important;
    }
}

@media only screen and (min-width:380px)and (max-width:410px) {
    .svg-icon-for_text {
        margin-top: -50px !important;
    }
    .nopurchasepower-box-title {
        padding: 5px;
    }
}

.pre-header-logo {
    height: 6.0rem;
}

.pre-header {
    background-color: $cp-white;
    border-bottom-left-radius: 0.8em;
    border-bottom-right-radius: 0.8em;
}

.pre-header-height {
    height: 6rem;
}

#app-splash {
    font-family: $cp-font;
    color: $cp-black !important;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-align: center;
    background-color: white;
}

.overlap {
    z-index: 99999;
}

.pay-btn-disable {
    opacity: .3 !important;
}

.text-lowercase {
    text-transform: lowercase;
}

.text-lowercase::-webkit-input-placeholder {
    /* WebKit browsers */
    text-transform: none !important;
}

.text-lowercase:-moz-placeholder {
    /* Mozilla Firefox 4 to 18 */
    text-transform: none !important;
}

.text-lowercase::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    text-transform: none !important;
}

.text-lowercase:-ms-input-placeholder {
    /* Internet Explorer 10+ */
    text-transform: none !important;
}

.text-lowercase::placeholder {
    /* Recent browsers */
    text-transform: none !important;
}

.pac-container {
    z-index: 9999;
}


/* finicity iframe header div */

.iframe-header {
    position: fixed;
    background-color: #F9FAFC;
    top: 0px;
    width: 100%;
    height: 70px;
    z-index: 11;
    padding: 15px;
}

.btn-manual-link {
    height: 33px !important;
    border-radius: 4px !important;
    border-color: transparent !important;
    background-color: $cp-primary !important;
    font-family: $cp-font;
    font-size: 11px;
    color: $cp-white;
    cursor: pointer;
}

.purchpower-header{
    padding: 11px 15px!important;
    border-bottom: 1px solid #669174;
  }
//   .purchpower-text{
//     text-align: left;
//     color: #ffffff;
//     font-size: 18px;
//     font-weight: 300;
//   }
  .tm-symbol{
    font-size: 9px;
    border: 1px solid #fff;
    width: 23px;
    height: 23px;
    padding: 1px;
    border-radius: 100%;
    font-weight: 600;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .cp-point-card{
      background-color: #0e7532 !important;
      border-color: #0e7532 !important;
      border-radius: 8px;
  }
  .cp-point-card-top{
    padding: 17px;
    text-align: left;
    color: #fff;
    border-bottom: 1px solid #669174;
  }
  .cp-point-val{
    font-weight: bold;
    font-size: 25px;
  }
  .cp-point-tag{
    font-weight: bold;
    font-size: 16px;
  }
  .pending-tran-val{
    color: white;
  }
  
  .cp-point-card-bottom{
    padding: 7px 17px;
    text-align: left;
    color: #fff;
  }
  .cp-point-details{
    color: #e9e9e9;
  }

  
  .cp-point-card-title{
    font-size: 19px;
    font-weight: bold;
    color: #ffffff;
  }

  .cp-point-convertion-text .value{
    font-size: 20px;
  }

  .purchpower-text {
    text-align: left;
    color: #ffffff;
    font-size: 18px;
    font-weight: 700;
  }
  .purchasepower-box-amount {
    font-family: "Montserrat";
    font-style: Regular;
    font-variant: Regular;
    color: white;
    font-size: 40px;
    line-height: 40px;
    font-weight: 500;
    }

    .helptext-label{
        color: white; font-size: 14px; margin-left: 0.5rem
      }
    .canpay-crew-termsandcondition-nav{
        height: 80px;
        background-color: rgb(20, 146, 64);
        border-bottom-left-radius: 12px;
        border-bottom-right-radius: 12px;
        padding-left: 10px;
        padding-right: 10px;   
    }
    .crew-tip-button {
        background-color: rgb(20, 146, 64);
        color: white;
        font-size: 14px;
        font-weight: 600;
        font-family:'Open Sans';
        height:34px;
        width:100%;
        border-radius: 8px;
        border: none;
        cursor: pointer;
    }
    .crew-justify-content-right{
        justify-content: right;
    }
    .img-section-merchant-crew{
        object-fit:contain;
        height:100%;
        width:100%;
        object-position:center;
        border-radius:8px;
    }
    .brand-new-text{
        color: #ff0043;
        font-weight: 700;
        font-family: 'Montserrat';
        font-size: 25px;
    }
    .alert-icon-red-circle {
        position: relative;
        top: -14px;
        left: -7px;
    }
    .alert-icon-red-circle-7{
        position: relative;
        top: -14px;
        left: -7px;
    }
    .alert-icon-red-circle-1 {
        position: relative;
        top: -12px;
        left: -7px;
    }
    .alert-icon-red-circle-2 {
        position: relative;
        top: -12px;
        left: -7px;
    }
    .alert-icon-red-circle-4 {
        position: relative;
        top: -12px;
        left: -7px;
    }
    .alert-icon-red-circle-5 {
        position: relative;
        top: -12px;
        left: -7px;
    }
    .alert-icon-red-circle-3 {
        position: relative;
        top: -12px;
        left: -7px;
    }
    .alert-icon-red-circle-6 {
        position: relative;
        top: -12px;
        left: -7px;
    }
    .alert-icon-red-circle-8{
        position: relative;
        top: -11px;
        left: -7px;
    }
    .alert-icon-red-circle-crew{
        position: relative;
        top: -11px;
        left: -9px;
    }
    .canpay-crew-heading{
        color: #ffffff;
        font-family: 'Open Sans';
        font-size: 27px;
        font-weight: bold;
    }
    .canpay-crew-inside-logo{
        position: absolute;
        top: 20px;
        width: 100%;
    }
    .canpay-crew-text{
        color: #ffffff;
        font-family: 'Open Sans';
    }
    .canpay-crew-text-400{
        font-weight: 400;
    }
    .canpay-crew-text-500{
        font-weight: 500;
    }
    .canpay-crew-text-bold{
        font-weight: bold;
    }
    .canpay-crew-text-700{
        font-weight: 700;
    }
    .canpay-crew-text-600{
        font-weight: 600;
    }
    .canpay-crew-text-font-27{
        font-size: 27px;
    }
    .canpay-crew-text-font-18{
        font-size: 18px;
    }
    .canpay-crew-consumer-box{
        background-color:#ffffff;
        padding:5px 10px;
        border-radius: 8px;
        margin-left:15px;
        word-break: break-word;
        overflow-wrap: break-word;
        white-space: normal;
    }
    .canpay-crew-consumer-box-1{
        background-color:#ffffff;
        padding:5px 10px;
        border-radius: 8px;
        white-space: nowrap;
    }
    .canpay-crew-text-font-16{
        font-size: 16px;
    }
    .text-red-crew{
        font-family: "montserrat";
        color: rgb(254, 38, 38);
        font-weight: 500;
        font-size: 12px;
        text-align: left;
        margin-left: 9px;
        margin-bottom: 6px !important;
    }
    .canpay-crew-general-input-box{
        width: 100%;
        height: 50px;
        padding: 10px;
        border-radius: 8px;
        border: none !important;
    }
    .canpay-crew-general-input-box-1{
        width: 100%;
        height: 50px;
        padding: 10px;
        border-radius: 8px;
        border: none !important;
        outline: 1px solid #000000
    }
    .canpay-crew-additional-contact-button{
        width:100%;
        padding:15px 20px;
        border-radius:8px;
        border:none;
        background-color:#1B9146;
        color:#ffffff;
        font-family:'montserrat';
    }
    .canpay-crew-accordian{
        background: #ECECEC;
        border-radius: 7px;
        margin-bottom: 10px !important;
        font-size:12px;
        padding:5px;
    }
    .canpay-crew-accordian-display{
        display: flex;
        justify-content: space-between;
        padding: 1px 5px;
    }
    .canpay-crew-total-signed-button{
        padding: 15px 22px;
        border: none;
        background-color: #056029;
        border-radius: 20px;
        font-size: 12px;
    }
    .canpay-crew-general-input-box-1:Focus{
        width: 100%;
        height: 50px;
        padding: 10px;
        border-radius: 8px;
        border: none !important;
        outline: 1px solid #000000;
    }
    .canpay-crew-general-input-box-1::placeholder{
        font-size:15px;
        font-family:'Open Sans';
        font-weight: 600;
    }
    .canpay-crew-general-input-box:focus{
        width: 100%;
        height: 50px;
        padding: 10px;
        border-radius: 8px;
        outline: 1px solid #000000;
        border: none !important;
    }
    .accept-button-petition {
        width: 100%;
        padding: 16px;
        background: #008F39;
        border: none;
        border-radius: 8px;
        color: white;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
    
        &:hover {
          background: darken(#008F39, 5%);
        }
      }
    .canpay-crew-general-input-box::placeholder{
        font-size:14px;
        font-family:'Open Sans';
        font-weight: 600;
    }
    .canpay-crew-just-lanuched-alignement{
        display:flex;
        justify-content:center;
    }
    .canpay-crew-just-launched-max-width{
        max-width:250px;
    }
    .canpay-crew-just-launched-image{
        object-fit: contain;
        width:100%;
    }
    .canpay-crew-p-margin-bottom{
        margin-bottom:0.5rem!important;
    }
    .canpay-crew-p-margin-bottom-1{
        margin-bottom:0.1rem!important;
    }
    .canpay-crew-p-margin-bottom-2{
        margin-bottom:0.8rem!important;
    }
    .text-red-color{
        color:rgb(255, 57, 57);
    }
    .canpay-crew-text-font-14{
        font-size:14px;
    }
    .canpay-crew-text-font-15{
        font-size:15px;
    }
    .canpay-crew-text-font-13{
        font-size:13px;
    }
    .canpay-crew-text-font-12{
        font-size:12px;
    }
    .brand-new-div{
        height: 100%;
        place-content: center;
    }
    .brand-new-div-1{
        height: 100%;
        place-content:center;
    }   
    .canpay-crew-petition-box-display{
        background-color: #ECECEC;
        width:48%;
        padding: 0px 10px;
        border-radius:10px;
        height:100px;
        place-content: center;
    }
    .canpay-crew-petition-description-text{
        font-size:12px;
    }
    .canpay-crew-input-search{
        border:none!important;
        font-size:14px;
        font-family:'Open Sans';
        font-weight: 600;
        width: 100%;
    }
    .canpay-crew-input-search:focus{
        outline:none!important;
        font-size:14px;
        font-family:'Open Sans';
        font-weight: 600;
    }
    .canpay-crew-input-search::placeholder{
        outline:none!important;
        font-size:14px;
        font-family:'Open Sans';
        font-weight: 600;
    }
    .canpay-crew-input-search-1{
        border: none !important;
        outline: none !important;
        font-size: 14px;
        font-family: "Open Sans";
        font-weight: 600;
        width: 85%;
        margin-left: 22px;
        background-color: #ECECEC !important;
    }
    .canpay-crew-input-search-1:focus{
        outline:none!important;
        font-size:14px;
        font-family:'Open Sans';
        font-weight: 600;
        background-color:#ECECEC!important;
    }
    .canpay-crew-input-search-1::placeholder{
        outline:none!important;
        font-size:14px;
        font-family:'Open Sans';
        font-weight: 600;
        background-color:#ECECEC!important;
    }
    .canpay-crew-all-info-div{
        background-color:#ECECEC!important; 
        height: 100%;
        display:flex;
        flex-direction: column;
        align-items:center;
    }
    .canpay-crew-text-font-18{
        font-size:18px;
    }
    .canpay-crew-text-font-21{
        font-size:21px;
    }
    .canpay-crew-text-font-22{
        font-size:22px;
    }
    .canpay-crew-state-font{
        color:#179346!important;
    }
    .canpay-crew-search{
        border-radius:8px;
        border:1px solid black;
        width:100%;
    }
    .canpay-crew-search-1{
        border-radius:8px;
        padding:8px;
        background-color:#ECECEC;
        width:100%;
    }
    .canpay-crew-info-box{
        padding:15px;
        background-color:#ffffff;
        border-radius: 20px 20px 0px 0px;
    }
    .canpay-crew-position-for-store-header{
        top: 31px;
        position: relative;
        left: 4px;
    }
    .canpay-crew-box-display{
        display:flex;
        justify-content:space-between;
    }
    .canpay-crew-participant-logo{
        position: relative;
        top: -4px;
    }
    .canpay-crew-padding-left-none{
        padding-left:0px!important;
    }
    .canpay-crew-padding-right-none{
        padding-right:0px!important;
    }
    .canpay-crew-petition-info-box{
        background-color:#ECECEC
    }
    .canpay-crew-petition-box{
        background-color:#ffffff;
        width:100%;
        border-radius: 8px;
    }
    .canpay-crew-signer{
        text-align: left;
        padding: 40px 5px 5px 15px;
        background-color: #ffffff;
        border-radius: 0px 0px 15px 15px;
    }
    .canpay-crew-petition-box-1{
        background-color:#ffffff;
        width:100%;
        border-radius: 8px;
    }
    .canpay-crew-petition-padding{
        padding: 2px 8px;
    }
    .canpay-crew-view-all-button {
        border: none;
        border-radius: 8px;
        background-color: #C9C9C9;
        color: #000000;
        width: 95%;
        padding: 6px 10px;
        margin-left: 13px;
        font-size: 12px;
    }
    .canpay-crew-search-icon-button{
        width: 36px;
        background-color: #ECECEC;
        border-radius: 6px;
        outline: 1px solid #000000;
    }
    .canpay-crew-calender-tick-icon{
        height:16px;
        position:relative;
        top:-2px;
    }
    .canpay-crew-petition-person-icon{
        height:16px;
        position:relative;
        top:-2px;
    }
    .canpay-crew-petition-text-date{
        font-size:12px;
        position:relative;
        top:-2px;
    }
    .canpay-crew-petition-text-count{
        font-size:12px;
        position:relative;
        top:-2px;
    }
    .canpay-crew-new-petition-button{
        border:none!important;
        background-color:#179346!important;
        color:#ffffff;
        padding:5px 15px;
        border-radius:40px;
        font-family:'montserrat';
    }
    #canpay-crew-edit-conatct-person-detail___BV_modal_body_{
        background-color: #ffffff;
        border-radius: 12px;
        margin: 10px;
      }
    #canpay-crew-edit-conatct-person-detail___BV_modal_content_{
        background-color: #ffffff;
        margin:12px;
      }
    #canpay-crew-error___BV_modal_body_{
    background-color: #ffffff;
    border-radius: 12px;
    margin: 10px;
    }
    #canpay-crew-error___BV_modal_content_{
        background-color: #ffffff;
        margin:12px;
      }
      #canpay-crew-calendly-setup-modal___BV_modal_body_{
        background-color: #ffffff;
        border-radius: 12px;
        margin: 0px;
    }
    #canpay-crew-calendly-setup-modal___BV_modal_content_{
        background-color: #ffffff;
        margin:12px;
    }
    #canpay-crewactive-petition___BV_modal_body_{
        background-color: #ffffff;
        border-radius: 12px;
        margin: 10px;
      }
      #canpay-crewactive-petition___BV_modal_content_{
          background-color: #ffffff;
          margin:12px;
      }
      #canpay-crew-calendly-setup-success___BV_modal_body_{
        background-color: #ffffff;
        border-radius: 12px;
        margin: 10px;
      }
      #canpay-crew-calendly-setup-success___BV_modal_content_{
          background-color: #ffffff;
          margin:12px;
      }
      #display-icon-modal___BV_modal_body_{
        background-color: #ffffff;
        border-radius: 12px;
        margin: 10px;
      }
      #display-icon-modal___BV_modal_content_{
          background-color: #ffffff;
          margin:12px;
      }
      #canpay-crew-share-petition___BV_modal_body_{
        background-color: #ffffff;
        border-radius: 12px;
        margin: 10px;
      }
      #canpay-crew-share-petition___BV_modal_content_{
          background-color: #ffffff;
          margin:12px;
      }
      #withdraw-from-petition-modal___BV_modal_body_{
        background-color: #ffffff;
        border-radius: 12px;
        margin: 10px;
      }
      #withdraw-from-petition-modal___BV_modal_content_{
          background-color: #ffffff;
          margin:12px;
      }
      #canpay-crewactive-fill-petition___BV_modal_body_{
        background-color: #ffffff;
        border-radius: 12px;
        margin: 10px;
      }
      #canpay-crewactive-fill-petition___BV_modal_content_{
          background-color: #ffffff;
          margin:12px;
      }
      #canpay-crew-create-petition___BV_modal_body_{
        background-color: #ffffff;
        border-radius: 12px;
        margin: 10px; 
      }
      #canpay-crew-create-petition___BV_modal_content_{
        background-color: #ffffff;
        margin:12px;
      }
      #show-warning-canpay-crew-for-backdrop___BV_modal_body_{
        background-color: #ffffff;
        border-radius: 12px;
        margin: 10px; 
      }
      #show-warning-canpay-crew-for-backdrop___BV_modal_content_{
        background-color: #ffffff;
        margin:12px;
      }
      #canpay-crew-address-modal___BV_modal_body_{
        background-color: #ffffff;
        border-radius: 12px;
        margin: 10px; 
      }
      #canpay-crew-address-modal___BV_modal_content_{
        background-color: #ffffff;
        margin:12px;
      }
    .canpay-crew-sign-petition-modal-button{
        border:none!important;
        background-color:#179346!important;
        color:#ffffff;
        font-weight:600;
        padding:14px;
        border-radius:8px;
        font-family:'montserrat';
        width:100%;
    }
    .accept-button-petition-text{
        font-weight:600;
    }
    .canpay-crew-sign-petition-not-ok-modal-button{
        border:none!important;
        background-color:#D8D8D8!important;
        color:#000000;
        font-weight:600;
        padding:14px;
        border-radius:8px;
        font-family:'montserrat';
        width:100%;
    }
    .canpay-crew-success-text{
        font-family: "Open Sans";
        color: #1B9142;
        font-size: 14px;
        font-weight:600;
    }
    .canpay-crew-accpeted-petition-button{
        font-family:'montserrat';
        color: #1B9142;
        font-size: 14px;
        font-weight:600;
        background-color:#ffffff;
        border: 1px solid #1B9142;
        padding: 5px 12px;
        border-radius:40px;
    }
    .canpay-crew-accepted-text{
        font-family: "Open Sans";
        color: #000000;
        font-weight:600;   
    }    
    .canpay-crew-accepted-text-1{
        font-family: "Open Sans";
        color: #000000; 
    }
    .canpay-crew-process-petition-button{
        font-family:'montserrat';
        background-color:#ECECEC!important;
        color:#000000;
        font-weight: 600;
        border:none!important;
        padding: 5px 12px;
        border-radius:40px;
    }
    .canpay-crew-admin-approval-button{
        border: none !important;
        background-color: #ffffff !important;
        color: #000000;
        font-weight:bold;
        padding: 5px 15px;
        border-radius: 40px;
        font-family: "montserrat";
        outline: 1px solid #000000;
    }
    .person-petition-icon{
        height:18px;
    }
    .petition-postiton-crew{
        position:relative;
        right:-1px;
        top:11px
    }
    .petition-count-crew{
        position: relative;
        top: 1px;
        margin-left: 2px;
    }
    .canpay-crew-admin-approval-button:hover{
        border: none !important;
        background-color: #ffffff !important;
        color: #000000;
        font-weight:bold;
        padding: 5px 15px;
        border-radius: 40px;
        font-family: "montserrat";
        outline: 1px solid #000000;
        cursor: pointer;
    }
    .canpay-crew-petition-button{
        border:none!important;
        background-color:#000000!important;
        color:#ffffff;
        padding:5px 15px;
        border-radius:40px;
        font-family:'montserrat';
    }
    .canpay-crew-complete-signed-petition-button{
        background-color: lightgray !important;
        color: #5f5e5e;
        outline: 1px solid #5f5e5e;
        font-family: "montserrat";
        width: 100%;
        font-weight:bold;
        box-shadow: none !important;
        padding: 16px 14px;
        border:none!important;
        border-radius: 7px;
    }
    .canpay-crew-icon-size-change{
        height:40px;
    }
    .canpay-crew-signed-button{
        border:none!important;
        background-color: #ffffff !important;
        color: #5f5e5e;
        font-weight:bold;
        padding:5px 15px;
        border-radius:40px;
        font-family:'montserrat';
        outline: 1px solid #5f5e5e;
    }
    .canpay-crew-signed-button:hover{
        cursor:pointer;
    }
    .canpay-crew-info-box-desc{
        background-color: #ECECEC;
        width:45%;
    }
    .canpay-crew-info-text{
        font-size: 21px !important;
        font-family: 'Open Sans';
        font-weight: bold;
    }
    .canpay-crew-margin-zero{
        margin:0px!important;
    }
    .canpay-crew-info-box-header-text{
        color: #179346;
        font-weight: 700;
        font-family: 'Montserrat';
        font-size: 25px;
    }
    .canpay-friend-text-button{
        background-color: #149240;
        color: #ffffff;
        border-radius: 8px;
        font-size: 13px;
        padding: 8px 0px;
        font-weight: 600;
        font-family: 'Open Sans';
        cursor: pointer;
    }
    .canpay-crew-store-button{
        background-color: #000000;
        color: #ffffff;
        font-family: "Monteserrat";
        font-family: 'Open Sans';
        width: 100%;
        box-shadow: none !important;
        padding: 16px 14px;
        border: none;
        border-radius: 7px;
    }
    .canpay-crew-active-petition-button{
        background-color: #000000;
        color: #ffffff;
        font-family: "Monteserrat";
        font-family: 'Open Sans';
        width: 100%;
        box-shadow: none !important;
        padding: 16px 14px;
        border: none;
        border-radius: 7px;
    }
    .canpay-crew-await-admin-approval-button{
        color: #000000;
        font-family: "Monteserrat";
        font-family: 'Open Sans';
        width: 100%;
        background-color:#ffffff!important;
        box-shadow: none !important;
        padding: 16px 14px;
        border: none;
        border-radius: 7px;
        font-weight:600;
        outline:1px solid #000000;
    }
    .canpay-crew-petitions-button{
        background-color: #1B9142;
        color: #ffffff;
        font-family: "Monteserrat";
        font-family: 'Open Sans';
        width: 100%;
        box-shadow: none !important;
        padding: 16px 14px;
        border: none;
        border-radius: 7px;
    } 
    .canpay-crew-petition-box-display-1{
        display:flex;
        align-items:center;
        flex-direction:column;
        background-color:#ECECEC;
        height: inherit;
        overflow-y: scroll;
        scrollbar-width: none;
        padding-bottom:12px;
    }
    .canpay-crew-free-spin-style {
        width: 125px;
        background-color: rgb(255, 255, 255);
        font-family: montserrat;
        padding: 1px 10px;
        border-radius: 30px;
        position: absolute;
        right: -2px;
        border: 1px solid #000000;
        -webkit-transform: scale(0.8);
        transform: scale(0.7);
        top: -5px;
    }
    .canpay-crew-successful-petition-display{
        display:flex;
        align-items:center;
        flex-direction:column;
        background-color:#ECECEC;
        height: inherit;
        font-family:'Open Sans';
        overflow-y: scroll;
        scrollbar-width: none;
    }
    .canpay-crew-small-free-spin {
        width: 160px;
        background-color: rgb(255, 255, 255);
        padding: 1px 10px;
        border-radius: 27px;
        position: absolute;
        font-family: "Montserrat";
        right: -40px;
        border: 1px solid #000000;
        -webkit-transform: scale(0.4);
        transform: scale(0.4);
        top: -19px;
    }
    .canpay-crew-margin-for-successfull-petition{
        margin: 14px 7px 7px 0px;
    }
    .canpay-crew-small-green-spin{
        width: 50px;
        height: 50px;
        border-radius: 50%;
        position: absolute;
        background-color: rgb(23, 147, 70);
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgb(255, 255, 255);
        font-weight: 700;
        right: 0px;
        top: 0px;
    }
    .canpay-crew-green-spin{
        width: 38px;
        height: 38px;
        border-radius: 50%;
        position: absolute;
        background-color: rgb(23, 147, 70);
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgb(255, 255, 255);
        font-weight: 700;
        right: 0px;
        top: 0px;
    }
  @media only screen and ( min-width:280px) and ( max-width:700px) {
    .purchpower-text {
        text-align: left;
        color: #ffffff;
        font-size: 12px;
        font-weight: 700;
    }
    .tm-symbol {
    font-size: 7px;
    border: 1px solid #fff;
    width: 17px;
    height: 17px;
    padding: 1px;
    border-radius: 100%;
    font-weight: 600;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    }
    .purchasepower-box-amount {
    font-family: "Montserrat";
    font-style: Regular;
    font-variant: Regular;
    color: white;
    font-size: 30px;
    line-height: 30px;
    font-weight: 500;
    }
    .helptext-label{
    color: white; font-size: 10px; margin-left: 0.5rem
    }
    .cp-point-logo{
    width: 30px;
    }
    .cp-point-card-title{
    font-size: 15px;
    font-weight: bold;
    color: #ffffff;
    }
    .cp-point-convertion-text .value {
    font-size: 16px;
    }
    .cp-point-convertion-text{
        font-size: 12px
    }
  }
  
  @media only screen and ( min-width:320px) and ( max-width:700px) {
    .purchpower-text {
        text-align: left;
        color: #ffffff;
        font-size: 14px;
        font-weight: 700;
    }
    .tm-symbol {
    font-size: 8px;
    border: 1px solid #fff;
    width: 18px;
    height: 18px;
    padding: 1px;
    border-radius: 100%;
    font-weight: 600;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    }
    .purchasepower-box-amount {
    font-family: "Montserrat";
    font-style: Regular;
    font-variant: Regular;
    color: white;
    font-size: 31px;
    line-height: 31px;
    font-weight: 500;
    }
    .helptext-label{
        color: white; font-size: 12px; margin-left: 0.5rem
      }
      .cp-point-logo{
        width: 35px;
      }
      .cp-point-card-title{
        font-size: 17px;
        font-weight: bold;
        color: #ffffff;
      }
      .cp-point-convertion-text .value {
        font-size: 16px;
        }
        .cp-point-convertion-text{
            font-size: 12px
        }
  }
  
  @media only screen and ( min-width:376px) and ( max-width:800px) {
    .purchpower-text {
    text-align: left;
    color: #ffffff;
    font-size: 18px;
    font-weight: 700;
    }
    .tm-symbol {
    font-size: 9px;
    border: 1px solid #fff;
    width: 23px;
    height: 23px;
    padding: 1px;
    border-radius: 100%;
    font-weight: 600;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    }
    .purchasepower-box-amount {
    font-family: "Montserrat";
    font-style: Regular;
    font-variant: Regular;
    color: white;
    font-size: 40px;
    line-height: 40px;
    font-weight: 500;
    }
    .cp-point-card-title{
    font-size: 19px;
    font-weight: bold;
    color: #ffffff;
    }
    .helptext-label{
        color: white; font-size: 14px; margin-left: 0.5rem
      }
      .cp-point-logo{
        width: 35px;
      }
      .cp-point-convertion-text .value {
        font-size: 20px;
        }
        .cp-point-convertion-text{
            font-size: 15px
        }
  }

    // @media only screen and ( min-width:376px) and ( max-width:800px) {
    // @media screen and (orientation: landscape) {
    //     html {
    //     /* Rotate the content container */
    //     transform: rotate(-90deg);
    //     transform-origin: left top;
    //     /* Set content width to viewport height */
    //     width: 100vh;
    //     /* Set content height to viewport width */
    //     height: 100vw;
    //     overflow-x: hidden;
    //     position: absolute;
    //     top: 100%;
    //     left: 0;
    //     }
    // }
    // }

    .vdate-picker{
        display: flex!important;
        height: unset!important;
    }
    .vdate-picker .btn{
        margin: 0!important;
        display: unset!important;
    }
    .vdate-picker .b-calendar-nav .btn{
        height: 40px!important;
    }
    .vdate-picker .b-calendar-nav .btn.focus, .btn:focus {
        outline: 0!important;
        -webkit-box-shadow: unset!important;
        box-shadow: unset !important;
    }
    .vdate-picker .form-control{
        height: unset!important;
        margin-bottom: 0;
        border: 0!important;
    }
    .vdate-picker .form-control:focus {
        border-color: unset!important;
        outline: 0!important;
        -webkit-box-shadow: unset!important;
        box-shadow: unset !important;
    }
    .vdate-picker .b-calendar-inner{
        padding: 10px;
    }
    .vdate-picker .b-calendar .b-calendar-grid .row{
        flex-wrap: nowrap;
    }
    .vdate-picker .b-calendar .b-calendar-grid-body .col[data-date] .btn {
        width: 32px;
        height: 32px;
        font-size: 14px;
        line-height: 1;
        margin: 3px auto;
        padding: 9px 0;
        display: inline-block!important;
    }
    .primary-green{
        background-color: #149240;
    }
    .vdate-picker .btn-primary:not(:disabled):not(.disabled).active, .btn-primary:not(:disabled):not(.disabled):active, .show>.btn-primary.dropdown-toggle{
        color: #fff!important;
        background-color: #149240!important;
        border-color: #149240!important;
    }
    .vdate-picker .btn-outline-primary:hover {
        color: #fff!important;
        background-color: #149240!important;
        border-color: #149240!important;
    }
    .vdate-picker .btn-outline-primary{
        color: #149240!important;
    }
    .vdate-picker .btn-primary:not(:disabled):not(.disabled).active:focus, .btn-primary:not(:disabled):not(.disabled):active:focus, .show>.btn-primary.dropdown-toggle:focus {
        -webkit-box-shadow: unset !important;
        box-shadow: unset !important;
    }
    .vdate-picker .btn-primary.focus, .btn-primary:focus{
        border-color: unset!important;
        outline: 0!important;
        -webkit-box-shadow: unset!important;
        box-shadow: unset !important;
    }
    .vdate-picker .form-control.is-invalid,.vdate-picker .was-validated label .form-control:invalid {
        border-color: unset!important;
        padding-right: unset!important;
        background-image: unset!important;
        background-repeat: unset!important;
        background-position: unset!important;
        background-size: unset!important;
    }
    .vdate-picker.is-valid,.vdate-picker label.is-valid {
        border-color: unset!important;
        padding-right: unset!important;
        background-image: unset!important;
        background-repeat: unset!important;
        background-position: unset!important;
        background-size: unset!important;
    }
    #invitation-modal___BV_modal_content_{
        margin: 10px;
        border-radius: 20px;
        margin-top: 55%;
    }
    #invitation-modal___BV_modal_body_{
        background: white;
        border-radius: 10px;
        padding: 30px;
    }
    #exchange-rate-notification-modal___BV_modal_content_{
        margin: 10px;
    }
    #exchange-rate-notification-modal___BV_modal_body_{
        background: white;
        border-radius: 10px;
    }
    #merchant-point-available-modal___BV_modal_body_{
    background-color: #ffffff;
    border-radius: 12px;
    margin: 10px;
    }
    #merchant-point-available-modal___BV_modal_content_{
        background-color: #ffffff;
        margin:12px;
    }
    #lottery-notification-modal___BV_modal_content_{
        margin: 10px;
    }
    #lottery-notification-modal___BV_modal_body_{
        background: white;
        border-radius: 10px;
    }

    .no-connection-toast{
        width: 100%;
        background: #e60a0a;
        color: #fff;
        padding-top: 5px;
        font-size: 13px;
        padding-bottom: 10px;
    }
    .connection-stable-toast{
        width: 100%;
        background: #0e7532;
        color: #fff;
        padding-top: 5px;
        font-size: 13px;
        padding-bottom: 10px;
    }
    .fade-enter-active, .fade-leave-active {
    transition: opacity .5s
    }
    .fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
    opacity: 0
    }

    @keyframes startRewardModalAnimation {
    0%{
        opacity: 1;
    }
    25%{
        opacity: 0.8;
    }
    50%{
        opacity: 0.2;
    }
    75%{
        opacity: 0;
    }
    100%{
        opacity:0;
    }
}

@keyframes merchant-svg-frame {
    @for $i from 1 through 5 {
        #{percentage($i/10)} {
        transform: translateY(#{$i * 15}px);
        }
    }
}

@keyframes startShrinkAnimation {
    0%{
        transform:translate(-50%, -50%);
        height: 290x;
    }
    25%{
        transform:translate(-50%, -50%);
        height: 250px;
    }
    50%{
        transform:translate(-50%, -50%);
        height: 150px;
    }
    75%{
        transform:translate(-50%, -50%);
        height: 100px;
    }
    100%{
        transform:translate(-50%, -50%);
        height: 55px;
    }
 }

 @keyframes startSmallShrinkAnimation {
    0%{
        transform:translate(-50%, -50%) ;
        height: 55px;
        width:300px;
    }
    25%{
        transform:translate(-50%, -50%) ;
        height: 55px;
        width:250px;
    }
    50%{
        transform:translate(-50%, -50%) ;
        height: 55px;
        width:200px;
    }
    75%{
        transform:translate(-50%, -50%);
        height: 55px;
        width:170px;
    }
    100%{
        transform:translate(-50%, -50%) ;
        height: 55px;
        width:150px;
    }
 }

 @keyframes startTranslateYAnimation{
    0%{
        transform:translate(-50%, -50%) translateY(15px);
        height: 55px; 
        width:150px;
    }
    100%{
        transform:translate(-50%, -50%) translateY(68px);
        height: 55px; 
        width:150px;
    }
 }

 @keyframes startTranslateYAnimation1{
    0%{
        transform:translateY(15px); 
    }
    100%{
        transform:translateY(68px); 
    }
 }

 @media only screen and (max-width:359px){
    .alert-icon-red-circle-2 {
        position: relative;
        top: -42px;
        left: 10px;
    }
 }
 @media only screen and (max-width:389px){
    .alert-icon-red-circle-4 {
        position: relative;
        top: -42px;
        left: 10px;
    }
 }
 @media only screen and (max-width:339px){
    .alert-icon-red-circle-5 {
        position: relative;
        top: -42px;
        left: 10px;
    }
 }
 @media only screen and (max-width:429px){
    .alert-icon-red-circle-3 {
        position: relative;
        top: -42px;
        left: 15px;
    }
 }
 @media only screen and (max-width:335px){
    .alert-icon-red-circle-3 {
        position: relative;
        top: -42px;
        left: 18px;
    }
 }
@media only screen and (max-width:330px){
    .alert-icon-red-circle-crew {
        position: relative;
        top: -41px;
        left: 10px;
    }
}
@media only screen and (max-width:369px){
    .alert-icon-red-circle-6 {
        position: relative;
        top: -41px;
        left: 8px;
    }    
 }
 @media only screen and (min-width:350px) and (max-width:421px){
        .canpay-crew-icon-size-change{
            height:35px;
        }
        .person-petition-icon{
            height:16px;
        }
        .petition-postiton-crew{
            position:relative;
            right:-1px;
            top:8px;
        }
        .petition-count-crew{
            position: relative;
            margin-left: 2px;
            font-size:12px;
            top:-1px;
        }
        .canpay-crew-petition-button{
            border:none!important;
            background-color:#000000!important;
            color:#ffffff;
            padding:5px 15px;
            border-radius:40px;
            font-family:'montserrat';
            font-size:12px;
        }
        .canpay-crew-process-petition-button{
            font-family:'montserrat';
            background-color:#ECECEC!important;
            color:#000000;
            font-weight: 600;
            border:none!important;
            padding: 5px 12px;
            border-radius:40px;
            font-size:12px;
        }
        .canpay-crew-admin-approval-button{
            border: none !important;
            background-color: #ffffff !important;
            color: #000000;
            font-weight:bold;
            padding: 5px 15px;
            border-radius: 40px;
            font-family: "montserrat";
            outline: 1px solid #000000;
            font-size:12px;
        }
        .canpay-crew-accpeted-petition-button{
            font-family:'montserrat';
            color: #1B9142;
            font-size: 14px;
            font-weight:600;
            background-color:#ffffff;
            border: 1px solid #1B9142;
            padding: 5px 12px;
            border-radius:40px;
            font-size:12px;
        }
        .canpay-crew-new-petition-button{
            border:none!important;
            background-color:#179346!important;
            color:#ffffff;
            padding:5px 15px;
            border-radius:40px;
            font-family:'montserrat';
            font-size:12px;
        }
        .canpay-crew-signed-button{
            border:none!important;
            background-color: #ffffff !important;
            color: #5f5e5e;
            font-weight:bold;
            padding:5px 15px;
            border-radius:40px;
            font-family:'montserrat';
            outline: 1px solid #5f5e5e;
            font-size:12px;
        }
        .canpay-crew-address-text-1{
            font-size:15px;
        }
        .canpay-crew-address-text-2{
            font-size:14px;
        }
 }
 @media only screen and (max-width:350px){
    .person-petition-icon{
        height:13px;
    }
    .petition-postiton-crew{
        position:relative;
        right:-1px;
        top:8px;
    }
    .petition-count-crew{
        position: relative;
        top: -1px;
        margin-left: 2px;
        font-size:11px;
    }
    .canpay-crew-petition-button{
        border:none!important;
        background-color:#000000!important;
        color:#ffffff;
        padding:5px 15px;
        border-radius:40px;
        font-family:'montserrat';
        font-size:10px;
    }
    .canpay-crew-process-petition-button{
        font-family:'montserrat';
        background-color:#ECECEC!important;
        color:#000000;
        font-weight: 600;
        border:none!important;
        padding: 5px 12px;
        border-radius:40px;
        font-size:10px;
    }
    .canpay-crew-icon-size-change{
        height:35px;
    }
    .canpay-crew-admin-approval-button{
        border: none !important;
        background-color: #ffffff !important;
        color: #000000;
        font-weight:bold;
        padding: 5px 15px;
        border-radius: 40px;
        font-family: "montserrat";
        outline: 1px solid #000000;
        font-size:10px;
    }
    .canpay-crew-accpeted-petition-button{
        font-family:'montserrat';
        color: #1B9142;
        font-size: 14px;
        font-weight:600;
        background-color:#ffffff;
        border: 1px solid #1B9142;
        padding: 5px 12px;
        border-radius:40px;
        font-size:10px;
    }
    .canpay-crew-new-petition-button{
        border:none!important;
        background-color:#179346!important;
        color:#ffffff;
        padding:5px 15px;
        border-radius:40px;
        font-family:'montserrat';
        font-size:10px;
    }
    .canpay-crew-signed-button{
        border:none!important;
        background-color: #ffffff !important;
        color: #5f5e5e;
        font-weight:bold;
        padding:5px 15px;
        border-radius:40px;
        font-family:'montserrat';
        outline: 1px solid #5f5e5e;
        font-size:10px;
    }
    .canpay-crew-address-text-1{
        font-size:13px;
    }
    .canpay-crew-address-text-2{
        font-size:12px;
    }
}

@media only screen and (max-width:370px) {
    .canpay-crew-petition-description-text{
        font-size:11px;
    }
}

@media only screen and (max-width:345px) {
    .canpay-crew-petition-description-text{
        font-size:10px;
    }
}

@media only screen and (max-width:371px){
    .canpay-crew-calender-tick-icon{
        height:14px;
        position:relative;
        top:-2px;
    }
    .canpay-crew-petition-person-icon{
        height:14px;
        position:relative;
        top:-2px;
    }
    .canpay-crew-petition-text-date{
        font-size:10px;
        position:relative;
        top:-2px;
    }
    .canpay-crew-petition-text-count{
        font-size:10px;
        position:relative;
        top:-2px;
    }
}
@media only screen and (max-width:351px){
    .alert-icon-red-circle-7{
        position: relative;
        top: -10px;
        left: -7px;
    }
}
@media only screen and (max-width:361px){
    .canpay-crew-position-for-store-header{
        top: 25px;
        position: relative;
        left: 4px;
    
    }
}
@media only screen and (max-width:367px){
    .accept-button-petition-text{
        font-size:13px;
        font-weight:600;
    }
}

@media only screen and (max-width:389px){
    .alert-icon-red-circle-8{
        position: relative;
        top: -41px;
        left: 9px;
    }
}