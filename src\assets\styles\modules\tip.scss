.style2 {
    background-color: #E9E9E9;
    height: 80px;
    width: 250px;
    border-radius: 5px;
    font-weight: bold;
    font-size: 30px;
}

.storename-box {
    display: table-cell;
    vertical-align: middle;
    width: 90%;
    height: 60px;
    background-color: $cp-lightgary !important;
    ;
    border-radius: 8px;
    margin: auto;
    font-family: $cp-font-secondary;
    font-size: 22px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 26.4px;
    letter-spacing: normal;
    text-align: center;
    color: #000000;
    font-weight: 600;
}

.store-name-text {
    font-family: $cp-font;
    font-size: 18px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #000000;
}

.style1 {
    height: 70px;
    width: 400px;
    border-radius: 10px;
}

.h-style {
    margin-left: 40px;
}

.modal-backdrop {
    background: rgba(0, 0, 0, 0.3);
}

.t-dot {
    height: 150px;
    width: 150px;
    background-color: white;
    border-radius: 50%;
    display: inline-block;
    margin-top: 50px;
}

.btn1 {
    height: 55px;
    width: 40%;
    margin: 15px;
    border-radius: 5px;
    font-size: 20px;
}

.btn2 {
    height: 55px;
    width: 40%;
    margin: 15px;
    border-radius: 5px;
    font-size: 20px;
}

.font {
    color: white;
    text-align: left;
    margin-left: 10px;
}

 ::placeholder {
    color: black;
    opacity: 1;
    text-align: center;
    font-size: 25px;
    font-weight: bold;
}

.color {
    background-color: $cp-primary;
}

.t-div {
    background-color: #ECECEC;
    width: 100%;
    border-radius: 5px;
    height: 60px;
    margin-top: 10px;
}

#tip-modal-center___BV_modal_content_ {
    border-radius: 10px;
    margin: 10px;
    background-color: #ffffff;
}

#tip-modal-center___BV_modal_body_ {
    background-color: #ffffff !important;
    border-radius: 12px;
    margin: 10px;
}

.QR-div {
    background-color: white;
    width: 100%;
    height: 400px;
}

.h4-style {
    float: left;
    font-family: $cp-font-secondary;
    font-size: 20px;
    font-weight: 400;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #000000;
    padding: 15px;
}

.h4-style1 {
    float: right;
    font-family: $cp-font-secondary;
    font-size: 20px;
    font-weight: 400;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #000000;
    padding: 15px;
}

.h4-style2 {
    text-align-last: end;
    padding-top: 10px;
    padding-left: 20px;
    padding-bottom: 20px;
}

.btn3 {
    border-radius: 6px;
    height: 50px;
    width: 100px;
    font-size: 15px !important;
    padding: 10px 10px 10px 10px;
    border-radius: 10px;
    width: 70px;
    background-color: black !important;
    font-family: $cp-font-secondary;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #ffffff;
}

.amount {
    font-size: 50px;
    font-weight: normal;
}

.Layer-2 {
    width: 211px;
    height: 211px;
    background-color: white;
}

.Payment-Code-Expired {
    font-size: 24px;
    font-weight: 100;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #000000;
    font-family: $cp-font-tertiary;
}

.Lorem-ipsum {
    font-family: $cp-font-tertiary;
    font-size: 16px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #666666;
    padding-left: 20px;
    padding-right: 20px;
}

.modal-backdrop {
    opacity: 0.5 !important;
}

.folder-selected {
    background: #d8d8d8;
}

.modal-body {
    background-color: $cp-primary;
}

.modal-content {
    border-radius: 6px;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    background-color: transparent;
    border: none !important;
}

.modal-footer {
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
    -webkit-border-bottom-left-radius: 6px;
    -webkit-border-bottom-right-radius: 6px;
    -moz-border-radius-bottomleft: 6px;
    -moz-border-radius-bottomright: 6px;
}

.modal-header {
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    -webkit-border-top-left-radius: 6px;
    -webkit-border-top-right-radius: 6px;
    -moz-border-radius-topleft: 6px;
    -moz-border-radius-topright: 6px;
}

.modal-content-purchasepower {
    border-radius: 6px;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    background-color: white;
}

.modal-body-purchasepower {
    background-color: white;
}

.leave-a-tip-modal-label {
    font-family: $cp-font;
    font-size: 24px;
    font-weight: 400;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    line-height: 19.4px;
    color: #000000;
    src: local('Open Sans Semibold'), local('OpenSans-Semibold'), url(http://fonts.gstatic.com/s/opensans/v13/MTP_ySUJH_bn48VBG8sNShampu5_7CjHW5spxoeN3Vs.woff2) format('woff2');
    height: 30px;
    margin-top: 30px;
    margin-left: 10px;
}

.Custom-Tip {
    font-family: $cp-font !important;
    font-size: 18px !important;
    font-weight: normal !important;
    font-stretch: normal !important;
    font-style: normal !important;
    line-height: normal !important;
    letter-spacing: normal !important;
    text-align: left !important;
    color: #010101 !important;
}

.cross-btn-div {
    float: right;
}

.cross-btn {
    background-color: transparent;
    border: 0px;
}

@media only screen and ( min-width:270px) and ( max-width:600px) {
    .payment-approve-roundoff-block {
        height: 120px;
        width: 120px;
        border-radius: 50%;
        background-color: #ececec !important;
        border-color: #ececec !important;
        display: inline-block;
        border: 2px solid #fff;
        margin-top: 20px;
    }
    .payment-approve-icon {
        enable-background: new 0 0 50 62.5;
        height: 80px;
        margin: 3px;
        margin-top: 28px;
    }
    .payment-approved-text-div {
        margin-top: 9px;
    }
    .payment-approved-text {
        font-family: $cp-font;
        font-size: 17px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #666666;
    }
    .payment-approved-amount {
        font-family: $cp-font-secondary;
        font-size: 38px;
        font-weight: 400;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #000000;
    }
    .store-name-row {
        margin-top: -23px;
    }
    .store-name-div {
        display: table;
        width: 90%;
        margin-top: 70px;
    }
    .tip-btn {
        width: 125px;
        margin-right: 10px;
        height: 43px;
        background-color: $cp-black !important;
        border-color: $cp-black !important;
        display: inline-block;
        vertical-align: top;
    }
    .tip-denger-btn {
        width: 125px;
        margin-right: 10px;
        height: 43px;
        background-color: #dc3545 !important;
        border-color: #dc3545 !important;
        display: inline-block;
        vertical-align: top;
    }
    .tip-ok-btn {
        width: 125px;
        height: 43px;
        background-color: $cp-primary !important;
        border-color: $cp-primary !important;
        display: inline-block;
        vertical-align: top;
    }
    .tip-btn-row {
        margin-bottom: 42px;
        margin-top: -10px;
    }
    .final-tip-amount {
        font-size: 20px;
    }
    .dollar-icon {
        margin-top: 24px;
    }
    .custom-tip-div {
        margin-top: 35px;
    }
    .purchase-power-modal-text {
        font-family: $cp-font;
        font-size: 15px;
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: center;
        line-height: 19.4px;
        color: #000000;
        margin-top: 40px;
        margin-bottom: 0px !important;
        src: local('Open Sans Semibold'), local('OpenSans-Semibold'), url(http://fonts.gstatic.com/s/opensans/v13/MTP_ySUJH_bn48VBG8sNShampu5_7CjHW5spxoeN3Vs.woff2) format('woff2');
    }
}

@media only screen and ( min-width:360px) and ( max-width:900px) {
    .payment-approve-roundoff-block {
        height: 160px;
        width: 160px;
        border-radius: 50%;
        background-color: #ececec !important;
        border-color: #ececec !important;
        display: inline-block;
        border: 2px solid #fff;
        margin-top: 25px;
    }
    .payment-approve-icon {
        enable-background: new 0 0 50 62.5;
        height: 95px;
        margin-top: 42px;
        margin-left: 12px;
    }
    .payment-approved-text-div {
        margin-top: 25px;
    }
    .payment-approved-text {
        font-family: $cp-font;
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #666666;
    }
    .payment-approved-amount {
        font-family: $cp-font-secondary;
        font-size: 45px;
        font-weight: 400;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #000000;
    }
    .store-name-row {
        margin-top: -20px;
    }
    .store-name-div {
        display: table;
        width: 90%;
        margin-top: 70px;
    }
    .tip-btn {
        width: 140px;
        margin-right: 10px;
        height: 50px;
        background-color: $cp-black !important;
        border-color: $cp-black !important;
        display: inline-block;
        vertical-align: top;
    }
    .tip-denger-btn {
        width: 125px;
        margin-right: 10px;
        height: 43px;
        background-color: #dc3545 !important;
        border-color: #dc3545 !important;
        display: inline-block;
        vertical-align: top;
    }
    .tip-ok-btn {
        width: 140px;
        height: 50px;
        background-color: $cp-primary !important;
        border-color: $cp-primary !important;
        display: inline-block;
        vertical-align: top;
    }
    .tip-btn-row {
        margin-bottom: 41px;
    }
    .final-tip-amount {
        font-size: 20px;
    }
    .dollar-icon {
        margin-top: 24px;
    }
    .custom-tip-div {
        margin-top: 35px;
    }
    .purchase-power-modal-text {
        font-family: $cp-font;
        font-size: 15px;
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: center;
        line-height: 19.4px;
        color: #000000;
        margin-top: 40px;
        margin-bottom: 0px !important;
        src: local('Open Sans Semibold'), local('OpenSans-Semibold'), url(http://fonts.gstatic.com/s/opensans/v13/MTP_ySUJH_bn48VBG8sNShampu5_7CjHW5spxoeN3Vs.woff2) format('woff2');
    }
}

@media only screen and ( min-width:800px) and ( max-width:1400px) {
    .payment-approve-roundoff-block {
        height: 160px;
        width: 160px;
        border-radius: 50%;
        background-color: #ececec !important;
        border-color: #ececec !important;
        display: inline-block;
        border: 2px solid #fff;
        margin-top: 25px;
    }
    .payment-approve-icon {
        enable-background: new 0 0 50 62.5;
        height: 95px;
        margin-top: 42px;
        margin-left: 12px;
    }
    .payment-approved-text-div {
        margin-top: 0px;
    }
    .payment-approved-text {
        font-family: $cp-font;
        font-size: 17px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #666666;
    }
    .payment-approved-amount {
        font-family: $cp-font-secondary;
        font-size: 40px;
        font-weight: 400;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #000000;
    }
    .store-name-row {
        margin-top: -20px;
    }
    .store-name-div {
        display: table;
        width: 90%;
    }
    .tip-btn {
        width: 140px;
        margin-right: 10px;
        height: 50px;
        background-color: $cp-black !important;
        border-color: $cp-black !important;
        display: inline-block;
        vertical-align: top;
    }
    .tip-denger-btn {
        width: 125px;
        margin-right: 10px;
        height: 43px;
        background-color: #dc3545 !important;
        border-color: #dc3545 !important;
        display: inline-block;
        vertical-align: top;
    }
    .tip-ok-btn {
        width: 140px;
        height: 50px;
        background-color: $cp-primary !important;
        border-color: $cp-primary !important;
        display: inline-block;
        vertical-align: top;
    }
    .tip-btn-row {
        margin-bottom: 41px;
    }
    .final-tip-amount {
        font-size: 20px;
    }
    .dollar-icon {
        margin-top: 24px;
    }
    .custom-tip-div {
        margin-top: 35px;
    }
    .purchase-power-modal-text {
        font-family: $cp-font;
        font-size: 15px;
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: center;
        line-height: 19.4px;
        color: #000000;
        margin-top: 40px;
        margin-bottom: 0px !important;
        src: local('Open Sans Semibold'), local('OpenSans-Semibold'), url(http://fonts.gstatic.com/s/opensans/v13/MTP_ySUJH_bn48VBG8sNShampu5_7CjHW5spxoeN3Vs.woff2) format('woff2');
    }
}

@media (min-width: 992px) {
    .payment-approve-roundoff-block {
        height: 160px;
        width: 160px;
        border-radius: 50%;
        background-color: #ececec !important;
        border-color: #ececec !important;
        display: inline-block;
        border: 2px solid #fff;
        margin-top: 25px;
    }
    .payment-approve-icon {
        enable-background: new 0 0 50 62.5;
        height: 95px;
        margin-top: 42px;
        margin-left: 12px;
    }
    .payment-approved-text-div {
        margin-top: 30px;
    }
    .payment-approved-text {
        font-family: $cp-font;
        font-size: 25px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #666666;
    }
    .payment-approved-amount {
        font-family: $cp-font-secondary;
        font-size: 45px;
        font-weight: 400;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #000000;
    }
    .store-name-row {
        margin-top: -20px;
    }
    .store-name-div {
        display: table;
        width: 90%;
        margin-top: 80px;
    }
    .tip-btn {
        width: 140px;
        margin-right: 10px;
        height: 50px;
        background-color: $cp-black !important;
        border-color: $cp-black !important;
        display: inline-block;
        vertical-align: top;
    }
    .tip-denger-btn {
        width: 125px;
        margin-right: 10px;
        height: 43px;
        background-color: #dc3545 !important;
        border-color: #dc3545 !important;
        display: inline-block;
        vertical-align: top;
    }
    .tip-ok-btn {
        width: 140px;
        height: 50px;
        background-color: $cp-primary !important;
        border-color: $cp-primary !important;
        display: inline-block;
        vertical-align: top;
    }
    .tip-btn-row {
        margin-bottom: 41px;
    }
    .final-tip-amount {
        font-size: 20px;
    }
    .dollar-icon {
        margin-top: 24px;
    }
    .custom-tip-div {
        margin-top: 35px;
    }
    .purchase-power-modal-text {
        font-family: $cp-font;
        font-size: 15px;
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: center;
        line-height: 19.4px;
        color: #000000;
        margin-top: 40px;
        margin-bottom: 0px !important;
        src: local('Open Sans Semibold'), local('OpenSans-Semibold'), url(http://fonts.gstatic.com/s/opensans/v13/MTP_ySUJH_bn48VBG8sNShampu5_7CjHW5spxoeN3Vs.woff2) format('woff2');
    }
}