<template>
<div>
    <div v-if="isLoading">
        <CanPayLoader/>
    </div>
  <div class="container" style="width:95%;margin-bottom: 15px;">

    <div>
    <b-modal 
        ref="link-account-modal"
        id="link-account-modal"
        centered
        hide-footer
        hide-header
        no-close-on-backdrop
    >
        <!-- Link an account -->
        <div class="cp-detail-card cp-card-padding mt-3" v-if="modalType == 1">

                <!-- first row for logo and cross sign-->
                <div class="row">
                    <div class="col-12 text-center">
                        <a class="pull-right mr-4" @click="hideModal('link-account-modal')">
                            <svg 
                            xmlns="http://www.w3.org/2000/svg" 
                            width="15" 
                            height="15" 
                            viewBox="0 0 50 50" 
                            fill="none">
                            <path 
                            d="M48.8004 1.14962C47.2676 -0.383206 44.7351 -0.383206 43.1356 1.14962L25.0083 19.2769L6.81439 1.14962C5.28157 -0.383206 2.74908 -0.383206 1.14962 1.14962C-0.383206 2.68244 -0.383206 5.21493 1.14962 6.81439L19.3436 24.9417L1.21626 43.1356C-0.316561 44.6684 -0.316561 47.2009 1.21626 48.8004C2.01599 49.6001 3.01566 50 4.01533 50C5.01499 50 6.08131 49.6001 6.81439 48.8004L25.0083 30.6065L43.2023 48.8004C44.002 49.6001 45.0017 50 46.0013 50C47.001 50 48.0673 49.6001 48.8004 48.8004C50.3332 47.2676 50.3332 44.7351 48.8004 43.1356L30.6731 24.9417L48.867 6.74775C50.3999 5.21493 50.3999 2.68244 48.8004 1.14962Z" 
                            fill="black"/>
                            </svg>
                        </a>

                        <div>
                            <img class="cp-image-logo ml-3" v-bind:src="selected_sponsor.logo_url" v-if="selected_sponsor.logo_url != null" alt="Sponsor Logo">
                        </div>

                    </div>
                </div>   

                <div class="div-horizontal">
                    <hr class="cp-horizontal">
                </div>

                <!-- second row is for link you bank account-->
                <div class="row mr-3 mt-4 mb-4">
                    <div class="col-12 text-center cp-text3">
                        <span>Link your</span>
                        <br>
                        <span>{{selected_sponsor.retailer}} account</span>
                    </div>
                </div>

                <!-- third row is routing number, PIN and LINK button -->

                <div class="row text-center mr-2">

                    <div class="col-12 mt-1">
                        <input ref="inputAccountNumber" v-model="accountNumber" placeholder="Account Number" class="cp-input pt-3 pb-3 pl-4 pr-4" />
                    </div>

                    <div class="col-12 mt-2">
                        <input ref="inputPin" v-model="pin" placeholder="PIN" class="cp-input pt-3 pb-3 pl-4 pr-4" />
                    </div>

                    <div class="col-12 mt-4 mb-3">
                        <button class="cp-button-1" @click="linkSponsorAccounts()">LINK</button>
                    </div>

                </div>

        </div>

        <!-- Successfully connect -->
        <div class="cp-detail-card cp-card-padding1 mt-1 mb-3" v-if="modalType == 2" style="padding-bottom:0px!important;">

            <!-- first row for logo-->
            <div class="row mr-5">
                <div class="col-12 text-center">
                    <div>
                        <img class="cp-image-logo ml-4" v-bind:src="selected_sponsor.logo_url" v-if="selected_sponsor.logo_url != null" alt="Sponsor Logo">
                    </div>

                </div>
            </div>   

            <div class="div-horizontal">
                <hr class="cp-horizontal">
            </div>

            <!-- second row is for link you bank account-->
            <div class="row mr-3 mb-3" v-if="transferMessageType">

                <div class="col-12 text-center cp-text4">
                    <span>Success!</span>
                </div>

                <div class="col-12 text-center cp-text19 mt-1">
                    <span>Your {{selected_sponsor.retailer}} account is now</span>
                    <br>
                    <span>linked with CanPay</span>
                </div>
            </div>
            <div class="row mr-3 mb-3" v-else>

                <div class="col-12 text-center cp-text4">
                    <span>You have {{selected_sponsor.retailer}}</span>
                    <br>
                    <span>funds available</span>
                </div>
            </div>

            <div class="div-horizontal">
                <hr class="cp-horizontal">
            </div>

            <!-- third row is routing number and PIN -->

            <div class="row text-center mr-2">
                <!-- to show the amount -->
                <div class="col-12">
                    <span class="cp-text20 mr-3 text-center">${{amountNumberFormatter(parseFloat(show_amount).toFixed(2))}}</span>
                    <p class="cp-text6 text-center mr-3">Available Balance:</p>
                </div>
            </div>

            <div class="row">
                <div class="col-12 text-center">
                    <span class="mr-4 cp-text21">Begin transferring your balance</span>
                    <br>
                    <span class="mr-5">
                        <span class="cp-text21 ml-3">into CanPay</span>
                    </span>
                </div>
            </div>

            <div class="row">
                <div class="col-12 mt-4">
                    <button class="cp-button3" :style="disableTransferNowButton(selected_sponsor.freeze_sponsor_points)" :disabled="selected_sponsor.freeze_sponsor_points == 1" @click="clickTransferNow">TRANSFER NOW</button>
                </div>
                <div class="col-12 text-center mt-2">
                    <p class="cp-text12 line-decoration mr-5 href-style-1" @click="hideModal('link-account-modal', true)">LATER</p>
                </div>
            </div>

        </div>

        <!-- Not succesfully connect -->
        <div class="cp-detail-card cp-card-padding mt-1 ml-2" v-else-if="modalType == 3">
            <!-- first row for logo-->
            <div class="row mr-5">
                <div class="col-12 text-center">
                    <div>
                        <img class="cp-image-logo ml-4" v-bind:src="selected_sponsor.logo_url" v-if="selected_sponsor.logo_url != null" alt="Sponsor Logo">
                    </div>

                </div>
            </div>   

            <div class="div-horizontal">
                <hr class="cp-horizontal">
            </div>

             <!-- second row is for showing failed message to link account -->
            <div class="row mr-3 mb-3">

                <div class="col-12 text-center cp-text4">
                    <svg 
                    xmlns="http://www.w3.org/2000/svg" 
                    width="60" 
                    height="60" 
                    viewBox="0 0 170 170" 
                    fill="none">
                    <path 
                    d="M85 170C131.944 170 170 131.944 170 85C170 38.0558 131.944 0 85 0C38.0558 0 0 38.0558 0 85C0 131.944 38.0558 170 85 170Z" fill="#E14343"/>
                    <path 
                    opacity="0.2" d="M169.999 85C169.999 84.4568 169.989 83.9159 169.978 83.375L120.73 34.1268C113.545 26.9413 101.529 27.2664 93.9439 34.851L66.5849 62.21C59 69.7949 58.6753 81.8111 65.8607 88.9966L84.8772 108.013L80.831 112.059L48.7834 80.0115L34.8503 93.9446C27.2654 101.529 26.9407 113.546 34.1262 120.731L83.3744 169.979C83.9153 169.989 84.4562 170 84.9994 170C131.943 170 169.999 131.944 169.999 85Z" fill="black"/>
                    <path 
                    d="M135.873 49.2687L120.731 34.1268C113.546 26.9413 101.529 27.2664 93.9447 34.851L66.5857 62.21C59.0008 69.7949 58.6761 81.8111 65.8615 88.9963L75.8288 98.9635L84.9852 89.8071L75.018 79.8402C72.8817 77.7039 73.2064 73.9025 75.7421 71.3667L103.101 44.0077C105.637 41.472 109.438 41.1469 111.575 43.2836L126.717 58.4255C128.853 60.5618 128.528 64.3632 125.992 66.8989L112.059 80.832L121.216 89.9884L135.149 76.0554C142.733 68.4704 143.058 56.4542 135.873 49.2687Z" fill="white"/>
                    <path 
                    d="M135.148 76.0557L121.216 89.9878L112.06 80.8313L125.992 66.8993C128.527 64.3636 128.854 60.5625 126.717 58.4262L119.145 50.8545L128.302 41.6981L135.874 49.2698C143.058 56.4539 142.734 68.4698 135.148 76.0557Z" fill="#DADADA"/>
                    <path 
                    d="M84.9835 89.8113L82.5864 87.4142L73.43 96.5706L75.8271 98.9677L84.9835 89.8113Z" 
                    fill="#DADADA"/>
                    <path 
                    d="M104.137 81.0037L94.1702 71.0368L85.0138 80.1932L94.9807 90.1605C97.117 92.2968 96.7923 96.0982 94.2566 98.6339L66.8975 125.993C64.3618 128.529 60.5604 128.854 58.4241 126.717L43.2821 111.575C41.1458 109.439 41.4706 105.637 44.0063 103.102L57.9393 89.1687L48.7829 80.0123L34.8499 93.9453C27.2649 101.53 26.9402 113.546 34.1257 120.732L49.2677 135.874C56.4531 143.059 68.4694 142.734 76.0539 135.149L103.413 107.79C110.998 100.205 111.323 88.1889 104.137 81.0037Z" 
                    fill="white"/>
                    <path 
                    d="M103.416 107.789L76.0571 135.148C68.4716 142.734 56.4554 143.058 49.2709 135.874L41.6992 128.302L50.8556 119.145L58.4273 126.717C60.5639 128.854 64.365 128.527 66.9007 125.992L94.2598 98.6326C96.7955 96.0968 97.1195 92.2981 94.9829 90.1618L87.4113 82.5901L96.5677 73.4337L104.139 81.0054C111.324 88.1892 111 100.205 103.416 107.789Z" fill="#DADADA"/>
                    </svg>
                </div>

                <div class="col-12 text-center cp-text23 mt-2">
                    <span>Linking Your {{selected_sponsor.retailer}}</span>
                    <br>
                    <span>account Failed!</span>
                </div>

                <div class="col-12 text-center cp-text24 mt-2">
                    <span>Double check your account</span>
                    <br>
                    <span>number and PIN.</span>
                </div>
            </div>

            <div class="div-horizontal">
                <hr class="cp-horizontal">
            </div>

            <!-- third row to display support from sponsor -->

            <div class="row text-center mr-2">
                <!-- to show the amount -->
                <div class="col-12">
                    <span class="cp-text9 mr-3 text-center">{{selected_sponsor.retailer}} Support</span>
                </div>
            </div>

            <div class="row text-center">
                <div class="col-12">
                    <span class="cp-text10 text-center  ml-2 mr-5">Phone: {{formattedPhoneNumber(selected_sponsor.contact_no)}}</span>
                </div>

                <div class="col-12">
                    <span class="cp-text10 text-center  ml-2 mr-5">Email: {{selected_sponsor.contact_email}}</span>
                </div>
            </div>

            <div class="div-horizontal">
                <hr class="cp-horizontal">
            </div>

            <div class="row">
                <div class="col-5 mt-2">
                    <button class="pull-right ml-5 cp-button7" @click="hideModal('link-account-modal')">CLOSE</button>
                </div>
                <div class="col-6 mt-2">
                    <button class="pull-left pt-3 pb-3 cp-button8" @click="openTraAgainModal()">TRY AGAIN</button>
                </div>
            </div>

        </div>

        <!-- fund transfer -->
        <div class="cp-detail-card cp-card-padding mt-1 ml-2" v-else-if="modalType == 4">
            <!-- first row for logo-->
            <div class="row mr-5">
                <div class="col-12 text-center">
                    <div>
                        <img class="cp-image-logo ml-4" v-bind:src="selected_sponsor.logo_url" v-if="selected_sponsor.logo_url != null" alt="Sponsor Logo">
                    </div>

                </div>
            </div>   

            <div class="div-horizontal">
                <hr class="cp-horizontal">
            </div>

             <!-- second row is for link you bank account-->
            <div class="row mr-3 mb-3">
                <div class="col-12 text-center cp-text8">
                    <span>{{selected_sponsor.retailer}} Funds:</span>
                </div>
                <div class="col-12 text-center cp-text22 mt-1">
                    <span>${{amountNumberFormatter(parseFloat(show_amount).toFixed(2))}}</span>
                </div>
            </div>

            <!-- third row is routing number and PIN -->

            <div class="row text-center mr-2">
                <!-- to show the amount -->
                <div class="col-12">
                    <span class="cp-text7 mr-3 text-center">Are now converted to:</span>
                </div>
            </div>

            <div class="row mr-4 mt-4">
                <div class="col-12 text-center">
                    <div class="row mt-1 mr-4">
                        <div class="col-5 cp-padding1">
                            <svg 
                            xmlns="http://www.w3.org/2000/svg" 
                            width="35" 
                            height="35"
                            class="pull-right"
                            viewBox="0 0 98 100" 
                            fill="none">
                            <path   
                            d="M86.7539 64.4247C91.3797 54.5985 89.4865 45.2027 88.9507 42.7103C88.3435 39.8951 86.8074 33.2428 81.2171 27.2718C78.5545 24.466 75.3924 22.1853 71.8939 20.5477C67.1787 18.3421 63.1601 17.9118 60.9633 17.6966C51.908 16.8001 44.4244 19.4539 40.2451 21.3545C42.2799 19.3973 44.6058 17.77 47.1392 16.5311C49.3781 15.4569 51.7512 14.6909 54.1942 14.2539C58.1942 13.4865 62.2951 13.4077 66.3214 14.0208C68.1789 14.3077 75.9304 15.6525 83.664 21.6773C94.934 30.4455 99.5599 42.6206 96.9344 56.6247C92.9336 78.0522 72.4655 90.6039 51.7115 85.0811C45.6032 83.4494 39.1735 79.5943 36.3158 75.8288C36.4587 75.8288 36.6194 75.7929 36.7087 75.8467C37.4231 76.3129 38.1197 76.815 38.852 77.2812C50.4613 84.597 62.3564 84.6508 74.1801 78.106C75.6772 77.2753 77.0995 76.3154 78.4309 75.237C83.5568 71.0771 85.8966 66.2357 86.7539 64.4247Z" 
                            fill="black"/>
                            <path 
                            d="M97.9166 65.7156C97.7916 66.4687 97.5773 67.5446 97.2379 68.8177C94.7731 77.9266 89.1471 83.6824 86.7359 86.0852C77.9128 94.8893 67.643 97.4355 62.9457 98.5472C53.2832 100.824 45.4781 99.8562 42.674 99.4079C32.5828 97.7941 25.653 93.7058 23.5275 92.361C19.1914 89.6277 15.305 86.234 12.0075 82.3018C8.15635 77.7279 5.13929 72.5076 3.09511 66.8811C2.57716 65.4467 0.612503 59.8163 0.112408 52.106C-0.351965 44.7543 0.755387 39.2675 1.02329 37.9944C1.89678 33.8353 3.30779 29.8086 5.22052 26.0165C6.22071 24.08 11.311 14.6124 22.5631 7.54762C25.1707 5.89797 31.9934 2.02489 41.656 0.554551C44.21 0.16007 66.2321 -2.88819 82.0029 10.9724C89.3078 17.3917 93.6122 25.9806 94.2909 27.3613C95.3358 29.494 96.2426 31.6921 97.0057 33.942C96.7557 33.6013 94.8982 30.9834 94.8446 30.9117C87.9862 20.7089 80.0204 15.9034 80.0204 15.9034C77.0083 14.127 73.7697 12.7697 70.3936 11.869C59.6594 8.96417 50.622 11.5462 48.4787 12.1917C35.9049 15.9752 28.4571 26.0344 28.0284 26.6262C20.4377 37.1337 20.5984 48.1074 20.7413 51.1557C21.2235 61.4122 25.6887 68.6563 27.7248 71.579C27.8855 71.7763 28.1177 72.0811 28.4035 72.4397C28.6178 72.7266 28.8321 73.0135 29.0464 73.3004C34.3689 80.3294 41.4238 85.2245 49.2646 87.6631C55.1077 89.457 61.282 89.8854 67.3154 88.9158C73.3488 87.9461 79.0814 85.604 84.0747 82.0686C88.4684 78.9487 91.1296 75.7032 92.7728 73.6949C94.9518 71.0411 96.5413 68.4232 97.0593 67.3832C97.1307 67.2577 97.8809 65.7156 97.9166 65.7156Z" fill="#007EE5"/>
                            <path 
                            d="M40.4949 65.877C31.0645 65.877 26.3493 59.6191 26.3493 50.0261C26.3493 40.1102 31.3146 34.1392 41.0307 34.1392C44.7814 34.1392 47.7998 35.0358 50.1217 36.9365C52.3007 38.8909 53.5152 41.5447 53.8188 44.9157H50.0324C48.675 44.9157 47.6927 44.2881 47.1033 43.0509C46.0674 40.8454 44.0313 39.7158 41.0486 39.7158C35.226 39.7158 32.6362 43.8399 32.6362 50.044C32.6362 56.0688 35.1189 60.3184 40.8521 60.3184C44.7814 60.3184 47.0318 58.1488 47.7105 54.7419H53.801C53.2473 61.8426 48.2642 65.877 40.4949 65.877Z" fill="black"/>
                            <path 
                            d="M73.0548 34.2108H62.3563C60.5524 34.2108 59.1236 35.7887 59.1236 37.5998V65.7693H65.5534V54.5266H73.5191C79.8239 54.5266 83.1996 50.5818 83.1996 44.3597C83.1995 37.797 79.7346 34.2108 73.0548 34.2108ZM72.3225 48.8245H66.0535V39.6798H72.644C75.6445 39.6798 77.1805 41.2039 77.1805 44.2701C77.1984 47.3363 75.591 48.8783 72.3225 48.8245Z" fill="#007EE5"/>
                            </svg>
                        </div>
                        <div class="col-7 cp-padding2">
                            <span class="cp-text11 pull-left">{{ pointNumberFormatter(reward_point) }}</span>
                        </div>
                    </div>


                </div>

            </div>

            <div class="row text-center">
                <div class="col-12">
                    <span class="cp-text8 text-center  ml-2 mr-5">CanPay Points</span>
                </div>
                <div class="col-12">
                    <span class="cp-text6 text-center   mr-4">Use Points for purchase on the Pay Now screen.</span>
                </div>
            </div>

            <div class="row mt-1">
                <div class="col-12 mt-3 text-center">
                    <button class="cp-button4 pull-right" @click="hideModal('link-account-modal', true)">OK</button>
                </div>
            </div>
        </div>

        
        <!-- fund transfer error -->
        <div class="cp-detail-card cp-card-padding mt-1 ml-2" v-else-if="modalType == 5">
            <!-- first row for logo-->
            <div class="row mr-5">
                <div class="col-12 text-center">
                    <div>
                        <img class="cp-image-logo ml-4" v-bind:src="selected_sponsor.logo_url" v-if="selected_sponsor.logo_url != null" alt="Sponsor Logo">
                    </div>

                </div>
            </div>   

            <div class="div-horizontal">
                <hr class="cp-horizontal">
            </div>

             <!-- second row is for showing failed message to link account -->
            <div class="row mr-3 mb-3">

                <div class="col-12 text-center cp-text4">
                    <svg
                    version="1.1"
                    id="Layer_1"
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    x="0px"
                    y="0px"
                    width="120"
                    height="120"
                    viewBox="0 0 100 125"
                    style="enable-background: new 0 0 100 125"
                    xml:space="preserve"
                    fill="#e14343"
                    >
                    <path
                        d="M96.2,47.5l-22-38c-0.4-0.6-1-1-1.7-1h-44c-0.7,0-1.4,0.4-1.7,1l-22,37.9c-0.4,0.6-0.4,1.4,0,2l22,38.1c0.4,0.6,1,1,1.7,1
            h44c0.7,0,1.4-0.4,1.7-1l22-38C96.6,48.9,96.6,48.1,96.2,47.5z M71.3,84.5H29.7L8.8,48.4l20.8-35.9h41.7l20.8,36L71.3,84.5z
            M50.5,60.5c1.1,0,2-0.9,2-2v-30c0-1.1-0.9-2-2-2c-1.1,0-2,0.9-2,2v30C48.5,59.6,49.4,60.5,50.5,60.5z M48.4,66.4
            c-0.6,0.6-0.9,1.3-0.9,2.1c0,0.8,0.3,1.6,0.9,2.1s1.3,0.9,2.1,0.9c0.8,0,1.6-0.3,2.1-0.9c0.6-0.6,0.9-1.3,0.9-2.1
            c0-0.8-0.3-1.6-0.9-2.1C51.5,65.3,49.5,65.3,48.4,66.4z"
                    />
                    </svg>
                </div>

                <div class="col-12 text-center cp-text23 mt-2">
                    <span>{{error_message}}</span>
                </div>
            </div>

            
            <div class="div-horizontal">
                <hr class="cp-horizontal">
            </div>
            
            <div class="row mt-4">
                <div class="col-12 mt-3 text-center">
                    <button class="cp-button4 pull-right" @click="hideModal('link-account-modal', true)">OK</button>
                </div>
            </div>

        </div>

        <div class="cp-detail-card cp-card-padding mt-1 ml-2" v-else-if="modalType == 6">
            <!-- first row for logo-->
            <div class="row mr-5">
                <div class="col-12 text-center">
                    <div>
                        <img class="cp-image-logo ml-4" v-bind:src="selected_sponsor.logo_url" v-if="selected_sponsor.logo_url != null" alt="Sponsor Logo">
                    </div>

                </div>
            </div>   

            <div class="div-horizontal">
                <hr class="cp-horizontal">
            </div>

             <!-- second row is for showing failed message to link account -->
            <div class="row mr-3 mb-3">

                <div class="col-12 text-center cp-text23 mt-2">
                    <span>{{error_message}}</span>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12 mt-3 text-center">
                    <button class="cp-button4 pull-right" @click="hideModal('link-account-modal', true)">OK</button>
                </div>
            </div>

        </div>
    </b-modal>
    <!----- MODAL FOR TRANSACTION CONFIRMATION----->
    <b-modal
        ref="unlink-modal"
        hide-footer
        v-b-modal.modal-center
        modal-backdrop
        no-close-on-backdrop
        hide-header
        id="unlink-modal"
        centered
      >
      
        <div class="color" v-if="showUnlinkConfirmation">
          <div class="col-12 text-center mb-5" >
            <img class="cp-image-logo ml-3" v-bind:src="selected_sponsor.logo_url" v-if="selected_sponsor.logo_url != null" alt="Sponsor Logo">
            <hr class="cp-horizontal">
          </div>
          <div class="purchaserpower-modal-text">
            <div class="d-block text-center " style="margin: 110px 0px;">
              <label class="purchasepower-def-label " >
                <p class="unlink-confirm-text">Confirm you would like to</p>
                <p class="unlink-confirm-text">Unlink {{selected_sponsor.retailer}} from</p>
                <p class="unlink-confirm-text">your CanPay account.</p>
              </label>
            </div>
            <div class="row justify-content-center mt-5 mb-3">
              <div class="col-11 row">
                <div class="col-5" style="padding:0px;">
                  <button
                  @click="hideModal('unlink-modal')"
                  class="cp-button9"
                  >
                    <label class="mt-2">CANCEL</label>
                  </button>
                </div>

                <div class="col-7" style="padding:0px;">
                  <button
                  @click="unlinkSponsorAccount()"
                  class="cp-button10 ml-3"
                  >
                    <label class="mt-2">Confirm Unlink</label>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="color" v-else>
            <div>
                <!-- logo section -->
                <div class="col-12 text-center mb-5">
                    <img class="cp-image-logo ml-1" v-bind:src="selected_sponsor.logo_url" v-if="selected_sponsor.logo_url != null" alt="Sponsor Logo">
                    <hr class="cp-horizontal">
                </div>
                <!-- success message section -->
                <div class="text-center  purchasepower-def-label" style="margin: 120px 0px;">
                    <p class="success-unlink-text-bold mt-5" style="font-size:22px;">Success!</p>
                    <p class="unlink-confirm-success">You have Unlinked your</p>
                    <p class="unlink-confirm-success">{{selected_sponsor.retailer}} account from CanPay.</p>
             

                </div>
                <!-- Ok button -->
                <div class="mt-5 mb-2 text-center">
                    <button class="cp-button3" @click="closeUnlinkModal()">OK</button>
                </div>
            </div>
        </div>
      </b-modal>
    <!----- To show sponsor website in iframe----->
    <b-modal
        ref="sponsor-website-modal"
        id="sponsor-website-modal"
        size="xl"
        hide-footer
        no-close-on-backdrop
    >
    <div class="dashed-border iframe-sponsor-container">
        <iframe :src="getWebsiteURL(selected_sponsor.website_address)" title="Sponsor Website"></iframe>
    </div>
    </b-modal>

    <!------ To show Sponsor Detail work ----->
    <b-modal
        ref="sponsor-detail-modal"
        id="sponsor-detail-modal"
        centered
        hide-footer
        hide-header
    >
        <div class="text-center">
            <img class="cp-image-logo" v-bind:src="selected_sponsor.logo_url" v-if="selected_sponsor.logo_url != null" alt="Sponsor Logo">
            <hr class="cp-horizontal">
        </div>

        <div class="cp-text3 text-center p-1">
            The Premier Medical Cannabis Employee Benefit
        </div>

        <div class=" text-center cp-text1 mt-3">
            CannaPlan offers employers a unique opportunity to support their employees by covering medical cannabis physician consultations and medications. By educating about the advantages of cannabinoids and offering cannabis as a natural alternative to prescription drugs, we aim to reduce healthcare expenses while simultaneously boosting employee retention and productivity.
        </div>

        <div class="text-center mt-5">
            <button class="cp-button11 " @click="hideModal('sponsor-detail-modal')">OK</button>
        </div>
    </b-modal>
    <div>
        
    </div>
    </div>

    <!-- link account 1st step -->
    <div v-if="sponsorList.length > 0">
        <div class="cp-detail-card cp-card-padding mt-3" v-for="(sponsor, index) in sponsorList" :key="index">
            <div v-if="sponsor.account_lined_count !='0' && sponsor.freeze_sponsor_points == 0">
                <div class="row">
                    <div class="col-12 cp-text12 ml-2">
                        Link New Account
                    </div>
                </div>

                <div class="row mt-3">
                    <!-- logo -->
                    <div class="col-4">
                        <img class="cp-image-logo" v-bind:src="sponsor.logo_url" v-if="sponsor.logo_url != null" alt="Sponsor Logo">
                    </div>
                    <div class="col-2">
                    </div>
                    <!-- link button -->
                    <div class="col-6 text-center">
                        <button class="cp-button ml-3" @click="openLinkModal(sponsor)">LINK</button>
                    </div>
                </div>

            </div>
            <div v-else-if="sponsor.freeze_sponsor_points == 0">
                <div>
                    <span class="cp-text ml-2">Link your {{sponsor.retailer}} account</span>
                    &nbsp;
                    <span @click="showModalSponsorDetail('sponsor-detail-modal',sponsor)">
                    <svg 
                        xmlns="http://www.w3.org/2000/svg" 
                        width="15" 
                        height="15" 
                        viewBox="0 0 50 50" 
                        fill="none">
                    <path d="M25 0C20.0555 0 15.222 1.46622 11.1108 4.21326C6.99953 6.96029 3.79521 10.8648 1.90302 15.4329C0.0108321 20.0011 -0.484251 25.0277 0.480379 29.8773C1.44501 34.7268 3.82603 39.1813 7.32234 42.6777C10.8187 46.174 15.2732 48.555 20.1227 49.5196C24.9723 50.4842 29.9989 49.9892 34.5671 48.097C39.1352 46.2048 43.0397 43.0005 45.7867 38.8892C48.5338 34.778 50 29.9445 50 25C50 18.3696 47.3661 12.0107 42.6777 7.32233C37.9893 2.63392 31.6304 0 25 0ZM25.31 37.765C25.4176 37.8228 25.5379 37.853 25.66 37.853C25.7821 37.853 25.9024 37.8228 26.01 37.765C26.4419 37.4513 26.8347 37.0871 27.18 36.68C27.68 36.085 28.12 35.44 28.615 34.77L29.31 35.19C28.31 36.86 27.28 38.41 25.75 39.57C24.9463 40.21 23.9802 40.6134 22.96 40.735C20.575 41.005 19.05 39.38 19.62 37.04C20.12 34.985 20.755 32.96 21.335 30.92C21.915 28.88 22.5 26.76 23.09 24.69C23.1375 24.4856 23.1726 24.2786 23.195 24.07C23.2403 23.8955 23.2465 23.7132 23.2132 23.536C23.1799 23.3589 23.1079 23.1912 23.0024 23.0451C22.8968 22.899 22.7603 22.7779 22.6026 22.6906C22.4449 22.6033 22.2699 22.5519 22.09 22.54C21.7276 22.508 21.3638 22.4946 21 22.5C21.0062 22.3801 21.0246 22.2612 21.055 22.145L21.125 21.645L29.745 20.265L29.145 22.355L29.01 22.855C27.76 27.2217 26.505 31.6033 25.245 36C25.1164 36.3443 25.034 36.7041 25 37.07C24.9998 37.2011 25.0272 37.3309 25.0807 37.4507C25.1341 37.5704 25.2122 37.6776 25.31 37.765ZM27.38 15.5C26.9707 15.4974 26.566 15.414 26.189 15.2547C25.8121 15.0953 25.4702 14.8632 25.1832 14.5714C24.8961 14.2797 24.6694 13.9342 24.5162 13.5547C24.3629 13.1753 24.2861 12.7692 24.29 12.36C24.29 11.9522 24.3703 11.5485 24.5264 11.1718C24.6824 10.795 24.9111 10.4528 25.1994 10.1644C25.4878 9.87611 25.8301 9.64739 26.2068 9.49135C26.5835 9.33531 26.9873 9.255 27.395 9.255C27.8028 9.255 28.2065 9.33531 28.5832 9.49135C28.96 9.64739 29.3022 9.87611 29.5906 10.1644C29.8789 10.4528 30.1076 10.795 30.2636 11.1718C30.4197 11.5485 30.5 11.9522 30.5 12.36C30.4987 13.1889 30.1702 13.9838 29.586 14.5718C29.0017 15.1598 28.2089 15.4934 27.38 15.5Z" fill="#BABABA"/>
                    </svg>
                    </span>

                </div>

                <div class="mt-3 row">
                    <div class="col-4">
                        <img class="cp-image-logo" v-bind:src="sponsor.logo_url" v-if="sponsor.logo_url != null" alt="Sponsor Logo">
                    </div>
                    <div class="col-2">
                    </div>
                    <div class="col-6 text-center">
                        <button class="cp-button" @click="openLinkModal(sponsor)">LINK</button>
                    </div>
                </div>   
                <div class="div-horizontal">
                    <hr class="cp-horizontal">
                </div>

                <div class="row">
                    <div class="col-12 cp-text1">
                        <span>If you don't have a {{sponsor.retailer}} account</span>
                        <a class="cp-text2 ml-3" href="https://cannaplan.com/" target="_blank">SIGN UP</a>
                    </div>
                </div>   
            </div>
            <div v-else>
                <div class="mt-3 row">
                    <div class="col-4">
                        <img class="cp-image-logo" v-bind:src="sponsor.logo_url" v-if="sponsor.logo_url != null" alt="Sponsor Logo">
                    </div>
                    <div class="col-2">
                    </div>
                </div> 
                <div>
                    <span class="cp-text ml-2">You can't link new account of {{sponsor.retailer}} at the moment. Please contact customer support for more details.</span>
                </div>
            </div>
        </div>
    </div>
    <div class="cp-detail-card mt-3" v-if="sponsorList.length > 0">
        <div class="custom-row">
            <div :class="selectedTab == '0' ? 'col-6 text-center p-2 selected-tab':'col-6 text-center p-2 not-selected-tab'" @click="selectedTab = 0">
                Linked
            </div>
            <div :class="selectedTab == '1' ? 'col-6 text-center p-2 selected-tab':'col-6 text-center p-2 not-selected-tab'" @click="selectedTab = 1">
                Unlinked
            </div>
        </div>
    </div>
    <div v-if="linkedSponsorAccounts.length > 0">
        <div class="cp-detail-card mt-3" v-for="(linkedSponsorAccount, index) in linkedSponsorAccounts" :key="index">
            <!-- first row for logo, linked account  and unlink account -->
            <div class="row pt-1">
                <div class="col-6 mt-2">
                        <p class="mt-3 ml-4 cp-text12">
                          Account Number: {{ linkedSponsorAccount.account_no }}
                        </p>
                </div>

                <div class="col-6 mt-2">
                    <div class="row ">
                        <div class="col-12">
                            <img class="cp-image-logo pull-right mr-3" v-bind:src="linkedSponsorAccount.logo_url" v-if="linkedSponsorAccount.logo_url != null" alt="Sponsor Logo">
                        </div>
                        <div class="col-12">
                            <a v-if="linkedSponsorAccount.account_status == 'linked'" style="color: #000000 !important" class="cp-text13 pull-right mr-4" @click="confirmUnlink(linkedSponsorAccount);">UNLINK ACCOUNT</a>   
                        </div>

                    </div>
               
                </div>
                
            </div>

             <!-- second row is for contact info -->
            <div class="row mr-3 mb-3">

                <div class="col-12">
                    <span class="cp-text14 ml-4">
                        {{linkedSponsorAccount.retailer}} Support
                    </span>
                </div>

                <div class="col-12">
                    <span class="cp-text15 ml-4">
                        Phone: {{formattedPhoneNumber(linkedSponsorAccount.contact_no)}}
                    </span>
                </div>

                <div class="col-12">
                    <span class="cp-text15 ml-4">
                        Email: {{linkedSponsorAccount.contact_email}}
                    </span>
                </div>

                <div class="col-12">
                    <span class="cp-text15 ml-4">
                        Website: <span @click="showWebsiteModal('sponsor-website-modal',linkedSponsorAccount)" class="show-cursor" style="color:blue;">{{linkedSponsorAccount.website_address}}</span>
                    </span>
                </div>
            </div>

            <!-- third row is used for life time benefit -->

            <div class="row cp-bg-greyshade cp-m-at">
                <div class="col-12 ml-2 pt-2 pb-2">
                    <div class="row">
                        <div class="col-5">
                            <span class="cp-text18">Lifetime Benefit</span>
                        </div>
                        <div class="col-7">
                            <span class="cp-text16 pull-right mr-3">${{amountNumberFormatter(parseFloat(linkedSponsorAccount.lifetime_reward_amount).toFixed(2))}}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Forth row is used for benefit used -->
            <div class="row cp-m-at">
                <div class="col-12 ml-2 pt-2 pb-2">
                    <div class="row">
                        <div class="col-5">
                            <span class="cp-text18">Benefit Used</span>
                        </div>
                        <div class="col-7">
                            <span class="cp-text16 pull-right mr-3">${{amountNumberFormatter(parseFloat(linkedSponsorAccount.benifit_used_amount).toFixed(2))}}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Fifth row is used for Available Benefit -->
            <div class="row cp-bg-greyshade cp-m-at">
                <div class="col-12 ml-2 pt-2 pb-2">
                    <div class="row">
                        <div class="col-5">
                            <span class="cp-text18">Available Benefit</span>
                        </div>
                        <div class="col-7">
                            <div class="row">
                                <div class="col-12">
                                    <span class="cp-text16 pull-right mr-3">${{amountNumberFormatter(parseFloat(linkedSponsorAccount.lifetime_reward_amount).toFixed(2) - parseFloat(linkedSponsorAccount.benifit_used_amount).toFixed(2))}}</span>
                                </div>
                                <div class="col-12">
                                    <span class="pull-right mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" 
                                    width="15" 
                                    height="15" 
                                    viewBox="0 0 38 39" 
                                    fill="none">
                                        <path d="M33.6679 25.1256C35.4631 21.2934 34.7284 17.6291 34.5205 16.657C34.2848 15.5591 33.6887 12.9647 31.5192 10.636C30.4859 9.54173 29.2587 8.65228 27.901 8.01358C26.0711 7.15344 24.5115 6.9856 23.659 6.90169C20.1447 6.55203 17.2405 7.58701 15.6185 8.32827C16.4082 7.56496 17.3109 6.93032 18.294 6.44714C19.1629 6.02817 20.0839 5.72944 21.032 5.55902C22.5843 5.25973 24.1758 5.22899 25.7384 5.46811C26.4592 5.58 29.4675 6.10448 32.4688 8.45415C36.8425 11.8738 38.6377 16.6221 37.6188 22.0836C36.0662 30.4404 28.1228 35.3355 20.0685 33.1816C17.6979 32.5453 15.2026 31.0418 14.0936 29.5732C14.1491 29.5732 14.2114 29.5592 14.2461 29.5802C14.5234 29.762 14.7937 29.9578 15.0779 30.1397C19.5833 32.9928 24.1996 33.0138 28.7882 30.4613C29.3692 30.1374 29.9212 29.763 30.4379 29.3424C32.4272 27.7201 33.3352 25.8319 33.6679 25.1256Z" fill="black"/>
                                        <path d="M38 25.6291C37.9515 25.9228 37.8683 26.3424 37.7366 26.8389C36.7801 30.3914 34.5967 32.6362 33.6609 33.5732C30.2368 37.0068 26.2513 37.9998 24.4283 38.4334C20.6784 39.3215 17.6494 38.9439 16.5612 38.7691C12.6449 38.1397 9.95553 36.5453 9.1307 36.0208C7.4479 34.9548 5.93963 33.6313 4.65994 32.0977C3.16536 30.3139 1.99448 28.278 1.20117 26.0836C1.00016 25.5242 0.237703 23.3284 0.0436239 20.3214C-0.136593 17.4542 0.293154 15.3143 0.397126 14.8178C0.736111 13.1958 1.2837 11.6253 2.02601 10.1464C2.41416 9.39118 4.38962 5.69884 8.7564 2.94357C9.76838 2.30021 12.4162 0.789707 16.1661 0.216275C17.1573 0.0624272 25.7037 -1.1264 31.8241 4.27925C34.6591 6.78277 36.3295 10.1324 36.5929 10.6709C36.9984 11.5027 37.3503 12.3599 37.6465 13.2374C37.5495 13.1045 36.8286 12.0835 36.8078 12.0555C34.1461 8.07649 31.0547 6.20234 31.0547 6.20234C29.8858 5.50952 28.629 4.9802 27.3187 4.6289C23.1529 3.49603 19.6456 4.50303 18.8139 4.75478C13.9342 6.23032 11.0438 10.1534 10.8774 10.3842C7.93156 14.4821 7.99395 18.7619 8.0494 19.9507C8.23654 23.9508 9.9694 26.776 10.7596 27.9158C10.822 27.9928 10.9121 28.1116 11.023 28.2515C11.1061 28.3634 11.1893 28.4753 11.2725 28.5872C13.3381 31.3284 16.076 33.2376 19.1189 34.1886C21.3865 34.8882 23.7827 35.0553 26.1241 34.6772C28.4656 34.299 30.6903 33.3856 32.6282 32.0068C34.3333 30.79 35.3661 29.5242 36.0037 28.741C36.8494 27.706 37.4663 26.685 37.6673 26.2795C37.695 26.2305 37.9861 25.6291 38 25.6291Z" fill="#007EE5"/>
                                        <path d="M15.7155 25.692C12.0557 25.692 10.2258 23.2515 10.2258 19.5102C10.2258 15.643 12.1527 13.3143 15.9234 13.3143C17.379 13.3143 18.5504 13.664 19.4515 14.4052C20.2971 15.1675 20.7685 16.2024 20.8863 17.5171H19.4168C18.8901 17.5171 18.5088 17.2724 18.2801 16.7899C17.8781 15.9297 17.0879 15.4891 15.9303 15.4891C13.6707 15.4891 12.6656 17.0976 12.6656 19.5172C12.6656 21.8668 13.6291 23.5242 15.8541 23.5242C17.379 23.5242 18.2524 22.678 18.5158 21.3493H20.8794C20.6645 24.1186 18.7306 25.692 15.7155 25.692Z" fill="black"/>
                                        <path d="M28.3515 13.3422H24.1996C23.4995 13.3422 22.945 13.9576 22.945 14.6639V25.65H25.4403V21.2654H28.5317C30.9785 21.2654 32.2885 19.7269 32.2885 17.3003C32.2885 14.7408 30.9438 13.3422 28.3515 13.3422ZM28.0673 19.0416H25.6344V15.4751H28.1921C29.3565 15.4751 29.9526 16.0695 29.9526 17.2653C29.9596 18.4611 29.3357 19.0626 28.0673 19.0416Z" fill="#007EE5"/>
                                    </svg>
                                    <span class="ml-1 cp-text17">{{pointNumberFormatter(parseFloat(linkedSponsorAccount.lifetime_reward_point).toFixed(2) - parseFloat(linkedSponsorAccount.benifit_used_point).toFixed(2))}}</span>
                                    </span>

                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        
            <!-- sixth row to show fund message -->
            <div v-if="linkedSponsorAccount.available_reward_amount > 0">
                <div class="row cp-m-at mt-3 cp-text5">
                    <div class="col-12 ml-2">
                        <span class="mr-2">
                            You have <b>${{amountNumberFormatter(parseFloat(linkedSponsorAccount.available_reward_amount).toFixed(2))}}</b> in new {{linkedSponsorAccount.retailer}} funds available
                        </span>

                    </div>
                </div>

                <!-- seventh row to transfer fund -->
                <div class="row" v-if="linkedSponsorAccount.account_status == 'linked'">
                    <div class="col-12 text-center mt-3">
                        <button class="cp-button5 pt-3 pb-3" :style="disableTransferNowButton(linkedSponsorAccount.freeze_sponsor_points)" :disabled="linkedSponsorAccount.freeze_sponsor_points == 1" @click="clickTransferNowWithId(linkedSponsorAccount.sponsor_link_id)">TRANSFER ${{amountNumberFormatter(parseFloat(linkedSponsorAccount.available_reward_amount).toFixed(2))}} NOW</button>
                    </div>
                </div>

            </div>

            <!-- eight row to benifit history -->
            <div class="row">
                <div class="col-12 text-center">
                    <a class="cp-text25 line-decoration mt-3" style="color: #000000 !important" @click="getSponsorTransactionHistory(linkedSponsorAccount)">
                        See Benefit Usage History
                    </a>
                </div>
            </div>
        <br>
        </div>
    </div>
    <div v-if="linkedSponsorAccounts.length == 0 && !isLoading" class="cp-detail-card mt-3 text-center cp-text6">
        <p class="pt-2 pb-2">No {{selectedTab == 0 ? 'Linked':'Unlinked'}} account found</p>
    </div>
  </div>
</div>
</template>

<script>
import moment from "moment";
import Loading from "vue-loading-overlay";
import sponsorapi from "../../api/sponsor.js";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  name: "linkaccount",
  components: {
    Loading,
    CanPayLoader
  },
  data() {
    let self = this;
    return {
      selectedTab:0,
      isLoading: false,
      completeLinkedAccount:true,
      sponsorList:[],
      linkedSponsorAccounts:[],
      selected_sponsor:'',
      showUnlinkConfirmation:true,
      modalType: 0,
      accountNumber: '',
      pin: '',
      show_amount: 0,
      reward_point: 0,
      transferMessageType: 0,
      sponsor_link_id: '',
      error_message: '',
    };
  },
  created() {
    var self = this;
    self.getSponsorList();
    self.getLinkedSponsorAccounts();
  },
    watch: {
        selectedTab(newValue, oldValue) {
            
            this.getLinkedSponsorAccounts();
        }
    },
  methods: {
    showModalSponsorDetail(modal, sponsor){
        let self = this;
        self.showModal(modal);
        self.selected_sponsor = sponsor;
    },
    getWebsiteURL(website){
        return 'https://'+website;
    },
    closeUnlinkModal(){
        let self = this;
        self.hideModal('unlink-modal');
    },
    closeWebsiteModal(modal){
        this.$refs[modal].hide();
    },
    getSponsorTransactionHistory(account){
        let self = this;
        var encryptedId = btoa(account.sponsor_link_id);
        // Construct the URL with encryptedId
        const url = `/sponsortransactionhistory?sponsor=${encodeURIComponent(encryptedId)}`;
        self.$router.push(url);
        localStorage.setItem('sponsor_account_no', account.account_no);
    },
    showModal(modal) {
      this.$refs[modal].show();
    },
    showWebsiteModal(modal, sponsorSelected){
        let self = this;  
        self.$refs[modal].show();
        self.selected_sponsor = sponsorSelected;
    },
    hideModal(modal, load_data = false) {
      this.$refs[modal].hide();
      this.accountNumber = '';
      this.modalType = 0;
      this.pin = '';
      if (load_data) {
        this.getSponsorList();
        this.getLinkedSponsorAccounts();
      }
    },
    formattedPhoneNumber(phone) {
      // Check if the phoneNumber has a length of 10
      if (phone.length === 10) {
        // Format the phone number (###) ###-####
        return `(${phone.substring(0, 3)}) ${phone.substring(3, 6)}-${phone.substring(6)}`;
      }
      // If the phone number doesn't have a length of 10, return the original value
      return phone;
    },
    // get total sponsor
    getSponsorList() {
      var self = this;
      self.isLoading = true;
      sponsorapi
        .sponsorList()
        .then(function (response) {
            self.sponsorList = response.data;
            self.isLoading = false;
        })
        .catch(function (err) {
            console.log(err);
            self.isLoading = false;
        });
    },
    // get total linked sponsor
    getLinkedSponsorAccounts() {
      var self = this;
      const paylaod = {
        is_linked: self.selectedTab == 0?1:0
      }
      self.isLoading = true;
      sponsorapi
        .linkedSponsorAccounts(paylaod)
        .then(function (response) {
            self.linkedSponsorAccounts = response.data;
            self.isLoading = false;
        })
        .catch(function (err) {
            console.log(err);
            self.isLoading = false;
        });
    },
    // Open ne link modal
    openLinkModal(selected_sponsor) {
      var self = this;
      self.selected_sponsor = selected_sponsor;
      self.modalType = 1;
      self.showModal("link-account-modal");
    },
    openTraAgainModal() {
      var self = this;
      self.modalType = 1;
    },
    // Open ne link modal
    saveSponsorAccounts() {
      var self = this;
      self.isLoading = true;
      var request = {
        corporate_parent_id: self.selected_sponsor.corporate_parent_id,
        merchant_id: self.selected_sponsor.merchant_id,
        account_no: self.accountNumber,
        pin: self.pin,
      };
      sponsorapi
      .linkSponsorAccounts(request)
      .then(function (response) {
        self.show_amount = response.data.balance;
        self.sponsor_link_id = response.data.sponsor_link_id;
        self.modalType = 2;
        self.transferMessageType = 1;
        self.isLoading = false;
      })
      .catch(function (err) {
        console.log(err);
        if (err.response.data.code == 599) {
            self.modalType = 3;
        }
        else{
            self.error_message = err.response.data.message;
            self.modalType = 5;
        }
        self.isLoading = false;
      });
    },
    clickTransferNowWithId(sponsor_link_id){
        var self = this;
        self.sponsor_link_id = sponsor_link_id;
        self.clickTransferNow();
    },
    clickTransferNow(){
      var self = this;
      self.isLoading = true;
      var request = {
        sponsor_link_id: self.sponsor_link_id
      };
      sponsorapi
      .transferSponsorFunds(request)
      .then(function (response) {
        self.show_amount = response.data.reward_amount;
        self.reward_point = response.data.reward_point;
        self.modalType = 4;
        self.showModal('link-account-modal');
        self.getSponsorList();
        self.getLinkedSponsorAccounts();
        self.isLoading = false;
      })
      .catch(function (err) {
          console.log(err);
          self.error_message = err.response.data.message;
          self.modalType = 5;
          self.isLoading = false;
          self.showModal('link-account-modal');
      });
    },
    // store sponsor link
    linkSponsorAccounts() {
      var self = this;
      if (self.accountNumber != '' && self.pin != '') {
        self.saveSponsorAccounts();
      } else if (self.accountNumber == '') {
        this.$refs.inputAccountNumber.focus();
      } else if (self.pin == '') {
        this.$refs.inputPin.focus();
      }
    },
    confirmUnlink(linkedSponsorAccount){
        var self = this;
        self.sponsor_link_id = linkedSponsorAccount.sponsor_link_id;
        self.selected_sponsor = linkedSponsorAccount;
        self.modalType = 6;
        self.showUnlinkConfirmation = true;
        self.showModal("unlink-modal");
    },
    unlinkSponsorAccount() {
        var self = this;
        self.showUnlinkConfirmation = true;
        self.isLoading = true;
        var request = {
            sponsor_account_id: self.sponsor_link_id
        };
        sponsorapi
        .unlinkSponsorAccounts(request)
        .then(function (response) {
            self.showUnlinkConfirmation = false;
            self.getLinkedSponsorAccounts();
            self.isLoading = false;
        })
        .catch(function (err) {
            console.log(err);
            self.isLoading = false;
        });
    },
    disableTransferNowButton(isDisable){
        // Define your default styles here
        let styles = {};

        // Add styles conditionally based on the disabled state
        if (isDisable == 1) {
            styles = {
                backgroundColor: '#ccc', // Change the background color
                color: '#666', // Change the text color
                cursor: 'not-allowed', // Change the cursor to indicate the button is not clickable
                // Add any other styles you want to customize for the disabled state
            };
            return styles;
        }
    }
  },
  mounted() {
    this.$root.$emit("changeWhiteBackground", [false, false, "LinAnAccountHeader"]);
    var element = document.getElementsByClassName("content-wrap");
    if (element[0]) {
        element[0].style.setProperty("background-color", "#149240");
        element[0].style.height = "114vh";
        if(window.innerWidth>1200){
        element[0].style.height = "121vh";
        }
    }
  },
};
</script>

<style lang="scss">
.line-decoration{
    text-decoration: underline;
}
.cp-detail-card{
    background: white;
    border-radius: 7px;
    text-align: left;
}
.cp-detail-tab-card{
    background: white;
    border-radius: 4px;
    text-align: left;  
}
.cp-text{
    font-weight:600;
    font-size:14px;
}
.cp-text1{
    font-weight:700;
    font-size:11px;
    font-family: "Open Sans";
}
.cp-text2{
    font-weight:bolder;
    font-size:12px;
    color:#1B9142;
    font-family: "Montserrat";
}
.cp-text2:hover{
    font-weight:bolder;
    text-decoration:none;
    font-size:12px;
    color:#1B9142;
    font-family: "Montserrat";
}
.cp-text3{
    font-weight:700;
    font-family:'Open Sans';
    font-size:19px;
    line-height: 2rem; 
}
.cp-text5{
    font-weight:400;
    font-size:15px; 
    font-family:'Open Sans';
}
.cp-text4{
    font-weight:700;
    font-size:22px; 
    font-family:'Open Sans';
}
.cp-text6{
    font-size:12px;
    font-weight:700;
}
.cp-text7{
    font-size:17px;
    font-weight:600;
}
.cp-text8{
    font-size:19px;
    font-weight:700;
}
.cp-text9{
    font-size: 17px;
    font-weight: bolder;
}
.cp-text10{
    font-weight:500;
    font-size:16px;
}
.cp-text11{
    font-weight:bolder;
    font-size:1.7rem;
    color:#007EE5;
    font-family:'Montserrat';
    white-space: nowrap;
}
.cp-text12{
    font-weight:700;
    font-size:14px;
    font-family:'Open Sans';
}
.cp-text13{ 
    font-weight:700;
    font-size:11px;
    text-decoration: underline;
}
.cp-text14{
    font-weight:800;
    font-size:12px;
    font-family:'Open Sans';
    font-style: normal;
}
.cp-text15{
    font-size:12px;
    font-weight:600;
    font-style: normal;
}
.cp-text16{
    font-size:15px;
    font-weight:900;
    font-weight: 'Montserrat';
}
.cp-text17{
    font-weight:bolder;
    font-size:12px;
    color:#007EE5;
    font-family:'Montserrat';
}
.cp-text18{
    font-size:14px;
    font-weight:700;
    font-style: normal;
    white-space:nowrap;
}
.cp-text19{
    font-weight:600;
    font-size:15.5px;
}
.cp-text20{
    font-weight:700;
    font-size:28px; 
    font-family:'Montserrat';
}
.cp-text21{
    font-size:15px;
    font-weight:500;
}
.cp-text22{
    font-size:34px;
    font-weight:700;
    font-family: "Montserrat";
}
.cp-text23{
    font-size:25px;
    font-weight:700;
    font-family: "Open Sans";
}
.cp-text24{
    font-weight:600;
    font-size:18px;
    font-family: "Open Sans";
}
.cp-text25{
    font-weight:500;
    font-size:14px;
    font-family:'Open Sans';
}
.txt-left-padding {
    margin-left:10px;
}
.cp-image-logo{
    width:130px;
    padding:4px 0px 4px 0px;
    height:46px;
}
.cp-button{
    background-color:#000;
    border-radius:60px;
    padding: 13px 33px;
    border:none;
    color:#fff;
    font-weight:500;
    font-size:11px;
    font-family: 'Montserrat';
}
.cp-button-1{
    background-color:#000;
    border-radius:60px;
    color:#fff;
    border:none;
    font-weight:600;
    font-size:16px;
    width:100%;
    padding:15px 95px;
    font-family: 'Montserrat';
}
.cp-button2{
    background-color:#CCC;
    border-radius:60px;
    border:none;
    color:#000;
    font-weight:bold;
    padding:16px 13px;
    margin-right:3%;
    font-size:13px;
    width:30%;
    font-family: 'Montserrat';
}
.cp-button3{
    background-color:#1B9142;
    border-radius:60px;
    border:none;
    color:#fff;
    font-weight:600;
    padding:16px 10px;
    font-size:13px;
    width:93%; 
    font-family:'Montserrat'; 
}
.cp-button4{
    background-color:#1B9142;
    border-radius:60px;
    border:none;
    color:#fff;
    padding: 15px 80px;
    margin-right: 8%;
    font-weight:bolder;
    font-size:18px;
    width:90%; 
    font-family: 'Montserrat';
}
.cp-button5{
    background-color:#1B9142;
    border-radius:60px;
    border:none;
    color:#fff;
    font-weight:bolder;
    font-size:15px;
    width:90%; 
    font-family: 'Montserrat';
}
.cp-button6{
    background-color:#000;
    border-radius:60px;
    padding: 16px 35px;
    color:#fff;
    border:none;
    font-weight:bolder;
    font-size:12px;
    width:70%;
    font-family: 'Montserrat';
}
.cp-button7{
    background-color:#CCC;
    border-radius:60px;
    border:none;
    color:#000;
    font-weight:bolder;
    font-size:15px;
    padding:16px 10px;
    width:100%;
    font-family: 'Montserrat';
}
.cp-button8{
    background-color:#1B9142;
    border-radius:60px;
    border:none;
    color:#fff;
    font-weight:600;
    padding:16px 10px;
    font-size:15px;
    width:100%; 
    font-family:'Montserrat'; 
}
.cp-button9{
    background-color:#CCC;
    border-radius:60px;
    border:none;
    color:#000;
    font-weight:bolder;
    padding:10px 10px;
    font-size:13px;
    width:100%; 
    font-family:'Montserrat'; 
}
.cp-button10{
    background-color:#1B9142;
    border-radius:60px;
    border:none;
    color:#fff;
    font-weight:600;
    padding:10px 10px;
    font-size:13px;
    width:93%; 
    font-family:'Montserrat'; 
}
.cp-button11{
    background-color:#000;
    border-radius:60px;
    padding: 16px 35px;
    color:#fff;
    border:none;
    font-weight:bolder;
    font-size:12px;
    width:100%;
    font-family: 'Montserrat';
}
.cp-horizontal{
    border: 0.1px solid #dedcdc;
}
.div-horizontal{
    margin-right:30px;
}
.cp-input{
    background-color:#EAEAEA!important;
    border:none;
    border-radius:7px;
    width:100%;
}
.cp-input::placeholder{
    font-weight:500;
    font-size:15px;
    font-family:'Open Sans';
}
.cp-card-padding{
    padding:20px 0px 20px 20px;
}
.cp-card-padding1{
    padding:20px 0px 20px 28px;  
}
.cp-bg-greyshade{
    background-color:#ECECEC;
}
.cp-m-at{
    margin:auto;
}
#link-account-modal___BV_modal_body_{
  background-color: #ffffff;
  border-radius: 12px;
  padding:0px;
}
.cp-padding1{
    padding-right:0px!important;
}
.cp-padding2{
     padding-left:6px!important;
}
#unlink-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#unlink-modal___BV_modal_content_{
  background-color: transparent!important;
}
#sponsor-website-modal___BV_modal_body_ {
  background-color: #ffffff;
  margin: 0px;
  height:82vh;
  overflow-y: hidden;
  padding:0px;
  border: 0;
}
#sponsor-detail-modal___BV_modal_body_{
  background-color: #ffffff;
  padding:16px;
}
#sponsor-detail-modal___BV_modal_content_{
    padding:16px;
    background-color: transparent!important;
}
#sponsor-website-modal___BV_modal_header_{
  background-color: #ffffff;
  padding:5px;
  border: 0;
}
#sponsor-website-modal___BV_modal_content_{
  background-color: transparent!important;
  margin: 0px;
  padding:0px;
  border: 0;
}
.tab-color-class{
    color:green;
}
.unlink-confirm-text{
    margin:0px;
    font-size:1.35rem;
}
.unlink-confirm-success{
    margin:0px;
    font-size:1.4rem!important;
}
.success-unlink-text-bold{
    font-weight:bolder;
    font-family:'Montserrat';
}
.iframe-sponsor-container iframe {
  width: 100vw;
  height: 100vh;
  border: 0;
  margin:0px;
}
.iframe-sponsor-container {
  width: 100vw;
  height: 92vh;
  border: 0;
}
.show-cursor{
    cursor:pointer;
}
.selected-tab{
    color: #fff;
    background-color: #000; 
    font-weight:700;
    font-size:14px;
    font-family:'Open Sans';
    border-radius:6px;
}
.not-selected-tab{
    color: #000;
    background-color: #fff; 
    font-weight:700;
    font-size:14px;
    font-family:'Open Sans';
    border-radius:6px;
}
.custom-row{
    display:flex;
    justify-content:center;
}
@media only screen and ( min-width:280px) and ( max-width:700px){
    .unlink-confirm-text{
        margin:0px;
        font-size:1.2rem;
    }
    .unlink-confirm-success{
        margin:0px;
        font-size:1.2rem!important;
    }

}
@media only screen and ( min-width:320px) and ( max-width:700px){
    .unlink-confirm-text{
        margin:0px;
        font-size:1.15rem;
    }
    .unlink-confirm-success{
        margin:0px;
        font-size:1.05rem!important;
    }
}
@media only screen and ( min-width:376px) and ( max-width:800px){
    .unlink-confirm-text{
        margin:0px;
        font-size:1.25rem;
    }
    .unlink-confirm-success{
        margin:0px;
        font-size:1.07rem!important;
    }
}
@media only screen and ( min-width:500px) and ( max-width:1000px){
    .unlink-confirm-text{
        margin:0px;
        font-size:1.35rem;
    }
    .unlink-confirm-success{
        margin:0px;
        font-size:1.30rem!important;
    }
}
</style>
