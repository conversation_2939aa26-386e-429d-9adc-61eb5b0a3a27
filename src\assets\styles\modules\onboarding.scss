.canpay-logo {
  height: 7rem !important;
  width: 7rem !important;
}
.onboarding-upgrage-modal {
  margin-left: -0.29rem !important;
}
.next-button-color-onboardingsingin {
  background-color: $cp-input-email !important;
  padding: 1.2rem;
  padding-left: 1rem;
  padding-right: 1rem;
}
.btn-reg-dropdown-onboard-style {
  color: $cp-black !important;
  width: 100% !important;
  background-color: $cp-white !important;
  height: 50px !important;
  border-radius: 6px !important;
  border: solid #ced4da;
  border-width: 1px 1px 1px 1px;
}
//checkbox

.squaredOne {
  width: 1.75rem * 0.7;
  height: 1.75rem * 0.7;
  position: relative;
  margin: 0.375rem auto;
  background: #d3d2d1;
  border-radius: 0.2rem;

  label {
    width: 1.25rem * 0.7;
    height: 1.25rem * 0.7;
    position: absolute;
    top: 0.25rem;
    left: 0.25rem;
    cursor: pointer;
    background: #d3d2d1;
    border-radius: 0.2rem;
    &:after {
      content: "";
      width: 1.25rem * 0.65;
      height: 1.25rem * 0.65;
      position: absolute;
      top: -0.5px;
      left: -0.5px;
      background: $cp-primary;
      opacity: 0;
      border-radius: 0.2rem;
    }
  }
  input[type="checkbox"] {
    visibility: hidden;
    &:checked + label:after {
      opacity: 1;
    }
  }
}

.bank-phone-image {
  margin-top: 0.5rem;
  height: 4rem;
  width: 4rem;
}
.canpay-logo-onboarding-header {
  height: 7rem !important;
  width: 7rem !important;
}
.canpay-logo-position {
  text-align: left;
  margin-left: 1.7rem !important;
}

.terms-checkbox-onboard-position {
  font-size: 0.9rem !important;
}
.btn-next-checkbox {
  background-color: $cp-primary !important;
  margin-bottom: 2rem !important;
  margin-top: 1rem !important;
}
.terms-and-condition-check-box {
  margin-top: 5rem !important;
}
.canpay-logo-header-position {
  text-align: center;
  margin-left: 16rem !important;
}

.onboarding-back-btn-postion {
  margin-left: -5.65rem;
  margin-top: 2.37rem;
}
.upgrade-image {
  height: 7rem !important;
  width: 7rem !important;
  margin: 2.5rem !important;
}

.mobile-verification-code {
  height: 7rem !important;
  width: 5.9rem !important;
  margin-top: 2.1rem !important;
  margin-bottom: 2.5rem !important;
  margin-left: 1rem;
}

.mobile-verification-code-label {
  margin-top: -0.9rem !important;
  font-size: 1.28rem !important;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: $cp-black;
}
.upgrade-to-the-new-CanPay {
  font-size: 1.4rem !important;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: $cp-black;
}

.as-an-existing-customer-there-are-just-a-few-easy-steps-to-get {
  margin-top: 0.3rem !important;
  font-size: 0.96rem !important;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: $cp-black;
  padding-left: 0rem !important;
  padding-right: 0rem !important;
}

.enter-the-email-you-use-to-sign-in-to-CanPay {
  font-size: 0.8rem !important;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: $cp-black;
}

.btn-get-started {
  background-color: $cp-primary !important;
  margin-bottom: 2rem !important;
}

.input-box-row-get-started {
  margin-top: 0.5rem !important;
  margin-left: 0.938rem !important;
  margin-right: 0.938rem !important;
}

.email-address {
  background-color: $cp-input-email !important;
  color: $cp-black !important;
}

.onboarding-success-modal-image {
  height: 6rem;
  width: 6rem;
  margin-top: 0rem;
  text-align: center !important;
  margin-left: 1rem;
}

.onboarding-success-popup-style {
  font-family: "Open Sans";
  font-size: 1rem;
  font-weight: 600;
  color: $cp-black;
}

.onboarding-terms-and-condition-header {
  padding-top: 0.5rem;
  font-size: 1.8rem;
  font-weight: bold;
  line-height: 1.4;
  text-align: left;
}
.onboarding-terms-and-condition-sub-header {
  padding-top: 0.9rem;
  color: $cp-secondary !important;
  text-align: left;
  font-size: 1rem;
  font-weight: bold !important;
  line-height: 1.4;
}

.onboarding-terms-and-condition-pragrapgh {
  padding-top: 0.5rem;
  text-align: left;
  font-size: 0.9rem;
  font-family: inherit;
}
.onboarding-terms-and-condition-header-two {
  padding-top: 2rem;
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.4;
  text-align: left;
}

.onboarding-terms-and-condition-sub-two {
  padding-top: 1.1rem;
  text-align: left;
  font-size: 0.9rem;
  font-weight: bold !important;
  line-height: 1.4;
}

.onboarding-terms-and-condition-sub-two span {
  font-weight: lighter !important;
}

.onboarding-terms-and-condition-sub-three
{
  padding-top: 0.5rem;
  text-align: left;
  font-size: 0.9rem;
  font-family: inherit
}
.onboarding-terms-and-condition-ancher
{
  color: $cp-secondary !important;
  text-decoration: underline;
}
.onboarding-terms-and-condition-ancher-color-white
{
  color: $cp-white !important;
  text-decoration: underline;
}
.onboarding-terms-and-condition-undeline
{

  text-decoration: underline;
}

.onboarding-terms-and-condition-ol {
  margin-left: -1.4rem;
}
.email-address::placeholder {
  color: $cp-black;
  opacity: 1;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
}
.email-address::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: $cp-black !important;
  font-family: "Open Sans" !important;
  font-size: 14px !important;
  font-weight: normal !important;
  font-stretch: normal !important;
  font-style: normal !important;
  line-height: normal !important;
  letter-spacing: normal !important;
  text-align: left !important;
}
.email-address::-moz-placeholder {
  /* Firefox 19+ */
  color: $cp-black !important;
  font-family: "Open Sans" !important;
  font-size: 0.875rem !important;
  font-weight: normal !important;
  font-stretch: normal !important;
  font-style: normal !important;
  line-height: normal !important;
  letter-spacing: normal !important;
  text-align: left !important;
}
.email-address:-ms-input-placeholder {
  /* IE 10+ */
  color: $cp-black !important;
  font-family: "Open Sans" !important;
  font-size: 0.875rem !important;
  font-weight: normal !important;
  font-stretch: normal !important;
  font-style: normal !important;
  line-height: normal !important;
  letter-spacing: normal !important;
  text-align: left !important;
}
.email-address:-moz-placeholder {
  /* Firefox 18- */
  color: $cp-black !important;
  font-family: "Open Sans" !important;
  font-size: 0.875rem !important;
  font-weight: normal !important;
  font-stretch: normal !important;
  font-style: normal !important;
  line-height: normal !important;
  letter-spacing: normal !important;
  text-align: left !important;
}

.checkbox-onboard {
  label {
    margin-right: 8px;
  }
}

.terms-checkbox-onboard {
  color: $cp-lightgreen;
  cursor: pointer;

}
.onboard-checkbox:checked:after {
  color: $cp-primary;
}

.canpay-logo-onboard {
  height: 7.5rem;
  width: 7.5rem;
  margin-top: 1.7rem;
}

.text-bold {
  font-weight: 600;
}
//onboardingsingin

.onboard-btn-sign-in {
  background-color: $cp-primary !important;
}

.canpay-logo-onboard {
  height: 7.5rem;
  width: 7.5rem;
  margin-top: 1.7rem;
}
.canpay-moble-screen {
  height: 15rem;
  width: 15rem;
  margin-top: 2.5rem;
}

.get-the-canpay-app-label {
  font-size: 1.4rem !important;
  font-weight: 700;
}
.get-the-canpay-app-description-label {
  font-size: 1rem !important;
  font-weight: 400;
}

.back-btn {
  height: 2.7rem;
  width: 2.7rem;
}

.back-btn-onboarding-header {
  height: 2.7rem;
  width: 2.7rem;
}

.re-send-verification {
  font-size: 0.8rem;
  margin-top: -0.5rem !important;
}

.back-btn-position {
  text-align: left;
  margin-left: 1.75rem;
}

.verification-name-label {
  font-size: 1.28rem;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: $cp-black;
}

.verification-code-description-label {
  margin-top: 0.3rem;
  font-size: 0.9rem;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: $cp-black;
  padding-left: 0rem !important;
  padding-right: 0rem !important;
}

.verification-code-label {
  font-size: 0.8rem;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left !important;
  color: $cp-black;
}

.input-box-row-get-started {
  margin-top: 0.5rem !important;
  margin-left: 0.938rem !important;
  margin-right: 0.938rem !important;
}

//responsive
// html font-size 75%
@media only screen and (min-width: 425px) and (max-width: 425px) {
  .canpay-logo {
    height: 5.25rem !important;
    width: 5.25rem !important;
  }
  .canpay-logo-position {
    text-align: left;
    margin-left: -0.4rem !important;
  }

  .upgrade-image {
    height: 5.25rem !important;
    width: 5.25rem !important;
    margin: 7.875rem !important;
  }
  .upgrade-to-the-new-CanPay {
    font-size: 1.05rem !important;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
  }

  .as-an-existing-customer-there-are-just-a-few-easy-steps-to-get {
    margin-top: 0.225rem !important;
    font-size: 0.72rem !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .enter-the-email-you-use-to-sign-in-to-CanPay {
    font-size: 0.6rem !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    color: $cp-black;
  }

  .btn-get-started {
    background-color: $cp-primary !important;
    margin-bottom: 0.5rem !important;
  }

  .input-box-row-get-started {
    margin-top: 0.375rem !important;
    margin-left: 0.7035rem !important;
    margin-right: 0.7035rem !important;
  }

  .email-address {
    background-color: $cp-input-email !important;
    color: $cp-black !important;
  }
}

@media only screen and (max-width: 360px) and (max-width: 360px) {
  .canpay-logo {
    height: (7rem * 0.9) !important;
    width: (7rem * 0.9) !important;
  }
  .canpay-logo-position {
    text-align: left;
    margin-left: (-0.41rem * 0.9) !important;
    margin-top: 0rem !important;
  }
  .upgrade-image {
    height: (7rem * 0.9) !important;
    width: (7rem * 0.9) !important;
    margin: (2.5rem * 0.9) !important;
  }
  .upgrade-to-the-new-CanPay {
    font-size: (1.39rem * 0.9) !important;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
  }

  .as-an-existing-customer-there-are-just-a-few-easy-steps-to-get {
    margin-top: (0.3rem * 0.95) !important;
    font-size: (0.9rem * 0.98) !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .enter-the-email-you-use-to-sign-in-to-CanPay {
    font-size: (0.8rem * 0.99) !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    color: $cp-black;
  }

  .btn-get-started {
    background-color: $cp-primary !important;
    margin-bottom: (2rem * 0.95) !important;
  }

  .input-box-row-get-started {
    margin-top: (0.5rem * 0.95) !important;
    margin-left: (0.938rem * 0.95) !important;
    margin-right: (0.938rem * 0.95) !important;
  }

  .email-address {
    background-color: $cp-input-email !important;
    color: $cp-black !important;
  }
}

@media only screen and (max-width: 764px) and (min-height: 1280px) {
  .canpay-logo {
    height: (7rem * 1.3) !important;
    width: (7rem * 1.3) !important;
  }
  .canpay-logo-position {
    text-align: left;
    margin-left: -0.8rem !important;
  }
  .upgrade-image {
    height: (7rem * 1.3) !important;
    width: (7rem * 1.3) !important;
    margin: (2.5rem * 1.3) !important;
  }
  .upgrade-to-the-new-CanPay {
    font-size: (1.4rem * 1.3) !important;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
  }

  .as-an-existing-customer-there-are-just-a-few-easy-steps-to-get {
    margin-top: (0.3rem * 1.3) !important;
    font-size: (0.96rem * 1.3) !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .enter-the-email-you-use-to-sign-in-to-CanPay {
    font-size: (0.8rem * 1.3) !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    color: $cp-black;
  }

  .btn-get-started {
    background-color: $cp-primary !important;
    margin-bottom: (2rem * 1.3) !important;
  }

  .input-box-row-get-started {
    margin-top: (0.5rem * 1.3) !important;
    margin-left: (0.938rem * 1.3) !important;
    margin-right: (0.938rem * 1.3) !important;
  }

  .email-address {
    background-color: $cp-input-email !important;
    color: $cp-black !important;
  }
}
// fontsize 100%
@media only screen and (min-width: 384px) and (max-width: 384px) {
  .canpay-logo {
    height: (7rem * 0.94) !important;
    width: (7rem * 0.94) !important;
  }
  .canpay-logo-position {
    text-align: left;
    margin-left: (-0.29rem * 0.94) !important;
  }
  .upgrade-image {
    height: (7rem * 0.94) !important;
    width: (7rem * 0.94) !important;
    margin: 2.00rem !important;
  }
  .upgrade-to-the-new-CanPay {
    font-size: (1.39rem * 0.94) !important;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
  }

  .as-an-existing-customer-there-are-just-a-few-easy-steps-to-get {
    margin-top: (0.3rem * 0.94) !important;
    font-size: (0.9rem * 0.99) !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .enter-the-email-you-use-to-sign-in-to-CanPay {
    font-size: (0.8rem * 0.99) !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    color: $cp-black;
  }

  .btn-get-started {
    background-color: $cp-primary !important;
    margin-bottom: (2rem * 0.94) !important;
  }

  .input-box-row-get-started {
    margin-top: (0.5rem * 0.94) !important;
    margin-left: (0.938rem * 0.94) !important;
    margin-right: (0.938rem * 0.94) !important;
  }

  .email-address {
    background-color: $cp-input-email !important;
    color: $cp-black !important;
  }
}
//font-size 125%
@media only screen and (min-width: 412px) and (max-width: 412px) {
  .canpay-logo {
    height: (7rem * 1) !important;
    width: (7rem * 1) !important;
  }
  .canpay-logo-position {
    text-align: left;
    margin-left: (-0.29rem * 2) !important;
    margin-top: 0.1rem;
  }
  .upgrade-image {
    height: (7rem * 0.9) !important;
    width: (7rem * 0.9) !important;
    margin: (2.5rem * 1) !important;
  }
  .upgrade-to-the-new-CanPay {
    font-size: (1.39rem * 1.1) !important;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
  }

  .as-an-existing-customer-there-are-just-a-few-easy-steps-to-get {
    margin-top: (0.3rem * 1) !important;
    font-size: (0.9rem * 1.2) !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .enter-the-email-you-use-to-sign-in-to-CanPay {
    font-size: (0.8rem * 1.2) !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    color: $cp-black;
  }

  .btn-get-started {
    background-color: $cp-primary !important;
    margin-bottom: (2rem * 1) !important;
  }

  .input-box-row-get-started {
    margin-top: (0.5rem * 1) !important;
    margin-left: (0.938rem * 1) !important;
    margin-right: (0.938rem * 1) !important;
  }

  .email-address {
    background-color: $cp-input-email !important;
    color: $cp-black !important;
  }
}

@media only screen and (max-width: 600px) and (min-width: 600px) {
  .canpay-logo {
    height: (7rem * 1) !important;
    width: (7rem * 1) !important;
  }
  .canpay-logo-position {
    text-align: left;
    margin-left: (-0.29rem * 2) !important;
  }
  .upgrade-image {
    height: (7rem * 1) !important;
    width: (7rem * 1) !important;
    margin: (2.5rem * 1) !important;
  }
  .upgrade-to-the-new-CanPay {
    font-size: (1.39rem * 1) !important;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
  }

  .as-an-existing-customer-there-are-just-a-few-easy-steps-to-get {
    margin-top: (0.3rem * 1) !important;
    font-size: (0.9rem * 1) !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .enter-the-email-you-use-to-sign-in-to-CanPay {
    font-size: (0.8rem * 1) !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    color: $cp-black;
  }

  .btn-get-started {
    background-color: $cp-primary !important;
    margin-bottom: (2rem * 1) !important;
  }

  .input-box-row-get-started {
    margin-top: (0.5rem * 1) !important;
    margin-left: (0.938rem * 1) !important;
    margin-right: (0.938rem * 1) !important;
  }

  .email-address {
    background-color: $cp-input-email !important;
    color: $cp-black !important;
  }
}

@media only screen and (max-width: 320px) and (max-height: 480px) {
  .canpay-logo {
    height: (7rem * 0.65) !important;
    width: (7rem * 0.65) !important;
  }
  .canpay-logo-position {
    text-align: left;
    margin-left: (-0.29rem * 2) !important;
  }
  .upgrade-image {
    height: (7rem * 0.65) !important;
    width: (7rem * 0.65) !important;
    margin: (2.5rem * 0.65) !important;
  }
  .upgrade-to-the-new-CanPay {
    font-size: (1.39rem * 0.65) !important;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
  }

  .as-an-existing-customer-there-are-just-a-few-easy-steps-to-get {
    margin-top: (0.3rem * 0.65) !important;
    font-size: (0.9rem * 0.65) !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .enter-the-email-you-use-to-sign-in-to-CanPay {
    font-size: (0.8rem * 0.65) !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    color: $cp-black;
  }

  .btn-get-started {
    background-color: $cp-primary !important;
    margin-bottom: (2rem * 0.65) !important;
    margin-bottom: 0.6rem !important;
  }

  .input-box-row-get-started {
    margin-top: (0.5rem * 0.65) !important;
    margin-left: (0.938rem * 0.65) !important;
    margin-right: (0.938rem * 0.65) !important;
  }

  .email-address {
    background-color: $cp-input-email !important;
    color: $cp-black !important;
  }
}

@media only screen and (max-width: 480px) and (max-height: 533px) {
  .canpay-logo {
    height: (7rem * 0.7) !important;
    width: (7rem * 0.7) !important;
  }
  .canpay-logo-position {
    text-align: left;
    margin-left: (-0.43rem) !important;
  }
  .upgrade-image {
    height: (7rem * 0.65) !important;
    width: (7rem * 0.65) !important;
    margin: 1rem !important;
  }
  .upgrade-to-the-new-CanPay {
    font-size: (1.39rem * 0.75) !important;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
  }

  .as-an-existing-customer-there-are-just-a-few-easy-steps-to-get {
    margin-top: (0.3rem * 0.65) !important;
    font-size: (0.9rem * 0.75) !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .enter-the-email-you-use-to-sign-in-to-CanPay {
    font-size: (0.8rem * 0.75) !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    color: $cp-black;
  }

  .btn-get-started {
    background-color: $cp-primary !important;
    margin-bottom: (2rem * 0.65) !important;
    margin-bottom: 0.6rem !important;
  }

  .input-box-row-get-started {
    margin-top: (0.5rem * 0.65) !important;
    margin-left: (0.938rem * 0.65) !important;
    margin-right: (0.938rem * 0.65) !important;
  }

  .email-address {
    background-color: $cp-input-email !important;
    color: $cp-black !important;
  }
}

@media only screen and (max-width: 414px) {
  .canpay-logo-position {
    text-align: left;
    margin-left: -0.4rem !important;
  }
}
@media only screen and (min-width: 540px) and (max-width: 800px) {
  .canpay-logo-position {
    text-align: left;
    margin-left: -0.4rem !important;
    margin-top: 1.5rem;
  }

  .enter-the-email-you-use-to-sign-in-to-CanPay {
    font-size: 0.9rem !important;
  }

  .upgrade-to-the-new-CanPay {
    font-size: 1.49rem !important;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
  }

  .as-an-existing-customer-there-are-just-a-few-easy-steps-to-get {
    font-size: 1.07rem !important;
  }
}

@media only screen and (max-width: 411px) and (min-height: 731px) {
  .upgrade-to-the-new-CanPay {
    font-size: 1.5rem !important;
  }
  .enter-the-email-you-use-to-sign-in-to-CanPay {
    font-size: 0.93rem !important;
  }
  .canpay-logo-position {
    text-align: left;
    margin-left: -0.4rem !important;
  }

  .as-an-existing-customer-there-are-just-a-few-easy-steps-to-get {
    font-size: 1.05rem !important;
  }
}

@media only screen and (max-width: 375px) and (min-height: 667px) {
  .canpay-logo-position {
    text-align: left;
    margin-left: -0.4rem !important;
  }
}

@media only screen and (max-width: 375px) and (min-height: 812px) {
  .canpay-logo-position {
    text-align: left;
    margin-left: -0.4rem !important;
  }
}

@media only screen and (max-width: 480px) and (min-height: 854px) {
  .canpay-logo-position {
    text-align: left;
    margin-left: -0.4rem !important;
  }
}

@media only screen and (max-width: 768px) and (min-height: 1024px) {
  .canpay-logo-position {
    text-align: left;
    margin-left: -0.4rem !important;
  }
}

@media only screen and (max-width: 1024px) and (min-height: 1366px) {
  .canpay-logo {
    height: (7rem * 1.5) !important;
    width: (7rem * 1.5) !important;
  }
  .canpay-logo-position {
    text-align: left;
    margin-left: (1.7rem * 1.5) !important;
  }
  .upgrade-image {
    height: (7rem * 1.5) !important;
    width: (7rem * 1.5) !important;
    margin: (2.5rem * 1.5) !important;
  }
  .upgrade-to-the-new-CanPay {
    font-size: (1.4rem * 1.5) !important;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
  }

  .as-an-existing-customer-there-are-just-a-few-easy-steps-to-get {
    margin-top: (0.3rem * 1.5) !important;
    font-size: (0.96rem * 1.5) !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .enter-the-email-you-use-to-sign-in-to-CanPay {
    font-size: (0.8rem * 1.5) !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    color: $cp-black;
  }
  .margin-ipad-pro {
    margin-left: 2.3rem !important;
  }
  .btn-get-started {
    background-color: $cp-primary !important;
    margin-bottom: (2rem * 1.5) !important;
  }

  .input-box-row-get-started {
    margin-top: (0.5rem * 1.5) !important;
    margin-left: (0.938rem * 1.5) !important;
    margin-right: (0.938rem * 1.5) !important;
  }

  .email-address {
    background-color: $cp-input-email !important;
    color: $cp-black !important;
  }
}

@media only screen and (max-width: 800px) and (min-height: 1280px) {
  .canpay-logo-position {
    text-align: left;
    margin-left: -(0.5rem) !important;
  }
}

// emailverification css

@media only screen and (min-width: 360x) and (max-width: 414px) {
  .back-btn-position {
    text-align: left;
  }
  .back-btn {
    margin-left: -4.15rem !important;
    height: 2rem;
    width: 2.7rem;
  }

  .upgrade-image {
    height: 5.74rem;
    width: 5.74rem;
    margin: 2rem;
  }
  .verification-name-label {
    font-size: 1.04rem;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
  }

  .re-send-verification {
    font-size: 0.656rem;
    margin-top: -0.5rem !important;
  }
  .verification-code-description-label {
    margin-top: 0.246rem;
    font-size: 0.738rem;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .input-box-row-get-started {
    margin-top: 0.41rem !important;
    margin-left: 0.76916rem !important;
    margin-right: 0.76916rem !important;
  }

  .email-address {
    background-color: $cp-input-email !important;
    color: $cp-black !important;
  }
}

@media only screen and (min-width: 1024px) and (min-height: 1366px) {
  .back-btn-position {
    text-align: left;
  }
  .back-btn {
    margin-left: -4.15rem !important;
    height: (2rem * 2) !important;
    width: (2.7rem * 2) !important;
  }
  .upgrade-image {
    height: (7rem * 1.5);
    width: (7rem * 1.5);
    margin: (2.5rem * 1.5);
  }
  .verification-name-label {
    font-size: (1.28rem * 1.5);
  }

  .verification-code-description-label {
    margin-top: (0.3rem * 1.9);
    font-size: (0.9rem * 1.9);
  }

  .verification-code-label {
    font-size: (0.8rem * 1.9) !important;
  }
  .re-send-verification {
    font-size: (0.8rem * 1.9) !important;
    margin-top: (-0.5rem) !important;
  }

  .input-box-row-get-started {
    margin-top: (0.5rem * 1.5) !important;
    margin-left: (0.938rem * 1.5) !important;
    margin-right: (0.938rem * 1.5) !important;
  }

  .email-address {
    background-color: $cp-input-email !important;
    color: $cp-black !important;
  }
}

@media only screen and (min-width: 320px) and (max-width: 412px) {
  .upgrade-image {
    height: 4.879rem;
    width: 4.879rem;
    margin: 1.5rem;
  }
}

//font-size 78%
@media only screen and (min-width: 425px) and (max-height: 538px) {
  .back-btn {
    margin-left: -3.21rem !important;
    height: 2.1rem !important;
    width: 2.1rem !important;
  }
  .re-send-verification {
    font-size: 0.6rem !important;
    margin-top: -0.35rem !important;
  }

  .back-btn-position {
    text-align: left;
    margin-left: 1.3rem !important;
  }
  .upgrade-image {
    height: 5.4rem !important;
    width: 5.4rem !important;
    margin: 1.9rem !important;
  }
  .verification-name-label {
    font-size: 0.9rem !important;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
  }

  .verification-code-description-label {
    margin-top: 0.2rem !important;
    font-size: 0.69rem !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .verification-code-label {
    margin-top: 3rem !important;
    font-size: 0.6rem !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left !important;
    color: $cp-black;
  }

  .email-address {
    background-color: $cp-input-email !important;
    color: $cp-black !important;
  }
}

#email-success-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 0.7rem;
  margin: 0.7rem;
}

#email-success-verification-code-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 0.7rem;
  margin: 0.7rem;
}

/// email success

.success-image {
  height: 7rem;
  width: 7rem;
  margin: 2rem;

  text-align: center !important;
}

.success-text-onboarding {
  margin-top: 0.738rem;
  font-size: 1.28rem;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: $cp-black;
}
.btn-next-onboarding {
  background-color: $cp-primary !important;
  margin-bottom: 1rem !important;
  margin-top: 2rem !important;
}

.success-description-label {
  margin-top: 0.246rem;
  font-size: 0.9rem;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: $cp-black;
  padding-left: 0rem !important;
  padding-right: 0rem !important;
}

@media only screen and (min-width: 280px) and (max-width: 414px) {
  .success-description-label {
    margin-top: 0.246rem;
    font-size: 0.9rem !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .success-image {
    height: 7rem;
    width: 7rem;
    margin: 2rem;
    text-align: center !important;
  }

  .success-text-onboarding {
    margin-top: 1.5rem;
    font-size: 1.4rem !important;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center !important;
    color: $cp-black;
  }
  .btn-next-onboarding {
    background-color: $cp-primary !important;
    margin-bottom: 1rem !important;
    margin-top: 2rem !important;
  }
}

@media only screen and (max-width: 280px) and (min-height: 653px) {
  .row-for-input {
    margin-right: 0.8rem !important;
    margin-left: 0.8rem !important;
    margin-bottom: 0rem !important;
  }
  .canpay-logo {
    height: (7rem * 0.8) !important;
    width: (7rem * 0.8) !important;
  }
  .canpay-logo-position {
    text-align: left;
    margin-left: (-0.41rem * 0.8) !important;
    margin-top: 0rem !important;
  }
  .upgrade-image {
    height: (7rem * 0.8) !important;
    width: (7rem * 0.8) !important;
    margin: (2.5rem * 0.8) !important;
  }
  .upgrade-to-the-new-CanPay {
    font-size: (1.39rem * 0.8) !important;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
  }

  .as-an-existing-customer-there-are-just-a-few-easy-steps-to-get {
    margin-top: (0.3rem * 0.8) !important;
    font-size: (0.9rem * 0.8) !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .enter-the-email-you-use-to-sign-in-to-CanPay {
    font-size: (0.8rem * 0.8) !important;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    color: $cp-black;
  }

  .btn-get-started {
    background-color: $cp-primary !important;
    margin-bottom: (2rem * 0.8) !important;
  }

  .input-box-row-get-started {
    margin-top: (0.5rem * 0.8) !important;
    margin-left: (0.938rem * 0.8) !important;
    margin-right: (0.938rem * 0.8) !important;
  }

  .email-address {
    background-color: $cp-input-email !important;
    color: $cp-black !important;
  }
}

@media only screen and (max-width: 320px) and (max-height: 568px) {
  .row-for-input-success {
    margin-top: -1rem !important;
  }
}

// existingphonenumberverification

.mobile-number-verification-image {
  height: 7rem;
  width: 5rem;

}
.duplicate-mobile-number-verification-image {
  height: 6rem;
  width: 5rem;
  margin-top: 3rem;
  margin-bottom: 2rem;
  margin-left: 1.5rem;
}
.re-send-verification {
  font-size: 0.8rem;
  margin-top: -0.5rem !important;
}
.cursor-pointer {
  cursor: pointer;
}
.pls-enter-you-number-label {
  font-size: 1.28rem;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: $cp-black;
}

.mobile-phone-number-label {
  font-size: 0.8rem;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left !important;
  color: $cp-black;
}

.phone-number {
  background-color: $cp-input-email !important;
  color: $cp-black !important;
}

.phone-number {
  background-color: $cp-input-email !important;
  color: $cp-black !important;
}

.phone-number::placeholder {
  color: $cp-black;
  opacity: 1;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
}
.phone-number::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: $cp-black !important;
  font-family: "Open Sans" !important;
  font-size: 14px !important;
  font-weight: normal !important;
  font-stretch: normal !important;
  font-style: normal !important;
  line-height: normal !important;
  letter-spacing: normal !important;
  text-align: left !important;
}
.phone-number::-moz-placeholder {
  /* Firefox 19+ */
  color: $cp-black !important;
  font-family: "Open Sans" !important;
  font-size: 0.875rem !important;
  font-weight: normal !important;
  font-stretch: normal !important;
  font-style: normal !important;
  line-height: normal !important;
  letter-spacing: normal !important;
  text-align: left !important;
}
.phone-number:-ms-input-placeholder {
  /* IE 10+ */
  color: $cp-black !important;
  font-family: "Open Sans" !important;
  font-size: 0.875rem !important;
  font-weight: normal !important;
  font-stretch: normal !important;
  font-style: normal !important;
  line-height: normal !important;
  letter-spacing: normal !important;
  text-align: left !important;
}
.phone-number:-moz-placeholder {
  /* Firefox 18- */
  color: $cp-black !important;
  font-family: "Open Sans" !important;
  font-size: 0.875rem !important;
  font-weight: normal !important;
  font-stretch: normal !important;
  font-style: normal !important;
  line-height: normal !important;
  letter-spacing: normal !important;
  text-align: left !important;
}

@media only screen and (min-width: 480px) and (max-width: 1280px) {
  .mobile-phone-number-label {
    margin-left: 0rem !important;
  }
}

@media only screen and (min-width: 480px) and (max-width: 1280px) {
  .mobile-phone-number-label {
    margin-left: 0rem !important;
  }
}

@media only screen and (max-width: 280px) and (min-height: 653px) {
  .row-for-input {
    margin-right: 0.9rem !important;
    margin-left: 0.9rem !important;
    margin-bottom: 0rem !important;
  }
}

@media only screen and (max-width: 320px) and (max-height: 480px) {
  .mobile-number-verification-image {

    height: 5rem !important;
    width: 3.5rem !important;
  }
}

@media only screen and (min-width: 425px) and (max-width: 540px) {

  .mobile-phone-number-label {
    margin-left: 0rem !important;
  }
}

.onboarding-color-black {
  color: $cp-black;
}

.quickaccesspin-input-box-color {
  background-color: $cp-input-email !important;
}

.quickaccesspin-label {
  margin-left: -3.5rem !important;
  font-size: 0.95rem !important;
}
.quickaccesspin-label-two {
  margin-left: -5.9rem !important;
  font-size: 0.95rem !important;
}

.quickaccesspin-heading {
  margin-top: 4rem;
  font-size: 1.3rem !important;
}

.quickaccesspin-sub-heading {
  font-size: 1rem !important;
}

.quick-access-next-btn {
  margin-top: 4rem !important;
}

.bank-name {
  font-size: 1rem !important;
  font-weight: 600;
}

.phone-icon-size-position {
  margin-top: -0.3rem;
}

// existing Phone number verification

.mobile-number-verification-image {
  height: 7rem;
  width: 5rem;

}
.re-send-verification {
  font-size: 0.8rem;
  margin-top: -0.5rem !important;
}
.cursor-pointer {
  cursor: pointer;
}
.pls-enter-you-number-label {
  font-size: 1.28rem;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: $cp-black;
}

.mobile-phone-number-label {
  font-size: 0.8rem;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left !important;
  color: $cp-black;
}

.phone-number {
  background-color: $cp-input-email !important;
  color: $cp-black !important;
}

.phone-number {
  background-color: $cp-input-email !important;
  color: $cp-black !important;
}

.phone-number::placeholder {
  color: $cp-black;
  opacity: 1;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
}
.phone-number::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: $cp-black !important;
  font-family: "Open Sans" !important;
  font-size: 14px !important;
  font-weight: normal !important;
  font-stretch: normal !important;
  font-style: normal !important;
  line-height: normal !important;
  letter-spacing: normal !important;
  text-align: left !important;
}
.phone-number::-moz-placeholder {
  /* Firefox 19+ */
  color: $cp-black !important;
  font-family: "Open Sans" !important;
  font-size: 0.875rem !important;
  font-weight: normal !important;
  font-stretch: normal !important;
  font-style: normal !important;
  line-height: normal !important;
  letter-spacing: normal !important;
  text-align: left !important;
}
.phone-number:-ms-input-placeholder {
  /* IE 10+ */
  color: $cp-black !important;
  font-family: "Open Sans" !important;
  font-size: 0.875rem !important;
  font-weight: normal !important;
  font-stretch: normal !important;
  font-style: normal !important;
  line-height: normal !important;
  letter-spacing: normal !important;
  text-align: left !important;
}
.phone-number:-moz-placeholder {
  /* Firefox 18- */
  color: $cp-black !important;
  font-family: "Open Sans" !important;
  font-size: 0.875rem !important;
  font-weight: normal !important;
  font-stretch: normal !important;
  font-style: normal !important;
  line-height: normal !important;
  letter-spacing: normal !important;
  text-align: left !important;
}

@media only screen and (min-width: 480px) and (max-width: 1280px) {
  .mobile-phone-number-label {
    margin-left: 0rem !important;
  }
}

@media only screen and (min-width: 480px) and (max-width: 1280px) {
  .mobile-phone-number-label {
    margin-left: 0rem !important;
  }
}

@media only screen and (min-width: 1024px) and (max-height: 1366px) {
  .pls-enter-you-number-label {
    font-size: 1.28rem;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
  }

  .mobile-phone-number-label {
    font-size: 0.8rem;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left !important;
    color: $cp-black;
  }
  .phone-number {
    background-color: $cp-input-email !important;
    color: $cp-black !important;
  }

  .re-send-verification {
    font-size: 0.8rem;
    margin-top: -0.5rem !important;
  }
}

@media only screen and (max-width: 280px) and (min-height: 653px) {
  .row-for-input {
    margin-right: 0.9rem !important;
    margin-left: 0.9rem !important;
    margin-bottom: 0rem !important;
  }
}

@media only screen and (min-width: 320px) and (max-height: 480px) {

}

@media only screen and (min-width: 425px) and (max-width: 540px) {

  .mobile-phone-number-label {
    margin-left: 0rem !important;
  }
}

// register primary phone number

.register-phone-image {
  height: 7rem;
  width: 7rem;
  margin: 2rem;
}

.register-phone-number-text {
  margin-top: 1rem;
  font-size: 1.28rem;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: $cp-black;
}
.primary-phone-number-registration-information {
  margin-top: 0.3rem;
  font-size: 0.9rem;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: $cp-black;
  padding-left: 0rem !important;
  padding-right: 0rem !important;
}

.primary-phone-number-label {
  margin-top: 4rem;
  font-size: 0.8rem;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left !important;
  color: $cp-black;
  margin-left: 2.27rem !important;
}

@media only screen and (min-width: 280px) and (max-width: 414px) {
  .register-phone-image {
    height: 5.5rem;
    width: 5.5rem;
    margin: 2rem;
  }

  .register-phone-number-text {
    margin-top: 1rem;
    font-size: 1.0496rem;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
  }
  .primary-phone-number-registration-information {
    margin-top: 0.246rem;
    font-size: 0.738rem;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .primary-phone-number-label {
    margin-top: 3.28rem;
    font-size: 0.656rem;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left !important;
    color: $cp-black;
    margin-left: 0rem !important;
  }
}

@media only screen and (min-width: 480px) and (max-width: 1280px) {
  .primary-phone-number-label {
    margin-left: 0rem !important;
  }
}

@media only screen and (min-width: 320px) and (max-height: 533px) {
  .primary-phone-number-registration-information {
    margin-top: 0.2091rem;
    font-size: 0.6273rem;

    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }
  .register-phone-number-text {
    margin-top: 1rem;
    font-size: 0.89216rem;
    font-weight: 600;
  }
  .primary-phone-number-label {
    margin-top: 2.788rem;
    font-size: 0.5576rem;
    margin-left: 0rem !important;
  }
}

@media only screen and (min-width: 480px) and (max-width: 1280px) {
  .primary-phone-number-label {
    margin-left: 0rem !important;
  }
}

@media only screen and (min-width: 1024px) and (max-height: 1366px) {
  .primary-phone-number-registration-information {
    margin-top: 0.3rem;
    font-size: 0.9rem;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .primary-phone-number-label {
    margin-top: 4rem;
    font-size: 0.8rem;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left !important;
    color: $cp-black;
    margin-left: 2.27rem !important;
  }
}

//font size -
@media only screen and (min-width: 320px) and (max-height: 480px) {
  .register-phone-image {
    height: 4.9rem;
    width: 4.9rem;
    margin: 2rem;
  }

  .register-phone-number-text {
    margin-top: 0.7rem;
    font-size: 0.896rem;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
  }
  .primary-phone-number-registration-information {
    margin-top: 0.21rem;
    font-size: 0.63rem;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .primary-phone-number-label {
    margin-top: 2.8rem;
    font-size: 0.56rem;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left !important;
    color: $cp-black;
    margin-left: 0rem !important;
  }
}
//font-size 80%
@media only screen and (min-width: 425px) and (max-width: 540px) {
  .register-phone-image {
    height: 5.6rem;
    width: 5.6rem;
    margin: 2rem;
  }

  .register-phone-number-text {
    margin-top: 0.8rem;
    font-size: 1.024rem;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
  }
  .primary-phone-number-registration-information {
    margin-top: 0.24rem;
    font-size: 0.72rem;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .primary-phone-number-label {
    margin-top: 3.2rem;
    font-size: 0.64rem;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left !important;
    color: $cp-black;
    margin-left: 0rem !important;
  }
}
@media only screen and (max-width: 280px) and (min-height: 653px) {
  .row-for-input {
    margin-right: 15px !important;
    margin-left: 15px !important;
    margin-bottom: 15px !important;
  }
}

@media only screen and (max-width: 425px) and (max-height: 538px) {
  .primary-phone-number-label {
    margin-top: 2rem;
  }
  .register-phone-image {
    margin: 2rem;
  }
}

// onboarding header

@media only screen and (min-width: 360px) and (max-width: 361px) {
  //checkbox

  .mobile-number-verification-image {

    height: (7rem * 0.8);
    width: (5rem * 0.8);
  }
  .lockkey-icon {
    height: 2.9rem !important;
    width: 2.9rem !important;
  }

  .terms-checkbox-onboard-position {
    font-size: 0.8rem !important;
  }
  .terms-and-condition-check-box {
    margin-top: 5rem !important;
  }

  //onboard
  .register-phone-image {
    height: 5.5rem !important;
    width: 5.5rem !important;
    margin: 2rem;
  }

  .canpay-logo-header-position {
    text-align: center !important;
    margin-left: 4rem !important;
  }

  .onboarding-back-btn-postion {
    margin-left: -1rem !important;
    margin-top: 2.37rem !important;
  }

  .back-btn-onboarding-header {
    margin-left: 0rem !important;
    height: 2rem !important;
    width: 2.7rem !important;
  }

  .canpay-logo-onboarding-header {
    height: 6.3rem !important;
    width: 6.3rem !important;
  }

  //email veriviaction code

  .upgrade-image {
    height: 5.74rem !important;
    width: 5.74rem !important;
    margin: 2rem !important;
  }
  .verification-name-label {
    font-size: 0.99rem !important;
    text-align: center !important;
  }

  .verification-code-label {
    font-size: 0.85rem !important;
  }

  .re-send-verification {
    font-size: 0.85rem !important;
    margin-top: -0.5rem !important;
  }
  .verification-code-description-label {
    margin-top: 0.246rem !important;
    font-size: 0.9rem !important;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .input-box-row-get-started {
    margin-top: 0.41rem !important;
    margin-left: 0.76916rem !important;
    margin-right: 0.76916rem !important;
  }

  // onboarding upgrage modal
  .onboarding-upgrage-modal {
    margin-left: -1.59rem !important;
  }

  // onboarding success modal image
  .onboarding-success-modal-image {
    height: 6rem !important;
    width: 6rem !important;
    margin-top: 0rem !important;
    text-align: center !important;
    margin-left: -0.5rem !important;
  }
  .onboarding-success-popup-style {
    font-family: "Open Sans";
    font-size: 1rem;
    font-weight: 600;
    color: #000000;
  }

  //mobile verification number
  .mobile-phone-number-label {
    margin-left: 0rem !important;
  }



  //duplicate mobile number verification

  .duplicate-mobile-number-verification-image {
    height: (6rem * 0.9) !important;
    width: (5rem * 0.9) !important ;
    margin-top: (3rem * 0.9) !important;
    margin-bottom: 0rem !important;
  }

  .primary-phone-number-registration-information {
    font-size: 0.85rem !important;
  }
  .primary-phone-number-label {
    font-size: 0.7rem !important;
  }

  //phone number verification code
  .mobile-verification-code {
    height: (7rem * 0.8) !important;
    width: (5.9rem * 0.8) !important;
    margin-top: (2.1rem * 0.8) !important;
    margin-bottom: (2.5rem * 0.8) !important;
  }

  .mobile-verification-code-label {
    margin-top: -0.9rem !important;
    font-size: (1.28rem * 0.9) !important;
  }

  // success page

  .success-image {
    height: 7rem !important;
    width: 7rem !important;
    margin: 2rem;

    text-align: center !important;
  }

  // pin
  .quickaccesspin-heading {
    margin-top: 3rem !important;
    font-size: 1.3rem !important;
  }

  //onboardingsigning
  .canpay-logo-onboard {
    height: 7.5rem !important;
    width: 7.5rem !important;
    margin-top: 1.7rem !important;
  }
  .canpay-moble-screen {
    height: 13rem !important;
    width: 13rem !important;
    margin-top: 2rem !important;
  }

  .get-the-canpay-app-label {
    font-size: 1.3rem !important;
    font-weight: 700 !important;
  }
  .get-the-canpay-app-description-label {
    font-size: 0.9rem !important;
    font-weight: 400 !important;
  }
  .next-button-color-onboardingsingin {
    background-color: $cp-input-email !important;
    padding: 1.76rem !important;
    padding-left: 1rem !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}

@media only screen and (min-width: 800px) and (max-width: 800px) and (min-height: 1280px) and (max-height: 1280px) {
  .canpay-logo-header-position {
    text-align: center;
    margin-left: 12rem !important;
  }

  .onboarding-upgrage-modal {
    margin-left: 0rem !important;
  }
  .onboarding-back-btn-postion {
    margin-left: -5.65rem !important;
    margin-top: 2.6rem !important;
  }

  .back-btn-onboarding-header {
    height: 3.7rem !important;
    width: 3.7rem !important;
  }

  .canpay-logo-onboarding-header {
    height: 9rem !important;
    width: 9rem !important;
  }

  //emailverificationcode

  .verification-code-label {
    font-size: 1.15rem;
    margin-left: 0rem !important;
  }
  .verification-name-label {
    font-size: 1.45rem;
  }
  .verification-code-description-label {
    margin-top: 0.3rem;
    font-size: 1.3rem;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }
  .re-send-verification {
    font-size: 1.15rem;
    margin-top: -0.5rem !important;
  }
  // onboarding success modal image
  .onboarding-success-modal-image {
    height: 6rem;
    width: 6rem;
    margin-top: 0rem;
    text-align: center !important;
    margin-left: 1.1rem;
  }
  .onboarding-success-popup-style {
    font-family: "Open Sans";
    font-size: 1.2rem;
    font-weight: 600;
    color: #000000;
  }
  // mobile number verification

  .pls-enter-you-number-label {
    font-size: 1.6rem;
  }
  .mobile-phone-number-label {
    font-size: 1.15rem;
  }

  //duplicate mobile number verification

  .duplicate-mobile-number-verification-image {
    height: (6rem * 1.3);
    width: (5rem * 1.3);
    margin-top: (3rem * 1.3);
    margin-bottom: 0rem;
  }

  .primary-phone-number-registration-information {
    font-size: (1rem * 1.3);
  }
  .primary-phone-number-label {
    font-size: (0.9rem * 1.3);
  }

  //phone number verification code
  .mobile-verification-code {
    height: (7rem * 1.2) !important;
    width: (5.9rem * 1.2) !important;
    margin-top: (2.1rem * 1.2) !important;
    margin-bottom: (2.5rem * 1.2) !important;
  }

  .mobile-verification-code-label {
    margin-top: -0.9rem !important;
    font-size: 1.6rem !important;
  }

  // success page

  .success-image {
    height: 7rem;
    width: 7rem;
    margin: 2rem;

    text-align: center !important;
  }

  .success-text-onboarding {
    margin-top: 0.9rem;
    font-size: 1.6rem !important;
  }
  .success-description-label {
    margin-top: 0.4rem;
    font-size: 1.2rem;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  // pin
  .quickaccesspin-heading {
    margin-top: 3rem;
    font-size: 1.9rem !important;
  }
  .quickaccesspin-sub-heading {
    font-size: 1.6rem !important;
  }

  .quickaccesspin-label {
    margin-left: 0rem !important;
    font-size: 1.3rem !important;
  }

  .quickaccesspin-label-two {
    margin-left: -3rem !important;
    font-size: 1.3rem !important;
  }
}

@media only screen and (min-width: 384px) and (max-width: 384px) and (min-height: 640px) and (max-height: 640px) {
  //checkbox
  .terms-checkbox-onboard-position {
    font-size: 0.8rem !important;
  }
  .terms-and-condition-check-box {
    margin-top: 5rem !important;
  }

  .register-phone-image {
    height: 5.5rem;
    width: 5.5rem;
    margin: 2rem;
  }

  .canpay-logo-header-position {
    text-align: center;
    margin-left: 4rem !important;
  }

  .onboarding-back-btn-postion {
    margin-left: -1rem;
    margin-top: 2.37rem;
  }

  .back-btn-onboarding-header {
    margin-left: 0rem !important;
    height: 2rem;
    width: 2.7rem;
  }

  .canpay-logo-onboarding-header {
    height: 6.3rem !important;
    width: 6.3rem !important;
  }

  //email veriviaction code

  .upgrade-image {
    height: 5.74rem;
    width: 5.74rem;
    margin: 2rem;
  }
  .verification-name-label {
    font-size: 0.99rem;
    text-align: center !important;
  }

  .verification-code-label {
    font-size: 0.85rem;
    margin-left: 0rem !important;
  }

  .re-send-verification {
    font-size: 0.85rem !important;
    margin-top: -0.5rem !important;
  }
  .verification-code-description-label {
    margin-top: 0.246rem;
    font-size: 0.9rem;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .input-box-row-get-started {
    margin-top: 0.41rem !important;
    margin-left: 0.76916rem !important;
    margin-right: 0.76916rem !important;
  }

  // onboarding upgrage modal

  .onboarding-upgrage-modal {
    margin-left: -1.1rem !important;
  }

  // onboarding success modal image
  .onboarding-success-modal-image {
    height: 6rem;
    width: 6rem;
    margin-top: 0rem;
    text-align: center !important;
    margin-left: 0rem;
  }
  .onboarding-success-popup-style {
    font-family: "Open Sans";
    font-size: 1rem;
    font-weight: 600;
    color: #000000;
  }

  .pls-enter-you-number-label {
    font-size: 1.2rem;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
  }

  .mobile-phone-number-label {
    font-size: 0.85rem;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left !important;
    color: $cp-black;
    margin-left: 0rem !important;
  }

  //duplicate mobile number

  .duplicate-mobile-number-verification-image {
    height: (6rem * 0.9);
    width: (5rem * 0.9);
    margin-top: (3rem * 0.9);
    margin-bottom: 0rem;
  }

  .primary-phone-number-registration-information {
    font-size: 1rem;
  }
  .primary-phone-number-label {
    font-size: 0.85rem;
  }

  //phone number verification code
  .mobile-verification-code {
    height: (7rem * 0.8) !important;
    width: (5.9rem * 0.8) !important;
    margin-top: (2.1rem * 0.8) !important;
    margin-bottom: (2.5rem * 0.8) !important;
  }

  .mobile-verification-code-label {
    margin-top: -0.9rem !important;
    font-size: 1.2rem !important;
  }

  // success page

  .success-image {
    height: 7rem;
    width: 7rem;
    margin: 2rem;

    text-align: center !important;
  }

  .success-text-onboarding {
    margin-top: 0.9rem;
    font-size: 1.3066rem !important;
  }
  .success-description-label {
    margin-top: 0.4rem;
    font-size: 0.891rem !important;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  // onboarding upgrage modal

  .onboarding-upgrage-modal {
    margin-left: -2rem;
  }

  //onboardingsigning
  .canpay-logo-onboard {
    height: 7.5rem;
    width: 7.5rem;
    margin-top: 1.7rem;
  }
  .canpay-moble-screen {
    height: 13rem;
    width: 13rem;
    margin-top: 2rem;
  }

  .get-the-canpay-app-label {
    font-size: 1.3rem !important;
    font-weight: 700;
  }
  .get-the-canpay-app-description-label {
    font-size: 0.9rem !important;
    font-weight: 400;
  }
  .next-button-color-onboardingsingin {
    background-color: $cp-input-email !important;
    padding: 1.76rem;
    padding-left: 1.3rem;
    padding-right: 1.1rem;
  }
}

@media only screen and (min-width: 412px) and (max-width: 412px) {
  .terms-checkbox-onboard-position {
    font-size: 0.8rem !important;
  }
  .terms-and-condition-check-box {
    margin-top: 5rem !important;
  }

  .mobile-number-verification-image {

    height: (7rem * 0.8);
    width: (5rem * 0.8);
  }

  .register-phone-image {
    height: 5.5rem;
    width: 5.5rem;
    margin: 2rem;
  }
  .canpay-logo-header-position {
    text-align: center;
    margin-left: 4.5rem !important;
  }

  .onboarding-back-btn-postion {
    margin-left: -1rem !important;
    margin-top: 2.37rem !important;
  }

  .back-btn-onboarding-header {
    height: 2rem !important;
    width: 2.7rem;
  }

  .canpay-logo-onboarding-header {
    height: 6.3rem !important;
    width: 6.3rem !important;
  }

  //email varification code

  .upgrade-image {
    height: 5.74rem;
    width: 5.74rem;
    margin: 3rem;
  }
  .verification-name-label {
    font-size: 0.99rem;
    text-align: center !important;
  }

  .verification-code-label {
    font-size: 0.9rem;
    margin-left: 0rem !important;
  }

  .re-send-verification {
    font-size: 0.9rem !important;
    margin-top: -0.5rem !important;
  }
  .verification-code-description-label {
    margin-top: 0.246rem;
    font-size: 1rem;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .input-box-row-get-started {
    margin-top: 0.41rem !important;
    margin-left: 0.76916rem !important;
    margin-right: 0.76916rem !important;
  }

  // onboarding success modal image
  .onboarding-success-modal-image {
    height: 6rem;
    width: 6rem;
    margin-top: 0rem;
    text-align: center !important;
    margin-left: 0rem;
  }
  .onboarding-success-popup-style {
    font-family: "Open Sans";
    font-size: 1rem;
    font-weight: 600;
    color: #000000;
  }
  // mobile number verification
  .mobile-phone-number-label {
    font-size: 0.88rem;
    margin-left: 0rem !important;
  }

  .pls-enter-you-number-label {
    font-size: 1.28rem;
  }

  //duplicate mobile number
  .register-phone-number-text {
    font-size: 1.21rem;
  }
  .duplicate-mobile-number-verification-image {
    height: (6rem * 0.9);
    width: (5rem * 0.9);
    margin-top: 3rem;
    margin-bottom: 0rem;
  }

  .primary-phone-number-registration-information {
    font-size: 1.05rem;
  }
  .primary-phone-number-label {
    font-size: 0.95rem;
    margin-top: 3rem;
  }

  //phone number verification code
  .mobile-verification-code {
    height: (7rem * 0.8) !important;
    width: (5.9rem * 0.8) !important;
    margin-top: (2.1rem * 0.8) !important;
    margin-bottom: (2.5rem * 0.8) !important;
  }

  .mobile-verification-code-label {
    margin-top: -0.9rem !important;
    font-size: 1.28rem !important;
  }

  // success page

  .success-image {
    height: 7rem;
    width: 7rem;
    margin: 2rem;

    text-align: center !important;
  }

  .success-text-onboarding {
    margin-top: 0.9rem;
    font-size: 1.3066rem !important;
  }
  .success-description-label {
    margin-top: 0.4rem;
    font-size: 0.891rem !important;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  // onboarding upgrage modal

  .onboarding-upgrage-modal {
    margin-left: -1rem !important;
  }

  //onboardingsigning
  .canpay-logo-onboard {
    height: 7.5rem !important;
    width: 7.5rem !important;
    margin-top: 1.7rem !important;
  }
  .canpay-moble-screen {
    height: 13rem !important;
    width: 13rem !important;
    margin-top: 2rem !important;
  }

  .get-the-canpay-app-label {
    font-size: 1.3rem !important;
    font-weight: 700;
  }
  .get-the-canpay-app-description-label {
    font-size: 0.9rem !important;
    font-weight: 400;
  }
  .next-button-color-onboardingsingin {
    background-color: $cp-input-email !important;
    padding: 1.76rem;
    padding-left: 1.3rem;
    padding-right: 1.1rem;
  }
}

@media only screen and (min-width: 600px) and (max-width: 600px) and (min-height: 960px) and (max-height: 960px) {
  .terms-checkbox-onboard-position {
    font-size: 0.8rem !important;
  }
  .terms-and-condition-check-box {
    margin-top: 5rem !important;
  }

  .canpay-logo-header-position {
    text-align: center;
    margin-left: 6rem !important;
  }

  .onboarding-back-btn-postion {
    margin-left: -1rem !important;
    margin-top: 3.4rem !important;
  }

  .back-btn-onboarding-header {
    height: 2.7rem !important;
    width: 3.7rem;
  }

  .canpay-logo-onboarding-header {
    height: 9rem !important;
    width: 9rem !important;
  }

  //email verification

  .verification-name-label {
    font-size: 1.4rem;
  }

  .verification-code-description-label {
    margin-top: 0.3rem;
    font-size: 1.28rem;

    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .verification-code-label {
    font-size: 1.1rem;
    margin-left: 0rem !important;
  }

  .re-send-verification {
    font-size: 1.1rem;
    margin-top: -0.5rem !important;
  }

  // onboarding success modal image
  .onboarding-success-modal-image {
    height: 6rem;
    width: 6rem;
    margin-top: 0rem;
    text-align: center !important;
    margin-left: 1.1rem;
  }
  .onboarding-success-popup-style {
    font-family: "Open Sans";
    font-size: 1rem;
    font-weight: 600;
    color: #000000;
  }

  // mobile number verification
  .mobile-phone-number-label {
    font-size: 1.1rem;
    margin-left: 0rem !important;
  }

  .pls-enter-you-number-label {
    font-size: 1.6rem;
  }
  //duplicate mobile number
  .register-phone-number-text {
    font-size: 1.6rem;
  }
  .duplicate-mobile-number-verification-image {
    height: (6rem * 1.3);
    width: (5rem * 1.3);
    margin-top: (3rem * 1.3);
    margin-bottom: 2rem;
  }
  .primary-phone-number-registration-information {
    font-size: 1.3rem;
  }
  .primary-phone-number-label {
    margin-top: 6rem;
    font-size: 1.27rem;
  }

  //phone number verification code
  .mobile-verification-code {
    height: (7rem * 1.1) !important;
    width: (5.9rem * 1.1) !important;
    margin-top: 4rem !important;
    margin-bottom: (2.5rem * 1.1) !important;
  }

  .mobile-verification-code-label {
    margin-top: -0.9rem !important;
    font-size: 1.6rem !important;
  }

  // success page

  .success-image {
    height: 7rem;
    width: 7rem;
    margin: 2rem;

    text-align: center !important;
  }

  .success-text-onboarding {
    margin-top: 0.9rem;
    font-size: 1.3066rem !important;
  }
  .success-description-label {
    margin-top: 0.4rem;
    font-size: 0.891rem !important;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }
}

@media only screen and (min-width: 320px) and (max-width: 320px) and (min-height: 533px) and (max-height: 533px) {
  .on-boarding-upgrade-alert-icon {
    margin-left: -0.3rem !important;
    height: 4.5rem;
    width: 4.5rem;
  }
  .terms-checkbox-onboard-position {
    font-size: 0.8rem !important;
  }
  .terms-and-condition-check-box {
    margin-top: 0rem !important;
  }

  .register-phone-image {
    margin: 2rem;
  }

  .canpay-logo-header-position {
    text-align: center;
    margin-left: 3rem !important;
  }

  .onboarding-back-btn-postion {
    margin-left: -1rem;
    margin-top: 2.37rem;
  }

  .back-btn-onboarding-header {
    height: 2rem;
    width: 2.7rem;
  }

  .canpay-logo-onboarding-header {
    height: 6.3rem !important;
    width: 6.3rem !important;
  }

  // email varification code

  .upgrade-image {
    height: 4.879rem;
    width: 4.879rem;
    margin: 1.5rem;
  }
  .verification-name-label {
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center !important;
  }

  .verification-code-label {
    font-size: 0.685rem;
    margin-left: 0rem !important;
  }
  .re-send-verification {
    font-size: 0.685rem;
    margin-top: 0rem !important;
  }
  .verification-code-description-label {
    margin-top: 0.2091rem;
    font-size: 0.72rem;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .input-box-row-get-started {
    margin-top: 0.3485rem !important;
    margin-left: 0.653786rem !important;
    margin-right: 0.653786rem !important;
  }

  // onboarding success modal image
  .onboarding-success-modal-image {
    height: 6rem;
    width: 6rem;
    margin-top: 0rem;
    text-align: center !important;
    margin-left: -1rem;
  }
  .onboarding-success-popup-style {
    font-family: "Open Sans";
    font-size: 1rem;
    font-weight: 600;
    color: #000000;
  }

  //mobile number verification

  .pls-enter-you-number-label {
    font-size: 0.89216rem;
  }

  .mobile-phone-number-label {
    font-size: 0.685rem;
  }

  .mobile-number-verification-image {
    height: (7rem * 0.8);
    width: (5rem * 0.8);

  }
  //duplicate mobile number
  .duplicate-mobile-number-verification-image {
    height: (6rem * 0.8);
    width: (5rem * 0.8);
    margin-top: 1.5rem;
    margin-bottom: 0rem;
  }

  .primary-phone-number-registration-information {
    font-size: 0.7rem;
  }
  .primary-phone-number-label {
    font-size: 0.65rem;
    margin-top: 2.3rem;
  }
  .register-phone-number-text {
    margin-top: 1rem;
    font-size: 0.85rem;
    font-weight: 600;
  }

  //phone number verification code
  .mobile-verification-code {
    height: (7rem * 0.7) !important;
    width: (5.9rem * 0.7) !important;
    margin-top: 1.7rem !important;
    margin-bottom: (2.5rem * 0.55) !important;
  }

  .mobile-verification-code-label {
    margin-top: -0.9rem !important;
    font-size: 0.89216rem !important;
  }

  // success page

  .success-image {
    height: (7rem * 0.7);
    width: (7rem * 0.7);
    margin: 2rem;

    text-align: center !important;
  }

  .success-text-onboarding {
    margin-top: 0.9rem;
    font-size: 1.2066rem !important;
  }
  .success-description-label {
    margin-top: 0.4rem;
    font-size: 0.8rem !important;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .btn-next-onboarding {
    margin-top: 2rem !important;
  }

  //
  .quick-access-next-btn {
    margin-top: 1rem !important;
  }

  .onboarding-modal-message {
    font-size: 0.9rem !important;
  }
  .on-boarding-upgrade-alert-icon {
    margin-left: -0.9rem;
    height: 4.5rem;
    width: 4.5rem;
  }
}

@media only screen and (min-width: 480px) and (max-width: 480px) and (min-height: 854px) and (max-height: 854px) {
  .terms-checkbox-onboard-position {
    font-size: 0.8rem !important;
  }
  .terms-and-condition-check-box {
    margin-top: 5rem !important;
  }

  .canpay-logo-header-position {
    text-align: center;
    margin-left: 6rem !important;
  }

  .onboarding-back-btn-postion {
    margin-left: -1rem;
    margin-top: 2.37rem;
  }

  .back-btn-onboarding-header {
    height: 2rem;
    width: 2.7rem;
  }

  .canpay-logo-onboarding-header {
    height: 6.3rem !important;
    width: 6.3rem !important;
  }
  // email verification code
  .verification-code-label {
    font-size: 1rem;
    margin-left: 0rem !important;
  }

  .verification-name-label {
    font-size: 1.28rem;
    text-align: center !important;
  }
  .verification-code-description-label {
    margin-top: 0.3rem;
    font-size: 1.1rem;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }
  .re-send-verification {
    font-size: 1.1rem;
    margin-top: -0.5rem !important;
  }

  // onboarding success modal image
  .onboarding-success-modal-image {
    height: 6rem;
    width: 6rem;
    margin-top: 0rem;
    text-align: center !important;
    margin-left: 0.5rem;
  }
  .onboarding-success-popup-style {
    font-family: "Open Sans";
    font-size: 1rem;
    font-weight: 600;
    color: #000000;
  }

  //mobile number verification

  .pls-enter-you-number-label {
    font-size: 1.6rem;
  }

  .mobile-phone-number-label {
    font-size: 1.15rem;
  }

  //duplicate mobile number
  .duplicate-mobile-number-verification-image {
    height: (6rem * 1.2);
    width: (5rem * 1.2);
    margin-top: 3rem;
    margin-bottom: 2rem;
  }

  .primary-phone-number-registration-information {
    font-size: 1.32rem;
  }
  .primary-phone-number-label {
    font-size: 1.15rem;
    margin-top: 6.3rem;
  }
  .register-phone-number-text {
    margin-top: 1rem;
    font-size: 1.59rem;
    font-weight: 600;
  }

  //phone number verification code
  .mobile-verification-code {
    height: (7rem * 1) !important;
    width: (5.9rem * 1) !important;
    margin-top: 2.7rem !important;
    margin-bottom: (2.5rem * 1) !important;
  }

  .mobile-verification-code-label {
    margin-top: -0.9rem !important;
    font-size: 1.6rem !important;
  }

  // success page

  .success-image {
    height: 7rem;
    width: 7rem;
    margin: 2rem;
    text-align: center !important;
  }

  .success-text-onboarding {
    margin-top: 0.9rem;
    font-size: 1.6rem !important;
  }
  .success-description-label {
    margin-top: 0.4rem;
    font-size: 1.3rem !important;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }
  //pin
  .btn-next-onboarding {
    margin-top: 5rem !important;
  }
  .quickaccesspin-sub-heading {
    font-size: 1.4rem !important;
  }

  .quickaccesspin-label {
    margin-left: 0.5rem !important;
  }
  .quickaccesspin-label-two {
    margin-left: -2.9rem !important;
  }

  .on-boarding-upgrade-alert-icon {
    margin-left: 0rem;
  }
}

@media only screen and (min-width: 768px) and (max-width: 768px) and (min-height: 1024px) and (max-height: 1024px) {
  .canpay-logo-header-position {
    text-align: center;
    margin-left: 8.5rem !important;
  }

  .onboarding-back-btn-postion {
    margin-left: -1rem;
    margin-top: 3.37rem;
  }

  .back-btn-onboarding-header {
    height: 2.7rem;
    width: 3.7rem;
  }

  .canpay-logo-onboarding-header {
    height: 9rem !important;
    width: 9rem !important;
  }

  //email verification code

  .upgrade-image {
    height: (7rem * 1.5);
    width: (7rem * 1.5);
    margin: (2.5rem * 1.5);
  }
  .verification-name-label {
    font-size: (1.28rem * 1.5);
    text-align: center !important;
  }

  .verification-code-description-label {
    margin-top: (0.3rem * 1.5);
    font-size: (0.9rem * 1.5);

    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .verification-code-label {
    font-size: (0.8rem * 1.5);
  }
  .re-send-verification {
    font-size: (0.8rem * 1.5) !important;
    margin-top: (-0.5rem) !important;
  }

  .input-box-row-get-started {
    margin-top: (0.5rem * 1.5) !important;
    margin-left: (0.938rem * 1.5) !important;
    margin-right: (0.938rem * 1.5) !important;
  }

  // onboarding success modal image
  .onboarding-success-modal-image {
    height: 6rem;
    width: 6rem;
    margin-top: 0rem;
    text-align: center !important;
    margin-left: 1.1rem;
  }
  .onboarding-success-popup-style {
    font-family: "Open Sans";
    font-size: 1rem;
    font-weight: 600;
    color: #000000;
  }

  //mobile number verification

  .pls-enter-you-number-label {
    font-size: 1.6rem;
  }

  .mobile-phone-number-label {
    font-size: 1.1rem;
  }

  //duplicate mobile number
  .duplicate-mobile-number-verification-image {
    height: (6rem);
    width: (5rem);
    margin-top: 3rem;
    margin-bottom: 2rem;
  }

  .primary-phone-number-registration-information {
    font-size: 1.4rem;
  }
  .primary-phone-number-label {
    font-size: 1.29rem;
    margin-top: 6.3rem;
  }
  .register-phone-number-text {
    margin-top: 1rem;
    font-size: 1.6rem;
    font-weight: 600;
  }

  //phone number verification code
  .mobile-verification-code {
    height: (7rem * 1.2) !important;
    width: (5.9rem * 1.2) !important;
    margin-top: 2.7rem !important;
    margin-bottom: (2.5rem * 1.2) !important;
  }

  .mobile-verification-code-label {
    margin-top: -0.9rem !important;
    font-size: 1.92rem !important;
  }
}

@media only screen and (min-width: 320px) and (max-width: 320px) and (min-height: 480px) and (max-height: 480px) {
  .terms-checkbox-onboard-position {
    font-size: 0.8rem !important;
  }
  .terms-and-condition-check-box {
    margin-top: 0rem !important;
  }

  .register-phone-image {
    margin: 2rem;
  }

  .canpay-logo-header-position {
    text-align: center;
    margin-left: 4rem !important;
  }

  .onboarding-back-btn-postion {
    margin-left: -1rem;
    margin-top: 1.37rem;
  }

  .back-btn-onboarding-header {
    height: 1.5rem;
    width: 1.7rem;
  }

  .canpay-logo-onboarding-header {
    height: 4.3rem !important;
    width: 4.3rem !important;
  }

  //email verification code

  .upgrade-image {
    height: 4.4rem !important;
    width: 4.4rem !important;
    margin: 1rem !important;
  }
  .verification-name-label {
    font-size: 0.832rem;
    font-weight: 600;
  }

  .verification-code-label {
    font-size: 0.685rem;
  }

  .re-send-verification {
    font-size: 0.685rem;
    margin-top: -0.4rem !important;
  }
  .verification-code-description-label {
    margin-top: 0.1968rem;
    font-size: 0.72rem;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  // onboarding success modal image
  .onboarding-success-modal-image {
    height: 6rem;
    width: 6rem;
    margin-top: 0rem;
    text-align: center !important;
    margin-left: -1.1rem;
  }
  .onboarding-success-popup-style {
    font-family: "Open Sans";
    font-size: 1rem;
    font-weight: 600;
    color: #000000;
  }

  //mobile number verification
  .mobile-phone-number-label {
    margin-left: 0rem !important;
    font-size: 0.685rem;
  }


  .pls-enter-you-number-label {
    font-size: (1.28rem * 0.8);
  }

  .duplicate-mobile-number-verification-image {
    height: (6rem * 0.7);
    width: (5rem * 0.7);
    margin-top: 1.5rem;
    margin-bottom: 0rem;
  }

  .primary-phone-number-registration-information {
    font-size: 0.7rem;
  }
  .primary-phone-number-label {
    font-size: 0.65rem;
    margin-top: 2rem;
  }
  .register-phone-number-text {
    margin-top: 0.8rem;
    font-size: 0.79rem;
    font-weight: 600;
  }

  //phone number verification code
  .mobile-verification-code {
    height: (7rem * 0.6) !important;
    width: (5.9rem * 0.6) !important;
    margin-top: 1.2rem !important;
    margin-bottom: (2.5rem * 0.6) !important;
  }

  .mobile-verification-code-label {
    margin-top: -0.9rem !important;
    font-size: 0.832rem !important;
  }

  // success page

  .success-image {
    height: (7rem * 0.65);
    width: (7rem * 0.65);
    margin: 2rem;
    text-align: center !important;
  }

  .success-text-onboarding {
    margin-top: 0.9rem;
    font-size: 1.0425rem !important;
  }
  .success-description-label {
    margin-top: 0.4rem;
    font-size: 0.6785rem !important;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .btn-next-onboarding {
    margin-top: 1.5rem !important;
  }

  //onboarding

  .quick-access-next-btn {
    margin-top: 2rem !important;
  }

  .quickaccesspin-label {
    margin-left: -6.7rem !important;
  }
  .quickaccesspin-label-two {
    margin-left: -8.2rem !important;
  }

  //onboarding modal
  .onboarding-upgrage-modal {
    margin-left: 0rem;
  }
  .onboarding-modal-message {
    font-size: 0.7rem !important;
  }
  .on-boarding-upgrade-alert-icon {
    margin-left: 0.4rem !important;
    height: 3rem;
    width: 3rem;
  }
}

@media only screen and (min-width: 411px) and (max-width: 411px) {
  .terms-checkbox-onboard-position {
    font-size: 0.8rem !important;
  }
  .terms-and-condition-check-box {
    margin-top: 5rem !important;
  }

  .canpay-logo-header-position {
    text-align: center;
    margin-left: 5rem !important;
  }

  .onboarding-back-btn-postion {
    margin-left: -1rem;
    margin-top: 2.37rem;
  }

  .back-btn-onboarding-header {
    height: 2rem;
    width: 2.7rem;
  }

  .canpay-logo-onboarding-header {
    height: 6.3rem !important;
    width: 6.3rem !important;
  }

  // onboarding success modal image
  .onboarding-success-modal-image {
    height: 6rem;
    width: 6rem;
    margin-top: 0rem;
    text-align: center !important;
    margin-left: -0rem;
  }
  .onboarding-success-popup-style {
    font-family: "Open Sans";
    font-size: 1rem;
    font-weight: 600;
    color: #000000;
  }

  //mobile number verification
  .mobile-phone-number-label {
    margin-left: 0rem !important;
    font-size: 1rem;
  }


  .pls-enter-you-number-label {
    font-size: 1.4rem;
  }

  .re-send-verification {
    font-size: 1rem;
  }

  //duplicate mobile number
  .duplicate-mobile-number-verification-image {
    height: (6rem);
    width: (5rem);
    margin-top: 3rem;
    margin-bottom: 2rem;
  }

  .primary-phone-number-registration-information {
    font-size: 1.1rem;
  }
  .primary-phone-number-label {
    font-size: 0.9rem;
    margin-top: 3.3rem;
  }
  .register-phone-number-text {
    margin-top: 1rem;
    font-size: 1.21rem;
    font-weight: 600;
  }

  .verification-name-label {
    font-size: 1.1rem;
  }

  .verification-code-description-label {
    margin-top: 0.3rem;
    font-size: 1.1rem;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .verification-code-label {
    font-size: 1rem;
    margin-left: 0rem !important;
  }

  .mobile-verification-code {
    height: (7rem * 1) !important;
    width: (5.9rem * 1) !important;
    margin-top: 1.2rem !important;
    margin-bottom: (2.5rem * 1) !important;
  }

  .mobile-verification-code-label {
    margin-top: -0.9rem !important;
    font-size: 1.1rem !important;
  }

  // success page

  .success-image {
    height: 7rem;
    width: 7rem;
    margin: 2rem;

    text-align: center !important;
  }

  .success-text-onboarding {
    margin-top: 0.9rem;
    font-size: 1.3066rem !important;
  }
  .success-description-label {
    margin-top: 0.4rem;
    font-size: 0.891rem !important;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }
  .on-boarding-upgrade-alert-icon {
    margin-left: -0.9rem;
  }
}

@media only screen and (min-width: 411px) and (max-width: 411px) and (min-height: 823px) and (max-height: 823px) {
  .terms-checkbox-onboard-position {
    font-size: 0.8rem !important;
  }
  .terms-and-condition-check-box {
    margin-top: 5rem !important;
  }

  .canpay-logo-header-position {
    text-align: center;
    margin-left: 5rem !important;
  }

  .onboarding-back-btn-postion {
    margin-left: -1rem;
    margin-top: 2.37rem;
  }

  .back-btn-onboarding-header {
    height: 2rem;
    width: 2.7rem;
  }

  .canpay-logo-onboarding-header {
    height: 6.3rem !important;
    width: 6.3rem !important;
  }

  // onboarding success modal image
  .onboarding-success-modal-image {
    height: 6rem;
    width: 6rem;
    margin-top: 0rem;
    text-align: center !important;
    margin-left: -0rem;
  }
  .onboarding-success-popup-style {
    font-family: "Open Sans";
    font-size: 1rem;
    font-weight: 600;
    color: #000000;
  }

  //mobile number verification
  .mobile-phone-number-label {
    margin-left: 0rem !important;
    font-size: 1rem;
  }


  .pls-enter-you-number-label {
    font-size: 1.4rem;
  }

  .re-send-verification {
    font-size: 1rem;
  }

  //duplicate mobile number
  .duplicate-mobile-number-verification-image {
    height: (6rem);
    width: (5rem);
    margin-top: 3rem;
    margin-bottom: 2rem;
  }

  .primary-phone-number-registration-information {
    font-size: 1.19rem;
  }
  .primary-phone-number-label {
    font-size: 1.05rem;
    margin-top: 6.3rem;
  }
  .register-phone-number-text {
    margin-top: 1rem;
    font-size: 1.4rem;
    font-weight: 600;
  }

  .verification-name-label {
    font-size: 1.1rem;
  }

  .verification-code-description-label {
    margin-top: 0.3rem;
    font-size: 1.1rem;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .verification-code-label {
    font-size: 1rem;
    margin-left: 0rem !important;
  }

  .mobile-verification-code {
    height: (7rem * 1) !important;
    width: (5.9rem * 1) !important;
    margin-top: 1.2rem !important;
    margin-bottom: (2.5rem * 1) !important;
  }

  .mobile-verification-code-label {
    margin-top: -0.9rem !important;
    font-size: 1.2rem !important;
  }

  // success page

  .success-image {
    height: 7rem;
    width: 7rem;
    margin: 2rem;

    text-align: center !important;
  }

  .success-text-onboarding {
    margin-top: 0.9rem;
    font-size: 1.4rem !important;
  }
  .success-description-label {
    margin-top: 0.4rem;
    font-size: 0.98rem !important;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }
  .btn-next-onboarding {
    margin-top: 4rem !important;
  }

  .on-boarding-upgrade-alert-icon {
    margin-left: -1rem;
  }
}

@media only screen and (min-width: 320px) and (max-width: 320px) and (min-height: 568px) and (max-height: 568px) {
  .terms-checkbox-onboard-position {
    font-size: 0.8rem !important;
  }
  .terms-and-condition-check-box {
    margin-top: 5rem !important;
  }

  .register-phone-image {
    margin: 2rem;
  }

  .canpay-logo-header-position {
    text-align: center;
    margin-left: 3rem !important;
  }

  .onboarding-back-btn-postion {
    margin-left: -1rem;
    margin-top: 2.37rem;
  }

  .back-btn-onboarding-header {
    height: 2rem;
    width: 2.7rem;
  }

  .canpay-logo-onboarding-header {
    height: 6.3rem !important;
    width: 6.3rem !important;
  }

  // email validation code
  .upgrade-image {
    margin-left: 1.25rem !important;
    margin-right: 1.25rem !important;
    margin-bottom: 1.25rem !important;
    margin-top: 0rem !important;
  }
  .btn-get-started {
    margin-bottom: 1rem !important;
  }
  .verification-name-label {
    font-size: 0.85rem;
  }

  .verification-code-label {
    font-size: 0.85rem;
    margin-left: 0rem !important;
  }

  .re-send-verification {
    font-size: 0.85rem !important;
    margin-top: -0.5rem !important;
  }
  .verification-code-description-label {
    margin-top: 0.246rem;
    font-size: 0.9rem;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .input-box-row-get-started {
    margin-top: 0.41rem !important;
    margin-left: 0.76916rem !important;
    margin-right: 0.76916rem !important;
  }

  // onboarding success modal image
  .onboarding-success-modal-image {
    height: 6rem;
    width: 6rem;
    margin-top: 0rem;
    text-align: center !important;
    margin-left: -1rem;
  }
  .onboarding-success-popup-style {
    font-family: "Open Sans";
    font-size: 1rem;
    font-weight: 600;
    color: #000000;
  }

  //mobile number verification
  .mobile-phone-number-label {
    margin-left: 0rem !important;
    font-size: 0.85rem;
  }


  .pls-enter-you-number-label {
    font-size: 1rem;
  }

  //duplicate mobile number
  .duplicate-mobile-number-verification-image {
    height: (6rem);
    width: (5rem);
    margin-top: 3rem;
    margin-bottom: 2rem;
  }

  .primary-phone-number-registration-information {
    font-size: 1.1rem;
  }
  .primary-phone-number-label {
    font-size: 0.9rem;
    margin-top: 3.3rem;
  }
  .register-phone-number-text {
    margin-top: 1rem;
    font-size: 1.21rem;
    font-weight: 600;
  }

  //duplicate mobile number

  .duplicate-mobile-number-verification-image {
    height: (6rem * 0.8);
    width: (5rem * 0.8);
    margin-top: 1.5rem;
    margin-bottom: 0rem;
  }

  .primary-phone-number-registration-information {
    font-size: 0.8rem;
  }
  .primary-phone-number-label {
    font-size: 0.72rem;
    margin-top: 2.3rem;
  }
  .register-phone-number-text {
    margin-top: 1rem;
    font-size: 0.85rem;
    font-weight: 600;
  }

  .mobile-verification-code {
    height: (7rem * 0.8) !important;
    width: (5.9rem * 0.8) !important;
    margin-top: 1.2rem !important;
    margin-bottom: (2.5rem * 0.7) !important;
  }

  .mobile-verification-code-label {
    margin-top: -0.9rem !important;
    font-size: 1.05rem !important;
  }
  // success page

  .success-image {
    height: (7rem * 0.8);
    width: (7rem * 0.8);
    margin: 2rem;

    text-align: center !important;
  }

  .success-text-onboarding {
    margin-top: 0.9rem;
    font-size: 1.4rem !important;
  }
  .success-description-label {
    margin-top: 0.4rem;
    font-size: 0.98rem !important;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .btn-next-onboarding {
    margin-top: 2rem !important;
  }

  // pin

  .quickaccesspin-heading {
    font-size: 1.2rem !important;
  }
  .quickaccesspin-sub-heading {
    font-size: 1rem !important;
  }
  .quickaccesspin-label {
    font-size: 0.95rem !important;
  }
  .quickaccesspin-label-two {
    font-size: 0.95rem !important;
  }
  .quick-access-next-btn {
    margin-top: 2rem !important;
  }

  // onboarding connect to bank

  .btn-next-onboarding-manual-banking {
    margin-bottom: 0.5rem !important;
  }

  .onboarding-upgrage-modal {
    margin-left: 0rem;
  }
  .onboarding-modal-message {
    font-size: 1rem !important;
  }
  .on-boarding-upgrade-alert-icon {
    margin-left: -0.5rem;
    height: 4.5rem;
    width: 4.5rem;
  }
}

@media only screen and (min-width: 375px) and (max-width: 375px) and (min-height: 667px) and (max-height: 667px) {
  .terms-checkbox-onboard-position {
    font-size: 0.8rem !important;
  }
  .terms-and-condition-check-box {
    margin-top: 5rem !important;
  }

  .register-phone-image {
    margin: 2rem;
  }

  .canpay-logo-header-position {
    text-align: center;
    margin-left: 4rem !important;
  }

  .onboarding-back-btn-postion {
    margin-left: -1rem;
    margin-top: 2.37rem;
  }

  .back-btn-onboarding-header {
    height: 2rem;
    width: 2.7rem;
  }

  .canpay-logo-onboarding-header {
    height: 6.3rem !important;
    width: 6.3rem !important;
  }

  //email validation code

  .verification-name-label {
    font-size: 1rem;
    font-weight: 600;
  }

  .verification-code-label {
    font-size: 0.75rem;
    text-align: left !important;
    margin-left: 0rem !important;
  }
  .re-send-verification {
    font-size: 0.75rem;
    margin-top: -0.5rem !important;
  }
  .verification-code-description-label {
    margin-top: 0.2091rem;
    font-size: 0.8rem;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .input-box-row-get-started {
    margin-top: 0.3485rem !important;
    margin-left: 0.653786rem !important;
    margin-right: 0.653786rem !important;
  }

  .onboarding-success-modal-image {
    height: 6rem;
    width: 6rem;
    margin-top: 0rem;
    text-align: center !important;
    margin-left: -0.5rem;
  }
  .onboarding-success-popup-style {
    font-family: "Open Sans";
    font-size: 1rem;
    font-weight: 600;
    color: #000000;
  }

  //mobile number verification
  .mobile-phone-number-label {
    margin-left: 0rem !important;
    font-size: 0.85rem;
  }


  .pls-enter-you-number-label {
    font-size: 1.3rem;
  }

  //duplicate mobile number

  .duplicate-mobile-number-verification-image {
    height: (6rem * 0.9);
    width: (5rem * 0.9);
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
  }

  .primary-phone-number-registration-information {
    font-size: 0.9rem;
  }
  .primary-phone-number-label {
    font-size: 0.8rem;
    margin-top: 5rem;
  }
  .register-phone-number-text {
    margin-top: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
  }

  .mobile-verification-code {
    height: (7rem * 0.8) !important;
    width: (5.9rem * 0.8) !important;
    margin-top: 3.2rem !important;
    margin-bottom: (2.5rem * 0.7) !important;
  }

  .mobile-verification-code-label {
    margin-top: -0.9rem !important;
    font-size: 1.05rem !important;
  }
  //success page
  .success-image {
    height: (7rem * 0.8);
    width: 7rem * 0.8;
    margin: 2rem;

    text-align: center !important;
  }

  .btn-next-onboarding {
    margin-top: 4rem !important;
  }

  .success-text-onboarding {
    font-size: 1.39rem !important;
  }

  .success-description-label {
    font-size: 1rem !important;
  }

  //pin

  .quickaccesspin-heading {
    font-size: 1.2rem !important;
  }
  .quickaccesspin-sub-heading {
    font-size: 1rem !important;
  }
  .quickaccesspin-label {
    font-size: 0.95rem !important;
  }
  .quickaccesspin-label-two {
    font-size: 0.95rem !important;
  }
  .quick-access-next-btn {
    margin-top: 2rem !important;
  }

  .on-boarding-upgrade-alert-icon {
    margin-left: -1.1rem;
  }
}

@media only screen and (min-width: 414px) and (max-width: 414px) and (min-height: 736px) and (max-height: 736px) {
  .terms-checkbox-onboard-position {
    font-size: 0.8rem !important;
  }
  .terms-and-condition-check-box {
    margin-top: 5rem !important;
  }

  .canpay-logo-header-position {
    text-align: center;
    margin-left: 5rem !important;
  }

  .onboarding-back-btn-postion {
    margin-left: -1rem;
    margin-top: 2.37rem;
  }

  .back-btn-onboarding-header {
    height: 2rem;
    width: 2.7rem;
  }

  .canpay-logo-onboarding-header {
    height: 6.3rem !important;
    width: 6.3rem !important;
  }

  //email validation code

  .verification-code-label {
    font-size: 0.8rem;
    margin-left: 0rem !important;
  }

  .verification-name-label {
    font-size: 1.1rem;
    font-weight: 600;

    text-align: center;
  }

  .verification-code-description-label {
    margin-top: 0.3rem;
    font-size: 0.99rem;
    text-align: center;

    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .re-send-verification {
    font-size: 0.8rem;
    margin-top: -0.85rem !important;
  }
  //onboarding success modal

  .onboarding-success-modal-image {
    height: 6rem;
    width: 6rem;
    margin-top: 0rem;
    text-align: center !important;
    margin-left: 0rem;
  }
  .onboarding-success-popup-style {
    font-family: "Open Sans";
    font-size: 1rem;
    font-weight: 600;
    color: #000000;
  }

  //mobile number verification
  .mobile-phone-number-label {
    margin-left: 0rem !important;
    font-size: 0.85rem;
  }


  .pls-enter-you-number-label {
    font-size: 1.3rem;
  }

  //duplicate mobile number
  .duplicate-mobile-number-verification-image {
    height: (6rem);
    width: (5rem);
    margin-top: 3rem;
    margin-bottom: 2rem;
  }

  .primary-phone-number-registration-information {
    font-size: 1.05rem;
  }
  .primary-phone-number-label {
    font-size: 0.9rem;
    margin-top: 3.3rem;
  }
  .register-phone-number-text {
    margin-top: 1rem;
    font-size: 1.21rem;
    font-weight: 600;
  }

  .mobile-verification-code {
    height: (7rem * 0.8) !important;
    width: (5.9rem * 0.8) !important;
    margin-top: 4.2rem !important;
    margin-bottom: (2.5rem * 0.7) !important;
  }

  .mobile-verification-code-label {
    margin-top: -0.9rem !important;
    font-size: 1.2rem !important;
  }
  //success page
  .success-image {
    height: (7rem * 0.8);
    width: 7rem * 0.8;
    margin: 2rem;

    text-align: center !important;
  }

  .btn-next-onboarding {
    margin-top: 4rem !important;
  }

  .success-text-onboarding {
    font-size: 1.39rem !important;
  }

  .success-description-label {
    font-size: 1rem !important;
  }

  //pin

  .quick-access-next-btn {
    margin-top: 5rem !important;
  }

  .on-boarding-upgrade-alert-icon {
    margin-left: -0.7rem;
  }
}
@media only screen and (min-width: 375px) and (max-width: 375px) and (min-height: 812px) and (max-height: 812px) {
  .terms-checkbox-onboard-position {
    font-size: 0.8rem !important;
  }
  .terms-and-condition-check-box {
    margin-top: 5rem !important;
  }

  .canpay-logo-header-position {
    text-align: center;
    margin-left: 4rem !important;
  }

  .onboarding-back-btn-postion {
    margin-left: -1rem;
    margin-top: 2.37rem;
  }

  .back-btn-onboarding-header {
    height: 2rem;
    width: 2.7rem;
  }

  .canpay-logo-onboarding-header {
    height: 6.3rem !important;
    width: 6.3rem !important;
  }
  //email verification code
  .verification-name-label {
    font-size: 1rem;
    text-align: center !important;
  }

  .verification-code-description-label {
    margin-top: 1.3rem;
    font-size: 0.95rem;
    text-align: center;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .verification-code-label {
    font-size: 0.85rem;
    text-align: left !important;
    margin-left: 0rem !important;
  }

  .re-send-verification {
    font-size: 1rem;
    margin-top: -0.5rem !important;
  }

  //onboarding success modal

  .onboarding-success-modal-image {
    height: 6rem;
    width: 6rem;
    margin-top: 0rem;
    text-align: center !important;
    margin-left: -0.4rem;
  }
  .onboarding-success-popup-style {
    font-family: "Open Sans";
    font-size: 1rem;
    font-weight: 600;
    color: #000000;
  }

  //mobile number verification
  .mobile-phone-number-label {
    margin-left: 0rem !important;
    font-size: 1rem;
  }


  .pls-enter-you-number-label {
    font-size: 1.3rem;
  }

  //duplicate mobile number

  .duplicate-mobile-number-verification-image {
    height: 6rem * 1.2;
    width: 5rem * 1.2;
    margin-top: 3rem;
    margin-bottom: 2rem;
  }

  .primary-phone-number-registration-information {
    font-size: 1.15rem;
  }
  .primary-phone-number-label {
    margin-top: 3.5rem;
    font-size: 1rem;
  }

  .register-phone-number-text {
    font-size: 1.35rem;
  }

  .mobile-verification-code {
    height: (7rem * 0.8) !important;
    width: (5.9rem * 0.8) !important;
    margin-top: 4.2rem !important;
    margin-bottom: (2.5rem * 0.7) !important;
  }

  .mobile-verification-code-label {
    margin-top: -0.9rem !important;
    font-size: 1.2rem !important;
  }

  //success
  .success-image {
    height: 7rem;
    width: 7rem;
    margin: 2rem;

    text-align: center !important;
  }

  .btn-next-onboarding {
    margin-top: 4rem !important;
  }

  .success-text-onboarding {
    font-size: 1.6rem !important;
  }

  .success-description-label {
    font-size: 1.2rem !important;
  }

  //pin

  .quickaccesspin-heading {
    font-size: 1.6rem !important;
  }
  .quickaccesspin-sub-heading {
    font-size: 1.2rem !important;
  }
  .quickaccesspin-label {
    font-size: 0.95rem !important;
  }
  .quickaccesspin-label-two {
    font-size: 0.95rem !important;
  }
  .quick-access-next-btn {
    margin-top: 4rem !important;
  }

  .on-boarding-upgrade-alert-icon {
    margin-left: -1.2rem;
  }
}

@media only screen and (min-width: 1024px) and (max-width: 1024px) and (min-height: 1366px) and (max-height: 1366px) {
  .canpay-logo-header-position {
    text-align: center;
    margin-left: 11rem !important;
  }

  .onboarding-back-btn-postion {
    margin-left: -1rem;
    margin-top: 3.37rem;
  }

  .back-btn-onboarding-header {
    height: 2.7rem;
    width: 3.7rem;
  }

  .canpay-logo-onboarding-header {
    height: 9rem !important;
    width: 9rem !important;
  }

  //onboarding success modal

  .onboarding-success-modal-image {
    height: 6rem;
    width: 6rem;
    margin-top: 0rem;
    text-align: center !important;
    margin-left: 1rem;
  }
  .onboarding-success-popup-style {
    font-family: "Open Sans";
    font-size: 1rem;
    font-weight: 600;
    color: #000000;
  }

  // mobile number verification

  .pls-enter-you-number-label {
    font-size: 2rem;
  }
  .mobile-phone-number-label {
    font-size: 1.6rem;
  }

  .mobile-verification-code {
    height: (7rem * 1.3) !important;
    width: (5.9rem * 1.3) !important;
    margin-top: 4.2rem !important;
    margin-bottom: (2.5rem * 1.3) !important;
  }

  .mobile-verification-code-label {
    margin-top: -0.9rem !important;
    font-size: 1.9rem !important;
  }

  //success page
  .success-image {
    height: (7rem * 1.3);
    width: (7rem * 1.3);
    margin: 2rem;

    text-align: center !important;
  }

  .btn-next-onboarding {
    margin-top: 4rem !important;
  }

  .success-text-onboarding {
    font-size: 1.9rem !important;
  }

  .success-description-label {
    font-size: 1.5rem !important;
  }

  //pin

  .quickaccesspin-heading {
    font-size: 1.9rem !important;
  }
  .quickaccesspin-sub-heading {
    font-size: 1.6rem !important;
  }
  .quickaccesspin-label {
    margin-left: -1.4rem !important;
    font-size: 1.2rem !important;
  }
  .quickaccesspin-label-two {
    margin-left: -3.9rem !important;
    font-size: 1.2rem !important;
  }
  .quick-access-next-btn {
    margin-top: 4rem !important;
  }
  .onboarding-upgrage-modal {
    margin-left: 0.29rem !important;
  }
}

@media only screen and (min-width: 540px) and (max-width: 540px) and (min-height: 720px) and (max-height: 720px) {
  .terms-checkbox-onboard-position {
    font-size: 0.8rem !important;
  }
  .terms-and-condition-check-box {
    margin-top: 5rem !important;
  }

  .canpay-logo-header-position {
    text-align: center;
    margin-left: 6.5rem !important;
  }

  .onboarding-back-btn-postion {
    margin-left: -1rem;
    margin-top: 2.37rem;
  }

  .back-btn-onboarding-header {
    height: 2rem;
    width: 2.7rem;
  }

  .canpay-logo-onboarding-header {
    height: 6.3rem !important;
    width: 6.3rem !important;
  }

  //onboarding success modal

  .onboarding-success-modal-image {
    height: 6rem;
    width: 6rem;
    margin-top: 0rem;
    text-align: center !important;
    margin-left: 1.2rem;
  }
  .onboarding-success-popup-style {
    font-family: "Open Sans";
    font-size: 1rem;
    font-weight: 600;
    color: #000000;
  }

  //duplicate mobile number
  .duplicate-mobile-number-verification-image {
    height: (6rem * 1);
    width: (5rem * 1);
    margin-top: 3rem;
    margin-bottom: 2rem;
  }

  .primary-phone-number-registration-information {
    font-size: 1.1rem;
  }
  .primary-phone-number-label {
    font-size: 1rem;
    margin-top: 3.3rem;
  }
  .register-phone-number-text {
    margin-top: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
  }

  .verification-code-description-label {
    font-size: 1.1rem;
  }
  .verification-code-label {
    font-size: 0.9rem;
  }
  .re-send-verification {
    font-size: 0.9rem;
  }

  //success page
  .success-image {
    height: (7rem * 1);
    width: (7rem * 1);
    margin: 2rem;

    text-align: center !important;
  }

  .btn-next-onboarding {
    margin-top: 4rem !important;
  }

  .success-text-onboarding {
    font-size: 1.35rem !important;
  }

  .success-description-label {
    font-size: 1rem !important;
  }

  //pin

  .quick-access-next-btn {
    margin-top: 3rem !important;
  }
}

@media only screen and (min-width: 280px) and (max-width: 280px) and (min-height: 653px) and (max-height: 653px) {
  .terms-checkbox-onboard-position {
    font-size: 0.8rem !important;
  }
  .terms-and-condition-check-box {
    margin-top: 5rem !important;
  }

  .canpay-logo-header-position {
    text-align: center;
    margin-left: 2rem !important;
  }

  .onboarding-back-btn-postion {
    margin-left: -1rem;
    margin-top: 2.37rem;
  }

  .back-btn-onboarding-header {
    height: 2rem;
    width: 2.7rem;
  }

  .canpay-logo-onboarding-header {
    height: 6.3rem !important;
    width: 6.3rem !important;
  }

  //email verification code

  .row-for-input {
    margin-right: 0.9rem !important;
    margin-left: 0.9rem !important;
    margin-bottom: 0rem !important;
  }

  .verification-name-label {
    font-size: 0.71rem !important;
    text-align: center !important;
  }

  .verification-code-label {
    margin-left: 0rem !important;
  }
  .re-send-verification {
    font-size: 0.8rem;
    margin-top: -0.5rem !important;
  }
  .verification-code-description-label {
    margin-top: 1rem;
    font-size: 0.66rem;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }
  //onboarding success modal

  .onboarding-success-modal-image {
    height: 6rem;
    width: 6rem;
    margin-top: 0rem;
    text-align: center !important;
    text-align: center !important;
  }
  .onboarding-success-popup-style {
    font-family: "Open Sans";
    font-size: 1rem;
    font-weight: 600;
    color: #000000;
  }

  //mobile number verification
  .mobile-phone-number-label {
    font-size: 0.8rem;
  }
  .mobile-number-verification-image {

    height: (7rem * 0.8);
    width: (5rem * 0.8);
  }

  .pls-enter-you-number-label {
    font-size: 1rem;
  }

  //duplicate mobile number
  .duplicate-mobile-number-verification-image {
    height: 6rem * 0.8;
    width: 5rem * 0.8;
    margin-top: 3rem * 0.8;
    margin-bottom: 2rem * 0.8;
    margin-left: 1.5rem;
  }
  .register-phone-number-text {
    font-size: 0.95rem;
  }

  .primary-phone-number-registration-information {
    margin-top: 0.5rem;
    font-size: 0.8rem;
  }

  .primary-phone-number-label {
    font-size: 0.75rem;
  }

  .mobile-verification-code {
    height: (7rem * 0.65) !important;
    width: (5.9rem * 0.65) !important;
    margin-top: 4.2rem !important;
    margin-bottom: (2.5rem * 0.65) !important;
  }

  .mobile-verification-code-label {
    margin-top: -0.9rem !important;
    font-size: 1rem !important;
  }

  //success page
  .success-image {
    height: (7rem * 0.9);
    width: (7rem * 0.9);
    margin: 2rem;

    text-align: center !important;
  }

  .btn-next-onboarding {
    margin-top: 4rem !important;
  }

  .success-text-onboarding {
    font-size: 1.35rem !important;
  }

  .success-description-label {
    font-size: 1rem !important;
  }
}

// 393 x 851 dp

@media only screen and (min-width: 393px) and (max-width: 394px) {
  .onboarding-upgrage-modal {
    margin-left: -1.45rem !important;
  }

  //canpay header

  .canpay-logo-header-position {
    text-align: center;
    margin-left: 4rem !important;
  }

  .onboarding-back-btn-postion {
    margin-left: -1rem;
    margin-top: 2.37rem;
  }

  .back-btn-onboarding-header {
    height: (2rem * 1.1);
    width: (2.7rem * 1.1);
  }

  .canpay-logo-onboarding-header {
    height: 6.3rem !important;
    width: 6.3rem !important;
  }
  .upgrade-image {
    height: (7rem * 0.8) !important;
    width: (7rem * 0.8) !important;
    margin: 2.5rem !important;
    margin-top: 5.5rem !important;
  }

  .upgrade-to-the-new-CanPay {
    font-size: 1.49rem !important;
    font-weight: 600;
  }
  .as-an-existing-customer-there-are-just-a-few-easy-steps-to-get {
    margin-top: 0.3rem !important;
    font-size: 1.05rem !important;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .enter-the-email-you-use-to-sign-in-to-CanPay {
    font-size: 0.9rem !important;
  }

  //email verification code

  .verification-name-label {
    font-size: 0.99rem;
    text-align: center !important;
  }

  .verification-code-label {
    font-size: 0.85rem;
    margin-left: 0.1rem !important;
  }

  .re-send-verification {
    font-size: 0.85rem !important;
    margin-top: -0.5rem !important;
  }
  .verification-code-description-label {
    margin-top: 0.246rem;
    font-size: 0.9rem;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .input-box-row-get-started {
    margin-top: 0.41rem !important;
    margin-left: 0.76916rem !important;
    margin-right: 0.76916rem !important;
  }

  .lockkey-icon {
    height: 2.9rem;
    width: 2.9rem;
  }

  .re-send-verification {
    font-size: 0.93rem !important;
    margin-top: -0.5rem !important;
  }

  // email verification success

  .onboarding-success-modal-image {
    height: 6rem;
    width: 6rem;
    margin-top: 0rem;
    text-align: center !important;
    margin-left: -0.6rem;
  }
  //mobile number verification image
  .mobile-phone-number-label {
    font-size: 0.95rem;
  }
  .register-phone-number-text {
    margin-top: 1rem;
    font-size: 1.4rem;
    font-weight: 600;
  }

  .primary-phone-number-registration-information {
    margin-top: 0.3rem;
    font-size: 1rem;
    padding-left: 0rem !important;
    padding-right: 0rem !important;
  }

  .primary-phone-number-label {
    margin-top: 3.28rem;
    font-size: 0.9rem;
    margin-left: 0rem !important;
  }

  .register-phone-number-text {
    margin-top: 0.8rem;
    font-size: 1.2rem;
    font-weight: 600;
  }
  .bank-phone-image {
    margin-top: 0.5rem;
    height: 4rem;
    width: 4rem;
  }

  //onboarding-signing

  .onboard-btn-sign-in {
    background-color: $cp-primary !important;
  }

  .canpay-logo-onboard {
    height: 8.5rem;
    width: 8.5rem;
    margin-top: 1.7rem;
  }
  .canpay-moble-screen {
    height: 15rem;
    width: 15rem;
    margin-top: 3.35rem;
  }

  .get-the-canpay-app-label {
    font-size: 1.4rem !important;
    font-weight: 700;
  }
  .get-the-canpay-app-description-label {
    font-size: 1rem !important;
    font-weight: 400;
  }
  .next-button-color-onboardingsingin {
    background-color: $cp-input-email !important;
    padding: 2rem;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .setup-label-onboarding
  {
    color: $cp-black;
    font-weight: 400;
    font-size: 17px;
    font-family: inherit;
    letter-spacing: 1px;
  }
  .manual-text-onboarding{
    font-size: 15px;
    color: $cp-black;
    font-family: $cp-font;
    display: block;
    font-weight: 100;
}
.capture-text-onboarding{
  font-size: 17px;
  color: $cp-black;
  font-family: $cp-font;
  display: block;
  font-weight: 100;
  margin-bottom: 60px;
}
}
