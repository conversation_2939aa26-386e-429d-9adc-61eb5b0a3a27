<template>
  <div>
    <b-modal
      ref="canpay-crew-create-petition"
      hide-footer      
      hide-header
      @hide="onModalHide"
      centered
      id="canpay-crew-create-petition"
      title="BootstrapVue"
    >
    <div style="font-family:Open Sans">
    <!------ initiating the canpay crew start -------->
      <div class="text-center mt-4" v-if="stepForCreatingPetition == 'initiateCreatingPetition'">
        <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 152 152" fill="none">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M29.5924 65.7609H95.3533C97.1683 65.7609 98.6413 64.2878 98.6413 62.4728C98.6413 60.6578 97.1683 59.1848 95.3533 59.1848H29.5924C27.7774 59.1848 26.3044 60.6578 26.3044 62.4728C26.3044 64.2878 27.7774 65.7609 29.5924 65.7609Z" fill="#179346"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M29.5924 85.4891H75.625C77.44 85.4891 78.9131 84.0161 78.9131 82.2011C78.9131 80.3861 77.44 78.913 75.625 78.913H29.5924C27.7774 78.913 26.3044 80.3861 26.3044 82.2011C26.3044 84.0161 27.7774 85.4891 29.5924 85.4891Z" fill="#179346"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M29.5924 105.217H69.0489C70.8639 105.217 72.337 103.744 72.337 101.929C72.337 100.114 70.8639 98.6413 69.0489 98.6413H29.5924C27.7774 98.6413 26.3044 100.114 26.3044 101.929C26.3044 103.744 27.7774 105.217 29.5924 105.217Z" fill="#179346"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M29.5924 124.946H69.0489C70.8639 124.946 72.337 123.473 72.337 121.658C72.337 119.843 70.8639 118.37 69.0489 118.37H29.5924C27.7774 118.37 26.3044 119.843 26.3044 121.658C26.3044 123.473 27.7774 124.946 29.5924 124.946Z" fill="#179346"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M115.082 151.25H16.4402C12.0803 151.25 7.8979 149.517 4.81371 146.436C1.73281 143.352 0 139.17 0 134.81C0 110.278 0 58.4976 0 41.6168C0 37.2569 1.73281 33.0744 4.81371 29.9902L29.9902 4.81366C33.0744 1.73276 37.2568 0 41.6168 0H108.505C112.865 0 117.048 1.73276 120.132 4.81366C123.213 7.89785 124.946 12.0803 124.946 16.4402V80.2776C140.12 84.5751 151.25 98.5394 151.25 115.082C151.25 135.043 135.043 151.25 115.082 151.25ZM118.37 79.061V16.4402C118.37 13.8229 117.331 11.3141 115.479 9.46627C113.632 7.6151 111.123 6.57609 108.505 6.57609C89.4348 6.57609 54.9333 6.57609 41.6168 6.57609C40.8835 6.57609 40.1602 6.65828 39.4565 6.81611V23.0163C39.4565 27.3762 37.7237 31.5587 34.6428 34.6429C31.5586 37.7238 27.3762 39.4565 23.0163 39.4565H6.81611C6.65828 40.1602 6.57609 40.8836 6.57609 41.6168V134.81C6.57609 137.427 7.6151 139.936 9.46627 141.784C11.3141 143.635 13.8229 144.674 16.4402 144.674H94.2912C84.9926 138.127 78.913 127.31 78.913 115.082C78.913 95.1198 95.1198 78.913 115.082 78.913C116.19 78.913 117.285 78.9624 118.37 79.061ZM115.082 85.4891C131.413 85.4891 144.674 98.7498 144.674 115.082C144.674 131.413 131.413 144.674 115.082 144.674C98.7498 144.674 85.4891 131.413 85.4891 115.082C85.4891 98.7498 98.7498 85.4891 115.082 85.4891ZM32.8804 11.2254L11.2254 32.8804H23.0163C25.6336 32.8804 28.1424 31.8414 29.9902 29.9902C31.8414 28.1424 32.8804 25.6336 32.8804 23.0163V11.2254Z" fill="#179346"/>
        <path d="M111.62 102.24C111.62 100.168 113.3 98.4884 115.372 98.4884C117.444 98.4884 119.124 100.168 119.124 102.24V127.566C119.124 129.638 117.444 131.318 115.372 131.318C113.3 131.318 111.62 129.638 111.62 127.566V102.24Z" fill="#179346"/>
        <path d="M103.178 118.186C101.106 118.186 99.4263 116.506 99.4263 114.434C99.4263 112.362 101.106 110.682 103.178 110.682H128.504C130.576 110.682 132.256 112.362 132.256 114.434C132.256 116.506 130.576 118.186 128.504 118.186H103.178Z" fill="#179346"/>
        </svg>
        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-font-18 canpay-crew-text-700 mt-4">Create new Petition & become</p>
        <p class="canpay-crew-p-margin-bottom canpay-crew-text-font-18 canpay-crew-text-700">the Mayor of this location!</p>
        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-font-16 mt-4">As the Mayor, you get extra</p>
        <p class=" canpay-crew-text-font-16" style="margin-bottom:40px;">points and spins.</p>

        <hr style="border:0.5px solid #DFDFDF;">
        <div class="row " style="margin-top:35px;">
            <div class="col-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 104 104" fill="none">
                <circle cx="52" cy="52" r="52" fill="#179346"/>
                <path d="M58.1975 55.2093C57.1131 55.2205 56.0398 54.9913 55.0546 54.5381C54.0695 54.085 53.197 53.4191 52.5 52.5884C51.7978 53.413 50.9243 54.0747 49.9403 54.5274C48.9563 54.98 47.8855 55.2127 46.8025 55.2093C45.7181 55.2205 44.6448 54.9913 43.6596 54.5381C42.6745 54.085 41.802 53.4191 41.105 52.5884C40.8948 52.8405 40.6663 53.0766 40.4213 53.2949C39.654 53.993 38.7475 54.5205 37.7615 54.8426C36.7755 55.1648 35.7324 55.2742 34.701 55.1637C32.8342 54.9337 31.1181 54.0226 29.8816 52.6053C28.6451 51.1879 27.9753 49.364 28.0007 47.4833V45.8651C27.9988 44.7036 28.1835 43.5494 28.5477 42.4465L32.1257 31.7349C32.389 30.9394 32.8963 30.247 33.5753 29.7561C34.2544 29.2652 35.0709 29.0006 35.9088 29H69.0911C69.929 29.0006 70.7455 29.2652 71.4246 29.7561C72.1037 30.247 72.6109 30.9394 72.8742 31.7349L76.4523 42.4465C76.8164 43.5494 77.0011 44.7036 76.9992 45.8651V47.4833C77.0259 49.3571 76.3625 51.1753 75.1353 52.5916C73.9082 54.0079 72.203 54.9234 70.3445 55.1637C69.313 55.2772 68.2691 55.1691 67.2826 54.8469C66.2962 54.5246 65.3899 53.9955 64.6243 53.2949C64.3736 53.067 64.1229 52.7935 63.895 52.5428C63.1961 53.3746 62.3243 54.0441 61.3404 54.5046C60.3564 54.9651 59.2839 55.2056 58.1975 55.2093ZM35.9088 32.4186C35.7892 32.4204 35.6729 32.4584 35.5753 32.5276C35.4777 32.5967 35.4033 32.6938 35.3619 32.806L31.8066 43.5177C31.5597 44.2526 31.429 45.0215 31.4192 45.7967V47.4149C31.3858 48.4437 31.7319 49.449 32.3917 50.2391C33.0515 51.0292 33.9789 51.549 34.9972 51.6995C35.5468 51.7564 36.1022 51.6986 36.6282 51.5295C37.1542 51.3605 37.6394 51.084 38.0529 50.7176C38.4664 50.3512 38.7993 49.9028 39.0305 49.401C39.2616 48.8992 39.386 48.3548 39.3957 47.8023C39.3957 47.349 39.5758 46.9142 39.8963 46.5937C40.2169 46.2731 40.6516 46.093 41.105 46.093C41.5583 46.093 41.993 46.2731 42.3136 46.5937C42.6341 46.9142 42.8142 47.349 42.8142 47.8023C42.7881 48.333 42.8734 48.8632 43.0646 49.3589C43.2558 49.8546 43.5487 50.3048 43.9243 50.6805C44.3 51.0562 44.7502 51.3491 45.2459 51.5403C45.7416 51.7315 46.2718 51.8168 46.8025 51.7907C47.8584 51.7847 48.8693 51.3626 49.616 50.6159C50.3626 49.8692 50.7847 48.8583 50.7907 47.8023C50.7907 47.349 50.9708 46.9142 51.2913 46.5937C51.6119 46.2731 52.0466 46.093 52.5 46.093C52.9533 46.093 53.388 46.2731 53.7086 46.5937C54.0291 46.9142 54.2092 47.349 54.2092 47.8023C54.1831 48.333 54.2684 48.8632 54.4596 49.3589C54.6508 49.8546 54.9437 50.3048 55.3194 50.6805C55.695 51.0562 56.1452 51.3491 56.6409 51.5403C57.1366 51.7315 57.6668 51.8168 58.1975 51.7907C59.2534 51.7847 60.2643 51.3626 61.011 50.6159C61.7576 49.8692 62.1797 48.8583 62.1857 47.8023C62.1857 47.349 62.3658 46.9142 62.6863 46.5937C63.0069 46.2731 63.4416 46.093 63.895 46.093C64.3483 46.093 64.7831 46.2731 65.1036 46.5937C65.4241 46.9142 65.6042 47.349 65.6042 47.8023C65.6045 48.3605 65.7219 48.9125 65.9488 49.4225C66.1758 49.9325 66.5073 50.3891 66.9219 50.7629C67.3365 51.1367 67.825 51.4193 68.3556 51.5924C68.8863 51.7655 69.4474 51.8253 70.0027 51.7679C71.021 51.6174 71.9484 51.0976 72.6082 50.3075C73.268 49.5173 73.6142 48.5121 73.5807 47.4833V45.8651C73.5709 45.0899 73.4402 44.321 73.1933 43.586L69.6381 32.8744C69.5967 32.7622 69.5222 32.6651 69.4246 32.5959C69.327 32.5268 69.2107 32.4888 69.0911 32.487L35.9088 32.4186Z" fill="white"/>
                <path d="M70.732 78H34.2679C33.212 77.994 32.2011 77.5719 31.4544 76.8252C30.7078 76.0786 30.2857 75.0676 30.2797 74.0116V52.3149C30.2797 51.8615 30.4598 51.4268 30.7803 51.1062C31.1009 50.7857 31.5356 50.6056 31.9889 50.6056C32.4423 50.6056 32.877 50.7857 33.1976 51.1062C33.5181 51.4268 33.6982 51.8615 33.6982 52.3149V74.0116C33.6982 74.1627 33.7582 74.3077 33.8651 74.4145C33.9719 74.5214 34.1168 74.5814 34.2679 74.5814H70.732C70.8831 74.5814 71.028 74.5214 71.1348 74.4145C71.2417 74.3077 71.3017 74.1627 71.3017 74.0116V52.2921C71.3017 51.8388 71.4818 51.404 71.8023 51.0834C72.1229 50.7629 72.5577 50.5828 73.011 50.5828C73.4643 50.5828 73.8991 50.7629 74.2196 51.0834C74.5401 51.404 74.7202 51.8388 74.7202 52.2921V74.0116C74.7142 75.0676 74.2921 76.0786 73.5455 76.8252C72.7988 77.5719 71.7879 77.994 70.732 78Z" fill="white"/>
                <path d="M60.4765 78H44.5235C44.072 77.9941 43.6406 77.8121 43.3214 77.4928C43.0021 77.1735 42.8201 76.7422 42.8142 76.2907V64.8953C42.8202 63.8394 43.2423 62.8284 43.9889 62.0818C44.7356 61.3351 45.7466 60.913 46.8025 60.907H58.1975C59.2534 60.913 60.2643 61.3351 61.011 62.0818C61.7576 62.8284 62.1797 63.8394 62.1857 64.8953V76.2907C62.1798 76.7422 61.9978 77.1735 61.6786 77.4928C61.3593 77.8121 60.928 77.9941 60.4765 78ZM46.2327 74.5814H58.7672V64.8953C58.7672 64.7442 58.7072 64.5993 58.6003 64.4925C58.4935 64.3856 58.3486 64.3256 58.1975 64.3256H46.8025C46.6513 64.3256 46.5064 64.3856 46.3996 64.4925C46.2927 64.5993 46.2327 64.7442 46.2327 64.8953V74.5814Z" fill="white"/>
                </svg>
            </div>
            <div class="col-10 text-left">
                <p class="canpay-crew-p-margin-bottom-1  ">{{storeData.store_name}} - {{storeData.state}}</p>
                <p class="canpay-crew-p-margin-bottom-1  " v-html="storeAddress(storeData)"></p>
            </div>
        </div>
        <div class="mt-4 mb-5">
            <button class="canpay-crew-sign-petition-modal-button" v-on:click="canpayCrewCreateNewPetition()">
                Yes, make me the Mayor
            </button>
            <button class="canpay-crew-sign-petition-not-ok-modal-button mt-2" v-on:click="closeCanPayCrewCreatePetition()">
                Not Right Now
            </button>
        </div>
      </div>
      <!------ initiating the canpay crew end -------->
      <!------ initiating the canpay crew fill up start -------->
      <div class="text-center mt-4" v-if="stepForCreatingPetition=='formFillUpForCreatingPetition'">
        <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 152 152" fill="none">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M29.5924 65.7609H95.3533C97.1683 65.7609 98.6413 64.2878 98.6413 62.4728C98.6413 60.6578 97.1683 59.1848 95.3533 59.1848H29.5924C27.7774 59.1848 26.3044 60.6578 26.3044 62.4728C26.3044 64.2878 27.7774 65.7609 29.5924 65.7609Z" fill="#179346"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M29.5924 85.4891H75.625C77.44 85.4891 78.9131 84.0161 78.9131 82.2011C78.9131 80.3861 77.44 78.913 75.625 78.913H29.5924C27.7774 78.913 26.3044 80.3861 26.3044 82.2011C26.3044 84.0161 27.7774 85.4891 29.5924 85.4891Z" fill="#179346"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M29.5924 105.217H69.0489C70.8639 105.217 72.337 103.744 72.337 101.929C72.337 100.114 70.8639 98.6413 69.0489 98.6413H29.5924C27.7774 98.6413 26.3044 100.114 26.3044 101.929C26.3044 103.744 27.7774 105.217 29.5924 105.217Z" fill="#179346"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M29.5924 124.946H69.0489C70.8639 124.946 72.337 123.473 72.337 121.658C72.337 119.843 70.8639 118.37 69.0489 118.37H29.5924C27.7774 118.37 26.3044 119.843 26.3044 121.658C26.3044 123.473 27.7774 124.946 29.5924 124.946Z" fill="#179346"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M115.082 151.25H16.4402C12.0803 151.25 7.8979 149.517 4.81371 146.436C1.73281 143.352 0 139.17 0 134.81C0 110.278 0 58.4976 0 41.6168C0 37.2569 1.73281 33.0744 4.81371 29.9902L29.9902 4.81366C33.0744 1.73276 37.2568 0 41.6168 0H108.505C112.865 0 117.048 1.73276 120.132 4.81366C123.213 7.89785 124.946 12.0803 124.946 16.4402V80.2776C140.12 84.5751 151.25 98.5394 151.25 115.082C151.25 135.043 135.043 151.25 115.082 151.25ZM118.37 79.061V16.4402C118.37 13.8229 117.331 11.3141 115.479 9.46627C113.632 7.6151 111.123 6.57609 108.505 6.57609C89.4348 6.57609 54.9333 6.57609 41.6168 6.57609C40.8835 6.57609 40.1602 6.65828 39.4565 6.81611V23.0163C39.4565 27.3762 37.7237 31.5587 34.6428 34.6429C31.5586 37.7238 27.3762 39.4565 23.0163 39.4565H6.81611C6.65828 40.1602 6.57609 40.8836 6.57609 41.6168V134.81C6.57609 137.427 7.6151 139.936 9.46627 141.784C11.3141 143.635 13.8229 144.674 16.4402 144.674H94.2912C84.9926 138.127 78.913 127.31 78.913 115.082C78.913 95.1198 95.1198 78.913 115.082 78.913C116.19 78.913 117.285 78.9624 118.37 79.061ZM115.082 85.4891C131.413 85.4891 144.674 98.7498 144.674 115.082C144.674 131.413 131.413 144.674 115.082 144.674C98.7498 144.674 85.4891 131.413 85.4891 115.082C85.4891 98.7498 98.7498 85.4891 115.082 85.4891ZM32.8804 11.2254L11.2254 32.8804H23.0163C25.6336 32.8804 28.1424 31.8414 29.9902 29.9902C31.8414 28.1424 32.8804 25.6336 32.8804 23.0163V11.2254Z" fill="#179346"/>
        <path d="M111.62 102.24C111.62 100.168 113.3 98.4884 115.372 98.4884C117.444 98.4884 119.124 100.168 119.124 102.24V127.566C119.124 129.638 117.444 131.318 115.372 131.318C113.3 131.318 111.62 129.638 111.62 127.566V102.24Z" fill="#179346"/>
        <path d="M103.178 118.186C101.106 118.186 99.4263 116.506 99.4263 114.434C99.4263 112.362 101.106 110.682 103.178 110.682H128.504C130.576 110.682 132.256 112.362 132.256 114.434C132.256 116.506 130.576 118.186 128.504 118.186H103.178Z" fill="#179346"/>
        </svg>
        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-font-18 canpay-crew-text-700 mt-4">New Petition to Accept CanPay</p>
        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-font-15 mt-4">Tell us who you know at this store so we can </p>
        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-font-15">contact them about accepting CanPay.</p>
        <hr style="border:0.5px solid #DFDFDF;">
        <div class="row">
            <div class="col-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 104 104" fill="none">
                <circle cx="52" cy="52" r="52" fill="#179346"/>
                <path d="M59.1975 55.2093C58.1131 55.2205 57.0398 54.9913 56.0546 54.5381C55.0695 54.085 54.197 53.4191 53.5 52.5884C52.7978 53.413 51.9243 54.0747 50.9403 54.5274C49.9563 54.98 48.8855 55.2127 47.8025 55.2093C46.7181 55.2205 45.6448 54.9913 44.6596 54.5381C43.6745 54.085 42.802 53.4191 42.105 52.5884C41.8948 52.8405 41.6663 53.0766 41.4213 53.2949C40.654 53.993 39.7475 54.5205 38.7615 54.8426C37.7755 55.1648 36.7324 55.2742 35.701 55.1637C33.8342 54.9337 32.1181 54.0226 30.8816 52.6053C29.6451 51.1879 28.9753 49.364 29.0007 47.4833V45.8651C28.9988 44.7036 29.1835 43.5494 29.5477 42.4465L33.1257 31.7349C33.389 30.9394 33.8963 30.247 34.5753 29.7561C35.2544 29.2652 36.0709 29.0006 36.9088 29H70.0911C70.929 29.0006 71.7455 29.2652 72.4246 29.7561C73.1037 30.247 73.6109 30.9394 73.8742 31.7349L77.4523 42.4465C77.8164 43.5494 78.0011 44.7036 77.9992 45.8651V47.4833C78.0259 49.3571 77.3625 51.1753 76.1353 52.5916C74.9082 54.0079 73.203 54.9234 71.3445 55.1637C70.313 55.2772 69.2691 55.1691 68.2826 54.8469C67.2962 54.5246 66.3899 53.9955 65.6243 53.2949C65.3736 53.067 65.1229 52.7935 64.895 52.5428C64.1961 53.3746 63.3243 54.0441 62.3404 54.5046C61.3564 54.9651 60.2839 55.2056 59.1975 55.2093ZM36.9088 32.4186C36.7892 32.4204 36.6729 32.4584 36.5753 32.5276C36.4777 32.5967 36.4033 32.6938 36.3619 32.806L32.8066 43.5177C32.5597 44.2526 32.429 45.0215 32.4192 45.7967V47.4149C32.3858 48.4437 32.7319 49.449 33.3917 50.2391C34.0515 51.0292 34.9789 51.549 35.9972 51.6995C36.5468 51.7564 37.1022 51.6986 37.6282 51.5295C38.1542 51.3605 38.6394 51.084 39.0529 50.7176C39.4664 50.3512 39.7993 49.9028 40.0305 49.401C40.2616 48.8992 40.386 48.3548 40.3957 47.8023C40.3957 47.349 40.5758 46.9142 40.8963 46.5937C41.2169 46.2731 41.6516 46.093 42.105 46.093C42.5583 46.093 42.993 46.2731 43.3136 46.5937C43.6341 46.9142 43.8142 47.349 43.8142 47.8023C43.7881 48.333 43.8734 48.8632 44.0646 49.3589C44.2558 49.8546 44.5487 50.3048 44.9243 50.6805C45.3 51.0562 45.7502 51.3491 46.2459 51.5403C46.7416 51.7315 47.2718 51.8168 47.8025 51.7907C48.8584 51.7847 49.8693 51.3626 50.616 50.6159C51.3626 49.8692 51.7847 48.8583 51.7907 47.8023C51.7907 47.349 51.9708 46.9142 52.2913 46.5937C52.6119 46.2731 53.0466 46.093 53.5 46.093C53.9533 46.093 54.388 46.2731 54.7086 46.5937C55.0291 46.9142 55.2092 47.349 55.2092 47.8023C55.1831 48.333 55.2684 48.8632 55.4596 49.3589C55.6508 49.8546 55.9437 50.3048 56.3194 50.6805C56.695 51.0562 57.1452 51.3491 57.6409 51.5403C58.1366 51.7315 58.6668 51.8168 59.1975 51.7907C60.2534 51.7847 61.2643 51.3626 62.011 50.6159C62.7576 49.8692 63.1797 48.8583 63.1857 47.8023C63.1857 47.349 63.3658 46.9142 63.6863 46.5937C64.0069 46.2731 64.4416 46.093 64.895 46.093C65.3483 46.093 65.7831 46.2731 66.1036 46.5937C66.4241 46.9142 66.6042 47.349 66.6042 47.8023C66.6045 48.3605 66.7219 48.9125 66.9488 49.4225C67.1758 49.9325 67.5073 50.3891 67.9219 50.7629C68.3365 51.1367 68.825 51.4193 69.3556 51.5924C69.8863 51.7655 70.4474 51.8253 71.0027 51.7679C72.021 51.6174 72.9484 51.0976 73.6082 50.3075C74.268 49.5173 74.6142 48.5121 74.5807 47.4833V45.8651C74.5709 45.0899 74.4402 44.321 74.1933 43.586L70.6381 32.8744C70.5967 32.7622 70.5222 32.6651 70.4246 32.5959C70.327 32.5268 70.2107 32.4888 70.0911 32.487L36.9088 32.4186Z" fill="white"/>
                <path d="M71.732 78H35.2679C34.212 77.994 33.2011 77.5719 32.4544 76.8252C31.7078 76.0786 31.2857 75.0676 31.2797 74.0116V52.3149C31.2797 51.8615 31.4598 51.4268 31.7803 51.1062C32.1009 50.7857 32.5356 50.6056 32.9889 50.6056C33.4423 50.6056 33.877 50.7857 34.1976 51.1062C34.5181 51.4268 34.6982 51.8615 34.6982 52.3149V74.0116C34.6982 74.1627 34.7582 74.3077 34.8651 74.4145C34.9719 74.5214 35.1168 74.5814 35.2679 74.5814H71.732C71.8831 74.5814 72.028 74.5214 72.1348 74.4145C72.2417 74.3077 72.3017 74.1627 72.3017 74.0116V52.2921C72.3017 51.8388 72.4818 51.404 72.8023 51.0834C73.1229 50.7629 73.5577 50.5828 74.011 50.5828C74.4643 50.5828 74.8991 50.7629 75.2196 51.0834C75.5401 51.404 75.7202 51.8388 75.7202 52.2921V74.0116C75.7142 75.0676 75.2921 76.0786 74.5455 76.8252C73.7988 77.5719 72.7879 77.994 71.732 78Z" fill="white"/>
                <path d="M61.4765 78H45.5235C45.072 77.9941 44.6406 77.8121 44.3214 77.4928C44.0021 77.1735 43.8201 76.7422 43.8142 76.2907V64.8953C43.8202 63.8394 44.2423 62.8284 44.9889 62.0818C45.7356 61.3351 46.7466 60.913 47.8025 60.907H59.1975C60.2534 60.913 61.2643 61.3351 62.011 62.0818C62.7576 62.8284 63.1797 63.8394 63.1857 64.8953V76.2907C63.1798 76.7422 62.9978 77.1735 62.6786 77.4928C62.3593 77.8121 61.928 77.9941 61.4765 78ZM47.2327 74.5814H59.7672V64.8953C59.7672 64.7442 59.7072 64.5993 59.6003 64.4925C59.4935 64.3856 59.3486 64.3256 59.1975 64.3256H47.8025C47.6513 64.3256 47.5064 64.3856 47.3996 64.4925C47.2927 64.5993 47.2327 64.7442 47.2327 64.8953V74.5814Z" fill="white"/>
                </svg>
            </div>
            <div class="col-10 text-left" style="margin-top:7px;">
                <p class=" canpay-crew-text-font-18 canpay-crew-text-700">{{storeData.store_name}} - {{storeData.city}}</p>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <p class="canpay-crew-text-bold canpay-crew-p-margin-bottom-1 mb-2">Who should we contact at this store?</p>
                <input type="text"
                 class="canpay-crew-general-input-box
                 canpay-crew-p-margin-bottom mb-2"
                 maxlength="50"
                 required placeholder="Store Manager or Employee: First Name"
                 :style="storeData.primary_contact_person_firstname == null?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}"
                 v-model="storeData.primary_contact_person_firstname">
                 <p class="text-red-crew" v-if="storeData.primary_contact_person_firstname == null">{{storeDataError.error_first_name}}</p>
                <input type="text"
                 class="canpay-crew-general-input-box
                 canpay-crew-p-margin-bottom mb-2"
                 maxlength="50"
                 required placeholder="Store Manager or Employee: Last Name"
                 :style="storeData.primary_contact_person_lastname == null?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}"
                 v-model="storeData.primary_contact_person_lastname">
                 <p class="text-red-crew" v-if="storeData.primary_contact_person_lastname == null">{{storeDataError.error_last_name}}</p>
                <input type="text"
                 class="canpay-crew-general-input-box
                 canpay-crew-p-margin-bottom mb-2"
                 maxlength="50"
                 required placeholder="Store Manager or Employee: Email"
                 :style="storeData.primary_contact_person_email == null || !isValidStoreEmail?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}"
                 v-model="storeData.primary_contact_person_email" @input="onEmailInput">
                 <p class="text-red-crew" v-if="storeData.primary_contact_person_email == null && !isValidStoreEmail">{{storeDataError.error_email}}</p>
                 <p class="text-red-crew" v-else-if="!isValidStoreEmail">{{storeDataError.error_invalid_email}}</p>
                <input type="text"
                 class="canpay-crew-general-input-box
                 canpay-crew-p-margin-bottom mb-2"
                 maxlength="50"
                 required placeholder="Store Manager or Employee: Job Title"
                 :style="storeData.primary_contact_person_title == null?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}"
                 v-model="storeData.primary_contact_person_title">
                 <p class="text-red-crew" v-if="storeData.primary_contact_person_title == null ">{{storeDataError.error_title}}</p>

            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <button class="canpay-crew-sign-petition-modal-button" v-on:click="addContactSuccess()">
                    Add Contact
                </button>
            </div>

        </div>
      </div>
      <!------ initiating the canpay crew fill up end -------->
      <!------ success the canpay crew fill up start -------->
      <div class="text-center mt-4" v-if="stepForCreatingPetition == 'formFillUpScucess'">
        <div> 
            <svg xmlns="http://www.w3.org/2000/svg" width="60" height="80"  viewBox="0 0 308 426" fill="none">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M154 101.1C123.444 101.1 98.6747 125.344 98.6747 155.25C98.6747 185.156 123.444 209.4 154 209.4C184.554 209.4 209.325 185.156 209.325 155.25C209.325 125.344 184.554 101.1 154 101.1ZM61.7913 155.25C61.7913 105.406 103.075 65 154 65C204.925 65 246.208 105.406 246.208 155.25C246.208 205.094 204.925 245.5 154 245.5C103.075 245.5 61.7913 205.094 61.7913 155.25Z" fill="black"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M154 299.65C90.5152 299.65 57.1426 321.667 40.4938 348.342C34.6998 357.627 36.157 366.383 43.2307 374.428C50.9471 383.203 64.9004 389.9 80.233 389.9H227.767C243.099 389.9 257.052 383.203 264.768 374.428C271.842 366.383 273.299 357.627 267.507 348.342C250.857 321.667 217.484 299.65 154 299.65ZM9.01844 329.523C33.902 289.654 80.9124 263.55 154 263.55C227.088 263.55 274.098 289.654 298.981 329.523C314.426 354.267 308.514 380.019 292.726 397.976C277.578 415.202 253.174 426 227.767 426H80.233C54.8255 426 30.4213 415.202 15.275 397.976C-0.514246 380.019 -6.42536 354.267 9.01844 329.523Z" fill="black"/>
            <path d="M155.231 6C160.388 6.00003 164.568 10.089 164.568 15.1328C164.568 18.4292 162.782 21.317 160.105 22.9229C160.26 23.1595 160.4 23.413 160.523 23.6836L184.26 75.7031L232.125 33.4229C231.613 32.2926 231.326 31.0425 231.326 29.7266C231.326 24.6827 235.506 20.5938 240.663 20.5938C245.82 20.5938 250 24.6827 250 29.7266C250 34.5157 246.231 38.44 241.436 38.8242V125.469C241.436 128.523 238.96 131 235.905 131H71.4961C68.4416 131 65.9658 128.523 65.9658 125.469V38.1377C60.9817 37.9464 57.001 33.9364 57.001 29.0146C57.001 23.971 61.1812 19.8821 66.3379 19.8818C71.4947 19.8818 75.6757 23.9709 75.6758 29.0146C75.6758 30.2798 75.412 31.4845 74.9365 32.5801C74.9604 32.5991 74.9851 32.6172 75.0088 32.6367L126.9 75.3135L150.46 23.6836C150.546 23.4943 150.641 23.3133 150.742 23.1406C147.853 21.5878 145.895 18.5847 145.895 15.1328C145.895 10.089 150.075 6 155.231 6Z" fill="black"/>
            </svg>
            <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-font-18 canpay-crew-text-700 mt-4">Success! You are now</p>
            <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-font-18 canpay-crew-text-700">the Mayor of this location.</p>

            <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-font-16 mt-3">Please add additional contacts at this</p>
            <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-font-16">store to help CanPay reach the right</p>
            <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-font-16">people so they start accepting</p>
            <p class="canpay-crew-p-margin-bottom canpay-crew-text-font-16">CanPay soon.</p>
        </div>
        <div class="row mt-5">
            <div class="col-12">
                <button class="canpay-crew-sign-petition-modal-button" v-on:click="addAdditionalContactData()">
                    Add Additional Contacts
                </button>
                    <button class="canpay-crew-sign-petition-not-ok-modal-button mt-3" v-on:click="closeCanPayCrewCreatePetition()">
                        Not Right Now
                    </button>
            </div>

        </div>
      </div>
      <!------ success the canpay crew fill up end  -------->
      <!------ initiating the canpay crew fill up start -------->
      <div class="text-center mt-4" v-if="stepForCreatingPetition=='showforFillUpAdditionContactPetition'">
        <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 152 152" fill="none">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M29.5924 65.7609H95.3533C97.1683 65.7609 98.6413 64.2878 98.6413 62.4728C98.6413 60.6578 97.1683 59.1848 95.3533 59.1848H29.5924C27.7774 59.1848 26.3044 60.6578 26.3044 62.4728C26.3044 64.2878 27.7774 65.7609 29.5924 65.7609Z" fill="#179346"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M29.5924 85.4891H75.625C77.44 85.4891 78.9131 84.0161 78.9131 82.2011C78.9131 80.3861 77.44 78.913 75.625 78.913H29.5924C27.7774 78.913 26.3044 80.3861 26.3044 82.2011C26.3044 84.0161 27.7774 85.4891 29.5924 85.4891Z" fill="#179346"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M29.5924 105.217H69.0489C70.8639 105.217 72.337 103.744 72.337 101.929C72.337 100.114 70.8639 98.6413 69.0489 98.6413H29.5924C27.7774 98.6413 26.3044 100.114 26.3044 101.929C26.3044 103.744 27.7774 105.217 29.5924 105.217Z" fill="#179346"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M29.5924 124.946H69.0489C70.8639 124.946 72.337 123.473 72.337 121.658C72.337 119.843 70.8639 118.37 69.0489 118.37H29.5924C27.7774 118.37 26.3044 119.843 26.3044 121.658C26.3044 123.473 27.7774 124.946 29.5924 124.946Z" fill="#179346"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M115.082 151.25H16.4402C12.0803 151.25 7.8979 149.517 4.81371 146.436C1.73281 143.352 0 139.17 0 134.81C0 110.278 0 58.4976 0 41.6168C0 37.2569 1.73281 33.0744 4.81371 29.9902L29.9902 4.81366C33.0744 1.73276 37.2568 0 41.6168 0H108.505C112.865 0 117.048 1.73276 120.132 4.81366C123.213 7.89785 124.946 12.0803 124.946 16.4402V80.2776C140.12 84.5751 151.25 98.5394 151.25 115.082C151.25 135.043 135.043 151.25 115.082 151.25ZM118.37 79.061V16.4402C118.37 13.8229 117.331 11.3141 115.479 9.46627C113.632 7.6151 111.123 6.57609 108.505 6.57609C89.4348 6.57609 54.9333 6.57609 41.6168 6.57609C40.8835 6.57609 40.1602 6.65828 39.4565 6.81611V23.0163C39.4565 27.3762 37.7237 31.5587 34.6428 34.6429C31.5586 37.7238 27.3762 39.4565 23.0163 39.4565H6.81611C6.65828 40.1602 6.57609 40.8836 6.57609 41.6168V134.81C6.57609 137.427 7.6151 139.936 9.46627 141.784C11.3141 143.635 13.8229 144.674 16.4402 144.674H94.2912C84.9926 138.127 78.913 127.31 78.913 115.082C78.913 95.1198 95.1198 78.913 115.082 78.913C116.19 78.913 117.285 78.9624 118.37 79.061ZM115.082 85.4891C131.413 85.4891 144.674 98.7498 144.674 115.082C144.674 131.413 131.413 144.674 115.082 144.674C98.7498 144.674 85.4891 131.413 85.4891 115.082C85.4891 98.7498 98.7498 85.4891 115.082 85.4891ZM32.8804 11.2254L11.2254 32.8804H23.0163C25.6336 32.8804 28.1424 31.8414 29.9902 29.9902C31.8414 28.1424 32.8804 25.6336 32.8804 23.0163V11.2254Z" fill="#179346"/>
        <path d="M111.62 102.24C111.62 100.168 113.3 98.4884 115.372 98.4884C117.444 98.4884 119.124 100.168 119.124 102.24V127.566C119.124 129.638 117.444 131.318 115.372 131.318C113.3 131.318 111.62 129.638 111.62 127.566V102.24Z" fill="#179346"/>
        <path d="M103.178 118.186C101.106 118.186 99.4263 116.506 99.4263 114.434C99.4263 112.362 101.106 110.682 103.178 110.682H128.504C130.576 110.682 132.256 112.362 132.256 114.434C132.256 116.506 130.576 118.186 128.504 118.186H103.178Z" fill="#179346"/>
        </svg>
        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-font-18 canpay-crew-text-700 mt-4">Add additional contact</p>
        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-font-15">Please let us know the name of your</p>
        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-font-15">contact at this store. More contacts will</p>
        <p class="canpay-crew-p-margin-bottom canpay-crew-text-font-15">help CanPay reach the right people.</p>
        <hr style="border:0.5px solid #DFDFDF;">
        <div class="row">
            <div class="col-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 104 104" fill="none">
                <circle cx="52" cy="52" r="52" fill="#179346"/>
                <path d="M59.1975 55.2093C58.1131 55.2205 57.0398 54.9913 56.0546 54.5381C55.0695 54.085 54.197 53.4191 53.5 52.5884C52.7978 53.413 51.9243 54.0747 50.9403 54.5274C49.9563 54.98 48.8855 55.2127 47.8025 55.2093C46.7181 55.2205 45.6448 54.9913 44.6596 54.5381C43.6745 54.085 42.802 53.4191 42.105 52.5884C41.8948 52.8405 41.6663 53.0766 41.4213 53.2949C40.654 53.993 39.7475 54.5205 38.7615 54.8426C37.7755 55.1648 36.7324 55.2742 35.701 55.1637C33.8342 54.9337 32.1181 54.0226 30.8816 52.6053C29.6451 51.1879 28.9753 49.364 29.0007 47.4833V45.8651C28.9988 44.7036 29.1835 43.5494 29.5477 42.4465L33.1257 31.7349C33.389 30.9394 33.8963 30.247 34.5753 29.7561C35.2544 29.2652 36.0709 29.0006 36.9088 29H70.0911C70.929 29.0006 71.7455 29.2652 72.4246 29.7561C73.1037 30.247 73.6109 30.9394 73.8742 31.7349L77.4523 42.4465C77.8164 43.5494 78.0011 44.7036 77.9992 45.8651V47.4833C78.0259 49.3571 77.3625 51.1753 76.1353 52.5916C74.9082 54.0079 73.203 54.9234 71.3445 55.1637C70.313 55.2772 69.2691 55.1691 68.2826 54.8469C67.2962 54.5246 66.3899 53.9955 65.6243 53.2949C65.3736 53.067 65.1229 52.7935 64.895 52.5428C64.1961 53.3746 63.3243 54.0441 62.3404 54.5046C61.3564 54.9651 60.2839 55.2056 59.1975 55.2093ZM36.9088 32.4186C36.7892 32.4204 36.6729 32.4584 36.5753 32.5276C36.4777 32.5967 36.4033 32.6938 36.3619 32.806L32.8066 43.5177C32.5597 44.2526 32.429 45.0215 32.4192 45.7967V47.4149C32.3858 48.4437 32.7319 49.449 33.3917 50.2391C34.0515 51.0292 34.9789 51.549 35.9972 51.6995C36.5468 51.7564 37.1022 51.6986 37.6282 51.5295C38.1542 51.3605 38.6394 51.084 39.0529 50.7176C39.4664 50.3512 39.7993 49.9028 40.0305 49.401C40.2616 48.8992 40.386 48.3548 40.3957 47.8023C40.3957 47.349 40.5758 46.9142 40.8963 46.5937C41.2169 46.2731 41.6516 46.093 42.105 46.093C42.5583 46.093 42.993 46.2731 43.3136 46.5937C43.6341 46.9142 43.8142 47.349 43.8142 47.8023C43.7881 48.333 43.8734 48.8632 44.0646 49.3589C44.2558 49.8546 44.5487 50.3048 44.9243 50.6805C45.3 51.0562 45.7502 51.3491 46.2459 51.5403C46.7416 51.7315 47.2718 51.8168 47.8025 51.7907C48.8584 51.7847 49.8693 51.3626 50.616 50.6159C51.3626 49.8692 51.7847 48.8583 51.7907 47.8023C51.7907 47.349 51.9708 46.9142 52.2913 46.5937C52.6119 46.2731 53.0466 46.093 53.5 46.093C53.9533 46.093 54.388 46.2731 54.7086 46.5937C55.0291 46.9142 55.2092 47.349 55.2092 47.8023C55.1831 48.333 55.2684 48.8632 55.4596 49.3589C55.6508 49.8546 55.9437 50.3048 56.3194 50.6805C56.695 51.0562 57.1452 51.3491 57.6409 51.5403C58.1366 51.7315 58.6668 51.8168 59.1975 51.7907C60.2534 51.7847 61.2643 51.3626 62.011 50.6159C62.7576 49.8692 63.1797 48.8583 63.1857 47.8023C63.1857 47.349 63.3658 46.9142 63.6863 46.5937C64.0069 46.2731 64.4416 46.093 64.895 46.093C65.3483 46.093 65.7831 46.2731 66.1036 46.5937C66.4241 46.9142 66.6042 47.349 66.6042 47.8023C66.6045 48.3605 66.7219 48.9125 66.9488 49.4225C67.1758 49.9325 67.5073 50.3891 67.9219 50.7629C68.3365 51.1367 68.825 51.4193 69.3556 51.5924C69.8863 51.7655 70.4474 51.8253 71.0027 51.7679C72.021 51.6174 72.9484 51.0976 73.6082 50.3075C74.268 49.5173 74.6142 48.5121 74.5807 47.4833V45.8651C74.5709 45.0899 74.4402 44.321 74.1933 43.586L70.6381 32.8744C70.5967 32.7622 70.5222 32.6651 70.4246 32.5959C70.327 32.5268 70.2107 32.4888 70.0911 32.487L36.9088 32.4186Z" fill="white"/>
                <path d="M71.732 78H35.2679C34.212 77.994 33.2011 77.5719 32.4544 76.8252C31.7078 76.0786 31.2857 75.0676 31.2797 74.0116V52.3149C31.2797 51.8615 31.4598 51.4268 31.7803 51.1062C32.1009 50.7857 32.5356 50.6056 32.9889 50.6056C33.4423 50.6056 33.877 50.7857 34.1976 51.1062C34.5181 51.4268 34.6982 51.8615 34.6982 52.3149V74.0116C34.6982 74.1627 34.7582 74.3077 34.8651 74.4145C34.9719 74.5214 35.1168 74.5814 35.2679 74.5814H71.732C71.8831 74.5814 72.028 74.5214 72.1348 74.4145C72.2417 74.3077 72.3017 74.1627 72.3017 74.0116V52.2921C72.3017 51.8388 72.4818 51.404 72.8023 51.0834C73.1229 50.7629 73.5577 50.5828 74.011 50.5828C74.4643 50.5828 74.8991 50.7629 75.2196 51.0834C75.5401 51.404 75.7202 51.8388 75.7202 52.2921V74.0116C75.7142 75.0676 75.2921 76.0786 74.5455 76.8252C73.7988 77.5719 72.7879 77.994 71.732 78Z" fill="white"/>
                <path d="M61.4765 78H45.5235C45.072 77.9941 44.6406 77.8121 44.3214 77.4928C44.0021 77.1735 43.8201 76.7422 43.8142 76.2907V64.8953C43.8202 63.8394 44.2423 62.8284 44.9889 62.0818C45.7356 61.3351 46.7466 60.913 47.8025 60.907H59.1975C60.2534 60.913 61.2643 61.3351 62.011 62.0818C62.7576 62.8284 63.1797 63.8394 63.1857 64.8953V76.2907C63.1798 76.7422 62.9978 77.1735 62.6786 77.4928C62.3593 77.8121 61.928 77.9941 61.4765 78ZM47.2327 74.5814H59.7672V64.8953C59.7672 64.7442 59.7072 64.5993 59.6003 64.4925C59.4935 64.3856 59.3486 64.3256 59.1975 64.3256H47.8025C47.6513 64.3256 47.5064 64.3856 47.3996 64.4925C47.2927 64.5993 47.2327 64.7442 47.2327 64.8953V74.5814Z" fill="white"/>
                </svg>
            </div>
            <div class="col-10 text-left" style="margin-top:7px;">
                <p class=" canpay-crew-text-font-18 canpay-crew-text-700">{{storeData.store_name}} - {{storeData.city}}</p>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <p class="canpay-crew-text-bold canpay-crew-p-margin-bottom-1 mb-2">Who should we contact at this store?</p>
                <input type="text"
                 class="canpay-crew-general-input-box canpay-crew-p-margin-bottom mb-2"
                 required placeholder="Store Manager or Employee: First Name"
                 maxlength="50"
                 :style="additionalContact.secondary_contact_person_firstname == null?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}"
                 v-model="additionalContact.secondary_contact_person_firstname">
                 <p class="text-red-crew" v-if="additionalContact.secondary_contact_person_firstname == null">{{additinalContactError.error_first_name}}</p>
                <input type="text"
                 class="canpay-crew-general-input-box canpay-crew-p-margin-bottom mb-2"
                 required placeholder="Store Manager or Employee: Last Name"
                 maxlength="50"
                 :style="additionalContact.secondary_contact_person_lastname == null?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}"
                 v-model="additionalContact.secondary_contact_person_lastname">
                 <p class="text-red-crew" v-if="additionalContact.secondary_contact_person_lastname == null">{{additinalContactError.error_last_name}}</p>
                <input type="text"
                 class="canpay-crew-general-input-box canpay-crew-p-margin-bottom mb-2"
                 required placeholder="Store Manager or Employee: Email"
                 maxlength="50"
                 @input="onSecondaryEmailInput"
                 :style="additionalContact.secondary_contact_person_email == null || !isValidAdditionalContactEmail?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}"
                 v-model="additionalContact.secondary_contact_person_email">
                 <p class="text-red-crew" v-if="additionalContact.secondary_contact_person_email == null && !isValidAdditionalContactEmail">{{additinalContactError.error_email}}</p>
                 <p class="text-red-crew" v-else-if="!isValidAdditionalContactEmail">{{additinalContactError.error_invalid_email}}</p>

                 <input type="text"
                 class="canpay-crew-general-input-box canpay-crew-p-margin-bottom mb-2"
                 required placeholder="Store Manager or Employee: Job Title"
                 maxlength="50"
                 :style="additionalContact.secondary_contact_person_title == null?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}"
                 v-model="additionalContact.secondary_contact_person_title">
                 <p class="text-red-crew" v-if="additionalContact.secondary_contact_person_title == null">{{additinalContactError.error_title}}</p>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <button class="canpay-crew-sign-petition-modal-button" v-on:click="addTheAdditionalContact()">
                    Add Contact
                </button>
            </div>

        </div>
      </div>
      <!------ initiating the canpay crew fill up end -------->
      <!------ Missing Details in Adress Start ---------------------->
      <div class="text-center mt-4" v-if="stepForCreatingPetition == 'checkTheGivenAddress'">
        <svg xmlns="http://www.w3.org/2000/svg" width="104" height="104" viewBox="0 0 104 104" fill="none">
        <circle cx="52" cy="52" r="52" fill="#179346"/>
        <path d="M58.1975 55.2093C57.1131 55.2205 56.0398 54.9913 55.0546 54.5381C54.0695 54.085 53.197 53.4191 52.5 52.5884C51.7978 53.413 50.9243 54.0747 49.9403 54.5274C48.9563 54.98 47.8855 55.2127 46.8025 55.2093C45.7181 55.2205 44.6448 54.9913 43.6596 54.5381C42.6745 54.085 41.802 53.4191 41.105 52.5884C40.8948 52.8405 40.6663 53.0766 40.4213 53.2949C39.654 53.993 38.7475 54.5205 37.7615 54.8426C36.7755 55.1648 35.7324 55.2742 34.701 55.1637C32.8342 54.9337 31.1181 54.0226 29.8816 52.6053C28.6451 51.1879 27.9753 49.364 28.0007 47.4833V45.8651C27.9988 44.7036 28.1835 43.5494 28.5477 42.4465L32.1257 31.7349C32.389 30.9394 32.8963 30.247 33.5753 29.7561C34.2544 29.2652 35.0709 29.0006 35.9088 29H69.0911C69.929 29.0006 70.7455 29.2652 71.4246 29.7561C72.1037 30.247 72.6109 30.9394 72.8742 31.7349L76.4523 42.4465C76.8164 43.5494 77.0011 44.7036 76.9992 45.8651V47.4833C77.0259 49.3571 76.3625 51.1753 75.1353 52.5916C73.9082 54.0079 72.203 54.9234 70.3445 55.1637C69.313 55.2772 68.2691 55.1691 67.2826 54.8469C66.2962 54.5246 65.3899 53.9955 64.6243 53.2949C64.3736 53.067 64.1229 52.7935 63.895 52.5428C63.1961 53.3746 62.3243 54.0441 61.3404 54.5046C60.3564 54.9651 59.2839 55.2056 58.1975 55.2093ZM35.9088 32.4186C35.7892 32.4204 35.6729 32.4584 35.5753 32.5276C35.4777 32.5967 35.4033 32.6938 35.3619 32.806L31.8066 43.5177C31.5597 44.2526 31.429 45.0215 31.4192 45.7967V47.4149C31.3858 48.4437 31.7319 49.449 32.3917 50.2391C33.0515 51.0292 33.9789 51.549 34.9972 51.6995C35.5468 51.7564 36.1022 51.6986 36.6282 51.5295C37.1542 51.3605 37.6394 51.084 38.0529 50.7176C38.4664 50.3512 38.7993 49.9028 39.0305 49.401C39.2616 48.8992 39.386 48.3548 39.3957 47.8023C39.3957 47.349 39.5758 46.9142 39.8963 46.5937C40.2169 46.2731 40.6516 46.093 41.105 46.093C41.5583 46.093 41.993 46.2731 42.3136 46.5937C42.6341 46.9142 42.8142 47.349 42.8142 47.8023C42.7881 48.333 42.8734 48.8632 43.0646 49.3589C43.2558 49.8546 43.5487 50.3048 43.9243 50.6805C44.3 51.0562 44.7502 51.3491 45.2459 51.5403C45.7416 51.7315 46.2718 51.8168 46.8025 51.7907C47.8584 51.7847 48.8693 51.3626 49.616 50.6159C50.3626 49.8692 50.7847 48.8583 50.7907 47.8023C50.7907 47.349 50.9708 46.9142 51.2913 46.5937C51.6119 46.2731 52.0466 46.093 52.5 46.093C52.9533 46.093 53.388 46.2731 53.7086 46.5937C54.0291 46.9142 54.2092 47.349 54.2092 47.8023C54.1831 48.333 54.2684 48.8632 54.4596 49.3589C54.6508 49.8546 54.9437 50.3048 55.3194 50.6805C55.695 51.0562 56.1452 51.3491 56.6409 51.5403C57.1366 51.7315 57.6668 51.8168 58.1975 51.7907C59.2534 51.7847 60.2643 51.3626 61.011 50.6159C61.7576 49.8692 62.1797 48.8583 62.1857 47.8023C62.1857 47.349 62.3658 46.9142 62.6863 46.5937C63.0069 46.2731 63.4416 46.093 63.895 46.093C64.3483 46.093 64.7831 46.2731 65.1036 46.5937C65.4241 46.9142 65.6042 47.349 65.6042 47.8023C65.6045 48.3605 65.7219 48.9125 65.9488 49.4225C66.1758 49.9325 66.5073 50.3891 66.9219 50.7629C67.3365 51.1367 67.825 51.4193 68.3556 51.5924C68.8863 51.7655 69.4474 51.8253 70.0027 51.7679C71.021 51.6174 71.9484 51.0976 72.6082 50.3075C73.268 49.5173 73.6142 48.5121 73.5807 47.4833V45.8651C73.5709 45.0899 73.4402 44.321 73.1933 43.586L69.6381 32.8744C69.5967 32.7622 69.5222 32.6651 69.4246 32.5959C69.327 32.5268 69.2107 32.4888 69.0911 32.487L35.9088 32.4186Z" fill="white"/>
        <path d="M70.732 78H34.2679C33.212 77.994 32.2011 77.5719 31.4544 76.8252C30.7078 76.0786 30.2857 75.0676 30.2797 74.0116V52.3149C30.2797 51.8615 30.4598 51.4268 30.7803 51.1062C31.1009 50.7857 31.5356 50.6056 31.9889 50.6056C32.4423 50.6056 32.877 50.7857 33.1976 51.1062C33.5181 51.4268 33.6982 51.8615 33.6982 52.3149V74.0116C33.6982 74.1627 33.7582 74.3077 33.8651 74.4145C33.9719 74.5214 34.1168 74.5814 34.2679 74.5814H70.732C70.8831 74.5814 71.028 74.5214 71.1348 74.4145C71.2417 74.3077 71.3017 74.1627 71.3017 74.0116V52.2921C71.3017 51.8388 71.4818 51.404 71.8023 51.0834C72.1229 50.7629 72.5577 50.5828 73.011 50.5828C73.4643 50.5828 73.8991 50.7629 74.2196 51.0834C74.5401 51.404 74.7202 51.8388 74.7202 52.2921V74.0116C74.7142 75.0676 74.2921 76.0786 73.5455 76.8252C72.7988 77.5719 71.7879 77.994 70.732 78Z" fill="white"/>
        <path d="M60.4765 78H44.5235C44.072 77.9941 43.6406 77.8121 43.3214 77.4928C43.0021 77.1735 42.8201 76.7422 42.8142 76.2907V64.8953C42.8202 63.8394 43.2423 62.8284 43.9889 62.0818C44.7356 61.3351 45.7466 60.913 46.8025 60.907H58.1975C59.2534 60.913 60.2643 61.3351 61.011 62.0818C61.7576 62.8284 62.1797 63.8394 62.1857 64.8953V76.2907C62.1798 76.7422 61.9978 77.1735 61.6786 77.4928C61.3593 77.8121 60.928 77.9941 60.4765 78ZM46.2327 74.5814H58.7672V64.8953C58.7672 64.7442 58.7072 64.5993 58.6003 64.4925C58.4935 64.3856 58.3486 64.3256 58.1975 64.3256H46.8025C46.6513 64.3256 46.5064 64.3856 46.3996 64.4925C46.2927 64.5993 46.2327 64.7442 46.2327 64.8953V74.5814Z" fill="white"/>
        </svg>

        <p class="canpay-crew-text-font-18 canpay-crew-p-margin-bottom-1 canpay-crew-text-700 mt-3">The store address is incomplete. Please add the missing details below</p>

        <div class="mt-3 row">
            <div class="col-12">
                <input type="text"
                 class="canpay-crew-general-input-box-1 canpay-crew-p-margin-bottom"
                 required placeholder="Store Name" v-model="storeData.store_name"
                 maxlength="255"
                :style="storeDataError.error_store_name == null?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}">
                <p class="text-red-crew" v-if="storeDataError.error_store_name == null">{{storeDataError.error_store_name}}</p>
                <input type="text"
                 class="canpay-crew-general-input-box-1 canpay-crew-p-margin-bottom"
                 required placeholder="Street Address" v-model="storeData.street_address"
                 maxlength="255"
                :style="storeDataError.error_street_address == null?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}">
                <p class="text-red-crew" v-if="storeDataError.error_street_address == null">{{storeDataError.error_street_address}}</p>
                <input type="text"
                 class="canpay-crew-general-input-box-1 canpay-crew-p-margin-bottom"
                 required placeholder="City" v-model="storeData.city"
                 maxlength="50"
                :style="storeDataError.error_city == null?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}">
                <p class="text-red-crew" v-if="storeDataError.error_city == null">{{storeDataError.error_city}}</p>
                <input type="text"
                 class="canpay-crew-general-input-box-1 canpay-crew-p-margin-bottom"
                 required placeholder="State" v-model="storeData.state"
                 maxlength="2"
                :style="storeDataError.error_state == null?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}">
                <p class="text-red-crew" v-if="storeDataError.error_state == null">{{storeDataError.error_state}}</p>
                <input type="text"
                 class="canpay-crew-general-input-box-1 canpay-crew-p-margin-bottom"
                 required placeholder="Zipcode" v-model="storeData.zipcode"
                 maxlength="50"
                :style="storeDataError.error_zipcode == null?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}">
                <p class="text-red-crew" v-if="storeDataError.error_zipcode == null">{{storeDataError.error_zipcode}}</p>

            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <button class="canpay-crew-sign-petition-modal-button" v-on:click="updateTheAddress()">
                    Update the Address
                </button>
            </div>

        </div>
      </div>
      </div>
      <!------ Missing Details in Adress End ------------------------->
      <!------- Proceed with Warning start ---------------------------------->
      <div class="text-center" v-if="stepForCreatingPetition == 'proceedWithWarning'">
                    <svg data-v-63e84763="" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="120" height="120" viewBox="0 0 100 110" xml:space="preserve" fill="#e14343"><path data-v-63e84763="" d="M96.2,47.5l-22-38c-0.4-0.6-1-1-1.7-1h-44c-0.7,0-1.4,0.4-1.7,1l-22,37.9c-0.4,0.6-0.4,1.4,0,2l22,38.1c0.4,0.6,1,1,1.7,1
					h44c0.7,0,1.4-0.4,1.7-1l22-38C96.6,48.9,96.6,48.1,96.2,47.5z M71.3,84.5H29.7L8.8,48.4l20.8-35.9h41.7l20.8,36L71.3,84.5z
					M50.5,60.5c1.1,0,2-0.9,2-2v-30c0-1.1-0.9-2-2-2c-1.1,0-2,0.9-2,2v30C48.5,59.6,49.4,60.5,50.5,60.5z M48.4,66.4
					c-0.6,0.6-0.9,1.3-0.9,2.1c0,0.8,0.3,1.6,0.9,2.1s1.3,0.9,2.1,0.9c0.8,0,1.6-0.3,2.1-0.9c0.6-0.6,0.9-1.3,0.9-2.1
					c0-0.8-0.3-1.6-0.9-2.1C51.5,65.3,49.5,65.3,48.4,66.4z"></path></svg>
        <p class="canpay-crew-text-font-15 canpay-crew-text-600 mb-4">The petition is intended to include the store’s official contact information. Please confirm whether to proceed with the provided details.</p>
        <button class="canpay-crew-sign-petition-modal-button" v-on:click="proceedWithWarning()">Proceed with Contact Details</button>
        <button class="canpay-crew-sign-petition-not-ok-modal-button mt-2" v-on:click="goBackAndChangeDetail()">Verify Contact Detail</button>
      </div>
      <!------- Proceed with warning end ------------------------------------>
    </b-modal>
    <b-modal
        ref="show-warning-canpay-crew-for-backdrop"
        hide-footer      
        hide-header
        centered
        id="show-warning-canpay-crew-for-backdrop"
        title="BootstrapVue"   
    >
    <div class="text-center">
            <svg
                version="1.1"
                id="Layer_1"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                x="0px"
                y="0px"
                width="120"
                height="120"
                viewBox="0 0 100 110"
                style="enable-background: new 0 0 100 125"
                xml:space="preserve"
                fill="#e14343"
            >
                <path
                d="M96.2,47.5l-22-38c-0.4-0.6-1-1-1.7-1h-44c-0.7,0-1.4,0.4-1.7,1l-22,37.9c-0.4,0.6-0.4,1.4,0,2l22,38.1c0.4,0.6,1,1,1.7,1
        h44c0.7,0,1.4-0.4,1.7-1l22-38C96.6,48.9,96.6,48.1,96.2,47.5z M71.3,84.5H29.7L8.8,48.4l20.8-35.9h41.7l20.8,36L71.3,84.5z
        M50.5,60.5c1.1,0,2-0.9,2-2v-30c0-1.1-0.9-2-2-2c-1.1,0-2,0.9-2,2v30C48.5,59.6,49.4,60.5,50.5,60.5z M48.4,66.4
        c-0.6,0.6-0.9,1.3-0.9,2.1c0,0.8,0.3,1.6,0.9,2.1s1.3,0.9,2.1,0.9c0.8,0,1.6-0.3,2.1-0.9c0.6-0.6,0.9-1.3,0.9-2.1
        c0-0.8-0.3-1.6-0.9-2.1C51.5,65.3,49.5,65.3,48.4,66.4z"
                />
            </svg>
            <p style="font-family:'Open Sans'">Are you sure you want to cancel this Petition?</p>
            <button class="canpay-crew-sign-petition-modal-button" v-on:click="closeTheCrewWarning()">Yes</button>
            <button class="canpay-crew-sign-petition-not-ok-modal-button mt-2" v-on:click="justShowTheCrewCreatePetitionModal()">No</button>
    </div>
    </b-modal>
  </div>
</template>

<script>
import { shallowRef } from 'vue';
export default {
    name:"CanpayCrewCreatePetition",
    props:{
        storeAddress:{
            type:Function
        },
        createThePetition:{
            type:Function
        },
        addAdditionalContact:{
            type:Function
        },
        getPetitionId:{
            type:Function
        },
        oldCanPayCrewLanding:{
            type:Number
        },
        changeComponent:{
            type:Function
        },
        canPayCrewLanding:{
            type:Number
        },
        CanPayCrewShowCancelPetition:{
            type:Boolean,
            default:true
        }
    },
    mounted(){
        this.reset();
    },
    data(){
        return{
            stepForCreatingPetition:"",
            storeData:{
            'primary_contact_person_title':"",
            'store_name':"",
            'street_address':"",
            'city':"",
            'type':"mayor",
            'state':"",
            'zipcode':"",
            'primary_contact_person_email':"",
            'primary_contact_person_lastname':"",
            'primary_contact_person_firstname':"",
            'status_id':"PENDING",
            },
            isValidStoreEmail:true,
            isValidAdditionalContactEmail:true,
            additionalContact:{
                'petition_id':"",
                'secondary_contact_person_firstname':"",
                'secondary_contact_person_lastname':"",
                'secondary_contact_person_email':"",
                'secondary_contact_person_title':""
            },
            additinalContactError:{
                "error_first_name":"First name is required",
                "error_last_name":"Last name is required",
                "error_title":"Title is required",
                "error_email":"Email is required",
                "error_invalid_email":"Provide a valid email"
            },
            storeDataError:{
                "error_store_name":"Store name is required",
                "error_street_address":"Street address is required",
                "error_city":"City is required",
                "error_state":"State is required",
                "error_zipcode":"Zipcode is required",
                "error_first_name":"Frist name is required",
                "error_last_name":"Last name is required",
                "error_email":"Email is required",
                "error_title":"Title is required",
                "error_invalid_email":"Provide a valid email"
            },
            currentUser:null,
            currentContact:"primaryContact",
            consumerWantToProceed:false,
            showWarning: true
        }
        
    },
    created(){
        this.currentUser = localStorage.getItem("consumer_login_response")
        ? JSON.parse(localStorage.getItem("consumer_login_response"))
        : null;
    },
    methods:{
        proceedWithWarning(){
            this.consumerWantToProceed = true;
            if(this.currentContact == 'primaryContact'){
                this.addContactSuccess();
            }else{
                this.addTheAdditionalContact();
            }   
        },
        goBackAndChangeDetail(){
            this.consumerWantToProceed = true;
            if(this.currentContact == 'primaryContact'){
                this.stepForCreatingPetition = 'formFillUpForCreatingPetition'
            }else{
                this.stepForCreatingPetition = 'showforFillUpAdditionContactPetition'
            }
        },
        onSecondaryEmailInput(event){
            const liveInput = event.target.value;
            if(!this.isValidAdditionalContactEmail) this.isValidAdditionalContactEmail = this.checkEmailValidation(liveInput);
        },
        onEmailInput(event){
            const liveInput = event.target.value;
            if(!this.isValidStoreEmail) this.isValidStoreEmail = this.checkEmailValidation(liveInput);
        },
        checkEmailValidation(email){
            const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return regex.test(email);
        },
        resetAdditionContact(){
            this.isValidAdditionalContactEmail = true;
            this.additionalContact = {
                'petition_id':"",
                'secondary_contact_person_firstname':"",
                'secondary_contact_person_lastname':"",
                'secondary_contact_person_email':"",
                'secondary_contact_person_title':""
            }
        },
        reset(){
            this.isValidStoreEmail = true;
            this.storeData = {
            'primary_contact_person_title':"",
            'store_name':"",
            'street_address':"",
            'city':"",
            'type':"mayor",
            'state':"",
            'zipcode':"",
            'primary_contact_person_email':"",
            'primary_contact_person_lastname':"",
            'primary_contact_person_firstname':"",
            'status_id':"PENDING",
            }
        },
        addStoreData(items){
            let self = this;
            self.storeData.store_name = items.store_name;
            self.storeData.street_address = items.street_address;
            self.storeData.city = items.city
            self.storeData.state = items.state
            self.storeData.zipcode = items.zipcode
            self.storeData.lat = items.latitude
            self.storeData.long = items.longitude
        },
        showCanpayCrewCreatePetition(item){ 
            let self = this;
            self.reset();
            self.resetAdditionContact();
            self.addStoreData(item);
            this.stepForCreatingPetition = "initiateCreatingPetition"
            this.currentContact = 'primaryContact'
            self.showWarning = true;
            setTimeout(()=>{
                this.$refs['canpay-crew-create-petition'].show();
            },400)
        },
        closeCanPayCrewCreatePetition(redirectToPetition = true){
            this.$refs['show-warning-canpay-crew-for-backdrop'].hide();
            this.$refs['canpay-crew-create-petition'].hide();
            if(redirectToPetition){
                setTimeout(() => {
                    if(this.$route.name === 'canpaycrewpetition') this.$router.go(0);
                    else this.$router.push("/canpaycrewpetition")
                },200)
            }

        },
        canpayCrewCreateNewPetition(){
            this.stepForCreatingPetition = "formFillUpForCreatingPetition"
        },
        validateTheAddress(){
            let self = this;
            if(self.storeData.store_name == null 
            || self.storeData.street_address == null
            || self.storeData.city == null
            || self.storeData.state == null
            || self.storeData.zipcode == null
            ){
                return false;
            }
            return true;
        },
        rectifyBlanckStoreAdd(){
            this.storeData.primary_contact_person_title = this.storeData.primary_contact_person_title == "" ? null: this.storeData.primary_contact_person_title;
            this.storeData.store_name = this.storeData.store_name == "" ? null: this.storeData.store_name;
            this.storeData.street_address = this.storeData.street_address == "" ? null: this.storeData.street_address;
            this.storeData.city = this.storeData.city == "" ? null: this.storeData.city;
            this.storeData.state = this.storeData.state == "" ? null: this.storeData.state;
            this.storeData.zipcode = this.storeData.zipcode == "" ? null: this.storeData.zipcode;
            this.storeData.primary_contact_person_email = this.storeData.primary_contact_person_email == "" ? null: this.storeData.primary_contact_person_email;
            this.storeData.primary_contact_person_lastname = this.storeData.primary_contact_person_lastname == "" ? null: this.storeData.primary_contact_person_lastname;
            this.storeData.primary_contact_person_firstname = this.storeData.primary_contact_person_firstname == "" ? null: this.storeData.primary_contact_person_firstname;
        },
        updateTheAddress(){
            let self = this;
            self.rectifyBlanckStoreAdd();
            if(self.validateTheAddress()){
                self.addContactSuccess();
            }
        },
        validateTheInput(){
            let self = this;
            if(self.storeData.primary_contact_person_title == null || self.storeData.primary_contact_person_firstname  === null || self.storeData.primary_contact_person_firstname  == null || self.storeData.primary_contact_person_email == null){
                return false;
            } 
            return true;
        },

        showFormSucessMessage(){
            this.showWarning = true;
            this.$refs['canpay-crew-create-petition'].show();
            this.stepForCreatingPetition = "formFillUpScucess"
        },
        verifyCurrentUser(contact){
            this.currentContact = contact;
            if(contact == 'primaryContact'){
                if(this.currentUser.first_name.trim().toLowerCase() == this.storeData.primary_contact_person_firstname.trim().toLowerCase() 
                && this.currentUser.last_name.trim().toLowerCase() == this.storeData.primary_contact_person_lastname.trim().toLowerCase()
                && this.currentUser.email.trim().toLowerCase() == this.storeData.primary_contact_person_email.trim().toLowerCase()){
                    this.stepForCreatingPetition = "proceedWithWarning";
                    return false;
                }
            }else{
                if(this.currentUser.first_name.trim().toLowerCase() == this.additionalContact.secondary_contact_person_firstname.trim().toLowerCase() 
                && this.currentUser.last_name.trim().toLowerCase() == this.additionalContact.secondary_contact_person_lastname.trim().toLowerCase()
                && this.currentUser.email.trim().toLowerCase() == this.additionalContact.secondary_contact_person_email.trim().toLowerCase()){
                    this.stepForCreatingPetition = "proceedWithWarning";
                    return false;
                }
            }

            // proceeed to send data.
            return true;
        },
        addContactSuccess(){
            this.rectifyBlanckStoreAdd();
            this.isValidStoreEmail = this.checkEmailValidation(this.storeData.primary_contact_person_email); 
            if(this.isValidStoreEmail){
                if(this.validateTheInput()){
                    if(this.verifyCurrentUser("primaryContact") || this.consumerWantToProceed){
                         if(this.consumerWantToProceed)this.consumerWantToProceed = false;
                        if(!this.validateTheAddress() ){
                            this.stepForCreatingPetition = "checkTheGivenAddress"        
                        }else{
                            try{
                                this.createThePetition(this.storeData);
                                
                            }
                            catch(err){
                            }
                        }
                    }

                }
            }



        },
        addAdditionalContactData(){
            
            this.stepForCreatingPetition = "showforFillUpAdditionContactPetition";
        },
        justShowTheCrewCreatePetitionModal(){
            let self = this;
            self.showWarning = true;
            self.$refs['show-warning-canpay-crew-for-backdrop'].hide();
            self.$refs['canpay-crew-create-petition'].show();
        },
        onModalHide(bvEvent){
           let self = this;
           if( self.showWarning && bvEvent.trigger === 'backdrop' && (self.stepForCreatingPetition == 'formFillUpForCreatingPetition' || self.stepForCreatingPetition == 'showforFillUpAdditionContactPetition')){
                self.$refs['show-warning-canpay-crew-for-backdrop'].show();
           }
        },
        closeTheCrewWarning(){
            let self = this;
            self.$refs['show-warning-canpay-crew-for-backdrop'].hide();
        },
        verifyAdditionalContact(){
            let self = this;
            if(
                self.additionalContact.secondary_contact_person_firstname == null ||
                self.additionalContact.secondary_contact_person_lastname == null ||
                self.additionalContact.secondary_contact_person_title == null ||
                self.additionalContact.secondary_contact_person_email == null
              ){
                return false;
              }
              return true;
        },
        rectifyBlanckAddAdditionalStore(){
            let self = this;
            self.additionalContact.secondary_contact_person_firstname  = (self.additionalContact.secondary_contact_person_firstname == "") ? null : self.additionalContact.secondary_contact_person_firstname;
            self.additionalContact.secondary_contact_person_lastname  = (self.additionalContact.secondary_contact_person_lastname == "") ? null : self.additionalContact.secondary_contact_person_lastname;    
            self.additionalContact.secondary_contact_person_email  = (self.additionalContact.secondary_contact_person_email == "") ? null : self.additionalContact.secondary_contact_person_email;
            self.additionalContact.secondary_contact_person_title  = (self.additionalContact.secondary_contact_person_title == "") ? null : self.additionalContact.secondary_contact_person_title;
        },
        addTheAdditionalContact(){
            let self = this;
            self.rectifyBlanckAddAdditionalStore();
            self.isValidAdditionalContactEmail = self.checkEmailValidation(self.additionalContact.secondary_contact_person_email);
            if(self.isValidAdditionalContactEmail){
                if(self.additionalContact.petition_id == null || self.additionalContact.petition_id == '')
                    self.additionalContact.petition_id = self.getPetitionId();
                try{
                    if(self.verifyCurrentUser("secondaryContact") || self.consumerWantToProceed){
                        if(self.consumerWantToProceed)self.consumerWantToProceed = false;
                        if(self.verifyAdditionalContact()){
                            self.addAdditionalContact(self.additionalContact);
                            if(self.canPayCrewLanding == 4){
                                self.changeComponent(self.oldCanPayCrewLanding)
                            }else{
                                this.$router.push("/canpaycrewpetition")
                            }
                        }
                    }

                    
                }catch(err){
                }
            }

            
        },
        provideAdditionalContacts(item, warningAllowed = true){
            let self = this;
            self.resetAdditionContact();
            self.showWarning = warningAllowed;
            self.reset();
            self.addStoreData(item);
            self.stepForCreatingPetition = "showforFillUpAdditionContactPetition"
            self.currentContact = 'pimaryContact';
            setTimeout(()=>{
                self.additionalContact.petition_id = item.id;
                self.$refs['canpay-crew-create-petition'].show();
            },400)    
        }
    }
}
</script>
<style scoped>

</style>