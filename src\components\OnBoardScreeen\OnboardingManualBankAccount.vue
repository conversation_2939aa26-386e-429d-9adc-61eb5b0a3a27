<template>
<div>
    <div v-if="isLoading">
     <CanPayLoader/>
    </div>
  <div class="container splash">
    <cp-onboardingheader></cp-onboardingheader>
    <!-- this div is visible when finicity portal is connected -->
    <div
      id="connect-container"
      v-if="showFloatDiv && !isLoading"
      class="d-flex justify-content-start iframe-header"
    >
      <label
        >Having trouble?<button
          @click="showManual()"
          type="button"
          class="ml-2 align-self-center btn-manual-link"
        >
          Enter Banking Manually
        </button>
      </label>
    </div>

    <div v-if="!isLoading" >
      <div class="row icon-space">
        <div class="col-12">
          <img
            class="register-phone-image"
            src="../../assets/images/bank-acount.png"
            style="margin-top: 2.5rem; height: 4rem; width: 4rem"
          />
        </div>
      </div>
      <div class="row icon-space">
        <div class="col-12">
          <span class="text-center">
            <label class="register-phone-number-text"
              >Manually Add Your Bank Account</label
            >
          </span>
        </div>
      </div>

      <div class="row routing-bottom-space"></div>
      <div class="row">
        <div class="col-12">
          <div class="form-group">
            <input
              v-model="routingNumber"
              autocomplete="nope"
              class="form-control left-padding email-address"
              placeholder="Routing Number"
              @keyup.enter="focusNext()"
              @keypress="isNumber($event)"
              type="text"
              inputmode="numeric"
              maxlength="9"
              minlength="9"
            />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-12 mt-3">
          <div class="form-group">
            <input
              class="form-control left-padding email-address"
              ref="accountNumber"
              autocomplete="nope"
              v-model="accountnumber"
              @keyup.enter="continueBankLinking"
              placeholder="Account Number (0-9 , A-Z only)"
              type="text"
              @keypress="isAlphanumeric($event)"
              maxlength="17"
              minlength="5"
            />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-12 mt-3">
          <button
            style="margin-top: 0.2rem !important"
            type="button"
            class="btn-login btn-next-onboarding"
            @click="continueBankLinking"
          >
            Next
          </button>
        </div>
      </div>
    </div>
    <!---- MODAL FOR 0 Error Modal ---->
    <div>
      <b-modal
        ref="zero-pp-modal"
        hide-footer
        v-b-modal.modal-center
        modal-backdrop
        hide-header
        id="zero-pp-modal"
        centered
      >
        <div class="color">
          <div class="purchaserpower-modal-text">
            <div class="col-12 text-center">
              <svg
                version="1.1"
                id="Layer_1"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                x="0px"
                y="0px"
                width="120"
                height="120"
                viewBox="0 0 100 125"
                style="enable-background: new 0 0 100 125"
                xml:space="preserve"
                fill="#e14343"
                class="on-boarding-upgrade-alert-icon"
              >
                <path
                  d="M96.2,47.5l-22-38c-0.4-0.6-1-1-1.7-1h-44c-0.7,0-1.4,0.4-1.7,1l-22,37.9c-0.4,0.6-0.4,1.4,0,2l22,38.1c0.4,0.6,1,1,1.7,1
	h44c0.7,0,1.4-0.4,1.7-1l22-38C96.6,48.9,96.6,48.1,96.2,47.5z M71.3,84.5H29.7L8.8,48.4l20.8-35.9h41.7l20.8,36L71.3,84.5z
	 M50.5,60.5c1.1,0,2-0.9,2-2v-30c0-1.1-0.9-2-2-2c-1.1,0-2,0.9-2,2v30C48.5,59.6,49.4,60.5,50.5,60.5z M48.4,66.4
	c-0.6,0.6-0.9,1.3-0.9,2.1c0,0.8,0.3,1.6,0.9,2.1s1.3,0.9,2.1,0.9c0.8,0,1.6-0.3,2.1-0.9c0.6-0.6,0.9-1.3,0.9-2.1
	c0-0.8-0.3-1.6-0.9-2.1C51.5,65.3,49.5,65.3,48.4,66.4z"
                />
              </svg>
            </div>
            <div class="d-block text-center">
              <label class="purchasepower-def-label">
                {{ pp_alert_message }}
              </label>
            </div>
            <br />
            <br />
            <div></div>
            <div v-if="check" class="text-center">
              <div class="row mt-2 mb-3">
                <div class="col-12">
                  <button
                    type="button"
                    class="btn-modal-black p-1"
                    @click="hideModal('zero-pp-modal')"
                  >
                    Enter Again
                  </button>
                </div>
              </div>
              <div class="row">
                <div class="col-12">
                  <button
                    type="button"
                    class="btn-modal-green p-1"
                    @click="bankLinkFinicity"
                  >
                    Direct Link
                  </button>
                </div>
              </div>
            </div>

            <div class="text-center" v-else>
              <button
                type="button"
                class="mx-auto col-10 offset-1 btn-black"
                style="height: 60px"
                @click="hideModal('zero-pp-modal')"
              >
                <label class="purchasepower-modal-ok-label">OK</label>
              </button>
            </div>
          </div>
        </div>
      </b-modal>
    </div>
  </div>
</div>
</template>
<script>
import OnboardingHeader from "./OnboardingHeader.vue";
import api from "../../api/onboarding.js";
import constants from "../Common/constant.js";
import Loading from "vue-loading-overlay";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
/**
 * write a component's description
 * this component is use to login the user as a consumer
 */
export default {
  name: "ManualBankLink",
  /**
   * @description-
   * routingNumber => this will take the consumer's phone no
   * routingNumber => this will take the consumer's accountnumber
   * pinColor => use to validate the accountnumber field
   * routingNumberColor => use to validate the phoneno field
   * token => use to store the user's token
   * @returns {any}
   */
  data() {
    let self = this;
    return {
      email: "",
      routingNumber: "",
      accountnumber: "",
      pp_alert_message: "",
      isLoading: false,
      fullPage: true,
      check: false,
      showFloatDiv: false,
      responceError: false,
      sessionId: null,
    };
  },
  components: {
    "cp-onboardingheader": OnboardingHeader,
    Loading,
    CanPayLoader
  },
  created() {},
  destroyed() {
    document
      .getElementById("app")
      .style.setProperty("background-color", "#149240");
  },
  mounted() {
    this.$root.$emit("show_header", false);
    this.$root.$emit("changeWhiteBackground", [true, false, ""]);
    document
      .getElementById("app")
      .style.setProperty("background-color", "#ffffff");

    var element = document.getElementsByClassName("wrapper");
    element[0].style.setProperty("background-color", "#ffffff");

    var elementHtml = document.getElementsByTagName("html")[0];
    elementHtml.style.setProperty("background-color", "#ffffff");
    this.sessionId = atob(window.location.href.split("/").splice(-2, 1)[0]);
  },
  methods: {
    focusNext() {
      this.$refs["accountNumber"].focus();
    },
    showModal(modal) {
      this.$refs[modal].show();
    },
    hideModal(modal) {
      if (this.check) {
        this.check = false;
        this.routingNumber = "";
        this.accountnumber = "";
      }

      this.pp_alert_message = "";
      this.$refs[modal].hide();
      if (this.responceError) {
        this.responceError = false;
        this.$router.push(
          "/onboringconnectbankaccount/" +
            btoa(this.sessionId) +
            "/onboringconnectbankaccount"
        );
      }
    },

    isNumber: function (evt) {
      const regex = /[0-9]/g;
      evt = evt ? evt : window.event;
      var inputKey = String(evt.key);
      if (!inputKey.match(regex)) {
        evt.preventDefault();
      } else {
        return true;
      }
    },
    isAlphanumeric: function (evt) {
      const regex = /[0-9a-zA-Z]/g;
      evt = evt ? evt : window.event;
      var inputKey = String(evt.key);
      if (!inputKey.match(regex)) {
        evt.preventDefault();
      } else {
        return true;
      }
    },
    continueBankLinking() {
      var self = this;
      var numberRegex = /^[0-9\s]+$/;
      var alphanumberRegex = /^[0-9a-zA-Z\s]+$/;
      if (
        self.accountnumber != "" &&
        self.routingNumber != "" &&
        self.accountnumber == self.routingNumber
      ) {
        this.pp_alert_message =
          "Account number and routing number can't be same. Please provide valid banking details or link directly.";
        self.check = true;
        this.showModal("zero-pp-modal");
        return false;
      } else if (
        (self.accountnumber === "" && self.routingNumber === "") ||
        (!alphanumberRegex.test(self.accountnumber) &&
          !numberRegex.test(self.routingNumber)) ||
        ((self.accountnumber.length > 30 || self.accountnumber.length < 5) &&
          self.routingNumber.length != 9) ||
        (!numberRegex.test(self.accountnumber) &&
          self.routingNumber.length != 9) && (self.accountnumber.indexOf(" ") >= 0 &&self.routingNumber.indexOf(" ") >= 0)
      ) {
        this.pp_alert_message =
          "Please provide valid routing and account number fields.";
        this.showModal("zero-pp-modal");
        return false;
      } else if (
        self.accountnumber.length > 30 ||
        self.accountnumber.length < 5
      ) {
        this.pp_alert_message =
          "Invalid account number (must be minimum 5 characters)";
        this.showModal("zero-pp-modal");
        return false;
      } else if (self.routingNumber.length != 9) {
        this.pp_alert_message = "Invalid routing number (must be 9 digits)";

        this.showModal("zero-pp-modal");
        return false;
      } else if (!alphanumberRegex.test(self.accountnumber)||self.accountnumber.indexOf(" ") >= 0) {
        this.pp_alert_message = "Account Number(Alphanumeric Only)";
        this.showModal("zero-pp-modal");
        return false;
      } else if (!numberRegex.test(self.routingNumber)||self.routingNumber.indexOf(" ") >= 0) {
        this.pp_alert_message = "Routing number (numbers only)";
        this.showModal("zero-pp-modal");
        return false;
      } else if (self.checkAccountNumberContainNumber(self.accountnumber)) {
        this.pp_alert_message = "Account Number must have minimum 4 numbers";
        this.showModal("zero-pp-modal");
        return false;
      }
      this.isLoading = true;
      var request = {
        session_id: self.sessionId,
        routingNumber: self.routingNumber,
        accountNumber: self.accountnumber,
      };
      api
        .ValidateBankdetail(request)
        .then((response) => {
          self.isLoading = false;
          if (response.code == 200) {
            this.$router.push("/registrationsuccess");
          }
        })
        .catch((err) => {
          self.isLoading = false;
          self.pp_alert_message = err.response.data.message;
          self.responceError = true;
          this.showModal("zero-pp-modal");
          if(err.response.data.data == 'Error:3001'){
            localStorage.setItem("microbilt_error_need_bank_link", true);
          }
        });
    },
    bankLinkFinicity() {
      let self = this;
      this.isLoading = true;
      this.hideModal("zero-pp-modal");
      var request = {
        session_id: self.sessionId,
      };
      api
        .ConnectBank(request)
        .then((response) => {
          if (response.code == 200) {
            const finicityConnectUrl = response.data.link;
            window.finicityConnect.launch(finicityConnectUrl, {
              selector: "#connect-container",
              overlay: "rgba(255,255,255, 0)",
              success: function (data) {
                console.log("Yay! We got data", data);
                if (data.code == 200) {
                  self.showFloatDiv = false;
                  localStorage.setItem("onboardingComplete", true);
                  self.redirectToSuccess();
                }
              },
              cancel: function () {
                self.isLoading = false;
                self.showFloatDiv = false;
                console.log("The user cancelled the iframe");
              },
              error: function (err) {
                self.isLoading = false;
                self.showFloatDiv = false;
                console.error(
                  "Some runtime error was generated during Finicity Connect",
                  err
                );
              },
              loaded: function () {
                self.isLoading = false;
                self.showFloatDiv = false;
                console.log(
                  "This gets called only once after the iframe has finished loading"
                );
              },
              route: function (event) {
                if (self.userStatus != "706") {
                  self.isLoading = false;
                  self.showFloatDiv = true;
                  document.getElementById("finicityConnectIframe").style.top =
                    "65px";
                  document.getElementById(
                    "finicityConnectIframe"
                  ).style.height = "calc(100vh - 65px)";
                }
                console.log(
                  "This is called as the user progresses through Connect"
                );
              },
            });
          } else {
            self.isLoading = false;
            self.pp_alert_message = response.message;
            this.showModal("zero-pp-modal");
          }
        })
        .catch((err) => {
          self.isLoading = false;
          console.log(err);
        });
    },
    redirectToSuccess() {
      this.isLoading = true;
      var request = {
        session_id: self.sessionId,
      };
      api
        .Finalonboarding(request)
        .then((response) => {
          self.isLoading = true;
          if (response.code == 200) {
            this.$router.push("/registrationsuccess");
          }
        })
        .catch((err) => {
          this.isLoading = false;
          this.pp_alert_message = err.response.data.message;
          this.showModal("zero-pp-modal");
          if (err.response.data.code == 401) {
            this.pp_alert_message = err.response.data.message;
            this.showModal("zero-pp-modal");
          }
          if(err.response.data.code == 597){
            self.generateConnectFix(
              err.response.data.data.institutionId
            );
          }
        });
    },
    showManual() {
      this.showFloatDiv = false;
      window.finicityConnect.destroy();
      this.$router.push(
        "/onboardingmanualbankaccount/" +
          btoa(this.sessionId) +
          "/onboardingmanualbankaccount"
      );
    },
    checkAccountNumberContainNumber(accountNumber) {
      let checkAccountNumber;
      checkAccountNumber = accountNumber.split("");
      let count;
      count = 0;
      checkAccountNumber.forEach(checkValue);

      function checkValue(value, index, array) {
        if (!isNaN(value)) {
          count++;
        }
      }
      var isFourDigit = count >= 4 ? false : true;
      return isFourDigit;
    },
    generateConnectFix(id) {
      let self = this;
      this.isLoading = true;
      var request = {
        institution_id: id,
      };
      api
        .generateConnectFix(request)
        .then((response) => {
          if (response.code == 200) {
            const finicityConnectUrl = response.data.link;
            window.finicityConnect.launch(finicityConnectUrl, {
              selector: "#connect-container",
              overlay: "rgba(255,255,255, 0)",
              success: function (data) {
                console.log("Yay! We got data", data);
                if (data.code == 200) {
                  //now store the details at canpay end
                  self.bankLinkFinicity();
                }
              },
              cancel: function () {
                self.isLoading = false;
                console.log("The user cancelled the iframe");
              },
              error: function (err) {
                console.log(err);
                self.isLoading = false;
                console.error(
                  "Some runtime error was generated during Finicity Connect",
                  err
                );
              },
              loaded: function () {
                self.isLoading = false;
                console.log(
                  "This gets called only once after the iframe has finished loading"
                );
              },
              route: function (event) {
                self.isLoading = false;
                console.log(
                  "This is called as the user progresses through Connect"
                );
              },
              user: function (event) {
                if (event.data.errorCode) {
                  setTimeout(() => {
                    window.finicityConnect.destroy();
                    //if error code is present then call the connect fix api
                    self.generateConnectFix(event.data.data.institutionId);
                  }, 2000);
                }
              },
            });
          } else {
            self.isLoading = false;
          }
        })
        .catch((err) => {
          self.isLoading = false;
          console.log(err);
        });
    },
  },
};
</script>

<style lang="scss">
#zero-pp-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
</style>
