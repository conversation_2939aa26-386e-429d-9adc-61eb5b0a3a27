<template>
  <div>
  <div v-if="isLoading">
    <CanPayLoader/>
  </div>
  <div class="container px-4 pb-3">
    <mx-connect-widget ref="mxRef" :on-event="handleWidgetEvent" :widget-url="mxConnectUrl" />
    <div v-if="!isLoading">
      <div v-if="consumer_type == lite_consumer" class="consumer-lite-consumer-information">
        Hi, {{
            currentUser.first_name.charAt(0).toUpperCase() +
            currentUser.first_name.slice(1)
          }}!
      </div>
      <!------- Scroll UI Start --------->
      <div>
        <div class="div-container information-scroll" v-if="consumer_type == lite_consumer" :class="{ 'mt-3': currentUser.email_required == 0 && currentUser.address_required == 0 && currentUser.dob_required == 0 && currentUser.id_validation_required == 0}">
          <div class="inner-div-scroll" v-if="currentUser.address_required == 1">
            <div 
              class="accordion accordian-style floatLeft information-card" 
            >
            <div class="space-between-information">
              <div class="consumer-lite-width-20">
                <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 148 148" fill="none">
                <circle cx="74" cy="74" r="74" fill="#EDEDED"/>
                <path d="M83.441 105.517H80.1782H65.7005H57.2761H46.8322C41.9015 105.517 37.9044 101.519 37.9044 96.5888V63.7154L52.0788 49.5499L74.5708 27.0669L97.0629 49.5499L111.237 63.7154V96.5888C111.237 101.519 107.24 105.517 102.309 105.517H91.8654H83.441Z" fill="#EDEDED"/>
                <path d="M95.5006 81.8704C95.5006 99.0986 74.5712 112.941 74.5712 112.941C74.5712 112.941 53.6417 99.0988 53.6417 81.8704C53.6417 70.3092 63.0207 60.9409 74.5712 60.9409C86.1323 60.9411 95.5006 70.3092 95.5006 81.8704Z" fill="#E35D1B"/>
                <path d="M95.498 81.87C95.498 99.0986 74.5708 112.94 74.5708 112.94C74.5708 112.94 53.6437 99.0986 53.6437 81.87C53.6437 70.3082 63.0196 60.9429 74.5708 60.9429C86.1324 60.9429 95.498 70.3082 95.498 81.87Z" fill="#179346"/>
                <path d="M82.8644 81.1622C82.8644 85.7397 79.1492 89.4552 74.5714 89.4552C69.9937 89.4552 66.2784 85.7399 66.2784 81.1622C66.2784 76.5844 69.9937 72.8799 74.5714 72.8799C79.1492 72.8799 82.8644 76.5846 82.8644 81.1622Z" fill="#F0EFFF"/>
                <path d="M102.31 106.829H91.8661C91.1415 106.829 90.5539 106.241 90.5539 105.516C90.5539 104.792 91.1417 104.204 91.8661 104.204H102.31C106.51 104.204 109.926 100.788 109.926 96.589V64.2595L74.5715 28.9222L39.2175 64.2595V96.589C39.2175 100.788 42.6338 104.204 46.8331 104.204H57.277C58.0015 104.204 58.5892 104.792 58.5892 105.516C58.5892 106.241 58.0013 106.829 57.277 106.829H46.8331C41.1866 106.829 36.5929 102.235 36.5929 96.589V63.7154C36.5929 63.3668 36.7312 63.0336 36.9774 62.7876L73.6438 26.139C74.1564 25.6264 74.9868 25.6264 75.4992 26.139L112.166 62.7876C112.412 63.0336 112.55 63.3668 112.55 63.7154V96.589C112.55 102.235 107.957 106.829 102.31 106.829Z" fill="black"/>
                <path d="M115.223 69.018C114.887 69.018 114.552 68.8898 114.295 68.6335L110.307 64.6456C109.795 64.133 109.795 63.3026 110.307 62.79C110.82 62.2774 111.65 62.2774 112.163 62.79L116.151 66.7779C116.664 67.2905 116.664 68.1209 116.151 68.6333C115.895 68.8898 115.559 69.018 115.223 69.018Z" fill="black"/>
                <path d="M33.9188 69.018C33.5831 69.018 33.2473 68.8899 32.991 68.6336C32.4784 68.1209 32.4784 67.2906 32.991 66.7781L36.9793 62.7902C37.4919 62.2776 38.3223 62.2776 38.8347 62.7902C39.3474 63.3029 39.3474 64.1332 38.8347 64.6459L34.8465 68.6337C34.5902 68.8899 34.2545 69.018 33.9188 69.018Z" fill="black"/>
                <g opacity="0.25">
                <path d="M95.4995 81.87C95.4995 99.0986 74.5723 112.94 74.5723 112.94C74.5723 112.94 73.9558 112.536 72.9406 111.774C77.8667 108.101 92.2412 96.1461 92.2412 81.87C92.2412 70.8573 83.7408 61.8389 72.9406 61.005C73.4793 60.9635 74.0233 60.9429 74.5723 60.9429C86.1339 60.9429 95.4995 70.3082 95.4995 81.87Z" fill="black"/>
                </g>
                <path d="M74.5709 114.253C74.319 114.253 74.0668 114.181 73.8473 114.035C72.9682 113.454 52.3292 99.6003 52.3292 81.87C52.3292 69.6056 62.3065 59.6283 74.5709 59.6283C86.8353 59.6283 96.8126 69.6056 96.8126 81.87C96.8126 99.6001 76.1735 113.454 75.2945 114.035C75.075 114.181 74.8231 114.253 74.5709 114.253ZM74.5709 62.2529C63.7538 62.2529 54.9536 71.053 54.9536 81.8702C54.9536 96.4062 70.9427 108.731 74.5709 111.34C78.1992 108.731 94.1882 96.406 94.1882 81.8702C94.1882 71.0529 85.3881 62.2529 74.5709 62.2529Z" fill="black"/>
                <path d="M74.5719 90.7678C69.276 90.7678 64.9669 86.4587 64.9669 81.1621C64.9669 75.8714 69.276 71.5674 74.5719 71.5674C79.8677 71.5674 84.1769 75.8714 84.1769 81.1621C84.1769 86.4587 79.8678 90.7678 74.5719 90.7678ZM74.5719 74.1918C70.7223 74.1918 67.5915 77.3185 67.5915 81.1621C67.5915 85.0116 70.7225 88.1434 74.5719 88.1434C78.4213 88.1434 81.5524 85.0114 81.5524 81.1621C81.5525 77.3185 78.4215 74.1918 74.5719 74.1918Z" fill="black"/>
                </svg>
              </div>
                <div class="consumer-lite-width-70 consumer-lite-text-left ">
                  <p class="consumer-lite-additional-information" @click="completeRegistration(constants.lite_consumer_address)"><span >Add Your Address</span><svg xmlns="http://www.w3.org/2000/svg"
                     width="20" height="20" class=" consumer-lite-arrow ml-1" viewBox="0 0 44 44" fill="none">
                      <circle cx="22" cy="22" r="22" fill="black"/>
                      <path d="M19.0296 32.1136L19.3832 32.4671L19.7367 32.1136L29.6168 22.2336L29.9703 21.88L29.6168 21.5264L19.7368 11.6464L19.3832 11.2929L19.0296 11.6464L17.6464 13.0296L17.2929 13.3832L17.6464 13.7368L25.7897 21.88L17.6464 30.0232L17.2929 30.3768L17.6464 30.7304L19.0296 32.1136Z" fill="white" stroke="white"/>
                      </svg></p>
                  <p class="consumer-lite-describe-information">
                    <span>
                      Did you know merchants <br>
                      can offer benefits to locals?
                    </span>
                  </p>
                </div>
              </div>
            </div>

            <div class="accordion accordian-style floatLeft" 
            style="width:100%;"
            >

            </div>
          </div>
          <div class="inner-div-scroll" v-if="currentUser.email_required == 1">
            <div 
              class="accordion accordian-style floatLeft information-card" 
            >
            <div class="space-between-information">
              <div class="consumer-lite-width-20">
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="60" height="60" viewBox="0 0 99 99" fill="none">
                <circle cx="49.5" cy="49.5" r="49.5" fill="#D9D9D9"/>
                <rect x="21" y="18" width="57.8472" height="64.0769" fill="url(#pattern0_199_236)"/>
                <defs>
                <pattern id="pattern0_199_236" patternContentUnits="objectBoundingBox" width="1" height="1">
                <use xlink:href="#image0_199_236" transform="matrix(0.0029304 0 0 0.0026455 -0.141758 0)"/>
                </pattern>
                <image id="image0_199_236" width="438" height="378" xlink:href="data:image/png;base64,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"/>
                </defs>
                </svg>
              </div>
                <div class="consumer-lite-width-70 consumer-lite-text-left ">
                  <p class="consumer-lite-additional-information" @click="completeRegistration(constants.lite_consumer_email)"><span >Add Your Email</span><svg xmlns="http://www.w3.org/2000/svg"
                     width="20" height="20" class="consumer-lite-arrow ml-1" viewBox="0 0 44 44" fill="none">
                      <circle cx="22" cy="22" r="22" fill="black"/>
                      <path d="M19.0296 32.1136L19.3832 32.4671L19.7367 32.1136L29.6168 22.2336L29.9703 21.88L29.6168 21.5264L19.7368 11.6464L19.3832 11.2929L19.0296 11.6464L17.6464 13.0296L17.2929 13.3832L17.6464 13.7368L25.7897 21.88L17.6464 30.0232L17.2929 30.3768L17.6464 30.7304L19.0296 32.1136Z" fill="white" stroke="white"/>
                      </svg></p>
                  <p class="consumer-lite-describe-information">
                    <span>
                      Get Exclusive deals, Rewards<br>
                      and more straight to your inbox.
                    </span>
                  </p>
                </div>
              </div>
            </div>

            <div class="accordion accordian-style floatLeft" 
            style="width:100%;"
            >

            </div>
          </div>
          <div class="inner-div-scroll" v-if="currentUser.dob_required == 1">
            <div 
              class="accordion accordian-style floatLeft information-card" 
            >
            <div class="space-between-information">
              <div class="consumer-lite-width-20">
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="60" height="60" viewBox="0 0 99 99" fill="none">
                <circle cx="49.5" cy="49.5" r="49.5" fill="#D9D9D9"/>
                <rect x="18" y="16" width="63" height="67" fill="url(#pattern0_192_242)"/>
                <defs>
                <pattern id="pattern0_192_242" patternContentUnits="objectBoundingBox" width="1" height="1">
                <use xlink:href="#image0_192_242" transform="matrix(0.00941143 0 0 0.00884956 -0.0364517 0)"/>
                </pattern>
                <image id="image0_192_242" width="114" height="113" xlink:href="data:image/png;base64,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"/>
                </defs>
                </svg>
              </div>
                <div class="consumer-lite-width-70 consumer-lite-text-left ">
                  <p class="consumer-lite-additional-information" @click="completeRegistration(constants.lite_consumer_dob)"><span >Add Your Date of Birth</span><svg xmlns="http://www.w3.org/2000/svg"
                     width="20" height="20" class="consumer-lite-arrow ml-1" viewBox="0 0 44 44" fill="none">
                      <circle cx="22" cy="22" r="22" fill="black"/>
                      <path d="M19.0296 32.1136L19.3832 32.4671L19.7367 32.1136L29.6168 22.2336L29.9703 21.88L29.6168 21.5264L19.7368 11.6464L19.3832 11.2929L19.0296 11.6464L17.6464 13.0296L17.2929 13.3832L17.6464 13.7368L25.7897 21.88L17.6464 30.0232L17.2929 30.3768L17.6464 30.7304L19.0296 32.1136Z" fill="white" stroke="white"/>
                      </svg></p>
                  <p class="consumer-lite-describe-information">
                    <span>
                      Enter your date of birth <br> 
                      to personalize your experience.
                    </span>
                  </p>
                </div>
              </div>
            </div>

            <div class="accordion accordian-style floatLeft" 
            style="width:100%;"
            >

            </div>
          </div>
          <div class="inner-div-scroll" v-if="currentUser.id_validation_required == 1">
            <div 
              class="accordion accordian-style floatLeft information-card" 
            >
            <div class="space-between-information">
              <div class="consumer-lite-width-20">
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"  width="60" height="60" viewBox="0 0 99 99" fill="none">
                <circle cx="49.5" cy="49.5" r="49.5" fill="#D9D9D9"/>
                <rect x="13" y="27" width="74" height="46" fill="url(#pattern0_192_242)"/>
                <defs>
                <pattern id="pattern0_192_242" patternContentUnits="objectBoundingBox" width="1" height="1">
                <use xlink:href="#image0_192_242" transform="matrix(0.00295858 0 0 0.00475945 0 -0.135387)"/>
                </pattern>
                <image id="image0_192_242" width="338" height="267" xlink:href="data:image/png;base64,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"/>
                </defs>
                </svg>
              </div>
                <div class="consumer-lite-width-70 consumer-lite-text-left ">
                  <p class="consumer-lite-additional-information" @click="completeRegistration(constants.lite_consumer_id_validation)">
                    <span >
                      {{ currentUser.id_validation_status == '202' 
                        ? 'Verification Awaiting' 
                        : currentUser.id_validation_status == '600' 
                        ? 'Verification Failed' 
                        : currentUser.id_validation_status == '723' 
                        ? 'Verification Failed' 
                        : 'Validate your identity' }}
                    </span><svg xmlns="http://www.w3.org/2000/svg"
                     width="20" height="20" class="consumer-lite-arrow ml-1" viewBox="0 0 44 44" fill="none">
                      <circle cx="22" cy="22" r="22" fill="black"/>
                      <path d="M19.0296 32.1136L19.3832 32.4671L19.7367 32.1136L29.6168 22.2336L29.9703 21.88L29.6168 21.5264L19.7368 11.6464L19.3832 11.2929L19.0296 11.6464L17.6464 13.0296L17.2929 13.3832L17.6464 13.7368L25.7897 21.88L17.6464 30.0232L17.2929 30.3768L17.6464 30.7304L19.0296 32.1136Z" fill="white" stroke="white"/>
                      </svg></p>
                  <p class="consumer-lite-describe-information">
                    <span v-if="currentUser.id_validation_status == '202'">
                      Only one enrollment allowed. <br>Your identity validation is pending.
                    </span>
                    <span v-else-if="currentUser.id_validation_status == '600'">
                      Your account information could <br> not be verified during enrollment.
                    </span>
                    <span v-else-if="currentUser.id_validation_status == '723'">
                      The identity verification failed due <br> to incorrect information provided.
                    </span>
                    <span v-else>
                      Confirming authenticity to ensure <br> secure access and data protection.
                    </span>
                  </p>
                </div>
              </div>
            </div>

            <div class="accordion accordian-style floatLeft" 
            style="width:100%;"
            >

            </div>
          </div>
        </div>
      </div>
      <!------- Scroll UI end -------------->
      <!------- UI for Consumer App Start -------->
      <div v-if="consumer_type != lite_consumer" class="row row-gap section-purchasepower mb-3 mt-3 px-3">
        <div class="pr-0">
          <span class="user-block mt-0 d-flex justify-content-center align-items-center">
            <img
              src="../../assets/images/user.png"
              class="user-avatar"
            />
          </span>
        </div>
        <label class="pay-user-label usernamelabel d-flex align-items-center m-0 pl-0">
          Hello,
          {{
            currentUser.first_name.charAt(0).toUpperCase() +
            currentUser.first_name.slice(1)
          }}!
        </label>
      </div>
    <!----------- UI for Consumer App End ------------------>
    <!------------- Balance User Interface start ----------->
    <div v-if="consumer_type == lite_consumer" class="d-flex col-12 flex-column purchase_power_style mx-0 px-0 mb-3">
      <!----------- Balance Header Start -------------------->
      <div class="col-12 purchase-power-header-for-dashboard information-border-except-below"
      >
        <div class="purchpower-text d-flex align-items-center">
            Balance
        </div>
      </div>
      <!---------- Balance Header End ---------------------->
      <div 
        class="information-border-except-top"
      >
      <!---------- Purchase Power Start -------------------->
      <div class="pl-3 pr-4 pb-2 pt-0 mt-3">
        <p class="purchasepower-box-amount float-left mb-0">{{ purchasepower }}</p>
        <span style="
          position: absolute;
          right: 11px;
        ">
          <svg width="17px" fill="#fff" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 90.6 100" xml:space="preserve"><path d="M45.3,0L0,30.1v21h90.6v-21L45.3,0z M9,42.1v-7.2l36.3-24.1l36.3,24.1v7.2L9,42.1z"></path><rect x="0.1" y="91" width="90.4" height="9"></rect><rect x="11.6" y="57.9" width="11.5" height="26.3"></rect><rect x="67.5" y="57.9" width="11.5" height="26.3"></rect><rect x="39.6" y="57.9" width="11.5" height="26.3"></rect></svg>
          &nbsp;<span v-if="currentUser.bank_details_required == 1" class="balance-add-bank"><span @click="changeBankAccount()" >Add Bank</span><svg @click="showModal('bank-info-modal')" xmlns="http://www.w3.org/2000/svg" class="ml-2" width="16" height="16" viewBox="0 0 45 45" fill="none" 
            style="
                position: relative;
                top: -1px;
              "
            >
            <path d="M22.5 0C10.05 0 0 10.05 0 22.5C0 34.95 10.05 45 22.5 45C34.95 45 45 34.95 45 22.5C45 10.1 34.9 0 22.5 0ZM22.5 41C12.3 41 4 32.7 4 22.5C4 12.3 12.3 4 22.5 4C32.7 4 41 12.3 41 22.5C41 32.7 32.7 41 22.5 41ZM20 10C20 8.6 21.1 7.5 22.5 7.5C23.9 7.5 25 8.6 25 10C25 11.4 23.9 12.5 22.5 12.5C21.1 12.5 20 11.4 20 10ZM28 32.7V34C28 34.55 27.55 35 27 35H18C17.45 35 17 34.55 17 34V32.7C17 32.25 17.3 31.9 17.7 31.75L19.75 31.1C19.9 31.05 20 30.9 20 30.75V20.5H18.45C17.65 20.5 17 19.85 17 19.05C17 18.45 17.4 17.9 18 17.7L23.7 15.9C24.25 15.75 24.8 16.05 24.95 16.55C25 16.65 25 16.75 25 16.85V30.75C25 30.9 25.1 31.05 25.25 31.1L27.3 31.75C27.7 31.85 28 32.25 28 32.7Z" fill="white"/>
            </svg>
          </span>
          <span v-else @click="gotoBankDetails()" class="balance-add-bank">x {{ last_four_account_no }}</span>
        </span>
      </div>
      <!---------- Purchase Power End ---------------------->
      <div style="height:125px;">
      <!---------- Swipe Merchant store 1 -------------------->
      <div class="div-container container swipe-merchant-top"
      >
          <div class="inner-div-scroll">
            <div 
              class="accordion floatLeft accordian-merchant-style"
            >
              <div
                class="accordian-merchant-inner-style-blue"
              >
                  <p class="merchant-balance-style"
                  >
                  ${{rewardPoints && rewardPoints.available_canpay_points.reward_amount ? amountNumberFormatter(parseFloat(rewardPoints.available_canpay_points.reward_amount).toFixed(2)) : 0.00}}
                  </p>
                  <p class="merchant-name-style"
                  >
                    CanPay Points
                  </p>
              </div>
            </div>
          </div>
          <div class="inner-div-scroll" v-if="false">
            <div 
              class="accordion floatLeft accordian-merchant-style"
            >
              <div
                class="accordian-merchant-inner-style"
              >
                  <p class="merchant-balance-style"
                  >
                  $20.00
                  </p>
                  <p class="merchant-name-style"
                  >
                    Trulive
                  </p>
              </div>
            </div>
          </div>
      </div>
      <!--------- Swipe Merchant Store 1 --------------------->
      </div>
      </div>
    </div>
    <!------------- Balance User Internface end ------------>
    <!------------- Purchase Power UI start ---------------->
    <div v-else class="mb-3 consumer-lite-border">
    <div class="d-flex col-12 flex-column purchase_power_style mx-0 px-0">
        <div class="col-12 purchase-power-header-for-dashboard">
          <div class="purchpower-text d-flex align-items-center" v-if="manual_bank_link">Spending Limit <span class="tm-symbol ml-2">TM</span></div>
          <div class="purchpower-text d-flex align-items-center" v-else>PurchPower <span class="tm-symbol ml-2">TM</span></div>
        </div>
        <div class="row" v-if="!show_purchase_power">
          <div class="col-7">
            <div class="progress-bar-animation progress-bar-alignment" id="progress-bar" :style="{ '--animation-duration': (animation_time_difference)+'ms' }" v-if="!show_purchase_power"></div>
          </div>
        <div class="col-5 py-3 mx-0 row justify-content-end align-items-center" v-if="!manual_bank_link" @click="gotoBankDetails()">
          <svg v-if="consumer_type != lite_consumer" class="mr-2" width="17px" fill="#fff" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
            viewBox="0 0 90.6 100" style="enable-background:new 0 0 90.6 100;" xml:space="preserve">
          <path d="M45.3,0L0,30.1v21h90.6v-21L45.3,0z M9,42.1v-7.2l36.3-24.1l36.3,24.1v7.2L9,42.1z"/>
          <rect x="0.1" y="91" width="90.4" height="9"/>
          <rect x="11.6" y="57.9" width="11.5" height="26.3"/>
          <rect x="67.5" y="57.9" width="11.5" height="26.3"/>
          <rect x="39.6" y="57.9" width="11.5" height="26.3"/>
          </svg>
          <p v-if="consumer_type != lite_consumer" style="border-bottom: 0.01rem solid #fff; line-height: 18px;margin-right:16px!important;" class="text-white m-0">
            x {{ last_four_account_no }}
          </p>
        </div>
        </div>
        <section v-else>
        <div  class="col-12 py-2 mx-0 row justify-content-end align-items-center" v-if="!manual_bank_link" @click="gotoBankDetails()">
          <svg v-if="consumer_type != lite_consumer" class="mr-2" width="17px" fill="#fff" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
            viewBox="0 0 90.6 100" style="enable-background:new 0 0 90.6 100;" xml:space="preserve">
          <path d="M45.3,0L0,30.1v21h90.6v-21L45.3,0z M9,42.1v-7.2l36.3-24.1l36.3,24.1v7.2L9,42.1z"/>
          <rect x="0.1" y="91" width="90.4" height="9"/>
          <rect x="11.6" y="57.9" width="11.5" height="26.3"/>
          <rect x="67.5" y="57.9" width="11.5" height="26.3"/>
          <rect x="39.6" y="57.9" width="11.5" height="26.3"/>
          </svg>
          <p style="border-bottom: 0.01rem solid #fff; line-height: 18px;" class="text-white m-0" v-if="consumer_type != lite_consumer">
            x {{ last_four_account_no }}
          </p>

        </div>
        </section>

        <div :class="!manual_bank_link ? 'pl-3 pr-4 pb-2 pt-0' : 'pl-3 pr-4 pb-2 pt-3'">
        <!-- start -->
        <p v-if="!show_purchase_power" style="margin:0px!important;text-align:left;font-size:12px;font-family:montserrat;color:white;font-weight:bolder;text-align:left;white-space:nowrap;" class="purchase-power-animation mt-3">
          <svg version="1.1" id="Layer_1" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd" xmlns:svg="http://www.w3.org/2000/svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 26.5 33.1" xml:space="preserve" width="22" height="22" fill="#ffffff"><g transform="translate(0,-270.54165)"><path d="M17.1,272.1c-0.3,0-0.6,0.1-0.8,0.2l-5.6,3.2H3.4c-1,0-1.8,0.8-1.8,1.8v16.3c0,1,0.8,1.8,1.8,1.8h18.8c1,0,1.8-0.8,1.8-1.8
              v-4.8h0.4c0.3,0,0.5-0.2,0.5-0.5v-4.7c0-0.3-0.2-0.5-0.5-0.5h-0.4v-5.7c0-1-0.8-1.8-1.8-1.8h-2l-1.5-2.6
              C18.3,272.4,17.7,272.1,17.1,272.1L17.1,272.1z M17.2,273.2c0.2,0,0.4,0.1,0.6,0.4l2.5,4.4H8.7l8-4.6
              C16.9,273.2,17,273.2,17.2,273.2L17.2,273.2z M3.4,276.6h5.4l-2.2,1.2H4.1c-0.7,0-0.7,1,0,1h18.8v4.2h-3.3c-1.6,0-2.9,1.3-2.9,2.9
              s1.3,2.9,2.9,2.9h3.3v4.8c0,0.4-0.3,0.7-0.7,0.7H3.4c-0.4,0-0.7-0.3-0.7-0.7v-16.3C2.6,277,2.9,276.6,3.4,276.6L3.4,276.6z
              M20.8,276.6h1.4c0.4,0,0.7,0.3,0.7,0.7v0.5h-1.4L20.8,276.6z M19.6,284.1h4.3v3.6h-4.3c-1,0-1.8-0.8-1.8-1.8
              C17.7,284.9,18.5,284.1,19.6,284.1z M19.8,284.9c-0.6,0-1.1,0.5-1.1,1c0,0.6,0.5,1,1.1,1s1.1-0.5,1.1-1
              C20.9,285.4,20.4,284.9,19.8,284.9z"></path></g></svg>
          Calculating Purchase Power...</p>
          <!-- end -->
          <p v-else class="purchasepower-box-amount float-left mb-0">{{
            purchasepower
          }}</p>
        </div>
        <div class="text-left px-3 pt-2 pb-2 pending-details-link" v-if="pending_transaction_amount > 0 && show_purchase_power">
          <a @click="gotoPending()" class="pending-tran-val">Pending Transactions ${{ parseFloat(pending_transaction_amount).toFixed(2) }}</a>
        </div>
        <div class="px-3 pt-2 pb-3">
          <div class="d-flex" @click="showModal('my-modal')">
            <svg
              version="1.1"
              id="Layer_1"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              x="0px"
              y="0px"
              width="17"
              fill="#ffffff"
              viewBox="0 0 90 90"
              style="enable-background: new 0 0 90 90; float: left"
              xml:space="preserve"
            >
              <path
                d="M45,0C20.1,0,0,20.1,0,45s20.1,45,45,45s45-20.1,45-45C90,20.2,69.8,0,45,0z M45,82C24.6,82,8,65.4,8,45S24.6,8,45,8
                  s37,16.6,37,37S65.4,82,45,82z M40,20c0-2.8,2.2-5,5-5s5,2.2,5,5s-2.2,5-5,5S40,22.8,40,20z M56,65.4V68c0,1.1-0.9,2-2,2H36
                  c-1.1,0-2-0.9-2-2v-2.6c0-0.9,0.6-1.6,1.4-1.9l4.1-1.3c0.3-0.1,0.5-0.4,0.5-0.7V41h-3.1c-1.6,0-2.9-1.3-2.9-2.9c0-1.2,0.8-2.3,2-2.7
                  l11.4-3.6c1.1-0.3,2.2,0.3,2.5,1.3c0.1,0.2,0.1,0.4,0.1,0.6v27.8c0,0.3,0.2,0.6,0.5,0.7l4.1,1.3C55.4,63.7,56,64.5,56,65.4z"
              />
            </svg>
            <span
              class="float-left helptext-label"
            >
              <span v-if="manual_bank_link" class="align-top"
                >How to increase my Spending Limit?</span>
                <span v-else>What is my Purchase Power?</span>
              </span>
        </div>
      </div>
      
    </div>
    </div>
    
    <div v-if="rwState == 'allowed' || rwState == 'partially_allowed'" class="d-flex col-12 flex-column purchase_power_style mx-0 px-0 mb-3">
      <div class="row">
          <div class="col-12">
            <a style="text-decoration: none;" href="javascript:void(0)" @click="redirectToPointsPage()">
            <div class="cp-point-card">
              <div class="cp-point-card-top information-border-except-below"
              >
                <div class="cp-point-tag row mx-0">

                    <div class="col-9" style="padding:0px!important;">
                      <span class="ml-2" style="font-weight:600!important;font-family:'Open Sans';font-size:18px;">CanPay Points Balance</span> 
                    </div>
                  <div  class="col-3" style="padding:0px!important;">
                  <div @click.stop="showModal('cp-program-info-modal')" class="ml-2 d-flex align-items-center" style="position:absolute; right:0px;">
                      <svg width="16px" fill="#fff" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                      viewBox="0 0 490 490" xml:space="preserve">
                      <g id="bold_copy_37_">
                      <path d="M245,0C109.684,0,0,109.684,0,245s109.684,245,245,245s245-109.684,245-245S380.316,0,245,0z M245,459.375
                      c-118.213,0-214.375-96.163-214.375-214.375S126.787,30.625,245,30.625S459.375,126.787,459.375,245S363.212,459.375,245,459.375z"
                      />
                      <polygon points="266.836,286.987 275.196,114.874 214.788,114.874 223.532,286.987 	"/>
                      <path d="M245.184,305.974c-20.136,0-34.178,14.424-34.178,34.576c0,19.738,13.674,34.576,34.178,34.576
                      c20.503,0,33.825-14.823,33.825-34.576C278.611,320.399,265.304,305.974,245.184,305.974z"/></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g></svg>
                  </div>
                  </div>
                </div>
                <div class="cp-point-val mb-1 d-flex align-items-center mt-2">
                  <svg class="ml-2" width="22" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="35" cy="35" r="35" fill="black"/>
                  <path d="M58.0327 44.4525C60.969 38.2154 59.7673 32.2515 59.4272 30.6694C59.0417 28.8825 58.0667 24.66 54.5183 20.8699C52.8282 19.0889 50.821 17.6413 48.6004 16.6018C45.6075 15.2019 43.0567 14.9287 41.6622 14.7921C35.9144 14.223 31.1643 15.9075 28.5114 17.114C29.8031 15.8716 31.2794 14.8387 32.8875 14.0523C34.3086 13.3704 35.8149 12.8842 37.3656 12.6069C39.9046 12.1197 42.5076 12.0697 45.0633 12.4589C46.2423 12.641 51.1626 13.4946 56.0714 17.3188C63.225 22.8844 66.1613 30.6125 64.4947 39.5016C61.9553 53.1026 48.9632 61.0697 35.7897 57.5641C31.9125 56.5284 27.8312 54.0814 26.0173 51.6912C26.108 51.6912 26.2101 51.6685 26.2667 51.7026C26.7202 51.9985 27.1624 52.3172 27.6272 52.6131C34.9962 57.2568 42.5465 57.291 50.0515 53.1367C51.0019 52.6094 51.9047 52.0001 52.7497 51.3156C56.0034 48.6751 57.4885 45.6021 58.0327 44.4525Z" fill="white"/>
                  <path d="M65.1182 45.2722C65.0389 45.7502 64.9028 46.4331 64.6874 47.2412C63.1229 53.023 59.5518 56.6765 58.0213 58.2016C52.4209 63.79 45.9022 65.4062 42.9206 66.1119C36.7873 67.5573 31.8331 66.9427 30.0532 66.6582C23.6479 65.6338 19.2492 63.0388 17.9001 62.1852C15.1477 60.4502 12.6808 58.2961 10.5878 55.8001C8.14327 52.8969 6.22821 49.5834 4.93068 46.012C4.60191 45.1014 3.35485 41.5276 3.03741 36.6335C2.74266 31.9671 3.44554 28.4843 3.6156 27.6762C4.17003 25.0362 5.06567 22.4803 6.27977 20.0733C6.91463 18.8441 10.1456 12.8346 17.2879 8.35027C18.9431 7.30316 23.2738 4.84474 29.407 3.91145C31.0282 3.66105 45.0066 1.72618 55.0171 10.5242C59.6539 14.5988 62.386 20.0505 62.8168 20.9269C63.4801 22.2806 64.0557 23.6759 64.54 25.104C64.3813 24.8877 63.2023 23.226 63.1683 23.1805C58.8149 16.7044 53.7587 13.6541 53.7587 13.6541C51.8468 12.5265 49.7911 11.665 47.6481 11.0932C40.8346 9.24941 35.0981 10.8884 33.7377 11.2981C25.7565 13.6996 21.0291 20.0847 20.757 20.4603C15.9388 27.1299 16.0408 34.0954 16.1315 36.0303C16.4376 42.5406 19.2718 47.1387 20.5642 48.9939C20.6663 49.1191 20.8137 49.3126 20.995 49.5403C21.1311 49.7224 21.2671 49.9045 21.4032 50.0866C24.7816 54.5482 29.2596 57.6553 34.2365 59.2032C37.9454 60.3419 41.8646 60.6138 45.6942 59.9984C49.5239 59.3829 53.1626 57.8962 56.3321 55.6522C59.121 53.6718 60.8102 51.6117 61.8532 50.337C63.2363 48.6525 64.2453 46.9908 64.5741 46.3306C64.6194 46.251 65.0956 45.2722 65.1182 45.2722Z" fill="#007EE5"/>
                  <path d="M28.7061 45.6C22.7202 45.6 19.7273 41.6278 19.7273 35.5387C19.7273 29.2447 22.879 25.4546 29.0462 25.4546C31.427 25.4546 33.3429 26.0237 34.8167 27.2301C36.1998 28.4707 36.9707 30.1552 37.1634 32.2949H34.76C33.8984 32.2949 33.2749 31.8966 32.9008 31.1112C32.2432 29.7113 30.9508 28.9943 29.0576 28.9943C25.3617 28.9943 23.7179 31.612 23.7179 35.5501C23.7179 39.3743 25.2937 42.0717 28.9329 42.0717C31.427 42.0717 32.8554 40.6945 33.2862 38.532H37.1521C36.8007 43.0392 33.6377 45.6 28.7061 45.6Z" fill="white"/>
                  <path d="M49.3373 25.2747H42.5465C41.4015 25.2747 40.4945 26.2762 40.4945 27.4258V45.3063H44.5758V38.17H49.632C53.634 38.17 55.7766 35.6661 55.7766 31.7166C55.7766 27.551 53.5773 25.2747 49.3373 25.2747ZM48.8725 34.5507H44.8932V28.746H49.0765C50.9811 28.746 51.9561 29.7135 51.9561 31.6597C51.9674 33.606 50.9471 34.5848 48.8725 34.5507H48.8725Z" fill="#007EE5"/>
                  </svg>
                  <span class="ml-2" style="font-weight:600!important;font-family:'Montserrat';font-size:17px;"> + </span>
                  <span class="ml-1" style="font-weight:600!important;font-family:'Montserrat';font-size:17px;">{{rewardPoints && rewardPoints.available_canpay_points.reward_point ? pointNumberFormatter(rewardPoints.available_canpay_points.reward_point) : 0}}</span>
                  

                </div>
                <div class="cp-point-val mb-1 d-flex align-items-center mt-2" v-if="rewardPoints.available_merchant_points > 0">
                  <svg class="ml-2" xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 45 45" fill="none">
                    <path d="M45 22.5C45 34.9264 34.9264 45 22.5 45C10.0736 45 0 34.9264 0 22.5C0 10.0736 10.0736 0 22.5 0C34.9264 0 45 10.0736 45 22.5Z" fill="black"/>
                    <path d="M37.3065 28.5767C39.194 24.5672 38.4215 20.7332 38.2029 19.7162C37.9551 18.5674 37.3283 15.8529 35.0472 13.4165C33.9607 12.2716 32.6704 11.3409 31.2428 10.6727C29.3188 9.77272 27.679 9.59712 26.7826 9.50932C23.0876 9.14348 20.0339 10.2264 18.3285 11.0019C19.1588 10.2033 20.1079 9.53928 21.1417 9.03373C22.0552 8.59538 23.0236 8.28281 24.0204 8.1045C25.6526 7.79137 27.326 7.7592 28.969 8.00939C29.7269 8.12646 32.8899 8.67521 36.0456 11.1336C40.6444 14.7115 42.532 19.6796 41.4606 25.394C39.8281 34.1375 31.4761 39.2592 23.0074 37.0056C20.5149 36.3398 17.8912 34.7667 16.7251 33.2302C16.7834 33.2302 16.849 33.2155 16.8855 33.2375C17.177 33.4277 17.4612 33.6326 17.76 33.8228C22.4972 36.8081 27.351 36.83 32.1757 34.1594C32.7866 33.8204 33.367 33.4287 33.9103 32.9887C36.0019 31.2912 36.9566 29.3157 37.3065 28.5767Z" fill="white"/>
                    <path d="M41.8619 29.1034C41.8109 29.4107 41.7234 29.8497 41.5849 30.3692C40.5792 34.0861 38.2835 36.4348 37.2996 37.4152C33.6993 41.0078 29.5087 42.0467 27.592 42.5004C23.6492 43.4296 20.4643 43.0345 19.3201 42.8516C15.2024 42.1931 12.3746 40.5249 11.5074 39.9761C9.738 38.8608 8.15213 37.476 6.80661 35.8714C5.23513 34.005 4.00402 31.8749 3.16989 29.579C2.95854 28.9937 2.15686 26.6962 1.9528 23.55C1.76331 20.5502 2.21517 18.3112 2.32449 17.7917C2.68091 16.0946 3.25668 14.4515 4.03717 12.9042C4.44529 12.114 6.52237 8.25072 11.1138 5.36793C12.1779 4.69479 14.9619 3.11438 18.9047 2.5144C19.9469 2.35344 28.933 1.10959 35.3683 6.76543C38.3491 9.38482 40.1055 12.8895 40.3824 13.4529C40.8088 14.3232 41.1788 15.2201 41.4902 16.1382C41.3882 15.9992 40.6302 14.9309 40.6083 14.9016C37.8098 10.7384 34.5593 8.77753 34.5593 8.77753C33.3302 8.05264 32.0087 7.49882 30.6311 7.13126C26.251 5.94595 22.5633 6.99956 21.6887 7.26297C16.558 8.8068 13.5189 12.9115 13.3439 13.1529C10.2465 17.4405 10.3121 21.9184 10.3704 23.1622C10.5672 27.3474 12.3892 30.3034 13.22 31.496C13.2856 31.5765 13.3804 31.7009 13.497 31.8472C13.5844 31.9643 13.6719 32.0813 13.7594 32.1984C15.9312 35.0666 18.8099 37.064 22.0094 38.0591C24.3937 38.7911 26.9131 38.9659 29.375 38.5703C31.837 38.1746 34.1762 37.2189 36.2137 35.7763C38.0065 34.5032 39.0924 33.1788 39.7629 32.3594C40.6521 31.2765 41.3007 30.2083 41.5121 29.7839C41.5412 29.7327 41.8473 29.1034 41.8619 29.1034Z" fill="#FFCB11"/>
                    <path d="M18.4306 29.1693C14.5825 29.1693 12.6585 26.6158 12.6585 22.7013C12.6585 18.6551 14.6845 16.2187 18.6492 16.2187C20.1797 16.2187 21.4114 16.5845 22.3588 17.3601C23.2479 18.1576 23.7435 19.2405 23.8674 20.616H22.3224C21.7685 20.616 21.3676 20.3599 21.1271 19.8551C20.7044 18.9551 19.8736 18.4942 18.6565 18.4942C16.2806 18.4942 15.2238 20.177 15.2238 22.7086C15.2238 25.167 16.2369 26.9011 18.5763 26.9011C20.1797 26.9011 21.098 26.0158 21.3749 24.6256H23.8601C23.6342 27.523 21.6009 29.1693 18.4306 29.1693Z" fill="white"/>
                    <path d="M31.7171 16.248H27.3515C26.6155 16.248 26.0324 16.8919 26.0324 17.6309V29.1255H28.6561V24.5379H31.9065C34.4792 24.5379 35.8566 22.9282 35.8566 20.3893C35.8566 17.7114 34.4428 16.248 31.7171 16.248ZM31.4183 22.2112H28.8602V18.4796H31.5494C32.7738 18.4796 33.4006 19.1016 33.4006 20.3527C33.4079 21.6039 32.752 22.2331 31.4183 22.2112Z" fill="#FFCB11"/>
                    </svg>
                    <span class="ml-2" style="font-weight:600!important;font-family:'Montserrat';font-size:17px;"> + </span>
                  <span class="ml-1" style="font-weight:600!important;font-family:'Montserrat';font-size:17px;">{{pointNumberFormatter(rewardPoints.available_merchant_points)}}</span>

                </div>

              </div>
              <div class="cp-point-card-bottom pb-3 pt-3 information-border-except-top"
              >
                <div class="d-flex align-items-center">
                  <svg class="cp-logo" width="40" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="35" cy="35" r="35" fill="black"/>
                  <path d="M58.0327 44.4525C60.969 38.2154 59.7673 32.2515 59.4272 30.6694C59.0417 28.8825 58.0667 24.66 54.5183 20.8699C52.8282 19.0889 50.821 17.6413 48.6004 16.6018C45.6075 15.2019 43.0567 14.9287 41.6622 14.7921C35.9144 14.223 31.1643 15.9075 28.5114 17.114C29.8031 15.8716 31.2794 14.8387 32.8875 14.0523C34.3086 13.3704 35.8149 12.8842 37.3656 12.6069C39.9046 12.1197 42.5076 12.0697 45.0633 12.4589C46.2423 12.641 51.1626 13.4946 56.0714 17.3188C63.225 22.8844 66.1613 30.6125 64.4947 39.5016C61.9553 53.1026 48.9632 61.0697 35.7897 57.5641C31.9125 56.5284 27.8312 54.0814 26.0173 51.6912C26.108 51.6912 26.2101 51.6685 26.2667 51.7026C26.7202 51.9985 27.1624 52.3172 27.6272 52.6131C34.9962 57.2568 42.5465 57.291 50.0515 53.1367C51.0019 52.6094 51.9047 52.0001 52.7497 51.3156C56.0034 48.6751 57.4885 45.6021 58.0327 44.4525Z" fill="white"/>
                  <path d="M65.1182 45.2722C65.0389 45.7502 64.9028 46.4331 64.6874 47.2412C63.1229 53.023 59.5518 56.6765 58.0213 58.2016C52.4209 63.79 45.9022 65.4062 42.9206 66.1119C36.7873 67.5573 31.8331 66.9427 30.0532 66.6582C23.6479 65.6338 19.2492 63.0388 17.9001 62.1852C15.1477 60.4502 12.6808 58.2961 10.5878 55.8001C8.14327 52.8969 6.22821 49.5834 4.93068 46.012C4.60191 45.1014 3.35485 41.5276 3.03741 36.6335C2.74266 31.9671 3.44554 28.4843 3.6156 27.6762C4.17003 25.0362 5.06567 22.4803 6.27977 20.0733C6.91463 18.8441 10.1456 12.8346 17.2879 8.35027C18.9431 7.30316 23.2738 4.84474 29.407 3.91145C31.0282 3.66105 45.0066 1.72618 55.0171 10.5242C59.6539 14.5988 62.386 20.0505 62.8168 20.9269C63.4801 22.2806 64.0557 23.6759 64.54 25.104C64.3813 24.8877 63.2023 23.226 63.1683 23.1805C58.8149 16.7044 53.7587 13.6541 53.7587 13.6541C51.8468 12.5265 49.7911 11.665 47.6481 11.0932C40.8346 9.24941 35.0981 10.8884 33.7377 11.2981C25.7565 13.6996 21.0291 20.0847 20.757 20.4603C15.9388 27.1299 16.0408 34.0954 16.1315 36.0303C16.4376 42.5406 19.2718 47.1387 20.5642 48.9939C20.6663 49.1191 20.8137 49.3126 20.995 49.5403C21.1311 49.7224 21.2671 49.9045 21.4032 50.0866C24.7816 54.5482 29.2596 57.6553 34.2365 59.2032C37.9454 60.3419 41.8646 60.6138 45.6942 59.9984C49.5239 59.3829 53.1626 57.8962 56.3321 55.6522C59.121 53.6718 60.8102 51.6117 61.8532 50.337C63.2363 48.6525 64.2453 46.9908 64.5741 46.3306C64.6194 46.251 65.0956 45.2722 65.1182 45.2722Z" fill="#007EE5"/>
                  <path d="M28.7061 45.6C22.7202 45.6 19.7273 41.6278 19.7273 35.5387C19.7273 29.2447 22.879 25.4546 29.0462 25.4546C31.427 25.4546 33.3429 26.0237 34.8167 27.2301C36.1998 28.4707 36.9707 30.1552 37.1634 32.2949H34.76C33.8984 32.2949 33.2749 31.8966 32.9008 31.1112C32.2432 29.7113 30.9508 28.9943 29.0576 28.9943C25.3617 28.9943 23.7179 31.612 23.7179 35.5501C23.7179 39.3743 25.2937 42.0717 28.9329 42.0717C31.427 42.0717 32.8554 40.6945 33.2862 38.532H37.1521C36.8007 43.0392 33.6377 45.6 28.7061 45.6Z" fill="white"/>
                  <path d="M49.3373 25.2747H42.5465C41.4015 25.2747 40.4945 26.2762 40.4945 27.4258V45.3063H44.5758V38.17H49.632C53.634 38.17 55.7766 35.6661 55.7766 31.7166C55.7766 27.551 53.5773 25.2747 49.3373 25.2747ZM48.8725 34.5507H44.8932V28.746H49.0765C50.9811 28.746 51.9561 29.7135 51.9561 31.6597C51.9674 33.606 50.9471 34.5848 48.8725 34.5507H48.8725Z" fill="#007EE5"/>
                  </svg>
                  <p class="ml-3 cp-point-details mb-0 d-flex align-items-center"><span>
                    <strong class="text-white">{{usedRewardPoints && usedRewardPoints.reward_point_used ? pointNumberFormatter(usedRewardPoints.reward_point_used) : 0}}</strong> 
                    Lifetime Paid With Points
                    <br> for Total Savings of 
                    <strong class="text-white">${{usedRewardPoints && usedRewardPoints.reward_amount_used ? amountNumberFormatter(usedRewardPoints.reward_amount_used) : 0.00}}</strong></span></p>
                </div>
              </div>
            </div>
            </a>
          </div>
      </div>
    </div>
      <div class="row">
        <div
          v-if="!disable_pay && cooldown_activated == 0"
          class="col-12 d-flex flex-column mb-3"
          style="margin: auto"
        >
          <button
            type="button"
            class="btn-black btn-verify my-0 pay-now-btn"
            @click="clickPayNow"
          >
            Pay Now
          </button>
        </div>
        <div
          v-else-if="cooldown_activated == 1"
          class="col-12 d-flex flex-column mb-3"
          style="margin: auto"
        >
          <button
            type="button"
            class="btn-black btn-verify my-0 pay-now-btn"
            @click="showCooldownAlert()"
          >
            Pay Now
          </button>
        </div>
        <div v-else class="col-12 d-flex flex-column mb-3" style="margin: auto">
          <button
            type="button"
            class="btn-black btn-verify my-0 pay-now-btn"
            @click="showAlert()"
          >
            Pay Now
          </button>
        </div>
      </div>
      <div class="row mb-3" v-if="manual_bank_link && other_banking_solution_enable">
        <div class="col-12 d-flex flex-column" style="margin: auto">
          <button
            type="button"
            class="btn-transparent-border-white direct-link-btn"
            @click="changeBankAccount"
          >
            Direct Link Your Bank
          </button>
        </div>
      </div>
      <div class="row mb-3" v-if="manual_bank_link && !other_banking_solution_enable">
        <div class="col-12 d-flex flex-column" style="margin: auto">
          <button
            type="button"
            class="btn-transparent-border-white direct-link-btn"
            @click="changeBankAccount"
          >
            Direct Link Other Bank
          </button>
        </div>
      </div>
    </div>
    
      <!-----------------------  MODAL FOR PURCHASE POWER  !---------------->
      <div>
        <b-modal
          ref="my-modal"
          hide-footer
          v-b-modal.modal-center
          modal-backdrop
          hide-header
          id="pay-modal-center"
          centered
          title="BootstrapVue"
        >
          <div class="color">
            <div class="purchaserpower-modal-text">
              <div class="col-12 text-center mt-5 mb-5">
                <svg
                  version="1.1"
                  v-if="manual_bank_link"
                  id="Layer_1"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  x="0px"
                  y="0px"
                  viewBox="0 0 64 55"
                  style="enable-background: new 0 0 64 55"
                  xml:space="preserve"
                  fill="#149240"
                  width="100"
                  height="100"
                >
                  <g>
                    <path
                      d="M63,0H1C0.4,0,0,0.4,0,1v32c0,0.6,0.4,1,1,1h62c0.6,0,1-0.4,1-1V1C64,0.4,63.6,0,63,0z M62,32H2V2h60V32z"
                    />
                    <path
                      d="M6,26c1.7,0,3,1.3,3,3c0,0.6,0.4,1,1,1h44c0.6,0,1-0.4,1-1c0-1.7,1.3-3,3-3c0.6,0,1-0.4,1-1V9c0-0.6-0.4-1-1-1
		c-1.7,0-3-1.3-3-3c0-0.6-0.4-1-1-1H10C9.4,4,9,4.4,9,5c0,1.7-1.3,3-3,3C5.4,8,5,8.4,5,9v16C5,25.6,5.4,26,6,26z M24,17
		c0-6.1,3.6-11,8-11s8,4.9,8,11s-3.6,11-8,11S24,23.1,24,17z M53.1,6C53.5,8,55,9.5,57,9.9v14.2c-2,0.4-3.5,1.9-3.9,3.9H37.3
		c3.1-2.8,4.8-6.8,4.7-11c0.1-4.2-1.6-8.2-4.7-11H53.1z M7,9.9C9,9.5,10.5,8,10.9,6h15.8c-3.1,2.8-4.8,6.8-4.7,11
		c-0.1,4.2,1.6,8.2,4.7,11H10.9C10.5,26,9,24.5,7,24.1V9.9z"
                    />
                    <path
                      d="M50,21c1.7,0,3-1.3,3-3s-1.3-3-3-3s-3,1.3-3,3S48.3,21,50,21z M50,17c0.6,0,1,0.4,1,1s-0.4,1-1,1s-1-0.4-1-1S49.4,17,50,17
		z"
                    />
                    <path
                      d="M14,21c1.7,0,3-1.3,3-3s-1.3-3-3-3s-3,1.3-3,3S12.3,21,14,21z M14,17c0.6,0,1,0.4,1,1s-0.4,1-1,1s-1-0.4-1-1S13.4,17,14,17
		z"
                    />
                    <path
                      d="M63,48H43v-4c0-0.3-0.1-0.5-0.3-0.7l-4-4c-0.4-0.4-1-0.4-1.4,0c0,0,0,0,0,0l-4,4C33.1,43.5,33,43.7,33,44v4H1
		c-0.6,0-1,0.4-1,1s0.4,1,1,1h32v4c0,0.6,0.4,1,1,1h8c0.6,0,1-0.4,1-1v-4h20c0.6,0,1-0.4,1-1S63.6,48,63,48z M41,53h-6v-8.6l3-3l3,3
		V53z"
                    />
                  </g>
                </svg>
                <svg
                  v-else
                  version="1.1"
                  id="Layer_1"
                  xmlns:cc="http://creativecommons.org/ns#"
                  xmlns:dc="http://purl.org/dc/elements/1.1/"
                  xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
                  xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
                  xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
                  xmlns:svg="http://www.w3.org/2000/svg"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  x="0px"
                  y="0px"
                  viewBox="0 0 26.5 33.1"
                  style="enable-background: new 0 0 26.5 33.1"
                  xml:space="preserve"
                  width="75"
                  height="75"
                  fill="#149240"
                >
                  <g transform="translate(0,-270.54165)">
                    <path
                      d="M17.1,272.1c-0.3,0-0.6,0.1-0.8,0.2l-5.6,3.2H3.4c-1,0-1.8,0.8-1.8,1.8v16.3c0,1,0.8,1.8,1.8,1.8h18.8c1,0,1.8-0.8,1.8-1.8
		v-4.8h0.4c0.3,0,0.5-0.2,0.5-0.5v-4.7c0-0.3-0.2-0.5-0.5-0.5h-0.4v-5.7c0-1-0.8-1.8-1.8-1.8h-2l-1.5-2.6
		C18.3,272.4,17.7,272.1,17.1,272.1L17.1,272.1z M17.2,273.2c0.2,0,0.4,0.1,0.6,0.4l2.5,4.4H8.7l8-4.6
		C16.9,273.2,17,273.2,17.2,273.2L17.2,273.2z M3.4,276.6h5.4l-2.2,1.2H4.1c-0.7,0-0.7,1,0,1h18.8v4.2h-3.3c-1.6,0-2.9,1.3-2.9,2.9
		s1.3,2.9,2.9,2.9h3.3v4.8c0,0.4-0.3,0.7-0.7,0.7H3.4c-0.4,0-0.7-0.3-0.7-0.7v-16.3C2.6,277,2.9,276.6,3.4,276.6L3.4,276.6z
		 M20.8,276.6h1.4c0.4,0,0.7,0.3,0.7,0.7v0.5h-1.4L20.8,276.6z M19.6,284.1h4.3v3.6h-4.3c-1,0-1.8-0.8-1.8-1.8
		C17.7,284.9,18.5,284.1,19.6,284.1z M19.8,284.9c-0.6,0-1.1,0.5-1.1,1c0,0.6,0.5,1,1.1,1s1.1-0.5,1.1-1
		C20.9,285.4,20.4,284.9,19.8,284.9z"
                    />
                  </g>
                </svg>
              </div>
              <div class="d-block text-center">
                <label class="purchasepower-def-label">
                  <b v-if="manual_bank_link">What is my Spending Limit?</b>
                  <b v-else>What is my Purchase Power?</b>
                </label>
              </div>
              <br />
              <h3
                v-if="manual_bank_link"
                class="purchasepower-modal-text text-justify"
              >
                Your Spending Limit is based on the default CanPay limit. If you
                would like a custom limit, please Direct Link your bank below
                and gain access to CanPay’s proprietary Purchase Power tool.
                Purchase Power calculates custom purchase limits which can
                include higher individual purchases and spending more often.
              </h3>
              <h3 v-else class="purchasepower-modal-text text-justify">
                Purchase Power is your available spending with CanPay. Your
                Purchase Power is impacted by many factors including: recent
                purchases through CanPay, your total CanPay spending history,
                and how long you've had a CanPay account. Negative items, like
                NSF payment returns, can also impact your Purchase Power.
              </h3>
              <div v-if="manual_bank_link" class="text-center mt-5">
                <div class="row">
                  <div class="col-12">
                    <button
                      type="button"
                      class="btn-modal-green p-1"
                      @click="changeBankAccount"
                    >
                      Direct Link Checking Account <br />(Recommended)
                    </button>
                  </div>
                </div>
                <div class="row mt-2 mb-3">
                  <div class="col-12">
                    <button
                      type="button"
                      class="btn-modal-black p-1"
                      @click="hideModal('my-modal')"
                    >
                      Close
                    </button>
                  </div>
                </div>
              </div>
              <div v-else class="text-center mt-5">
                <button
                  type="button"
                  class="mx-auto col-10 offset-1 btn-black"
                  style="height: 60px"
                  @click="hideModal('my-modal')"
                >
                  <label class="purchasepower-modal-ok-label">OK</label>
                </button>
              </div>
            </div>
          </div>
        </b-modal>
      </div>
      <!-----------------------  MODAL FOR PURCHASE POWER  !----------------->
      <!-----------------------  MODAL FOR UPDATED PURCHASE POWER  !---------------->
      <div>
        <b-modal
          ref="updated-purchase-power"
          hide-footer
          v-b-modal.modal-center
          modal-backdrop
          hide-header
          no-close-on-backdrop
          id="updated-purchase-power"
          centered
          title="BootstrapVue"
        >
        <div style="text-align:center;">
          <svg width="50" height="50" viewBox="0 0 216 182" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H216V182H0V0Z" fill="url(#pattern0_10067_872)"/>
          <defs>
          <pattern id="pattern0_10067_872" patternContentUnits="objectBoundingBox" width="1" height="1">
          <use xlink:href="#image0_10067_872" transform="scale(0.00462963 0.00549451)"/>
          </pattern>
          <image id="image0_10067_872" width="216" height="182" xlink:href="data:image/png;base64,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"/>
          </defs>
          </svg>

         <p style="font-family:'montserrat';font-weight:bolder;font-size:15px;margin-top:15px;">Your Purchase Power calculation has completed.</p>
         <button class="black-button-ok" @click="hideModal('updated-purchase-power')">OK</button>
        </div>
        </b-modal>
      </div>
      <!-----------------------  MODAL FOR UPDATED PURCHASE POWER  !----------------->
      <!-----------------------  MODAL TO PREVENT CONSUMER FROM PAY NOW WHILE PURCHASE POWER IS BEING CALCULATED  !---------------->
      <div>
        <b-modal
          ref="pay-now-after-pp-calc-modal"
          hide-footer
          v-b-modal.modal-center
          modal-backdrop
          hide-header
          no-close-on-backdrop
          id="pay-now-after-pp-calc-modal"
          centered
          title="BootstrapVue"
        >
        <div style="text-align:center;">
          <div><svg width="50" height="50" viewBox="0 0 216 186" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H216V186H0V0Z" fill="url(#pattern0_10067_785)"/>
            <defs>
            <pattern id="pattern0_10067_785" patternContentUnits="objectBoundingBox" width="1" height="1">
            <use xlink:href="#image0_10067_785" transform="scale(0.00462963 0.00537634)"/>
            </pattern>
            <image id="image0_10067_785" width="216" height="186" xlink:href="data:image/png;base64,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"/>
            </defs>
            </svg>

          </div>

         <p style="font-family:'montserrat';font-weight:bolder;font-size:15px;margin-top:15px;">Please wait for your Purchase Power to finish calculating.</p>
         <button class="black-button-ok" @click="hideModal('pay-now-after-pp-calc-modal')">OK</button>
        </div>
        </b-modal>
      </div>
      <!-----------------------  MODAL TO PREVENT CONSUMER FROM PAY NOW WHILE PURCHASE POWER IS BEING CALCULATED  !----------------->
      <!----- MODAL FOR Transaction Cooldown----->
      <b-modal
        ref="transaction-cooldown-modal"
        hide-footer
        v-b-modal.modal-center
        modal-backdrop
        hide-header
        id="transaction-cooldown-modal"
        centered
      >
        <div class="color">
          <div class="purchaserpower-modal-text">
            <div class="col-12 text-center mt-5 mb-2">
              <svg
              version="1.1"
              class="align-self-center"
              id="Layer_1"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              x="0px"
              y="0px"
              width="100"
              height="100"
              viewBox="0 0 100 125"
              style="enable-background: new 0 0 100 125"
              xml:space="preserve"
              fill="#e24141"
            >
              <path
                d="M96.2,47.5l-22-38c-0.4-0.6-1-1-1.7-1h-44c-0.7,0-1.4,0.4-1.7,1l-22,37.9c-0.4,0.6-0.4,1.4,0,2l22,38.1c0.4,0.6,1,1,1.7,1
                  h44c0.7,0,1.4-0.4,1.7-1l22-38C96.6,48.9,96.6,48.1,96.2,47.5z M71.3,84.5H29.7L8.8,48.4l20.8-35.9h41.7l20.8,36L71.3,84.5z
                  M50.5,60.5c1.1,0,2-0.9,2-2v-30c0-1.1-0.9-2-2-2c-1.1,0-2,0.9-2,2v30C48.5,59.6,49.4,60.5,50.5,60.5z M48.4,66.4
                  c-0.6,0.6-0.9,1.3-0.9,2.1c0,0.8,0.3,1.6,0.9,2.1s1.3,0.9,2.1,0.9c0.8,0,1.6-0.3,2.1-0.9c0.6-0.6,0.9-1.3,0.9-2.1
                  c0-0.8-0.3-1.6-0.9-2.1C51.5,65.3,49.5,65.3,48.4,66.4z"
              />
            </svg>
            </div>
            <div class="d-block text-center">
              <label class="purchasepower-def-label">
                <p><b>Multiple Payments Alert.</b></p>

                <p style="text-align:justify">Your most recent payment attempt of ${{ amount }} at {{ store_name }} was successful.</p>

                <p style="text-align:justify">In order to avoid duplicate payments, no additional Payment Codes can be generated within {{ cooldown_time }} minutes of your most recent purchase.</p>
 
                <p style="text-align:justify">If the store believes the last payment was unsuccessful for any reason, please show them your CanPay transaction history and ask them to review their own CanPay Merchant reporting to confirm the status of your payment.</p>
              </label>
            </div>
            <br />
            <br />
            <div class="text-center">
              <button
                type="button"
                class="mx-auto col-10 offset-1 btn-black"
                style="height: 60px"
                @click="$router.push('/transactiondetails')"
              >
                <label class="purchasepower-modal-ok-label">Review Transaction</label>
              </button>
            </div>
          </div>
        </div>
      </b-modal>

      <!----- MODAL FOR 0 PURCHASE POWER----->
      <b-modal
        ref="zero-pp-modal"
        hide-footer
        v-b-modal.modal-center
        modal-backdrop
        hide-header
        id="zero-pp-modal"
        centered
      >
        <div class="color">
          <div class="purchaserpower-modal-text">
            <div class="col-12 text-center mt-5 mb-5">
              <svg
                version="1.1"
                v-if="manual_bank_link"
                id="Layer_1"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                x="0px"
                y="0px"
                viewBox="0 0 64 55"
                style="enable-background: new 0 0 64 55"
                xml:space="preserve"
                fill="#149240"
                width="100"
                height="100"
              >
                <g>
                  <path
                    d="M63,0H1C0.4,0,0,0.4,0,1v32c0,0.6,0.4,1,1,1h62c0.6,0,1-0.4,1-1V1C64,0.4,63.6,0,63,0z M62,32H2V2h60V32z"
                  />
                  <path
                                      d="M6,26c1.7,0,3,1.3,3,3c0,0.6,0.4,1,1,1h44c0.6,0,1-0.4,1-1c0-1.7,1.3-3,3-3c0.6,0,1-0.4,1-1V9c0-0.6-0.4-1-1-1
                      c-1.7,0-3-1.3-3-3c0-0.6-0.4-1-1-1H10C9.4,4,9,4.4,9,5c0,1.7-1.3,3-3,3C5.4,8,5,8.4,5,9v16C5,25.6,5.4,26,6,26z M24,17
                      c0-6.1,3.6-11,8-11s8,4.9,8,11s-3.6,11-8,11S24,23.1,24,17z M53.1,6C53.5,8,55,9.5,57,9.9v14.2c-2,0.4-3.5,1.9-3.9,3.9H37.3
                      c3.1-2.8,4.8-6.8,4.7-11c0.1-4.2-1.6-8.2-4.7-11H53.1z M7,9.9C9,9.5,10.5,8,10.9,6h15.8c-3.1,2.8-4.8,6.8-4.7,11
                      c-0.1,4.2,1.6,8.2,4.7,11H10.9C10.5,26,9,24.5,7,24.1V9.9z"
                                    />
                                    <path
                                      d="M50,21c1.7,0,3-1.3,3-3s-1.3-3-3-3s-3,1.3-3,3S48.3,21,50,21z M50,17c0.6,0,1,0.4,1,1s-0.4,1-1,1s-1-0.4-1-1S49.4,17,50,17
                      z"
                                    />
                                    <path
                                      d="M14,21c1.7,0,3-1.3,3-3s-1.3-3-3-3s-3,1.3-3,3S12.3,21,14,21z M14,17c0.6,0,1,0.4,1,1s-0.4,1-1,1s-1-0.4-1-1S13.4,17,14,17
                      z"
                                    />
                                    <path
                                      d="M63,48H43v-4c0-0.3-0.1-0.5-0.3-0.7l-4-4c-0.4-0.4-1-0.4-1.4,0c0,0,0,0,0,0l-4,4C33.1,43.5,33,43.7,33,44v4H1
                      c-0.6,0-1,0.4-1,1s0.4,1,1,1h32v4c0,0.6,0.4,1,1,1h8c0.6,0,1-0.4,1-1v-4h20c0.6,0,1-0.4,1-1S63.6,48,63,48z M41,53h-6v-8.6l3-3l3,3
                      V53z"
                  />
                </g>
              </svg>
              <svg
                v-else
                version="1.1"
                id="Layer_1"
                xmlns:cc="http://creativecommons.org/ns#"
                xmlns:dc="http://purl.org/dc/elements/1.1/"
                xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
                xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
                xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
                xmlns:svg="http://www.w3.org/2000/svg"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                x="0px"
                y="0px"
                viewBox="0 0 26.5 33.1"
                style="enable-background: new 0 0 26.5 33.1"
                xml:space="preserve"
                width="100"
                height="100"
                fill="#149240"
              >
                <g transform="translate(0,-270.54165)">
                  <path
                                  d="M17.1,272.1c-0.3,0-0.6,0.1-0.8,0.2l-5.6,3.2H3.4c-1,0-1.8,0.8-1.8,1.8v16.3c0,1,0.8,1.8,1.8,1.8h18.8c1,0,1.8-0.8,1.8-1.8
                  v-4.8h0.4c0.3,0,0.5-0.2,0.5-0.5v-4.7c0-0.3-0.2-0.5-0.5-0.5h-0.4v-5.7c0-1-0.8-1.8-1.8-1.8h-2l-1.5-2.6
                  C18.3,272.4,17.7,272.1,17.1,272.1L17.1,272.1z M17.2,273.2c0.2,0,0.4,0.1,0.6,0.4l2.5,4.4H8.7l8-4.6
                  C16.9,273.2,17,273.2,17.2,273.2L17.2,273.2z M3.4,276.6h5.4l-2.2,1.2H4.1c-0.7,0-0.7,1,0,1h18.8v4.2h-3.3c-1.6,0-2.9,1.3-2.9,2.9
                  s1.3,2.9,2.9,2.9h3.3v4.8c0,0.4-0.3,0.7-0.7,0.7H3.4c-0.4,0-0.7-0.3-0.7-0.7v-16.3C2.6,277,2.9,276.6,3.4,276.6L3.4,276.6z
                  M20.8,276.6h1.4c0.4,0,0.7,0.3,0.7,0.7v0.5h-1.4L20.8,276.6z M19.6,284.1h4.3v3.6h-4.3c-1,0-1.8-0.8-1.8-1.8
                  C17.7,284.9,18.5,284.1,19.6,284.1z M19.8,284.9c-0.6,0-1.1,0.5-1.1,1c0,0.6,0.5,1,1.1,1s1.1-0.5,1.1-1
                  C20.9,285.4,20.4,284.9,19.8,284.9z"
                  />
                </g>
              </svg>
            </div>
            <div class="d-block text-center" v-if="blacklisted_account_no">
              <label class="purchasepower-def-label">
                Error:1001. There is an issue with your account. Please contact the support with the visible error code for more details.
              </label>
            </div>
            <div class="d-block text-center" v-else-if="blocked_routing_number == 1">
              <label class="purchasepower-def-label">
                Error:1003. There is a problem with your bank. Please change your bank and try again.
              </label>
            </div>
            <div class="d-block text-center" v-else-if="probable_return_found">
              <label class="purchasepower-def-label">
                Error:1007. There is an issue with your previous transaction. Please contact Support via the live chat.
              </label>
            </div>
            <div class="d-block text-center" v-else><label class="purchasepower-def-label">
                Your {{ pp_alert_message }} is zero. No purchases can be made at
                this time.
              </label>
              
            </div>
            <br />
            <br />
            <div class="text-center">
              <button
                type="button"
                class="mx-auto col-10 offset-1 btn-black"
                style="height: 60px"
                @click="hideModal('zero-pp-modal')"
              >
                <label class="purchasepower-modal-ok-label">OK</label>
              </button>
            </div>
          </div>
        </div>
      </b-modal>
      <!----- MODAL FOR TRANSACTION CONFIRMATION----->

      <b-modal
        ref="consumer-suspended-modal"
        hide-footer
        v-b-modal.modal-center
        modal-backdrop
        hide-header
        id="consumer-suspended-modal"
        centered
      >
        <div class="color">
          <div class="col-12 text-center">
            <svg
              version="1.1"
              id="Layer_1"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              x="0px"
              y="0px"
              width="120"
              height="120"
              viewBox="0 0 100 125"
              style="enable-background: new 0 0 100 125"
              xml:space="preserve"
              fill="#e14343"
            >
              <path
                d="M96.2,47.5l-22-38c-0.4-0.6-1-1-1.7-1h-44c-0.7,0-1.4,0.4-1.7,1l-22,37.9c-0.4,0.6-0.4,1.4,0,2l22,38.1c0.4,0.6,1,1,1.7,1
	h44c0.7,0,1.4-0.4,1.7-1l22-38C96.6,48.9,96.6,48.1,96.2,47.5z M71.3,84.5H29.7L8.8,48.4l20.8-35.9h41.7l20.8,36L71.3,84.5z
	 M50.5,60.5c1.1,0,2-0.9,2-2v-30c0-1.1-0.9-2-2-2c-1.1,0-2,0.9-2,2v30C48.5,59.6,49.4,60.5,50.5,60.5z M48.4,66.4
	c-0.6,0.6-0.9,1.3-0.9,2.1c0,0.8,0.3,1.6,0.9,2.1s1.3,0.9,2.1,0.9c0.8,0,1.6-0.3,2.1-0.9c0.6-0.6,0.9-1.3,0.9-2.1
	c0-0.8-0.3-1.6-0.9-2.1C51.5,65.3,49.5,65.3,48.4,66.4z"
              />
            </svg>
          </div>
          <div class="purchaserpower-modal-text">
            <div class="d-block text-center">
              <label class="purchasepower-def-label">
                {{ consumer_suspended_msg }}
              </label>
            </div>
          </div>
        </div>
      </b-modal>
      <b-modal
        ref="overlay-modal"
        hide-footer
        modal-backdrop
        hide-header
        no-close-on-backdrop
        id="overlay-modal"
        centered
      >
        <div class="row text-center">
          <div class="col-12">
            <span class="success-text">Welcome to the New CanPay!</span>
          </div>
        </div>
        <br />
        <div class="row text-center">
          <div class="col-12">
            <span style="color: white; font-weight: 400"
              >Click to confirm your primary bank account.</span
            >
          </div>
        </div>
        <div class="row text-center mt-5">
          <div class="col-12">
            <button type="button" @click="gotoBankList()" class="btn-trans">
              OK
            </button>
          </div>
        </div>
      </b-modal>
      <b-modal
        ref="update-bank-modal"
        hide-footer
        no-close-on-backdrop
        modal-backdrop
        hide-header
        id="update-bank-modal"
        centered
      >
        <div class="color">
          <div class="col-12 text-center">
            <svg
              version="1.1"
              id="Layer_1"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              x="0px"
              y="0px"
              width="120"
              height="120"
              viewBox="0 0 100 125"
              style="enable-background: new 0 0 100 125"
              xml:space="preserve"
              fill="#e14343"
            >
              <path
                d="M96.2,47.5l-22-38c-0.4-0.6-1-1-1.7-1h-44c-0.7,0-1.4,0.4-1.7,1l-22,37.9c-0.4,0.6-0.4,1.4,0,2l22,38.1c0.4,0.6,1,1,1.7,1
	h44c0.7,0,1.4-0.4,1.7-1l22-38C96.6,48.9,96.6,48.1,96.2,47.5z M71.3,84.5H29.7L8.8,48.4l20.8-35.9h41.7l20.8,36L71.3,84.5z
	 M50.5,60.5c1.1,0,2-0.9,2-2v-30c0-1.1-0.9-2-2-2c-1.1,0-2,0.9-2,2v30C48.5,59.6,49.4,60.5,50.5,60.5z M48.4,66.4
	c-0.6,0.6-0.9,1.3-0.9,2.1c0,0.8,0.3,1.6,0.9,2.1s1.3,0.9,2.1,0.9c0.8,0,1.6-0.3,2.1-0.9c0.6-0.6,0.9-1.3,0.9-2.1
	c0-0.8-0.3-1.6-0.9-2.1C51.5,65.3,49.5,65.3,48.4,66.4z"
              />
            </svg>
          </div>
          <div class="purchaserpower-modal-text">
            <div class="d-block text-center">
              <label class="purchasepower-def-label">
                There was some problem trying to connect to your Financial
                Institution. Please update your banking details.
              </label>
            </div>
            <br />
            <br />
            <div class="row">
              <div class="col-12 text-center">
                <button
                  type="button"
                  class="mx-auto col-10 offset-1 btn-black"
                  style="height: 60px"
                  @click="changeBankAccount()"
                >
                  <span class="purchasepower-modal-ok-label"
                    >Update Banking</span
                  >
                </button>
              </div>
            </div>
          </div>
        </div>
      </b-modal>

        <b-modal
          ref="blocked-routing-number-modal"
          hide-footer
          v-b-modal.modal-center
          modal-backdrop
          hide-header
          no-close-on-backdrop
          id="blocked-routing-number-modal"
          centered
          title="BootstrapVue"
        >
          <div class="color">
            <div class="purchaserpower-modal-text">
              <div class="pin-success-top-spacing"></div>
              <div class="row" style="margin-bottom: 5px">
                <div class="col-12 text-center">
                  <svg
                    version="1.1"
                    id="Layer_1"
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    x="0px"
                    y="0px"
                    width="120"
                    height="120"
                    viewBox="0 0 100 125"
                    style="enable-background: new 0 0 100 125"
                    xml:space="preserve"
                    fill="#e14343"
                  >
                    <path
                      d="M96.2,47.5l-22-38c-0.4-0.6-1-1-1.7-1h-44c-0.7,0-1.4,0.4-1.7,1l-22,37.9c-0.4,0.6-0.4,1.4,0,2l22,38.1c0.4,0.6,1,1,1.7,1
	h44c0.7,0,1.4-0.4,1.7-1l22-38C96.6,48.9,96.6,48.1,96.2,47.5z M71.3,84.5H29.7L8.8,48.4l20.8-35.9h41.7l20.8,36L71.3,84.5z
	 M50.5,60.5c1.1,0,2-0.9,2-2v-30c0-1.1-0.9-2-2-2c-1.1,0-2,0.9-2,2v30C48.5,59.6,49.4,60.5,50.5,60.5z M48.4,66.4
	c-0.6,0.6-0.9,1.3-0.9,2.1c0,0.8,0.3,1.6,0.9,2.1s1.3,0.9,2.1,0.9c0.8,0,1.6-0.3,2.1-0.9c0.6-0.6,0.9-1.3,0.9-2.1
	c0-0.8-0.3-1.6-0.9-2.1C51.5,65.3,49.5,65.3,48.4,66.4z"
                    />
                  </svg>
                </div>
              </div>
              <div class="d-block text-center">
                <label class="success-popup-style onboarding-modal-message">
                  Error:1003. There is a problem with your bank. Please change your bank and try again.
                </label>
              </div>
              <div class="success-bottom-spacing"></div>
            </div>

            <div class="row mt-3">
              <div class="col-12">
                <button
                  type="button"
                  class="btn-login btn-get-started"
                  @click="changeBankAccount"
                >
                  Update Bank
                </button>
              </div>
            </div>
          </div>
        </b-modal>
        <real-account-number-modal page_name="pay" ref="RealAccountNumberModal"></real-account-number-modal>
      <!-- Accept Or Reject -->
      <b-modal
        ref="transaction-modification-show-modal"
        hide-footer
        modal-backdrop
        hide-header
        no-close-on-backdrop
        id="transaction-modification-show-modal"
        centered
      >
        
        
        <div class="row">
          <div class="col-12 text-center mt-5 mb-5"><svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 64 55" xml:space="preserve" fill="#149240" width="100" height="100"><g><path d="M63,0H1C0.4,0,0,0.4,0,1v32c0,0.6,0.4,1,1,1h62c0.6,0,1-0.4,1-1V1C64,0.4,63.6,0,63,0z M62,32H2V2h60V32z"></path><path d="M6,26c1.7,0,3,1.3,3,3c0,0.6,0.4,1,1,1h44c0.6,0,1-0.4,1-1c0-1.7,1.3-3,3-3c0.6,0,1-0.4,1-1V9c0-0.6-0.4-1-1-1
		c-1.7,0-3-1.3-3-3c0-0.6-0.4-1-1-1H10C9.4,4,9,4.4,9,5c0,1.7-1.3,3-3,3C5.4,8,5,8.4,5,9v16C5,25.6,5.4,26,6,26z M24,17
		c0-6.1,3.6-11,8-11s8,4.9,8,11s-3.6,11-8,11S24,23.1,24,17z M53.1,6C53.5,8,55,9.5,57,9.9v14.2c-2,0.4-3.5,1.9-3.9,3.9H37.3
		c3.1-2.8,4.8-6.8,4.7-11c0.1-4.2-1.6-8.2-4.7-11H53.1z M7,9.9C9,9.5,10.5,8,10.9,6h15.8c-3.1,2.8-4.8,6.8-4.7,11
		c-0.1,4.2,1.6,8.2,4.7,11H10.9C10.5,26,9,24.5,7,24.1V9.9z"></path><path d="M50,21c1.7,0,3-1.3,3-3s-1.3-3-3-3s-3,1.3-3,3S48.3,21,50,21z M50,17c0.6,0,1,0.4,1,1s-0.4,1-1,1s-1-0.4-1-1S49.4,17,50,17
		z"></path><path d="M14,21c1.7,0,3-1.3,3-3s-1.3-3-3-3s-3,1.3-3,3S12.3,21,14,21z M14,17c0.6,0,1,0.4,1,1s-0.4,1-1,1s-1-0.4-1-1S13.4,17,14,17
		z"></path><path d="M63,48H43v-4c0-0.3-0.1-0.5-0.3-0.7l-4-4c-0.4-0.4-1-0.4-1.4,0c0,0,0,0,0,0l-4,4C33.1,43.5,33,43.7,33,44v4H1
		c-0.6,0-1,0.4-1,1s0.4,1,1,1h32v4c0,0.6,0.4,1,1,1h8c0.6,0,1-0.4,1-1v-4h20c0.6,0,1-0.4,1-1S63.6,48,63,48z M41,53h-6v-8.6l3-3l3,3
		V53z"></path></g></svg></div>
          <div class="col-sm-12 text-center">
            <p>Modification Reason :  {{reasonVIew(reason)}} <a v-if="reasonVIewMoreButton(reason)" @click="showingFullTex(reason)" class="btn-link">More</a></p>
            <p>Transaction amount ${{previousAmount}} </p>
            <p>Updated transaction amount ${{modificationAmount}} </p>
          </div>
          <div class="col-sm-12 text-center">
               
              <button
                  type="button"
                  class="btn-login btn-get-started col-4"
                @click="hideModal('transaction-modification-show-modal')"
                >
                 OK
                </button>
          </div>
        </div>
      </b-modal>
      <!-----------------------  Simple MODAL  !------------------------>
      <b-modal
        ref="show-more-modal"
        hide-footer
        v-b-modal.modal-center
        no-close-on-backdrop
        modal-backdrop
        hide-header
        id="show-more-modal"
        centered
        title="BootstrapVue"
      >
        <div class="color">
          
          <div class="d-block text-center">
            <label class="purchasepower-def-label">Transaction Modification Reason</label>
          </div>
          <label
            class="text-center"
            style="margin: 10px;word-break: break-all;"
            >{{ transactionMoReasonMessage }}</label
          >
          <br />
          <br />
          <div class="text-center">
            <button
              type="button"
              @click="hideModal('show-more-modal')"
              class="mx-auto col-10 offset-1 btn-black"
              style="height: 60px"
            >
              <label class="purchasepower-modal-ok-label">Close</label>
            </button>
          </div>
        </div>
      </b-modal>
      
      <!-----------------------  after-manual-identity-review-modal  !------------------------>
      <b-modal
        ref="after-manual-identity-review-modal"
        hide-footer
        v-b-modal.modal-center
        no-close-on-backdrop
        modal-backdrop
        hide-header
        id="show-more-modal"
        centered
        title="BootstrapVue"
      >
        <div class="color">
          
          <div class="d-block text-center">
            <label class="purchasepower-def-label">{{ currentUser.id_validation_status == '601' 
                        ? 'Verification Approved' 
                        : currentUser.id_validation_status == '600' 
                        ? 'Verification Failed' 
                        : currentUser.id_validation_status == '723' 
                        ? 'Verification Failed' 
                        : 'Validate your identity' }}</label>
          </div>
          <label class="text-center" style="margin: 10px;">
            {{ currentUser.id_validation_status == '601' 
                        ? 'Your identity verification has been successfully approved by the CanPay Admin.' 
                        : currentUser.id_validation_status == '600' 
                        ? 'Your identity verification has failed. You may attempt it again after 90 days.' 
                        : currentUser.id_validation_status == '723' 
                        ? 'Identity verification failed because the information provided was incorrect or incomplete.' 
                        : 'Validate your identity' }}
          </label>
          <br />
          <br />
          <div class="text-center">
            <button
              type="button"
              @click="hideModal('after-manual-identity-review-modal')"
              class="mx-auto col-10 offset-1 btn-black"
              style="height: 60px"
            >
              <label class="purchasepower-modal-ok-label">Close</label>
            </button>
          </div>
        </div>
      </b-modal>

      <upload-bank-documents-modal page_name="pay" ref="UploadBankDocumentsModal"></upload-bank-documents-modal>
      <incomplete-account-number-modal page_name="pay" ref="IncompleteAccountNumberModal"></incomplete-account-number-modal>
    <!------------- Purchase Power UI end ------------------>
      <!-- //////////// INVITAION MODAL //////////// -->
      <b-modal
      ref="invitation-modal"
      hide-footer
      v-b-modal.modal-center
      no-close-on-backdrop
      modal-backdrop
      hide-header
      id="invitation-modal"
      centered
      title="BootstrapVue"
      >
        <InvitationComponent :modal="$refs['invitation-modal']">
          <template v-slot:closeModal="">
            <a class="close-modal" @click="hideModal('invitation-modal')" href="javascript:void(0)">
              <svg fill="#000000" width="20px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M4.293,18.293,10.586,12,4.293,5.707A1,1,0,0,1,5.707,4.293L12,10.586l6.293-6.293a1,1,0,1,1,1.414,1.414L13.414,12l6.293,6.293a1,1,0,1,1-1.414,1.414L12,13.414,5.707,19.707a1,1,0,0,1-1.414-1.414Z"/></svg>
            </a>
          </template>
        </InvitationComponent>
      </b-modal>


      <!------------------------------------------------------------------------>
      <!-----------------------  MODAL FOR REWARD PROGRAM INFO  !------------------------>
      <!--------------------------------------------------------------------------->
      <b-modal
      ref="cp-program-info-modal"
      hide-footer
      v-b-modal.modal-center
      no-close-on-backdrop
      modal-backdrop
      hide-header
      id="cp-program-info-modal"
      centered
      title="BootstrapVue"
      >
        <div>
          <h2 class="cp-program-info-title">More Information on the CanPay Rewards Points Program</h2>
          <ul class="cp-program-info-list">
            <li>Each day offers a new Free Daily Spin Wheel</li>
            <li>Earn an additional spin on the Spin-to-Win Rewards Wheel with each purchase</li>
            <li>Points won from the Free Daily Spin Wheel are instantly available for use towards future purchases made with CanPay</li>
            <li>Points earned on the Spin-to-Win Rewards Wheel after a purchase will be accessible once the transaction is fully processed</li>
            <li>When you have points to redeem, the Payment Code screen displays a slider to select the desired number of points for a discount</li>
            <li>Points can be used to reduce the amount debited from your bank account for a purchase</li>
            <li>Redeeming 100 points provides a $0.01 discount</li>
            <li>Example: For a $100 purchase, using 25,000 points offers a $2.50 discount. Your bank account will be debited for $97.50 ($100 - $2.50 points discount), and your points balance will decrease by 25,000 points.</li>
          </ul>
          <div class="d-flex justify-content-center">
            <button @click="hideModal('cp-program-info-modal')" type="button" class="cp-program-info-btn">OK</button>
          </div>
        </div>
      </b-modal>

      <!-----------------------------bank-info-modal---------------------------------------------->
      <b-modal
      ref="bank-info-modal"
      hide-footer
      v-b-modal.modal-center
      no-close-on-backdrop
      modal-backdrop
      hide-header
      id="bank-info-modal"
      centered
      title="BootstrapVue"
      >
        <div>
          <div class="col-12"><label class="label-permission"> With your permission, <strong>CanPay</strong> uses trusted <strong>Bank Linking Providers</strong> to securely access, process and validate your selected bank account(s) for CanPay purchases. </label></div>
          <div class="d-flex justify-content-center">
            <button @click="hideModal('bank-info-modal')" type="button" class="cp-program-info-btn">OK</button>
          </div>
        </div>
      </b-modal>

      <!-- Validation modal -->
      <b-modal
        ref="validation-modal"
        hide-footer
        v-b-modal.modal-center
        modal-backdrop
        hide-header
        id="validation-modal"
        centered
      >
        <div class="color">
          <div class="purchaserpower-modal-text">
            <div class="d-block text-center">
              <label class="purchasepower-def-label">
                {{ error_message }}
              </label>
            </div>
            <br />
            <br />
            <div class="text-center">
              <button
                type="button"
                class="mx-auto col-10 offset-1 btn-black"
                style="height: 60px"
                @click="hidevalidationModal"
              >
                <label class="purchasepower-modal-ok-label">OK</label>
              </button>
            </div>
          </div>
        </div>
      </b-modal>

      <!-- Mx Relink Modal -->
      <b-modal
        ref="mx-relink-modal"
        hide-footer
        v-b-modal.modal-center
        modal-backdrop
        hide-header
        id="mx-relink-modal"
        centered
      >
      <div class="color">
          <div class="col-12 text-center">
            <svg
              version="1.1"
              id="Layer_1"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              x="0px"
              y="0px"
              width="120"
              height="120"
              viewBox="0 0 100 125"
              style="enable-background: new 0 0 100 125"
              xml:space="preserve"
              fill="#e14343"
            >
              <path
                d="M96.2,47.5l-22-38c-0.4-0.6-1-1-1.7-1h-44c-0.7,0-1.4,0.4-1.7,1l-22,37.9c-0.4,0.6-0.4,1.4,0,2l22,38.1c0.4,0.6,1,1,1.7,1
	h44c0.7,0,1.4-0.4,1.7-1l22-38C96.6,48.9,96.6,48.1,96.2,47.5z M71.3,84.5H29.7L8.8,48.4l20.8-35.9h41.7l20.8,36L71.3,84.5z
	 M50.5,60.5c1.1,0,2-0.9,2-2v-30c0-1.1-0.9-2-2-2c-1.1,0-2,0.9-2,2v30C48.5,59.6,49.4,60.5,50.5,60.5z M48.4,66.4
	c-0.6,0.6-0.9,1.3-0.9,2.1c0,0.8,0.3,1.6,0.9,2.1s1.3,0.9,2.1,0.9c0.8,0,1.6-0.3,2.1-0.9c0.6-0.6,0.9-1.3,0.9-2.1
	c0-0.8-0.3-1.6-0.9-2.1C51.5,65.3,49.5,65.3,48.4,66.4z"
              />
            </svg>
          </div>
          <div class="purchaserpower-modal-text">
            <div class="d-block text-center">
              <label class="purchasepower-def-label">
                We are facing problem with your bank. Please reconnect to continue using CanPay
              </label>
            </div>
            <br />
            <br />
            <div class="row">
              <div class="col-12 text-center">
                <button
                  type="button"
                  class="mx-auto col-10 offset-1 btn-black"
                  style="height: 60px"
                  @click="openMxLink"
                >
                  <span class="purchasepower-modal-ok-label"
                    >Reconnect</span
                  >
                </button>
              </div>
            </div>
          </div>
        </div>
      </b-modal>
      <!-- issue calculating purchase power -->
    <div>
    <b-modal
      ref="delay-in-refresh-balance"
      hide-footer
      no-close-on-backdrop
      modal-backdrop
      hide-header
      id="delay-in-refresh-balance"
      centered
    >
    <div class="text-center" style="display: flex; justify-content: center">
    <div>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="48" height="48" viewBox="0 0 254 222" fill="none">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H254V222H0V0Z" fill="url(#pattern0_10044_8)"/>
  <defs>
    <pattern id="pattern0_10044_8" patternContentUnits="objectBoundingBox" width="1" height="1">
      <use xlink:href="#image0_10044_8" transform="scale(0.00393701 0.0045045)"/>
    </pattern>
    <image id="image0_10044_8" width="254" height="222" xlink:href="data:image/png;base64,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"/>
  </defs>
</svg>
    </div>
    </div>
    <div style="font-weight:bolder;font-family:'montserrat';margin-top:30px;text-align:center;">
      We’re currently experiencing issues retrieving your balance. We appreciate your patience.
    </div>
    <button class="black-button-ok mt-4" @click="hideModal('delay-in-refresh-balance')">OK</button>
    </b-modal>
    </div>
  </div>
    <!--- Problematic Account Modal Start --->
    <problematic-account-modal ref="ProblematicAccountModal" :handleCanPayLoader="handleCanPayLoader" :connectMXBankAccount="connectMXBankAccount" :account_details="problematic_account_details"></problematic-account-modal>
    <!--- Problematic Account Modal End --->
  <!-----------------------  Non Actional Action Status Start  !----------------->
    <non-actional-action-status-modal ref="NonActionalActionStatusModal"/>
  <!-----------------------  Non Actional Action Status End  !----------------->
  <!-----------------------  Non Actional Action Resolve Status Start  !----------------->
    <non-actional-resolve-modal ref="NonActionalResolveModal"/>
  <!-----------------------  Non Actional Action Resolve Status End  !----------------->
  <no-primary-account-modal ref="NoPrimaryAccountModal"></no-primary-account-modal>
  </div>
</template> 
<script>
import { db } from "../../firebaseConfig.js";
import api from "../../api/payment.js";
import registrationApi  from "../../api/registration.js";
import accountAPI from "../../api/account.js";
import transactionapi from "../../api/transactiondetails.js";
import rewardwheelapi from "../../api/rewardwheel.js";
import Loading from "vue-loading-overlay";
import constants from "../Common/constant.js";
import RealAccountNumberModal from './RealAccountNumberModal.vue';
import NoPrimaryAccountModal from './NoPrimaryAccountModal.vue';
import InvitationComponent from '../InvitationComponent.vue';
import UploadBankDocumentsModal from './UploadBankDocumentsModal.vue';
import IncompleteAccountNumberModal from './IncompleteAccountNumberModal.vue';
import MxConnectWidget from '../MxConnectWidget.vue';
import CanPayLoader from '../CustomLoader/CanPayLoader.vue'
import ProblematicAccountModal from "../Modal/ProblematicAccountModal.vue"
import $store from '../../store';
import NonActionalActionStatusModal from "../Modal/NonActionalActionStatusModal.vue";
import NonActionalResolveModal from "../Modal/NonActionalResolveModal.vue";
import moment from 'moment';
export default {
  name: "Pay",
  components: {
    Loading,
    RealAccountNumberModal,
    NoPrimaryAccountModal,
    InvitationComponent,
    UploadBankDocumentsModal,
    IncompleteAccountNumberModal,
    MxConnectWidget,
    CanPayLoader,
    ProblematicAccountModal,
    NonActionalActionStatusModal,
    NonActionalResolveModal
  },
  methods: {
    handleCanPayLoader(value){
      this.isLoading = value;
    },
    showValidationModal(msg) {
      this.error_message = msg;
      this.$refs["validation-modal"].show();
    },
    hidevalidationModal() {
      this.error_message = "";
      this.$refs["validation-modal"].hide();
    },
    redirectToPointsPage(){
      this.$router.push('/rewardpoints')
    },
    isNumber: function (evt) {
      const regex = /[0-9]/g;
      evt = evt ? evt : window.event;
      var inputKey = String(evt.key);
      if (!inputKey.match(regex)) {
        evt.preventDefault();
      } else {
        return true;
      }
    },
    showModal(modal) {
      if(modal=="my-modal"){
        if(this.show_purchase_power == false){
          this.$refs['pay-now-after-pp-calc-modal'].show();
          return;
        }
      }
      this.$refs[modal].show();
    },
    hideModal(modal) {
      this.$refs[modal].hide();
      if (modal == 'after-manual-identity-review-modal' && this.currentUser.id_validation_status == '601') {
        this.updateConsumerLiteToStandard();
      }
    },
    reasonVIew(reason) {
      if(reason.length > 24){
        return `${reason.slice(0, 24).trim()}...`;
      }else{
        return reason;
      }
    },
    reasonVIewMoreButton(reason) {
      return reason.length > 24 ? true : false;
    },
    showingFullTex(reason) {
      let self = this;
      self.transactionMoReasonMessage = reason;
      self.showModal('show-more-modal');
    },
    toggleModal() {
      // We pass the ID of the button that we want to return focus to
      // when the modal has hidden
      this.$refs["my-modal"].toggle("#toggle-btn");
    },
    showAlert() {
      var self = this;
      self.pp_alert_message = "Purchase Power";
      if (self.manual_bank_link) {
        self.pp_alert_message = "Spending Limit";
      }
      self.showModal("zero-pp-modal");
    },
    showCooldownAlert() {
      this.showModal("transaction-cooldown-modal");
    },
    payNow() {
      var self = this;
      self.$router.push("/dashboard");
    },
    clickPayNow() {
      var self = this;
      if(self.show_purchase_power == false){
        self.showModal("pay-now-after-pp-calc-modal");
        return;
      }
      api
        .getLastTransaction()
        .then(function (response) {
          if (response.code == 200) {
            if (response.data == "suspended") {
              self.consumer_suspended_msg = response.message;
              self.showModal("consumer-suspended-modal");
            } else {
              self.$router.push("/dashboard");
            }
          }else if(response.code == 598){
            setTimeout(() => {
              self.$refs.RealAccountNumberModal.checkForRealAccountNumber();
            }, 2000);
          }
        })
        .catch(function (err) {
          if (err.response.data.message == 401) {
            this.$router.push("/login");
          } else if(err.response.data.code!= 598) {
            self.nopurchasepower = true;
            self.purchasepower = err.response.data.message;
          }
        });
    },
    getPurchasePower: function () {
      var self = this;
      api
        .getPurchasePower()
        .then(function (response) {
          self.disable_pay = false;
          self.show_purchase_power = true;
          var reponse_data = response.data;
          if (
            Math.floor(response.data) <= 0 &&
            (
              (self.rewardPoints && 
                self.rewardPoints.available_canpay_points.reward_point <= 0 &&
                self.rewardPoints.available_canpay_points.available_merchant_points <= 0
              ) || 
              (self.consumer_type === self.lite_consumer)
            )
          ) {
            self.disable_pay = true;
            reponse_data = '0';
          }
          self.isLoading = false;
          self.nopurchasepower = "false";
          self.purchasepower = "$" + reponse_data;
          localStorage.setItem("nopurchasepower", false);
          localStorage.setItem("purchasepower", "$" + reponse_data);
          // check if mulitple bank accounts are connected and user is logging in for the first time
          //then show the overlay to change their primary bank account
          if (
            self.currentUser.account_count > 1 &&
            localStorage.getItem("overlay_show") != null
          ) {
            setTimeout(() => {
              self.showModal("overlay-modal");
            }, 500);

          }
          if (self.currentUser.banking_solution_name == "finicity" && self.currentUser.bank_link_type == 1) {
            self.$refs.NoPrimaryAccountModal.showFinicitySpecialMessageModal();
          }
        })
        .catch(function (err) {
          self.show_purchase_power = true;
          console.log(err);
          self.isLoading = false;
          if (err.response.data.code === 598) {
            self.purchasepower = err.response.data.data;
            self.disable_pay = true;
            if(err.response.data.data == constants.blockedRoutingNumber){
              self.blocked_routing_number = 1;
              setTimeout(() => {
                self.$refs['blocked-routing-number-modal'].show();
              }, 1200);
            }
          }else if (err.response.data.code === 597) {
            self.probable_return_found = true;
            self.disable_pay = true;
            self.purchasepower = err.response.data.data;
          } else {
            self.purchasepower = 0.0;
          }
          if (err.response.data.code === 596) {
            self.last_four_account_no = 'XXXX';
            self.$refs.NoPrimaryAccountModal.showModal();
          }
          if(err.response.data.data == self.blacklistedAccountNumber){
            self.blacklisted_account_no = true;
          }
          self.nopurchasepower = "true";
          localStorage.setItem("nopurchasepower", false);
          localStorage.setItem("purchasepower", self.purchasepower);

          if(self.currentUser.account_no == 'XXXX' && self.purchasepowerNoPrimaryAccountModelCalled == false){
            self.getdata();
          } else if (self.currentUser.banking_solution_name == "finicity" && self.currentUser.bank_link_type == 1) {
            self.$refs.NoPrimaryAccountModal.showFinicitySpecialMessageModal();
          }
        });
    },
    // direct link for the first time for manually liked consumer
    //TODO: Need to remove this code
    connectBankAccount() {
      let self = this;
      this.isLoading = true;
      accountAPI
        .directLinkGenerate()
        .then((response) => {
          if (response.code == 200) {
            const finicityConnectUrl = response.data.link;
            window.finicityConnect.launch(finicityConnectUrl, {
              selector: "#connect-container",
              overlay: "rgba(255,255,255, 0)",
              success: function (data) {
                console.log("Yay! We got data", data);
                if (data.code == 200) {
                  //now store the details at canpay end
                  self.storeBankDetails();
                  setTimeout(() => {
                    self.$refs.RealAccountNumberModal.checkForRealAccountNumber();
                  }, 2000);
                }
              },
              cancel: function () {
                self.isLoading = false;
                console.log("The user cancelled the iframe");
              },
              error: function (err) {
                console.log(err);
                self.isLoading = false;
                console.error(
                  "Some runtime error was generated during Finicity Connect",
                  err
                );
              },
              loaded: function () {
                self.isLoading = false;
                console.log(
                  "This gets called only once after the iframe has finished loading"
                );
              },
              route: function (event) {
                self.isLoading = false;
                console.log(
                  "This is called as the user progresses through Connect"
                );
              },
            });
          } else {
            self.isLoading = false;
          }
        })
        .catch((err) => {
          self.isLoading = false;
          console.log(err);
        });
    },
    getReturnTransactions() {
      var self = this;
      self.isLoading = true;
      transactionapi
        .getReturnTransactions()
        .then((response) => {
          if (response.code == 200 && response.data.length != 0) {
            self.isLoading = false;
            self.getdata();
            self.$router.push("/returns");
          } else {
           if (localStorage.getItem("show_upload_documents_modal")) {
              setTimeout(() => {
                self.$refs.UploadBankDocumentsModal.showModal();
              }, 2000);
            } else {
              self.getModifyData();
            }
          }
        })
        .catch(function (error) {
          self.isLoading = false;
          if (error.response.data.code === 598) {
            self.getdata();
            self.$router.push("/returns");
          } else {
            alert(error.response.data.message);
          }
        });
    },
    getModifyData() {
      var self = this;
      transactionapi
        .getMofifiedTransaction()
        .then(function (response) {
          if (response.code == 200) {
            if(response.data.length == 0){
              self.setdata('transaction_modification');
              const payload = {
                last_successful_login_id: localStorage.getItem("last_successful_login_id"),
                user_id:self.currentUser.user_id
              }
              self.isLoading = false;
              if(self.showProblematicModal == true){
                this.$refs.ProblematicAccountModal.showModal();
                self.showProblematicModal = false;
              }
              if(self.showMxNonUserActionalStatusDeteced == true){
                self.showMxNonUserActionalStatusDeteced = false;
                self.$refs.NonActionalActionStatusModal.showModal();
              }
              if(self.showMxNonActionableStatusResolved == true){
                self.showMxNonActionableStatusResolved = false;
                self.$refs.NonActionalResolveModal.showModal();
              }
              setTimeout(() => {
               localStorage.setItem('show_pp_animation',0);
              },1000);
              if(self.refresh_balance_call_required == true){
                localStorage.setItem("refresh_balance_call_required",0);
              registrationApi
              .refreshBalance(payload)
              .then((res)=>{
                self.getPurchasePower();
                setTimeout(() => {
                  self.$refs.RealAccountNumberModal.checkForRealAccountNumber();
                }, 2000);
                self.last_four_account_no = self.no_primary_account == 1 ? 'XXXX': self.last_four_account_no;
              })
              .catch((err)=>{
              if(err.response.data.data != null && err.response.data.data.active_account_delinked && err.response.data.data.active_account_delinked == 1){
                this.$refs.NoPrimaryAccountModal.showModal();
              }
              else if(err.response.data.code == 599){
                  self.showModal("delay-in-refresh-balance");
                }
                self.getPurchasePower();
                setTimeout(() => {
                  self.$refs.RealAccountNumberModal.checkForRealAccountNumber();
                }, 2000);
                self.last_four_account_no = self.no_primary_account == 1 ? 'XXXX': self.last_four_account_no;
              })
              
              }else{
                setTimeout(()=>{
                self.getPurchasePower();
                setTimeout(() => {
                  self.$refs.RealAccountNumberModal.checkForRealAccountNumber();
                }, 2000);
                self.last_four_account_no = self.no_primary_account == 1 ? 'XXXX': self.last_four_account_no;
                },500);

              }

            }else{
              localStorage.setItem(
              "updated_transaction_data",
              JSON.stringify(response.data)
            );
              self.setdata('transaction_successful');
              self.$router.push("/updatedtransaction");
            }
          }
        })
        .catch((err)=>{
          self.isLoading = false;
          if(self.showProblematicModal == true){
            self.$refs.ProblematicAccountModal.showModal();
            self.showProblematicModal = false;
          }
        })
    },
    checkBankAccountState() {
      var self = this;
      self.isLoading = true;
      localStorage.removeItem("check_bank");
      accountAPI
        .checkBankAccountState()
        .then((response) => {
          if (response.data != null) {
            alert(
              "There was some problem trying to connect to your Financial Institution. Please update your banking details."
            );
            self.update_bank = true;
            self.generateConnectFix(
              response.data.institutionId,
              response.data.institutionLoginId
            );
          }
        })
        .catch(function (error) {
          self.isLoading = false;
          if (error.response.data.code === 599) {
            self.showModal("update-bank-modal");
          }
        });
    },
    // direct link for the first time for manually liked consumer
    storeBankDetails() {
      var self = this;
      self.isLoading = true;
      accountAPI
        .directLinkBank()
        .then((response) => {
          if (response.code == 200) {
            alert(response.message);
            self.manual_bank_link = false;
            localStorage.setItem(
              "consumer_login_response",
              JSON.stringify(response.data)
            );
            const consumer_login_response = JSON.parse(localStorage.getItem("consumer_login_response"));
            consumer_login_response.account_no = response.data.account_no;
            localStorage.setItem("consumer_login_response", JSON.stringify(consumer_login_response));
            self.getPurchasePower();
            setTimeout(() => {
              self.$refs.RealAccountNumberModal.checkForRealAccountNumber();
            }, 2000);
            self.isLoading = false;
          } else {
            alert(response.message);
            self.isLoading = false;
          }
        })
        .catch(function (error) {
          if(error.response.data.code == 597){
            self.generateConnectFix(
              error.response.data.data.institutionId,
              error.response.data.data.institutionLoginId
            );
          }else{
            alert(error.response.data.message);
          }
          self.isLoading = false;
        });
    },
    // update banking for already finicity linked consumer
    //TODO: Need to remove this code
    updateBankDetails() {
      var self = this;
      self.isLoading = true;
      accountAPI
        .updateBank()
        .then((response) => {
          const consumer_login_response = JSON.parse(localStorage.getItem("consumer_login_response"));
          consumer_login_response.account_no = response.data.account_no;
          localStorage.setItem("consumer_login_response", JSON.stringify(consumer_login_response));
          self.update_bank = false;
          self.getPurchasePower();
          self.isLoading = false;
        })
        .catch(function (error) {
          if(error.response.data.code == 597){
            self.generateConnectFix(
              error.response.data.data.institutionId,
              error.response.data.data.institutionLoginId
            );
          }else{
            alert(error.response.data.message);
          }
          if(error.response.data.code == 598){
            self.no_primary_account = 1;
            self.last_four_account_no = 'XXXX';
            self.getPurchasePower();
          }
          self.isLoading = false;
        });
    },
    gotoBankDetails() {
      this.$router.push("/banklisting");
    },
    gotoBankList() {
      localStorage.removeItem("overlay_show");
      this.$router.push("/banklisting");
    },
    // update banking for already finicity linked consumer

    //TODO: Need to remove this code
    clickUpdateBanking() {
      var self = this;
      self.isLoading = true;
      accountAPI
        .updateLinkGenerate()
        .then((response) => {
          if (response.code == 200) {
            const finicityConnectUrl = response.data.link;
            window.finicityConnect.launch(finicityConnectUrl, {
              selector: "#connect-container",
              overlay: "rgba(255,255,255, 0)",
              success: function (data) {
                console.log("Yay! We got data", data);
                if (data.code == 200) {
                  //now store the details at canpay end
                  self.updateBankDetails();
                }
              },
              cancel: function () {
                self.isLoading = false;
                console.log("The user cancelled the iframe");
              },
              error: function (err) {
                self.isLoading = false;
                console.error(
                  "Some runtime error was generated during Finicity Connect",
                  err
                );
              },
              loaded: function () {
                self.isLoading = false;
                console.log(
                  "This gets called only once after the iframe has finished loading"
                );
              },
              route: function (event) {
                self.isLoading = false;
              },
              user: function (event) {
                if (event.data.errorCode) {
                  console.log(event.data.data.institutionId);
                  setTimeout(() => {
                    window.finicityConnect.destroy();
                    //if error code is present then call the connect fix api
                    self.generateConnectFix(
                      event.data.data.institutionId,
                      null
                    );
                  }, 2000);
                }
              },
            });
          } else {
            self.isLoading = false;
          }
        })
        .catch((err) => {
          self.isLoading = false;
          console.log(err);
        });
    },
    //TODO: Need to remove this code
    generateConnectFix(id, login_id) {
      let self = this;
      this.isLoading = true;
      var request = {
        institution_id: id,
        login_id: login_id,
      };
      accountAPI
        .generateConnectFix(request)
        .then((response) => {
          if (response.code == 200) {
            const finicityConnectUrl = response.data.link;
            window.finicityConnect.launch(finicityConnectUrl, {
              selector: "#connect-container",
              overlay: "rgba(255,255,255, 0)",
              success: function (data) {
                console.log("Yay! We got data", data);
                if (data.code == 200) {
                  if (self.update_bank == true) {
                    self.updateBankDetails();
                  } else {
                    //now store the details at canpay end
                    self.storeBankDetails();
                    setTimeout(() => {
                      self.$refs.RealAccountNumberModal.checkForRealAccountNumber();
                    }, 2000);
                  }
                }
              },
              cancel: function () {
                self.isLoading = false;
                console.log("The user cancelled the iframe");
              },
              error: function (err) {
                console.log(err);
                self.isLoading = false;
                console.error(
                  "Some runtime error was generated during Finicity Connect",
                  err
                );
              },
              loaded: function () {
                self.isLoading = false;
                console.log(
                  "This gets called only once after the iframe has finished loading"
                );
              },
              route: function (event) {
                self.isLoading = false;
                console.log(
                  "This is called as the user progresses through Connect"
                );
              },
              user: function (event) {
                if (event.data.errorCode) {
                  console.log(event.data.data.institutionId);
                  setTimeout(() => {
                    window.finicityConnect.destroy();
                    //if error code is present then call the connect fix api
                    self.generateConnectFix(
                      event.data.data.institutionId,
                      null
                    );
                  }, 2000);
                }
              },
            });
          } else {
            self.isLoading = false;
          }
        })
        .catch((err) => {
          self.isLoading = false;
          console.log(err);
        });
    },
    //TODO: Need to remove this code
    saveBankDetails() {
      var self = this;
      this.isLoading = true;
      accountAPI
        .updateBank()
        .then((response) => {
          if (response.code == 200) {
            localStorage.setItem(
              "consumer_login_response",
              JSON.stringify(response.data)
            );
            this.getBankAccountList();
          }
        })
        .catch(function (error) {
          self.isLoading = false;
          if (error.response.data.code == 598) {
            self.$refs["delink-modal"].show();
          } else if (error.response.data.code == 597) {
            self.generateConnectFix(
              error.response.data.data.institutionId,
              error.response.data.data.institutionLoginId
            );
          } else {
            self.showValidationModal(error.response.data.message);
          }
        });
    },
    completeRegistration(type) {
      var self = this;
      if (self.currentUser.id_validation_status != '723'){
        // For address, only pass first 5 digits of zip code
        if (type === constants.lite_consumer_address) {
          const user = JSON.parse(localStorage.getItem("consumer_login_response"));
          if (user && user.zip_code && user.zip_code.length > 5) {
            user.zip_code = user.zip_code.substring(0, 5);
            localStorage.setItem("consumer_login_response", JSON.stringify(user));
          }
        }
        localStorage.setItem("lite_to_standard_current_step", type);
        self.$router.push("/completeregistration"); //redirect to registration
      }
    },
    //calls the api to change bank account through finicity portal
    changeBankAccount() {
      let self = this;
      if(self.show_purchase_power == false){
        self.showModal("pay-now-after-pp-calc-modal");
        return;
      }
      self.$router.push("/banklinking");
    },
    getdata() {
      let self = this;
      self.purchasepowerNoPrimaryAccountModelCalled = true;
      self.$bvModal.hide("delink-modal");
      let ref = db
        .collection("users")
        .doc(String(String(this.currentUser.user_id)));
      ref.get().then((snapshot) => {


        ref.onSnapshot((convo) => {
          let source = convo.metadata.hasPendingWrites ? "Local" : "Server";
          // TODO: add messages to store
          let ref = db
            .collection("users")
            .doc(String(this.currentUser.user_id));
          ref.get().then((snapshot) => {
            if (snapshot.exists) {
              
              this.users = snapshot.data();
              const containsKey = (obj, key) => Object.keys(obj).includes(key);
              const hasName = containsKey(this.users, "transaction_modification");
              const hasAllBankDelinked = containsKey(this.users, "all_bank_delinked");
              const hasPostToBankName = containsKey(this.users, "post_to_bank");
              const hasReviewStatusCode = containsKey(this.users, "review_status_code");

              const hasExchangeRateNotification = containsKey(this.users, "exchange_rate_notification");
              const hasRewardWheelLottery = containsKey(this.users, "lottery_winning_details");
              const mxActionNeeded = containsKey(this.users,"mx_action_needed");
              const mxNonActionalStatus = containsKey(this.users, "mx_non_actionable_status");
              const mxNonActionableStatusResolved = containsKey(this.users, "mx_non_actionable_status_resolved");
              this.$bvModal.hide("account-no-modal");
              if(mxActionNeeded == true){
                if(this.users.mx_action_needed && this.users.mx_action_needed.mx_user_action_needed == 1){
                    this.problematic_account_details.bank_name = this.users.mx_action_needed.bank_name;
                    this.problematic_account_details.account_no = this.users.mx_action_needed.account_no;
                    this.problematic_account_details.active_bank_id = this.users.mx_action_needed.active_bank_id;
                    this.showProblematicModal = true;
                }
              }
              if(mxNonActionalStatus == true){
                if(this.users.mx_non_actionable_status && this.users.mx_non_actionable_status.mx_non_user_actionable_status_detected == 1){
                  this.$refs.NonActionalActionStatusModal.showModal();
                  this.setdata('mx_non_actionable_status');

                }
              }
              if(mxNonActionableStatusResolved == true){
                if(this.users.mx_non_actionable_status_resolved == 1){
                     this.$refs.NonActionalResolveModal.showModal();
                     this.setdata("mx_non_actionable_status_resolved");
                }
              }
              if(hasAllBankDelinked == true){
                if (self.currentUser.user_id!='' && this.users.all_bank_delinked != null) {
                  const hasBanklLinkType = containsKey(this.users, "bank_link_type");
                  if(hasBanklLinkType){
                    self.currentUser.account_id = this.users.active_account_id;
                    self.currentUser.account_no = this.users.active_account_no;
                    self.currentUser.bank_link_type = this.users.bank_link_type;
                    localStorage.setItem(
                      "consumer_login_response",
                      JSON.stringify(self.currentUser)
                    );
                  }
                  if (this.users.all_bank_delinked == 1) {
                    this.$bvModal.hide("delink-modal");
                    this.$refs.NoPrimaryAccountModal.showModal();
                  } else {
                    this.$refs.NoPrimaryAccountModal.hideModal();
                    self.setdata('all_bank_delinked');
                  }
                } else if(self.currentUser.account_no == 'XXXX' && self.consumer_type != self.lite_consumer){
                  this.$refs.NoPrimaryAccountModal.showModal();
                }
              }else if(self.currentUser.account_no == 'XXXX' && self.consumer_type != self.lite_consumer){
                this.$refs.NoPrimaryAccountModal.showModal();
              }
              if (hasName == true) {
                
                if (this.users.transaction_modification != null) {

                  const expiryTime = new Date(this.users.transaction_modification.expiry_time);

                  const currentTime=new Date(new Date().toLocaleString("en-US", {timeZone: this.users.transaction_modification.time_zone}));
                  if(expiryTime < currentTime)
                  {
                    self.setdata('transaction_modification');
                  }
                  else{
                    self.setdata('transaction_successful');
                    self.$router.push("/updatedtransaction");
                  }
                }
              }
              if (hasPostToBankName == true) {
                if (this.users.post_to_bank != null) {
                  if (Object.keys(this.users.post_to_bank).length > 0) {
                    self.$router.push("/acceptedtransaction");
                  }
                }
              }

              if(hasExchangeRateNotification == true){
                if(this.users.exchange_rate_notification == 1){
                  const event = new CustomEvent('exchangeRateNotification', {detail: {modal: true}});
                  document.dispatchEvent(event);
                }else{
                  const event = new CustomEvent('exchangeRateNotification', {detail: {modal: false}});
                  document.dispatchEvent(event);
                }
              }

              if(hasRewardWheelLottery == true){
                if(this.users.lottery_winning_details){
                  const event = new CustomEvent('lotteryNotification', {detail: {modal: true, details: this.users.lottery_winning_details}});
                  document.dispatchEvent(event);
                }else{
                  const event = new CustomEvent('lotteryNotification', {detail: {modal: false}});
                  document.dispatchEvent(event);
                }
              }
              if(hasReviewStatusCode == true){
                if(this.users.review_status_code){
                  self.currentUser.id_validation_status = this.users.review_status_code;
                  localStorage.setItem("consumer_login_response", JSON.stringify(self.currentUser));
                  self.showModal('after-manual-identity-review-modal')
                  self.setdata('review_status_code');
                }
              }
            }
          });
        });
      });
    },
    setdata(type) {
      var data = {};
      if(type == 'mx_non_actionable_status'){
          data = {
            mx_non_actionable_status: null
          }
      }
      else if(type == "mx_non_actionable_status_resolved"){
        data = {
          mx_non_actionable_status_resolved:null
        }
      }
      else if(type == 'transaction_modification'){
        data = {
          transaction_modification: null,
        };
      }else if(type == 'transaction_successful'){
        data = {
          transaction_successful: null,
        };
      }else if(type == 'purchase_power_updated'){
        data = {
          purchase_power_updated: null
        };
      }
      else if(type == 'all_bank_delinked'){
        data = {
          all_bank_delinked: null,
        };
      }
      else if(type == 'problematic_status'){
        data = {
          mx_action_needed: null
        }
      }
      else if(type == 'review_status_code'){
        data = {
          review_status_code: null
        }
      }
      else{
        return true;
      }
      var self = this;
      var washingtonRef = db
        .collection("users")
        .doc(String(this.currentUser.user_id));
      // Set the "capital" field of the city 'DC'
      return washingtonRef
        .update(data)
        .then(function () {
          console.log("Document successfully updated!");
        })
        .catch(function (error) {
          // The document probably doesn't exist.
          console.error("Error updating document: ", error);
        });
    },
    // get total pending transaction amount
    getPendingTransactionAmount() {
      var self = this;
      transactionapi
        .getPendingTransactionAmount()
        .then(function (response) {
          var total_pending_amount = response.data;
          if (response.data <= 0) {
            total_pending_amount = 0.0;
          }
          
          self.pending_transaction_amount = total_pending_amount;
          localStorage.setItem("pending_transaction_amount", total_pending_amount);
          self.getUserRewards()
        })
        .catch(function (err) {
          self.pending_transaction_amount = 0.0;
          localStorage.setItem("pending_transaction_amount", self.pending_transaction_amount);
        });
    },
    checkTransactionDisabled() {
    var self = this;
    transactionapi
        .checkTransactionDisabled()
        .then(function(response) {
            console.log(response.data);
            if (response.data == 1) {
              self.$bvModal.show("mx-relink-modal");
            }
        })
        .catch(function(err) {
            self.isLoading = false;
        });
    },
    openMxLink(){
      var self = this;
      accountAPI
        .openMxLink()
        .then(function (response) {
          self.hideModal("mx-relink-modal");
          self.mxConnectUrl = response.data;
        })
        .catch(function (err) {
          self.isLoading = false;
        });
    },
    gotoPending() {
      this.$router.push("/pendingtransactiondetails");
    },  
    getUserRewards(){
      rewardwheelapi
      .userRewards()
      .then((response) => {
        this.rewardPoints = response.data
      })
      .catch(function (error) {
      });

      rewardwheelapi
      .usedRewardPoint()
      .then((response) => {
        this.usedRewardPoints = response.data
      })
      .catch(function (error) {
      });
    },
    checkForTransactionCooldown(){
      var self = this;
      api
        .checkTransactionCooldown()
        .then(function (response) {
          if (response.data.minutes_to_last_transaction < response.data.cooldown_time) {
            self.cooldown_activated = 1;
            self.cooldown_time = response.data.cooldown_time;
            self.timeLeft = parseInt(response.data.cooldown_time) - parseInt(response.data.minutes_to_last_transaction);
            self.store_name = response.data.store_name;
            self.amount = response.data.amount;
            self.startTimer();
          }
        })
        .catch(function (err) {
          console.log(err);
        });
    },
    startTimer() {
      this.timerId = setInterval(() => {
        this.timeLeft -= 1;
        if (this.timeLeft <= 0) {
          clearInterval(this.timerId);
        }
      }, 60000);
    },
    handleWidgetEvent(event) {
      var self = this;
      console.log('MX PostMessage: ', event);
      if (event.type === 'mx/connect/memberConnected') {
        console.log("Got here");
        self.$router.push({ name: 'mx-success', params: { 'user_guid': event.metadata.user_guid, 'member_guid':event.metadata.member_guid, 'mx_user_type':'challenged', 'fix_connection': self.fix_connection, 'active_bank_id': self.problematic_account_details.active_bank_id } })
        if(self.fix_connection == true){
          self.fix_connection = false;
        }
      } else if (event.type === 'mx/connect/loaded') {
        // Handle widget loading completion
      }
    },
    connectMXBankAccount() {
      let self = this;
      self.fix_connection = true;
      this.isLoading = true;
      let request = {
            active_bank_id:this.problematic_account_details.active_bank_id
      };
      accountAPI
        .directLinkGenerateForMx(request)
        .then((response) => {
          if (response.code == 200) {
            const finicityConnectUrl = response.data;
            window.finicityConnect.launch(finicityConnectUrl, {
              selector: "#connect-container",
              overlay: "rgba(255,255,255, 0)",
              success: function (data) {
                console.log("Yay! We got data", data);
                if (data.code == 200) {
                  if (self.update_bank == true) {
                    // self.updateBankDetails();
                  } else {
                    //now store the details at canpay end
                    self.storeBankDetails();
                    setTimeout(() => {
                      self.$refs.RealAccountNumberModal.checkForRealAccountNumber();
                    }, 2000);
                  }
                }
              },
              cancel: function () {
                self.isLoading = false;
                console.log("The user cancelled the iframe");
              },
              error: function (err) {
                console.log(err);
                self.isLoading = false;
                console.error(
                  "Some runtime error was generated during Finicity Connect",
                  err
                );
              },
              loaded: function () {
                self.isLoading = false;
                console.log(
                  "This gets called only once after the iframe has finished loading"
                );
              },
              route: function (event) {
                self.isLoading = false;
                console.log(
                  "This is called as the user progresses through Connect"
                );
              },
              user: function (event) {
                if (event.data.errorCode) {
                  console.log(event.data.data.institutionId);
                  setTimeout(() => {
                    window.finicityConnect.destroy();
                    //if error code is present then call the connect fix api
                    self.generateConnectFix(
                      event.data.data.institutionId,
                      null
                    );
                  }, 2000);
                }
              },
            });
          } else {
            self.isLoading = false;
          }
        })
        .catch((err) => {
          self.fix_connection = false;
          self.isLoading = false;
          console.log(err);
        });
    },
    updateConsumerLiteToStandard() {
      let self = this;
      self.isLoading = true;
      registrationApi
        .updateConsumerLiteToStandard()
        .then((response) => {
          self.isLoading = false;
          if (response.data.status == 200) {
            self.currentUser.consumer_type = self.constants.standard_consumer;
            self.currentUser.id_validation_required = 0;
            localStorage.setItem("consumer_login_response", JSON.stringify(self.currentUser));
            window.location.reload();
          }
        })
        .catch((err) => {
          self.isLoading = false;
        });
    },
  },
  watch: {
    timeLeft(newTime, oldTime) {
      if (newTime <= 0) {
        this.$nextTick(() => {
          this.cooldown_activated = 0;
          this.hideModal("transaction-cooldown-modal");
        });
      }
    },
    show_purchase_power(newTime,oldTime){
      if(newTime == true){
        this.hideModal('pay-now-after-pp-calc-modal');
      }
    },
  },
  data() {
    let self = this;
    return {
      purchasepower: localStorage.getItem("purchasepower"),
      currentUser: {},
      nopurchasepower: localStorage.getItem("nopurchasepower"),
      last_four_account_no: "",
      account_no: "",
      manual_bank_link: true,
      isLoading: false,
      fullPage: true,
      disable_pay: false,
      pp_alert_message: "",
      transaction_msg: "",
      consumer_suspended_msg: "",
      update_bank: false,
      blacklisted_account_no: false,
      probable_return_found: false,
      blocked_routing_number: 0,
      constants: constants,
      blacklistedAccountNumber: constants.blacklistedAccountNumber,
      no_primary_account:0,
      transactionId:null,
      storeName:null,
      modificationAmount:null,
      previousAmount:null,
      reason:'',
      transactionMoReasonMessage:null,
      pending_transaction_amount: localStorage.getItem("pending_transaction_amount"),
      selected_bank: JSON.parse(localStorage.getItem('selected_bank')),
      purchasepowerNoPrimaryAccountModelCalled:false,
      rewardPoints: {},
      usedRewardPoints: {},
      rwState: '',
      rwInvited: 0,
      timeLeft: 10,
      timerId: null,
      cooldown_activated: 0,
      store_name: null,
      amount: null,
      error_message: '',
      cooldown_time: 0,
      mxConnectUrl: null,
      other_banking_solution_enable: false,
      show_purchase_power:localStorage.getItem('show_pp_animation') == 1?false:true,
      initating_canpay_loader_start_time:new Date(),
      animation_time_difference:process.env.VUE_APP_ANIMATION_TIME_FOR_NON_MX,
      refresh_balance_call_required: localStorage.getItem("refresh_balance_call_required") == 1?true:false,
      problematic_account_details:{
        bank_name:'',
        account_no:'',
        active_bank_id:''
      },
      fix_connection: false,
      showProblematicModal:false,
      showMxNonUserActionalStatusDeteced: false,
      showMxNonActionableStatusResolved:false,
      consumer_type: localStorage.getItem("consumer_login_response")
        ? JSON.parse(localStorage.getItem("consumer_login_response")).consumer_type
        : null,
      lite_consumer: constants.lite_consumer
    };
  },
  created() {
    if (localStorage.getItem("need_bank_link") != null) {
      localStorage.removeItem("need_bank_link");
    }
    if (localStorage.getItem("can_choose_manual_direct_bank") != null) {
      localStorage.removeItem("can_choose_manual_direct_bank");
    }
    if (localStorage.getItem("updated_transaction_data") != null) {
      localStorage.removeItem("updated_transaction_data");
    }
    var self = this;

    this.currentUser = localStorage.getItem("consumer_login_response")
      ? JSON.parse(localStorage.getItem("consumer_login_response"))
      : null;

    if (this.currentUser.bank_link_type == 1) {
      this.manual_bank_link = false;
    }
    this.other_banking_solution_enable = this.currentUser.other_banking_solution_enable;
    this.last_four_account_no = this.currentUser.account_no;
    //check if duplicate bank account present  then user must change their account
    if (this.currentUser.duplicate_bank_account_present) {
      this.$router.push("/banklinking");
    }
    // check if user has four digit account number
    if (this.currentUser.routing_no) {
      setTimeout(() => {
        this.$refs.IncompleteAccountNumberModal.showAccountNoModal();
      }, 500);
    } else {
      const currTime = new Date();
      const previousTime = new Date(localStorage.getItem("returnApiTimeStamp"));
      if(currTime.getTime() - previousTime.getTime()>process.env.VUE_APP_RETURN_API_CALL_DIFF){
       self.getReturnTransactions();
      }else{
        self.isLoading = true;
      }

    }
    // get total pending transaction amount
    self.getPendingTransactionAmount();
    
  },

  mounted() {
    // to prevent consumer to moving to same wheel again and again fter transaction.
    $store.commit('setRewardWheelID', null)
    console.log(new Date());
    this.getdata();
    document.addEventListener('rwStateCahnged', (event) => {
        this.rwState = event.detail.rw_state
        this.rwInvited = event.detail.rw_invited
    });

    const consumer_login_response = JSON.parse(localStorage.getItem("consumer_login_response"));
    this.rwState = consumer_login_response.rw_state
        
    var element = document.getElementsByClassName("content-wrap");
    if (element[0]) {
      element[0].style.setProperty("background-color", "#149240");
      element[0].style.height = "114vh";
      if(window.innerWidth>1200){
        element[0].style.height = "121vh";
      }
    }
    setTimeout(()=>{
    this.$root.$emit("loginapp", [""]);
    this.$root.$emit("changeWhiteBackground", [false, true, "common"]);
    },1000);


    // Check for transaction cooldown
    this.checkForTransactionCooldown();
    setTimeout(() => {
        this.checkTransactionDisabled();
    }, 2000);
    const screenWidth = window.innerWidth;
    if(screenWidth <=900){

    }else{

    }
  },
};
</script>

<style lang="css">
.b-button {
  background-color: transparent;
}
#pay-modal-center___BV_modal_content_ {
  border-radius: 10px;
  margin: 10px;
  background-color: #ffffff;
}
#pay-modal-center___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#zero-pp-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#transaction-cooldown-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}

#blocked-routing-number-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#transaction-modification-show-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#common-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
.pending-tran-val{
  color: #ffffff;
  position: relative;
  text-decoration: underline;
  -webkit-text-decoration-line: underline;
}
/* .pending-tran-val::after{
  content: "";
  position: absolute;
  width: 100%;
  height: 0.01rem;
  background: #fff;
  left: 0;
  bottom: 2px;
} */
.pending-details-link a:focus{
  color:#ffffff;
}
.pending-details-link a:hover{
  color:#ffffff; 
  text-decoration: underline;
  -webkit-text-decoration-line: underline;
}
.user-avatar{
  width: 30%;
}
.pay-now-btn, .direct-link-btn{
  height: 68px!important;
  font-size: 18px;
  font-weight: 600;
  border-radius: 7px!important;
}

#cp-program-info-modal___BV_modal_body_{
    background-color: #fff!important;
    border-radius: 6px;
}
#bank-info-modal___BV_modal_body_{
    background-color: #fff!important;
    border-radius: 6px;
}
.cp-program-info-title{
  font-size: 18px;
}
.cp-program-info-list{
  font-size: 16px;
  /* list-style-type: lower-alpha; */
  margin: 0;
}
.cp-program-info-list li{
  margin-bottom: 10px;
}
.cp-program-info-btn{
  border: 0;
  padding: 10px 20px;
  background: #000;
  color: #ffff;
  border-radius: 6px;
  width: 110px;
}

#consumer-suspended-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}

#mx-relink-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}

#after-manual-identity-review-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}

@media only screen and ( min-width:280px) and ( max-width:700px) {
  .pay-user-label{
    font-size: 17px;
    word-break: break-word;
    text-align: initial;
  }
  .user-block {
    height: 60px;
    width: 60px;
    margin-right: 15px;
    border-radius: 50%;
    background-color: #0e7532 !important;
    border-color: #0e7532 !important;
    display: inline-block;
    border: 2px solid #fff;
    margin-top: 20px;
  }
  .purchpower-text {
    text-align: left;
    color: #ffffff;
    font-size: 12px;
    font-weight: 400;
  }
  .tm-symbol {
    font-size: 7px;
    border: 1px solid #fff;
    width: 17px;
    height: 17px;
    padding: 1px;
    border-radius: 100%;
    font-weight: 600;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }
  .purchasepower-box-amount {
    font-family: "Montserrat";
    font-style: Regular;
    font-variant: Regular;
    color: white;
    font-size: 30px;
    line-height: 30px;
    font-weight: 500;
  }
  .pending-tran-val {
    color: #ffffff;
    position: relative;
    font-size: 10px;
  }
  .helptext-label{
    color: white; font-size: 10px; margin-left: 0.5rem
  }
  .cp-point-val {
    font-weight: bold;
    font-size: 20px;
  }
  .cp-point-tag {
    font-weight: bold;
    font-size: 12px;
  }
  .cp-point-details {
    color: #e9e9e9;
    font-size: 9px;
  }
  .cp-logo{
    width: 30px;
  }
  .pay-now-btn, .direct-link-btn {
    height: 60px!important;
    font-size: 13px;
    font-weight: 600;
    border-radius: 7px!important;
  }
  .cp-point-card-top {
    padding: 10px;
    text-align: left;
    color: #fff;
    border-bottom: 1px solid #669174;
  }
}

@media only screen and ( min-width:320px) and ( max-width:700px) {
  .pay-user-label{
    font-size: 19px;
    word-break: break-word;
    text-align: initial;
  }
  .user-block {
    height: 70px;
    width: 70px;
    margin-right: 15px;
    border-radius: 50%;
    background-color: #0e7532 !important;
    border-color: #0e7532 !important;
    display: inline-block;
    border: 2px solid #fff;
    margin-top: 20px;
  }
  .purchpower-text {
    text-align: left;
    color: #ffffff;
    font-size: 14px;
    font-weight: 400;
  }
  .tm-symbol {
    font-size: 8px;
    border: 1px solid #fff;
    width: 18px;
    height: 18px;
    padding: 1px;
    border-radius: 100%;
    font-weight: 600;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }
  .purchasepower-box-amount {
    font-family: "Montserrat";
    font-style: Regular;
    font-variant: Regular;
    color: white;
    font-size: 31px;
    line-height: 31px;
    font-weight: 500;
  }
  .pending-tran-val {
    color: #ffffff;
    position: relative;
    font-size: 12px;
  }
  .helptext-label{
    color: white; font-size: 12px; margin-left: 0.5rem
  }
  .cp-point-val {
    font-weight: bold;
    font-size: 22px;
  }
  .cp-point-tag {
    font-weight: bold;
    font-size: 13px;
  }
  .cp-point-details {
    color: #e9e9e9;
    font-size: 12px;
  }
  .cp-logo{
    width: 35px;
  }
  .pay-now-btn, .direct-link-btn {
    height: 64px!important;
    font-size: 16px;
    font-weight: 600;
    border-radius: 7px!important;
  }
  .cp-point-card-top {
    padding: 12px;
    text-align: left;
    color: #fff;
    border-bottom: 1px solid #669174;
  }
}

@media only screen and ( min-width:376px) and ( max-width:800px) {
  .pay-user-label{
    font-size: 22px;
    word-break: break-word;
    text-align: initial;
  }
  .user-block {
      height: 80px;
      width: 80px;
      margin-right: 15px;
      border-radius: 50%;
      background-color: #0e7532 !important;
      border-color: #0e7532 !important;
      display: inline-block;
      border: 2px solid #fff;
      margin-top: 20px;
  }
  .purchpower-text {
    text-align: left;
    color: #ffffff;
    font-size: 18px;
    font-weight: 400;
  }
  .tm-symbol {
    font-size: 9px;
    border: 1px solid #fff;
    width: 23px;
    height: 23px;
    padding: 1px;
    border-radius: 100%;
    font-weight: 600;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }
  .purchasepower-box-amount {
    font-family: "Montserrat";
    font-style: Regular;
    font-variant: Regular;
    color: white;
    font-size: 40px;
    line-height: 40px;
    font-weight: 500;
  }
  .pending-tran-val {
    color: #ffffff;
    position: relative;
    font-size: 15px;
  }
  .helptext-label{
    color: white; font-size: 14px; margin-left: 0.5rem
  }
  .cp-point-val {
    font-weight: bold;
    font-size: 25px;
  }
  .cp-point-tag {
    font-weight: bold;
    font-size: 15px;
  }
  .cp-point-details {
    color: #e9e9e9;
    font-size: 14px;
  }
  .cp-logo{
    width: 40px;
  }
  .pay-now-btn, .direct-link-btn {
    height: 68px!important;
    font-size: 18px;
    font-weight: 600;
    border-radius: 7px!important;
  }
  .cp-point-card-top {
    padding: 17px;
    text-align: left;
    color: #fff;
    border-bottom: 1px solid #669174;
  }
}
.progress-bar-animation{
  width:20%;
  height:0.6rem;
  margin-left:15px;
  animation: progress-bar-pp var(--animation-duration) ease-in infinite;
}

@keyframes progress-bar-pp {
  0% {
    width:0%;
    background-color:#1abb38
  }
  35% {
    width:15%;
    background-color:#1abb38
  }
  50%{
    width:25%;
    background-color:#1abb38
  }
  70%{
    width:35%;
    background-color:#1abb38
  }
  90%{
    width:90%;
    background-color:#1abb38
  }
  100%{
    width:100%;
    background-color:#1abb38
    /* background-color:hsl(113, 39%, 32%) */
  }
}
.purchase-power-animation{
  animation: animation-pp 3s linear infinite;
}
@keyframes animation-pp {
  0% {
    transform:translateY(0px);
  }
  25% {
    transform:translateY(-1px);
  }
  50% {
    transform:translateY(-2px);
  }
  75% {
    transform:translateY(-1px);
  }
  100%{
    transform:translateY(0px);
    /* background-color:hsl(113, 39%, 32%) */
  }
}
#updated-purchase-power___BV_modal_content_{
  background-color:#ffffff;
}
#updated-purchase-power___BV_modal_body_{
  background-color:#ffffff;
  border-radius:10px;
}
#pay-now-after-pp-calc-modal___BV_modal_content_{
  background-color:#ffffff;
}
#pay-now-after-pp-calc-modal___BV_modal_body_{
  background-color:#ffffff;
  border-radius:10px;
}
.black-button-ok{
 padding:8px 15px;
 width:100%;
 border-radius:10px;
 border:none;
 font-family: 'Montserrat';
 background-color: #000000;
 color:#ffffff;
 font-weight:bolder;
 margin-top:10px;
}
.progress-bar-alignment{
  margin-top:25px;
  margin-left:18px;
  }
  #delay-in-refresh-balance___BV_modal_content_{
  background-color:#ffffff;
}
#delay-in-refresh-balance___BV_modal_body_{
  background-color:#ffffff;
  border-radius:10px;
}
#bank-link-success___BV_modal_content_{
  background-color:#ffffff;
}
#bank-link-success___BV_modal_body_{
  background-color:#ffffff;
  border-radius:10px;
}
#bank-link-error___BV_modal_content_{
  background-color:#ffffff;
}
#bank-link-error___BV_modal_body_{
  background-color:#ffffff;
  border-radius:10px;
}
.inner-div-scroll {
    height: 100%;
    white-space: nowrap;
}
.inner-div-scroll:nth-child(n+2){
  margin-left:15px;
}
.information-scroll{
    scrollbar-width: thin;
    scrollbar-color: #149240 #149240;
    display:flex;
}
.information-card{
  width: 310px;
  margin-top: 15px;
  background-color: rgb(255, 255, 255);
  padding: 10px;
  border-radius: 7px;
  position: relative;
  height: 80px;
}
.space-between-information{
  display:flex;
  justify-content:space-between;
}
.information-border-except-below{
  border-left: 1px solid #7cbb93da;
  border-top: 1px solid #7cbb93da;
  border-right: 1px solid #7cbb93da;
  border-radius: 5px 5px 0px 0px;
}
.information-border-except-top{
  border-left: 1px solid #7cbb93da;
  border-bottom: 1px solid #7cbb93da;
  border-right: 1px solid #7cbb93da;
  border-radius: 0px 0px 5px 5px;
}
.balance-add-bank{
  text-decoration: underline;
  color: rgb(255, 255, 255);
  padding-top: 8px;
  font-size: 15px;
  font-weight:500;
  position: relative;
  top: 3px;
}
.swipe-merchant-top{
  scrollbar-width: thin;
  scrollbar-color: #0e7532 #0e7532;
  display:flex;
  padding-top:10px;
}
.accordian-merchant-style{
  width:250px;
  background-color: #188642;
  border-radius: 4px;
  border:1px solid #7cbb93da;
  position: relative;
  height: 55px;
  color:#ffffff;
  margin-top:10px;
  margin-bottom:5px;
}
.accordian-merchant-inner-style{
  width: 100%;
  height: 100%;
  border-left: 4px solid rgb(255, 202, 17);
  border-radius: 4px;
  text-align:left;
}
.accordian-merchant-inner-style-blue{
  width: 100%;
  height: 100%;
  border-left: 4px solid #007EE5;
  border-radius: 4px;
  text-align:left;
}
.merchant-balance-style{
  margin-bottom:0px!important;
  padding-left:10px;
  font-weight:600;
  padding-top:3px;
  font-family:'Montserrat';
}
.merchant-name-style{
  margin-bottom:0px!important;
  padding-left:10px;
}
.swipe-merchant-bottom{
  scrollbar-width: thin;
  scrollbar-color: #0e7532 #0e7532;
  display:flex;
}
.accordian-merchant-style-1{
  width:250px;
  background-color: #188642;
  border-radius: 4px;
  border:1px solid #7cbb93da;
  position: relative;
  height: 55px;
  color:#ffffff;
  margin-top:0px;
  margin-bottom:5px;
}
.consumer-lite-consumer-information{
  font-family: Open Sans; 
  font-size:13px;
  position:absolute;
  top:71px;
  z-index:99999999999;
  font-weight:600;
}
.consumer-lite-width-20{
  width:20%;
}
.consumer-lite-width-70{
  width:70%;
}
.consumer-lite-text-left{
  text-align:left;
}
.consumer-lite-additional-information{
  margin: 0px !important;
  font-weight: 600;
  text-decoration:underline;
}
.consumer-lite-arrow{
  transform: scale(0.85);
  position: relative;
  top: -2px;
}
.consumer-lite-describe-information{
  margin: 0px !important;
  font-weight: 400;
  font-size:12px;
}
.consumer-lite-border{
  border:1px solid #7cbb93da;
  border-radius:8px;
}
.purchase-power-header-for-dashboard{
  border-bottom: 1px solid #669174;
  padding: 11px 15px!important;
}
</style>
