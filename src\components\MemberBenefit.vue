<template>
<div>
    <div v-if="isLoading">
        <CanPayLoader/>
    </div>
    <div class="container" style="width:100%;margin-bottom: 15px;height:100vh;background-color:#149240;">
        <br>
        <div v-if="couponCodeData.length > 0">
        <div v-for="(item, index) in couponCodeData" :key="index">
        <div class="text-center bg-white adjust-padding">
            <div style="width:100%;background-color:#EFEFEF;border-radius:12px 12px 0px 0px;">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="120" height="60" viewBox="0 0 398 199" fill="none">
            <rect width="398" height="199" fill="url(#pattern0_14279_82)"/>
            <defs>
            <pattern id="pattern0_14279_82" patternContentUnits="objectBoundingBox" width="1" height="1">
            <use xlink:href="#image0_14279_82" transform="scale(0.000833333 0.00166667)"/>
            </pattern>
            <image id="image0_14279_82" width="1200" height="600" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABLAAAAJYBAMAAACAyAIpAAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAAAkUExURUdwTORgbuRgbuRgbh5KfR5KfR5KfR5KfR5KfeRgbh5Kff///zo4dgEAAAAJdFJOUwA7ZsApV4e03VT3AV8AAAABYktHRAsf18TAAAAAB3RJTUUH5wUJDw0w6lZIvAAAAAFvck5UAc+id5oAACcaSURBVHja7Z29c9vG1sZphxSVVLy5lTpe523c6SaVO8w7ku1hhcKyM6xQRLKHFYvEzrBi4a9h5XvzNu5SWBKHf+VrAiRwdvecxS6xACjz+VU2sAfYj0fPLg5AoNMBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOBz+8eCXjAf/GngF3vtxE/nzg3+03QiwZ9x78Mvngr9+/pdz5I8/PaKRDwZtNwXsD/cUcaT84iatH3/RA//6edB2c8Ce8MCQ1RoHgdz7iQv87G534Bvm3i+fef4qE8iPj4TIXwZtNwq0jqiOtWlZI3+SA//6n7abBVrmx882LMq699AaienwsPnnZzv/HQiBJbr6/PnfbTcNtEiZrr4qiw8s1RU865D5sVQdkrLKdfX587Dt5oGWuO+gDn5O+8kpcth2A0Er3HvkJI/Pp0Zk+Qya8teg7SaCNnCZzljncXM6cRoF3zaOtrN2Hi3S0ek+49LwEHG2HUMfJx6Rw7abCZrG3XY0ffgo0jA78K3jPhHq+vAxLEyGh8Y9L3UoV4ZejoUrwwPDz3ZUfTz0ivxP200FDeLnOpo+PIOHbTcWNIef6aQMdo1GMutw8DcsRR97bVnd84uLqO0OPlR2MKw7YlnnLxerr7yO2u7ig2QXw7oTq6zeq9WG26jtTj5EdjIsRR9+RwhwYdg9Oysv9Hixyrlpu5MPEN8cVnXLGlSs8dcJ7uPHD1fP7KWOiK5Wq9/b7ubD4/sdhbW7ZZ1Wqm/35UYryz9sxVRdrZZx2/18cDzaVVg7W1alO4bdV4Va3lmKqbparb603c+Hxne76qoly3pF1SIra7LSidru6TvD86urq8vKR/GTRBjLqpBxeKKqJRGKPTV0tZo3OTZ3mkWIq51dl+7VLGvn6vY0tSwjp2K4MPRhFqK3KsyEFdLvp7tW15jh3roVw1zoQRBh+TmNzmDHA+06F/YNtdy6FcNc6EEIYVWaCStY1mD3JmtM3Yp95bqdYbp7hBBWpZmwgmWd7lTbvptceMNaLdsaqLtGCGF5qSGgZe02F07c5MIb1moVtzZUd4sQwqqoq90ta5fK9li5JHoxwbD4WROYBBBW1Zlw91zWcIfajli5GCl1ybCQfHckgLBObGP/y88PHjwwX0cqK8THsnZ5xGHBykXvgiNJV1i9OxJAWFbVDLMyPz5yVYiPZe1wv9BxTT4RhXXrf86DpLqw7MmGfIFt/9HhsDiej2UNvGs7FvQSKaV6oq5wWehIdWGVLLGG23L/azOtHS3r1Lu2C0EviVJqJAsLuXc3qgvrxD72RU7AqphhcUAPy/rkW1nxYm9OSxmPy8gSBALVhfWoZPCHeUmbt+2Wy/LOZIlWpNwu7K8grKpUF5bH4NvMbVAU87CsXZrLolzsTWzCmrY6XneGysIq95dhUdjibrtZ1tCvsvKinPZBV9lze6k8Fojb0G5UFlZ5etRRMoOimLtlnfpV9lgU1q1U6n2kOdhbv1MeKpWFdeJlKxbJ7GRZninSMVXIciEIi+oo/TGhki5F6t2JysJ6VD76NVqW5+qdSukmohMjSU8p82VixEFYTjQhLGpZFoPbJZfll3vvaVZEDSzKS9GZcLOmH0NYvlQVltNDfv91Kz8sijlb1sCnsseaFR2zwqIzYWwGQlhOVBWW26MNwyIgsGUNfSpLjCdtc9+UkHpNuE1CHEFYvjQjrPos69S3sXTtdMQJS7e1NT0Iy5eqwjpxG/+hU8QOluVzWUi8KGtyjxMWsbVbJhLCcqOqsBydpTbL8rksPNKtiBMW1dA8D11AWJ5UFdYjR2cZFiEnTjJxtCyfy8Jj3Yo4YdF1V6R0E4TlQ1VhOerK1bIGRTE3y/IRVjHJbbLnnLBG5tK9o1wp4hFSJyoKy/0nhcMiKKhlDfzaqqiIExaZ9KZFKBEWbuk4UVFY7jdf6rKsoXNdi9XTtsGMsOimqIgdQ1ieNCasuixr6FzXQjNzYwuXCqVzHoTlS0Vhefz0qybLOnWua9+wIsaeyJw3JbFk5TVvcbTuEBWF9b27sGqyrE/Odc3FkbfXFBZ9KDniYvGgnyMVhXXiMvZ+ljUsirlY1ifnuk4MaZjCIqkupVcgLF8aFFY96Xf31PvMsCIio83L18hL/OY0FsLypaKwXDylXstyTr13TSuiwsq2kCVWTIOJsJI2h+vu0KSwarEsZ2H1TCsiwspy8V1DaRvGEJYnFYX1iBnpZi3LOfV+ZFqRISxyP0dNsBNhxa4nPGzCC+vUIoZhEWmxLK8LQ2dhHZtWZCzVxaUUhOVLDcKyiKGGXJazsEamFfV1YUlLLLojanG07hDhhTW0iWFYhAayLGdhjU0rMmY+fWrMgbB8qUNYjVqWt7DiYpMurCNdaEo3cYt6IFGHsBq1LN+pkAqDCCt9yopksaZq8ALC8qQWYVW3rGFRrMSynIXVN62I3HGer/9PluiRGlwICy9ec6MWYTVpWc7C6hIFbdCFJcrHeFoelFGPsBq0LPdHSCfG1R4R1rSj3DrUllg9CMuXeoRV3bKc0+/uwuobSySypko6poEVyKt6IFCTsJqzLI+H3ie6LkaqsMgSK1Yj+xCWL+GFdZruqGxZrqssD2H1FtqdvpGqJDmnQLwMP9Jxoy5hNWZZPj/TefxG/eYnEVZkXaGTgngy2Y3wwvqU7WnKsqp8G5rMfR1lIfW3XHDe4ODcZcILa7Psbir9XuELvkQv6/SCtpSnTNTLR1BORWE9lEe6IcsKJyyinojpJQjLi/DC2s5NDVlWFWEVUlp3gZgeVX7FmjQ4OHeZisLiDGe7rxnL2uVDTVsKYV3b0qPKi0LiJkfnDlODsAabfc1Y1qcKjVeERXJVc61cD8LypqKwuB+snm53NmJZp8515RufZ6dGsnjoC9qixobmblODsD5tdzaSyzqt2Pjcoyxrd5IfxVMzjlQU1n2raJqwrKFzXU2KNfmU/sfoj6cQljc1COuzfW9gyxo619WECouso4y7NiQ/isexHKkorHv2sW7Asga7V55c7CV0HTXVC5JZEk/NOFJRWOyrIj/le+u3rCp3dIhJxZ0fLBd+JD+KhxscqUNYZLBrt6xAworIdGcso7g3v4MSqgqLVc4g3117LqtK4v2IaqlwJaM78Jb3HagqLNZsPhX767asT4715KDC6lq0Q9NYeGrGkarCYl/pR+anui3rtELd+8SkiCtN5XJ4asaZqsLihTMsCtRsWUPHenIcE2ER8cR6udHKojrAU1VY93YQg6dlDYtipkoHFepeCOaa5EDNFChJY+HhBleqCkv4NMWwKFCrZVW5KCTC+qI9mmX2kWhngKeysB56iyGkZVW5KCRi+qLej9ZYQFj+VBbWibcYQlpWlaexSEJ9TsQz14spH7WPxKOdXZydSbvOzuQ4eqbz51dXl8+cym5DLq6uri6eebe9u9Muj5DKwvrOWwwhLeu0StULYU3JRWGiF6PZBvke9JO1ND/8wex5/Od6T1w+Os//zE7y8fVZaeFNyMvsD2L54TLyaPj5yzcfP7x5zcixu971kd0lcfY8DXlzqVa6srAcnjqo0bIGVapeTH/JkcWTaLZBvAe9tbV3xp4n2wvPEnpkKbf8IyorvuYxmaRvf3Ntdu/VNuZ9rO06/zPf5Sit85dFFZSYysKSPizXiGVVWruT6S8+tkiHZhtEYeVXlYm2I5fs1F4ZKpK1w0Xl9X+hRKz+sJX98ytZgSOqxkQ84PJ3hx7svlKr8LqodXVhPfQUQ0jLqrR2J+/vi8f5P82bgWPSc2JXzaT4mXxgCh3ubMzjsuo/0SIYtyxYbCuvnmiZkDK/qocrV9ZjvdKk1tWF9b33/BXOsj5VqXixrlpGk/zfc6PcjHTcdemxbsSTWOtiDNHqNioZVSPCpqytsPQTLeO8yAv9cGXKemHWoDhedWHd9xRDSMsaOtaRH0wy5kV3J0Y5F2EV+VWtM4sF2ubbFyzdGTNGNgNipWhL326FNdFD8voeGUcjouN4suLYBlUXVtd3/trJsgZFMaLSShUvevKWZBSMzlSyDdLDDcV4aZ1ZLN5swvqVHSPbuLJStDzfuhFW34yZyge0CuPximfjtNWFJdvR0KFMJcuqtsQqhHWjPECjQR+ake5Bk4+GaZ42chHWET9EtnF5yofMpfIbYTHqubUcMJErwDomqXYAYX3fiGUNi2K5Sj9Vqjd5uOFY6xVx1IWBk3/sOnYQFu8+K5tl9YQI0bIyYeVNXn4sdDFN61D8/+NHB2mrdf5IDrdZmwUQ1n0vMYS0rKFjDXkKNX0pfOWLpdhKzBmQGUZ7YKvof1lYgvvYHv6aSCGxEJAJaxP24fLs7Oxim4G6pXX4cPns7Oz8+azkcGTyXr6+eJYeLtdWuswKICwpk1W7ZVXLYpHx/FKM09QoNiLDJs0NpMxc3VMISzQTyX0sIX0xRNJiKqzNmbbZ1222IMkNK0/MbhNUpVfByyJ11X2xldZ1J4ywTjzEENKyKt0oVB5uyP/YGOWMHQyBlJkqO8gMI6pEOYFKxEeIc6c8lut6XGctLrIIm5zWl61SlyR7nylLstntX6Ka789vHiRhhPWdhxhCWtbQtYJlA/rONpQTOm4xfygy0FNlB3EjSVjCyl2QecpTOULKlqWOlVaTZjGebGuWNZKqZCNevgbbOutZ3O4kl3cIYXV85q+AllWx1oWw3thGf0HHLWKPRL8krY6Eg7AU4WpM+ZCFJSSSQ5ZpNSLz5HGWUlETZ0dbN5PrbN4d2HppEkZYJ+5iCGhZFWdCYjPFZZC5pqCikWYG+W00ZDEkCEteYa2ki9BjW0jMnyZvxVTZnKln3ucaNxNrfSSfa6Os6zDC+s5ZDCEt67RirWfMsJh/oMrACxON/DYaogGhl8f+wprZQiL+NFth3XLHyhZf+rr/WDzeps7sLZ9NX0RBhCVfF9ZoWRWvCfkJZSp1lNV15LfRjMqE1V3ZmHMhfVuEbY3FNTCt+u2MMaxN1RKxzoJsskTE2zDCOnEUQ0jLqjoTskMaG8VchDUSSxA/4ntZm9aWH0l6UhDWRAtYONRwKyxDPYUhX/MnYqrwVOqrrF8XuVarC8vNjMJa1tC1cgLs4sYspox8+XSmlSAi4FNCiipWr9epRnLjcFpW79tnnc751aK0htvzmJWYbSMTvulMtbNGib8Jf1paGQ8euYkhoGXdq1plTljM3/uI7hd6alaUuHbdk6H44SaNpLwEp6RCid4UKaO5kJS6PRwzhx7xTe5ZDYte7gQQ1ndOYghqWVXh8kfMsIxK9q9ZSCXoJSWbEx/ToyebjTNjC4VoNXeNibFFqGJk7OizFc9qz/+tHVsV3CGWFeKdT81bVlW4NTAz+BO6n+9NulpTB5a64rwksjh5IZ3YDFH+HqLNxmK+ngqtXUgj3bMEzlgtTkTN660KISw3MeyTZXHZIKZ3FWHxfkDHei7uYY6t/k4j2m4thBWZIdTjrs3jJEJrF2L9F6KGs7brOzLZWN9suK1jCGFV/9wSKdWIZY0YYTG9u6D75+yRqDymyh4qrMQyAmpkLiwud7DgDvnU1gISxtVhJp4q6yL9iH1LT2gdEuT1h3fOsuigbhFHxCqsY26sjT2x/eBLZitjC/yPHIs/kkho7UKsw1jWwIgT48h6HqUFQYR15yxrYuqKGUg12zVlj0S9Lxb3MEMhvHgr33ZjP1ex4sv/SMRfbCzstsRP8n2uzROhpyjjgMK6c5bFCItZm6tJiWnpkSKugyVh8Yrs2uozY2uT18D6BCk/0JmrzrldfW5PWvxve9/2QwrrrlkWHSLTNbaoSYmk7EiaJxDJ3doDyRjYkuGKziPzQOJQLgShbkUw5XYdMcLqWToip1tSGz/ulmUpTy3IhuQiLHIkTT68ctQxWumOUZzSnJ/odcINUwMxu7QQfeZIM0x911zddsz8/ZjMQgrrblkWd6uQ6d1+WQH1SJqwiORu7MeOuc1zI2REQr4wNRDvsyzstsQvxntMJUZOihmFFNbdsizujg7Tu2q2Ky47kpZ4F/ekjHlB2rKdxAKJfRY1sAsr4fb0ZAvihDWxnmZLP6iwmrKsQYjKMnd0uGWQ+hRwJHciN7BH4p4UohK6urMIiyp1yZ1nLjV3Yf+7cBfWTLQ+IzLcZzyasayqz8tkMHd0uAXKSCkRcUeipvZFPMfciKMqSdhTJnoIVeoNd56p1NyFWH3hjuAaRlhdWaHm+YIJqxHL+msQpK7MHR3O4B2ERYuooyCn5LW9imGM882x46mKtiRSc9cDzT9Y3ZUlwAgr8zf2QAqToMJqxLLCGBZ3R2fKFBvTAnyHjsVD0HMkthooIzCRhTXhD2jxuC2LlTTf+QnrSPY3s1PCCasBywpkWNwdnaSsGD8yE/EQNDi2xb3lt0daBE2RLNnzmGfZsBD1kAqLT1Mwwuo7CuZpWGE1YFmBDItLvEdlxfiRmYnymdgOLi2xLA83CEssm8dtCSWsdNZ1+KCQqwBdqd+yBoFqSuVgkY0iLNaxlExrJJ3DDJV+2lMcz4ih68I5e56oIxBKWCNzE4vw9Onu1G1Z/w5VUSoH0wNyyh1LSAFo55AexDR3duWYsWByshSVIpbfCfEexAgrrcK0vHt7oYVVt2UNAtWTSbyznVsuLPlt3fQcZh8TlSh+0ZNPt+DP1LVXMA8NJqzEsX+Dfo62XssKZliMsOZcuRktwXZUXywgp+S1IytnPhJP1xPOZJFiTihhpX9osUMHyyfckXotaxCqmswdnYQrpwiLHbdjYbhVyRnCEtfuR2KMlOKXpVgQSlgzV2HNQgurVssKZljcHZ2IKzcpdayRKB/5Xk/H8rP8vnA0OS0mRxQEFRbbU0zHhRVWnZY1CFZL844Ov/Atd6yxKB96n3Guh0lrd/qiQUtdIjaifmGtj+OQeK9DWDVaVqgcVoe7o1OepCq/blQHgUpuqoeNpcMWvqQ9dyg+nlNEyAmmxoU1Di+s2iwrVNJ9DZ1WbH/tVDa89qj0pmJsYglTR7VQ3FyNEJdsYgQhqLBcOrgGYdVmWQENi7mj8zdbThEW21ELUT5UcrEWRdOqc+GM6nb5ZncRMRXbG1JYDrcK6xFWTZYV0rCYOzrT8nJcjyp5i9gYhC2RFkavShNlz0yqkWiAYoRWlwDCsjxjozGqQVg1WVZIw2Lu6MRsuVLHEn7e0LGm5MXHkjtUjomyXb51JEXoZUIIy9mxahFWLZYV1LCYOzoRW25c5lhK4l05Rs8WOZKixHfNiBl+2/dalPbefceqxbKCGpb5Gx2ht0qF1ZfGW3wWIWUiHZXIMVJ2iIlYEhGLDW7csca1CKsOyxqErKCZeHd5RSh3OfSDKDxr4n0mnZlERWJNFB3Yvg6bE0hY7V4VduqwrHBJ97zLFIQU0IiW4f5U6XirHXlsObr8+x3yuTA1hHrsnO5w+nJdUGHJp1H7pQZhhbesQdD6mXd05nxBRVhcj1JhXYt75pbzfxFOKE6R2tWf7bPDOY0La1KPsIJbVljDYu7oJHzB0p9/kTlNG56JpISO/MgelaN8R1tdS0lSVAglrJnQDQZ1CSu0ZQ3CVs/8aEjMF1Rv/TA9SoU1F/ckWhQ1wqkQdS1GqPWQpKgQVFhCXxkF6xBWYMsKeknYYe7oSAvSsp/YK5eXU2UX3ZNoYRNxXxEl+58qrGKP5b2goYQ1aV1YQS3rn4PAtRuvNKQ+UBdjZo8qS5+E7unaAmfSPhI1V7ZTmS6FY1l+5BBUWEmnHPmEVQlpWcFR/v5tf+xlb5tRhBVLe4zl7sIhairW41Y41lxucChhjU1nlo9aj7CCWlZoqGOkSB+QLHvxGvsSY2PP0nZUKSqh20eisLq26uWEEtaoRMBKaD3C2mvLoo5hHRP12XizR5U1WCTt0S/XZDcjFwsx3a78JUiJ98Te4BDC+kEurRD851+EPbYs86cUiVRSkaD9i9G34h69h+X7QMSZYrJZNc5r4VhSIzrhhNU3zs8T+gerCvtrWWbiPZKKylaRQqcoVVhjSQkdm5uN+SqpWQ/lcJLHqYQSluu7G0Z1Cmt/LctIvMu3vyYrUQRrqHxuxEBdWLLPzfgqKbWQnh+1JS5DCcvyNi2zX+oS1v5aliEsuQuocpgeleWzsAhL9rkFu11z2GuhBpHc4lDC6pZZ44ZZrcLaW8sy7ujIy9GRUs7o0YV0EEULurCoWm+FKLpdu1FwzdfAZiShhJUpJint4UWtwtpbyzLu6Mwdi061vUfiQRTtasJSPzkoHI+OykwWVlc4kkYwYaUG+XdZBwd+VaTBvlqWakMrWwZINTdtALoz8SDKKeSvGGpy4L8hrc/c13z96JHOn6mnDCasEefAQq/VJ6x9tayxNlKWRYM6qDe240zpLkU86kD8qkQJr4mkMXp16eGIgFUpvo/ogYMJy+0976O6hVXdsmqp1kQXllxUS3lFdN8T2fYsiafHapQySAsuxsi6CbMk2bxu4Fv9wEGEZfnegFGrGoVV2bJCP9ZA281PIjoLpSTtZn2GovvkxFNvoYVF/BG/CMdSq8u/0cZMCgQTVtf4G5KOWa+wKlpW2J/m5Ohja1szqO5Gv3mjH2VOomZSlLos0/76R2TzW+FYqrCo6PSvgSVai4MIK6tNySIr6EeaBKpZVj2GZcwttptfY7Vokve6riva21o+o1BCd6JHkfFX7h/NhWOtifIQKrpcij2zVeGENTbcUOyzWoVVybJqMixDWHNLYW0i2n5ht2f4iL7GYYVl6oqcXJHQ1BISb/fx6Y6JOa7hhNXX/ho4Fg0Iq5Jl1WNY5q1CWzfpK6n0aqv7wvAr+mdsmEyUbe++MqPIoCpanQrnV0Q35oS1jaC2Ek5YvZUcoFWgXmFVsaxBPVUyhBVbChv29uHy4mq24ki2x1/we1hdFd3/hD3YmAn5wjZkutk60eS8JpywMvlb58Kx3rJ6cLOsWl+RrKGbgH3FsFi5sllkSbMks52OvybHeLPVpkVVdEm2sa8fYtuIQMIaKSrmjrjQqlkTu1vWoKYa6cKyPwbCWYbAOt3dfcwp8f2zc272pGM0UbfGtrNHXDsSdViVGT6gsEqz6rmyaxbWzpZVl2FZb5GY/OAurOWbq5d/uhdX+v8Fq50eH5JeAOovoIjTiLG+ISWgsDYWHnckZk0Ja1fLGtRVIX1tbX/Ulls9O/HBtWDSMbL42yeWx3zEeq+RuYj12pKxDymsrE7iX6PLS5wDsduF4f/VVh9dKlNrae4jv06cuRa8fXam+9Vm3deTQm6eneu6SnVE86/1XBXmf5ex0F9pvW5nTQhrJ8uqKYdFeiYnsRc3RtCNG49lv1kyU8VYjFiam6KO/EB0SGFt2iXIJuvceSPC2smyasphFW1XR8TCaDdhJcxrA91JLyh6XiGRNqFONS0EE9amQ35nD5epLmpGWI6WRfVXo2HpU2HZQyC7LbJuva4nDdJBmXiFaBmLem5C59u/niBmYrI2X9f3E3sVf8uq0bB0pZS2f7HaganN6m5LD3mj1/NFWcRXx1KEWM9jMynjTSsiY8/GMpOmhOW9yqrTsKy/TeCQBSKz9otjcWc8K4tfV4oWuim/htCuLJVRDyusbQe+13c8zv8qGhKWt2XVaVi6sOZl5XeZC6e2uN/KtXqtPWnqsGKzPFwYWFi5NaqPqXa2ueGkOWF5WlathqUnEKalAaVjapCmoSSTeeeg1S/Ge0CeloWotYyVFgQWVq7hW3Ka7na2vuk0JyzP9HttSfeMhWUIOErH1GCaxvGCfCfvIsIyfqjhd4l4bbY4oLCKHlm+jjabHuf3HJJOg8Lys6xBvZWZKGMQlZb3G9NVfvdxzO27Sc83KjnCfGwcb7LyIFZbEFpYRPbL15cXFxdXxa2s/A+nEWF5WVbNhqWOqkvzx9L4CbdtkiyMW71vLqVYrb4v/vlR2TFdh/S5kDd8BfRGhRaWZS7PGticsHwsa1BzXZQheusQIFnW+2jBbb6Ww/JLdEary3NprNII83H5r2MnXHnGWgOCC8u4uZk3Ikl3NycsjwvDug3L8h4+iV/ZTnwfsfNTkTk0pFCkfpg/+besJ61yA2SWesmIDTAyKOGFJXTJNh/foLCcLaveS8K82YoblMG5RXqxfSz3bcdcSWXrq4yxHnbTEYR1w1Q7450wScd6/WsQFv887B+kqg0Jy9myas1hZZC/fZeZsMMaTNqHzGz3rojS9uZXUOm+haEG4eoz2YbowrvlHZNJ+dYgLFZZW101KixHy/ppUH9VimfknD6zsEa/pbK8zLYbI6vc6BgzEVu0Vcrv0qUiGR9t+omFtEVs1L4OYTF98lu+q0lhOVpWI+Qj5GhYHb0XP8SbzcYD9DENIrb0IdaPqPzFr31uvOIgceqMvJ5yF0wA06azrwjtWu+K5F3WPjlXnpelLezKRw2Po2U1wXaEfP6onheDSM1HtSz9lv8TJiKvBAl9bx5qgzKv0R9kpNMOIyxnEw7B81xab36rfrRd2SPLyqxEu9dVwvnLNGj54ZKGKS5i2tLjj0ZEEZp71rt0/2xVKpNcWcv0EoG7a+RuwkE4u7h6+frq4qz6kXZnjyyr03v55s2lb1D34vLS6MLCRZbK6jyPsXT68zT2w+aPnROW/jhdN1P3+zg7d8uGtS/skWUFpPt8sUhl9WyH2Ivnl8+izX8YXb0zQ75axOX2TMzF6rTt7miDfbKsoHTPzquvVRn7uSk5qJn5csvLfXN8m5YViLE5rcUlIebkmbTdinb4Zi0rAIxh/V4SYt6wc/hqxLcJLEtkYk6EJRH6b6EdLO6bBZYlwazDk5IQ8xZQmcV9w8CyBMzl0ruSCNOwGruJsofYLKvturWJaVilCSlzsR+33Yo2kS2rgcca9hfTsKYlEeZiv8zivm1kyxq0XbUWMa/vSqc1w7AOMudOOIFhmZiGFZdEmIZ1sKmGDfdgWAZ9f5WYK6xp261omxMYls7EWyXMcw1R261om3swLI2+v0rMHNaB3iWknMCwVCbeKjFzWAe/xOrwljVou1ItwiTdy64Jmd9dzNtuxh5wAsOimIZVZj+MYWHt3uEsa9B2lVqE+521/U3O7C8Qk7bbsQ88hGEVjBmVzO0hMyYkbrsd+8B9GFYO+xqtqTWE/cl03HZD9oKHMKwt7O+fE2vIhAuJ2m7IXnAfhrWBW4eXCIt9h1DpF8APhIcwrAz+TSCxLYRblCE/uuE+DCtj5i0s/m1dENaGhzCsNfyr8axPwIzYkEN+elThPgxrzYQXliWCX5Thjk7OQxiWOq3dLJyERd/19gbCMrkPw1KntWduCyayKLs9grAYHsKw6LT2tuckLLooS8gl5Rf3s37r3IdhEWEsIyIZy0p8TEuRabHhtxftNQ8P3bDo0v1tx0lY1OMSOpPO227LHnH/0A2L3CZcRtS+5AVTX1EfEda07cbsEw8P3LCOFSkduwhrrEhpDGGx3D9sw6IXeLEiLHElTj2uA2GJ/KvtCrQKuQxcL6qeOgiLzITr1TpZoyVttwbsDceq37isxImS4o5ueQCkFLJI7w06zGtkJrxRjwBhgS1kJkwX60RYiRDS17RHcg9R280B+4I2E7osmMaqkEhSC8/5gS0TTRXl8xoRUupx2jUiAB1FFVnaqlxYJDc/Xf/f7e4iOCz09ZLDgmmklTiCsICBrhL1/g5L4WnZzUS329bgsChUks2E5QsmMvPN0w1OdxfBYWGopHzBRC4jY30DhAUyDJX0S4U10QuQ2RTP+YEMQyVEacJXdhe6jMZFCJ7zAyldw2yI/fDzmp5sUBIU87YbBPYDMvEl2ZZJmbD0y0jlA4fTthsE9gNTJcR++AWTnmxQfjuWtN0gsB8sdJXQtxnNuQgio82C6gjCAho9w56o/Uy5kGNDRfSnq3HbLQJ7gbnE6pcJq7gE3OZPyXSKp2ZAClFJlG0ptR9j8qTLfTzcAFKMhbjy0quYiTAnT+XH9m03COwFXatK2HvQ5uRJl/u4Bw3WmLlO+tAMaz9kQRVlW+hyH7cKwZofrCph7Wdi6I4u93GrEKwZGyo5KrOfhbGbXhRO224R2AtmhkroReHfTARZUM03m8hFIfKjIGVhqGRcYj/E0WLjIEhjgZSeaTWzEvvpGyKiqzKkscCaI0MlyndPIiakWO5za3ekscCavmE1R2X2U0yV21UZubJEtgGkHFtVUpJtmBtb+OU+ODyKRME2/0TX7mxOqpBRstlC1+7TtlsE9oKRoQiqkjkXUizu42yD8kmLuO0Wgb1grNuPopKECymEFWUbaOILF4UgZaxbzYgKK+JCFrqIZiQCt6BBii4s5fM4fOpgpu1WPgGGO4UgRRdWv1wlurDoah9rd5Chr7Fm5SrJi2RTofrNwrjtBoH9oBDWfP3fpw4qWahanNAI5N1BRrFWv4073RcrB5UUpnYTdc5fKSHIu4MMchG4/LhQRCItxIlFfdRDpm23B+wJxyuZKR8ysYREbbcH7Al9f5WM5AhkscCGnr9KLCY3b7s5YF/o+qvEYnJx280Be8NCVEkkRMgmh2QDyBn7r5dELc7bbgzYH55KKpmKIRNfjwMHyJEgEsu0Jq3ekR0FBGFis7yiVlpkJW03BewT/CJL+iJFysxzUQYOEX4utL5Tm1+XJW23BOwX3FrcnjhQngbECgvwHPm7D2NZ1rkTHCS/+k2EX+nOfKUIDhBDJu9LQ3r6ZPh7240Ae4imrPdReYimrD/abgLYS7qvvEXSI2Jc/tZ2A8C+8vzPjUg+PHOM6L7YSGv5Om679mB/6Z4/f/ny9aWrrNKQi6s3L68uz9quOthrzi6urq6eRb4hl34hAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEDt/D9x44YmouFomQAAACV0RVh0ZGF0ZTpjcmVhdGUAMjAyMy0wNS0wOVQxNToxMzoyOCswMDowMA1GN2oAAAAldEVYdGRhdGU6bW9kaWZ5ADIwMjMtMDUtMDlUMTU6MTM6MjgrMDA6MDB8G4/WAAAAKHRFWHRkYXRlOnRpbWVzdGFtcAAyMDIzLTA1LTA5VDE1OjEzOjQ4KzAwOjAw7WGnjgAAAABJRU5ErkJggg=="/>
            </defs>
            </svg>
            </div>
            <hr class="manage-horizontal">

            <div>
                <p class="adjust-margin make-text-bold adjust-font-size">
                    Save big on your Med Card 
                </p>
                <p class="adjust-margin make-text-bold adjust-font-size">
                    renewal through NuggMD.
                </p>
            </div>
            <div class="mt-3 ml-2 mr-2 mb-3">
                <p class="nuggmd-highlight-text make-text-bold-1 adjust-font-size-1">
                    Just let NuggMD know when your current card expires, and they will send you a customized Deep Discount Code 30–60 days before expiration.
                </p>
            </div>
            <div class="mt-3 mb-3">
                <button class="learn-more-button-css" @click="handleNuggmdModal()">Learn More
                <svg class="abjust-arrow-position" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 25 42" fill="none">
                <path d="M24.0047 19.3612L5.31415 0.67097C4.88186 0.23834 4.30479 0 3.68948 0C3.07417 0 2.4971 0.23834 2.06481 0.67097L0.688385 2.04706C-0.207266 2.94373 -0.207266 4.40109 0.688385 5.2964L16.3833 20.9913L0.67097 36.7036C0.238681 37.1362 0 37.713 0 38.3279C0 38.9436 0.238681 39.5203 0.67097 39.9533L2.0474 41.329C2.48003 41.7617 3.05676 42 3.67207 42C4.28738 42 4.86445 41.7617 5.29674 41.329L24.0047 22.6218C24.438 22.1878 24.676 21.6083 24.6746 20.9923C24.676 20.3739 24.438 19.7948 24.0047 19.3612Z" fill="white"/>
                </svg>
                </button>
            </div>
            <div style="width:100%;font-weight:bolder;font-size:0.8rem;" class="text-left pb-2 ml-2 mb-2 mr-2 nuggmd-card">
                <p class="nuggmd-note">
                    * Discounts Available to all CanPay Members in Medical markets
                </p>
            </div>
            <div>
            </div>
        </div>
        </div>
        </div>
        <div v-if="couponCodeData.length == 0">
        <div class="text-center bg-white adjust-padding">
            <div style="width:100%;background-color:#EFEFEF;border-radius:12px 12px 0px 0px;">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="120" height="60" viewBox="0 0 398 199" fill="none">
            <rect width="398" height="199" fill="url(#pattern0_14279_82)"/>
            <defs>
            <pattern id="pattern0_14279_82" patternContentUnits="objectBoundingBox" width="1" height="1">
            <use xlink:href="#image0_14279_82" transform="scale(0.000833333 0.00166667)"/>
            </pattern>
            <image id="image0_14279_82" width="1200" height="600" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABLAAAAJYBAMAAACAyAIpAAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAAAkUExURUdwTORgbuRgbuRgbh5KfR5KfR5KfR5KfR5KfeRgbh5Kff///zo4dgEAAAAJdFJOUwA7ZsApV4e03VT3AV8AAAABYktHRAsf18TAAAAAB3RJTUUH5wUJDw0w6lZIvAAAAAFvck5UAc+id5oAACcaSURBVHja7Z29c9vG1sZphxSVVLy5lTpe523c6SaVO8w7ku1hhcKyM6xQRLKHFYvEzrBi4a9h5XvzNu5SWBKHf+VrAiRwdvecxS6xACjz+VU2sAfYj0fPLg5AoNMBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOBz+8eCXjAf/GngF3vtxE/nzg3+03QiwZ9x78Mvngr9+/pdz5I8/PaKRDwZtNwXsD/cUcaT84iatH3/RA//6edB2c8Ce8MCQ1RoHgdz7iQv87G534Bvm3i+fef4qE8iPj4TIXwZtNwq0jqiOtWlZI3+SA//6n7abBVrmx882LMq699AaienwsPnnZzv/HQiBJbr6/PnfbTcNtEiZrr4qiw8s1RU865D5sVQdkrLKdfX587Dt5oGWuO+gDn5O+8kpcth2A0Er3HvkJI/Pp0Zk+Qya8teg7SaCNnCZzljncXM6cRoF3zaOtrN2Hi3S0ek+49LwEHG2HUMfJx6Rw7abCZrG3XY0ffgo0jA78K3jPhHq+vAxLEyGh8Y9L3UoV4ZejoUrwwPDz3ZUfTz0ivxP200FDeLnOpo+PIOHbTcWNIef6aQMdo1GMutw8DcsRR97bVnd84uLqO0OPlR2MKw7YlnnLxerr7yO2u7ig2QXw7oTq6zeq9WG26jtTj5EdjIsRR9+RwhwYdg9Oysv9Hixyrlpu5MPEN8cVnXLGlSs8dcJ7uPHD1fP7KWOiK5Wq9/b7ubD4/sdhbW7ZZ1Wqm/35UYryz9sxVRdrZZx2/18cDzaVVg7W1alO4bdV4Va3lmKqbparb603c+Hxne76qoly3pF1SIra7LSidru6TvD86urq8vKR/GTRBjLqpBxeKKqJRGKPTV0tZo3OTZ3mkWIq51dl+7VLGvn6vY0tSwjp2K4MPRhFqK3KsyEFdLvp7tW15jh3roVw1zoQRBh+TmNzmDHA+06F/YNtdy6FcNc6EEIYVWaCStY1mD3JmtM3Yp95bqdYbp7hBBWpZmwgmWd7lTbvptceMNaLdsaqLtGCGF5qSGgZe02F07c5MIb1moVtzZUd4sQwqqoq90ta5fK9li5JHoxwbD4WROYBBBW1Zlw91zWcIfajli5GCl1ybCQfHckgLBObGP/y88PHjwwX0cqK8THsnZ5xGHBykXvgiNJV1i9OxJAWFbVDLMyPz5yVYiPZe1wv9BxTT4RhXXrf86DpLqw7MmGfIFt/9HhsDiej2UNvGs7FvQSKaV6oq5wWehIdWGVLLGG23L/azOtHS3r1Lu2C0EviVJqJAsLuXc3qgvrxD72RU7AqphhcUAPy/rkW1nxYm9OSxmPy8gSBALVhfWoZPCHeUmbt+2Wy/LOZIlWpNwu7K8grKpUF5bH4NvMbVAU87CsXZrLolzsTWzCmrY6XneGysIq95dhUdjibrtZ1tCvsvKinPZBV9lze6k8Fojb0G5UFlZ5etRRMoOimLtlnfpV9lgU1q1U6n2kOdhbv1MeKpWFdeJlKxbJ7GRZninSMVXIciEIi+oo/TGhki5F6t2JysJ6VD76NVqW5+qdSukmohMjSU8p82VixEFYTjQhLGpZFoPbJZfll3vvaVZEDSzKS9GZcLOmH0NYvlQVltNDfv91Kz8sijlb1sCnsseaFR2zwqIzYWwGQlhOVBWW26MNwyIgsGUNfSpLjCdtc9+UkHpNuE1CHEFYvjQjrPos69S3sXTtdMQJS7e1NT0Iy5eqwjpxG/+hU8QOluVzWUi8KGtyjxMWsbVbJhLCcqOqsBydpTbL8rksPNKtiBMW1dA8D11AWJ5UFdYjR2cZFiEnTjJxtCyfy8Jj3Yo4YdF1V6R0E4TlQ1VhOerK1bIGRTE3y/IRVjHJbbLnnLBG5tK9o1wp4hFSJyoKy/0nhcMiKKhlDfzaqqiIExaZ9KZFKBEWbuk4UVFY7jdf6rKsoXNdi9XTtsGMsOimqIgdQ1ieNCasuixr6FzXQjNzYwuXCqVzHoTlS0Vhefz0qybLOnWua9+wIsaeyJw3JbFk5TVvcbTuEBWF9b27sGqyrE/Odc3FkbfXFBZ9KDniYvGgnyMVhXXiMvZ+ljUsirlY1ifnuk4MaZjCIqkupVcgLF8aFFY96Xf31PvMsCIio83L18hL/OY0FsLypaKwXDylXstyTr13TSuiwsq2kCVWTIOJsJI2h+vu0KSwarEsZ2H1TCsiwspy8V1DaRvGEJYnFYX1iBnpZi3LOfV+ZFqRISxyP0dNsBNhxa4nPGzCC+vUIoZhEWmxLK8LQ2dhHZtWZCzVxaUUhOVLDcKyiKGGXJazsEamFfV1YUlLLLojanG07hDhhTW0iWFYhAayLGdhjU0rMmY+fWrMgbB8qUNYjVqWt7DiYpMurCNdaEo3cYt6IFGHsBq1LN+pkAqDCCt9yopksaZq8ALC8qQWYVW3rGFRrMSynIXVN62I3HGer/9PluiRGlwICy9ec6MWYTVpWc7C6hIFbdCFJcrHeFoelFGPsBq0LPdHSCfG1R4R1rSj3DrUllg9CMuXeoRV3bKc0+/uwuobSySypko6poEVyKt6IFCTsJqzLI+H3ie6LkaqsMgSK1Yj+xCWL+GFdZruqGxZrqssD2H1FtqdvpGqJDmnQLwMP9Jxoy5hNWZZPj/TefxG/eYnEVZkXaGTgngy2Y3wwvqU7WnKsqp8G5rMfR1lIfW3XHDe4ODcZcILa7Psbir9XuELvkQv6/SCtpSnTNTLR1BORWE9lEe6IcsKJyyinojpJQjLi/DC2s5NDVlWFWEVUlp3gZgeVX7FmjQ4OHeZisLiDGe7rxnL2uVDTVsKYV3b0qPKi0LiJkfnDlODsAabfc1Y1qcKjVeERXJVc61cD8LypqKwuB+snm53NmJZp8515RufZ6dGsnjoC9qixobmblODsD5tdzaSyzqt2Pjcoyxrd5IfxVMzjlQU1n2raJqwrKFzXU2KNfmU/sfoj6cQljc1COuzfW9gyxo619WECouso4y7NiQ/isexHKkorHv2sW7Asga7V55c7CV0HTXVC5JZEk/NOFJRWOyrIj/le+u3rCp3dIhJxZ0fLBd+JD+KhxscqUNYZLBrt6xAworIdGcso7g3v4MSqgqLVc4g3117LqtK4v2IaqlwJaM78Jb3HagqLNZsPhX767asT4715KDC6lq0Q9NYeGrGkarCYl/pR+anui3rtELd+8SkiCtN5XJ4asaZqsLihTMsCtRsWUPHenIcE2ER8cR6udHKojrAU1VY93YQg6dlDYtipkoHFepeCOaa5EDNFChJY+HhBleqCkv4NMWwKFCrZVW5KCTC+qI9mmX2kWhngKeysB56iyGkZVW5KCRi+qLej9ZYQFj+VBbWibcYQlpWlaexSEJ9TsQz14spH7WPxKOdXZydSbvOzuQ4eqbz51dXl8+cym5DLq6uri6eebe9u9Muj5DKwvrOWwwhLeu0StULYU3JRWGiF6PZBvke9JO1ND/8wex5/Od6T1w+Os//zE7y8fVZaeFNyMvsD2L54TLyaPj5yzcfP7x5zcixu971kd0lcfY8DXlzqVa6srAcnjqo0bIGVapeTH/JkcWTaLZBvAe9tbV3xp4n2wvPEnpkKbf8IyorvuYxmaRvf3Ntdu/VNuZ9rO06/zPf5Sit85dFFZSYysKSPizXiGVVWruT6S8+tkiHZhtEYeVXlYm2I5fs1F4ZKpK1w0Xl9X+hRKz+sJX98ytZgSOqxkQ84PJ3hx7svlKr8LqodXVhPfQUQ0jLqrR2J+/vi8f5P82bgWPSc2JXzaT4mXxgCh3ubMzjsuo/0SIYtyxYbCuvnmiZkDK/qocrV9ZjvdKk1tWF9b33/BXOsj5VqXixrlpGk/zfc6PcjHTcdemxbsSTWOtiDNHqNioZVSPCpqytsPQTLeO8yAv9cGXKemHWoDhedWHd9xRDSMsaOtaRH0wy5kV3J0Y5F2EV+VWtM4sF2ubbFyzdGTNGNgNipWhL326FNdFD8voeGUcjouN4suLYBlUXVtd3/trJsgZFMaLSShUvevKWZBSMzlSyDdLDDcV4aZ1ZLN5swvqVHSPbuLJStDzfuhFW34yZyge0CuPximfjtNWFJdvR0KFMJcuqtsQqhHWjPECjQR+ake5Bk4+GaZ42chHWET9EtnF5yofMpfIbYTHqubUcMJErwDomqXYAYX3fiGUNi2K5Sj9Vqjd5uOFY6xVx1IWBk3/sOnYQFu8+K5tl9YQI0bIyYeVNXn4sdDFN61D8/+NHB2mrdf5IDrdZmwUQ1n0vMYS0rKFjDXkKNX0pfOWLpdhKzBmQGUZ7YKvof1lYgvvYHv6aSCGxEJAJaxP24fLs7Oxim4G6pXX4cPns7Oz8+azkcGTyXr6+eJYeLtdWuswKICwpk1W7ZVXLYpHx/FKM09QoNiLDJs0NpMxc3VMISzQTyX0sIX0xRNJiKqzNmbbZ1222IMkNK0/MbhNUpVfByyJ11X2xldZ1J4ywTjzEENKyKt0oVB5uyP/YGOWMHQyBlJkqO8gMI6pEOYFKxEeIc6c8lut6XGctLrIIm5zWl61SlyR7nylLstntX6Ka789vHiRhhPWdhxhCWtbQtYJlA/rONpQTOm4xfygy0FNlB3EjSVjCyl2QecpTOULKlqWOlVaTZjGebGuWNZKqZCNevgbbOutZ3O4kl3cIYXV85q+AllWx1oWw3thGf0HHLWKPRL8krY6Eg7AU4WpM+ZCFJSSSQ5ZpNSLz5HGWUlETZ0dbN5PrbN4d2HppEkZYJ+5iCGhZFWdCYjPFZZC5pqCikWYG+W00ZDEkCEteYa2ki9BjW0jMnyZvxVTZnKln3ucaNxNrfSSfa6Os6zDC+s5ZDCEt67RirWfMsJh/oMrACxON/DYaogGhl8f+wprZQiL+NFth3XLHyhZf+rr/WDzeps7sLZ9NX0RBhCVfF9ZoWRWvCfkJZSp1lNV15LfRjMqE1V3ZmHMhfVuEbY3FNTCt+u2MMaxN1RKxzoJsskTE2zDCOnEUQ0jLqjoTskMaG8VchDUSSxA/4ntZm9aWH0l6UhDWRAtYONRwKyxDPYUhX/MnYqrwVOqrrF8XuVarC8vNjMJa1tC1cgLs4sYspox8+XSmlSAi4FNCiipWr9epRnLjcFpW79tnnc751aK0htvzmJWYbSMTvulMtbNGib8Jf1paGQ8euYkhoGXdq1plTljM3/uI7hd6alaUuHbdk6H44SaNpLwEp6RCid4UKaO5kJS6PRwzhx7xTe5ZDYte7gQQ1ndOYghqWVXh8kfMsIxK9q9ZSCXoJSWbEx/ToyebjTNjC4VoNXeNibFFqGJk7OizFc9qz/+tHVsV3CGWFeKdT81bVlW4NTAz+BO6n+9NulpTB5a64rwksjh5IZ3YDFH+HqLNxmK+ngqtXUgj3bMEzlgtTkTN660KISw3MeyTZXHZIKZ3FWHxfkDHei7uYY6t/k4j2m4thBWZIdTjrs3jJEJrF2L9F6KGs7brOzLZWN9suK1jCGFV/9wSKdWIZY0YYTG9u6D75+yRqDymyh4qrMQyAmpkLiwud7DgDvnU1gISxtVhJp4q6yL9iH1LT2gdEuT1h3fOsuigbhFHxCqsY26sjT2x/eBLZitjC/yPHIs/kkho7UKsw1jWwIgT48h6HqUFQYR15yxrYuqKGUg12zVlj0S9Lxb3MEMhvHgr33ZjP1ex4sv/SMRfbCzstsRP8n2uzROhpyjjgMK6c5bFCItZm6tJiWnpkSKugyVh8Yrs2uozY2uT18D6BCk/0JmrzrldfW5PWvxve9/2QwrrrlkWHSLTNbaoSYmk7EiaJxDJ3doDyRjYkuGKziPzQOJQLgShbkUw5XYdMcLqWToip1tSGz/ulmUpTy3IhuQiLHIkTT68ctQxWumOUZzSnJ/odcINUwMxu7QQfeZIM0x911zddsz8/ZjMQgrrblkWd6uQ6d1+WQH1SJqwiORu7MeOuc1zI2REQr4wNRDvsyzstsQvxntMJUZOihmFFNbdsizujg7Tu2q2Ky47kpZ4F/ekjHlB2rKdxAKJfRY1sAsr4fb0ZAvihDWxnmZLP6iwmrKsQYjKMnd0uGWQ+hRwJHciN7BH4p4UohK6urMIiyp1yZ1nLjV3Yf+7cBfWTLQ+IzLcZzyasayqz8tkMHd0uAXKSCkRcUeipvZFPMfciKMqSdhTJnoIVeoNd56p1NyFWH3hjuAaRlhdWaHm+YIJqxHL+msQpK7MHR3O4B2ERYuooyCn5LW9imGM882x46mKtiRSc9cDzT9Y3ZUlwAgr8zf2QAqToMJqxLLCGBZ3R2fKFBvTAnyHjsVD0HMkthooIzCRhTXhD2jxuC2LlTTf+QnrSPY3s1PCCasBywpkWNwdnaSsGD8yE/EQNDi2xb3lt0daBE2RLNnzmGfZsBD1kAqLT1Mwwuo7CuZpWGE1YFmBDItLvEdlxfiRmYnymdgOLi2xLA83CEssm8dtCSWsdNZ1+KCQqwBdqd+yBoFqSuVgkY0iLNaxlExrJJ3DDJV+2lMcz4ih68I5e56oIxBKWCNzE4vw9Onu1G1Z/w5VUSoH0wNyyh1LSAFo55AexDR3duWYsWByshSVIpbfCfEexAgrrcK0vHt7oYVVt2UNAtWTSbyznVsuLPlt3fQcZh8TlSh+0ZNPt+DP1LVXMA8NJqzEsX+Dfo62XssKZliMsOZcuRktwXZUXywgp+S1IytnPhJP1xPOZJFiTihhpX9osUMHyyfckXotaxCqmswdnYQrpwiLHbdjYbhVyRnCEtfuR2KMlOKXpVgQSlgzV2HNQgurVssKZljcHZ2IKzcpdayRKB/5Xk/H8rP8vnA0OS0mRxQEFRbbU0zHhRVWnZY1CFZL844Ov/Atd6yxKB96n3Guh0lrd/qiQUtdIjaifmGtj+OQeK9DWDVaVqgcVoe7o1OepCq/blQHgUpuqoeNpcMWvqQ9dyg+nlNEyAmmxoU1Di+s2iwrVNJ9DZ1WbH/tVDa89qj0pmJsYglTR7VQ3FyNEJdsYgQhqLBcOrgGYdVmWQENi7mj8zdbThEW21ELUT5UcrEWRdOqc+GM6nb5ZncRMRXbG1JYDrcK6xFWTZYV0rCYOzrT8nJcjyp5i9gYhC2RFkavShNlz0yqkWiAYoRWlwDCsjxjozGqQVg1WVZIw2Lu6MRsuVLHEn7e0LGm5MXHkjtUjomyXb51JEXoZUIIy9mxahFWLZYV1LCYOzoRW25c5lhK4l05Rs8WOZKixHfNiBl+2/dalPbefceqxbKCGpb5Gx2ht0qF1ZfGW3wWIWUiHZXIMVJ2iIlYEhGLDW7csca1CKsOyxqErKCZeHd5RSh3OfSDKDxr4n0mnZlERWJNFB3Yvg6bE0hY7V4VduqwrHBJ97zLFIQU0IiW4f5U6XirHXlsObr8+x3yuTA1hHrsnO5w+nJdUGHJp1H7pQZhhbesQdD6mXd05nxBRVhcj1JhXYt75pbzfxFOKE6R2tWf7bPDOY0La1KPsIJbVljDYu7oJHzB0p9/kTlNG56JpISO/MgelaN8R1tdS0lSVAglrJnQDQZ1CSu0ZQ3CVs/8aEjMF1Rv/TA9SoU1F/ckWhQ1wqkQdS1GqPWQpKgQVFhCXxkF6xBWYMsKeknYYe7oSAvSsp/YK5eXU2UX3ZNoYRNxXxEl+58qrGKP5b2goYQ1aV1YQS3rn4PAtRuvNKQ+UBdjZo8qS5+E7unaAmfSPhI1V7ZTmS6FY1l+5BBUWEmnHPmEVQlpWcFR/v5tf+xlb5tRhBVLe4zl7sIhairW41Y41lxucChhjU1nlo9aj7CCWlZoqGOkSB+QLHvxGvsSY2PP0nZUKSqh20eisLq26uWEEtaoRMBKaD3C2mvLoo5hHRP12XizR5U1WCTt0S/XZDcjFwsx3a78JUiJ98Te4BDC+kEurRD851+EPbYs86cUiVRSkaD9i9G34h69h+X7QMSZYrJZNc5r4VhSIzrhhNU3zs8T+gerCvtrWWbiPZKKylaRQqcoVVhjSQkdm5uN+SqpWQ/lcJLHqYQSluu7G0Z1Cmt/LctIvMu3vyYrUQRrqHxuxEBdWLLPzfgqKbWQnh+1JS5DCcvyNi2zX+oS1v5aliEsuQuocpgeleWzsAhL9rkFu11z2GuhBpHc4lDC6pZZ44ZZrcLaW8sy7ujIy9GRUs7o0YV0EEULurCoWm+FKLpdu1FwzdfAZiShhJUpJint4UWtwtpbyzLu6Mwdi061vUfiQRTtasJSPzkoHI+OykwWVlc4kkYwYaUG+XdZBwd+VaTBvlqWakMrWwZINTdtALoz8SDKKeSvGGpy4L8hrc/c13z96JHOn6mnDCasEefAQq/VJ6x9tayxNlKWRYM6qDe240zpLkU86kD8qkQJr4mkMXp16eGIgFUpvo/ogYMJy+0976O6hVXdsmqp1kQXllxUS3lFdN8T2fYsiafHapQySAsuxsi6CbMk2bxu4Fv9wEGEZfnegFGrGoVV2bJCP9ZA281PIjoLpSTtZn2GovvkxFNvoYVF/BG/CMdSq8u/0cZMCgQTVtf4G5KOWa+wKlpW2J/m5Ohja1szqO5Gv3mjH2VOomZSlLos0/76R2TzW+FYqrCo6PSvgSVai4MIK6tNySIr6EeaBKpZVj2GZcwttptfY7Vokve6riva21o+o1BCd6JHkfFX7h/NhWOtifIQKrpcij2zVeGENTbcUOyzWoVVybJqMixDWHNLYW0i2n5ht2f4iL7GYYVl6oqcXJHQ1BISb/fx6Y6JOa7hhNXX/ho4Fg0Iq5Jl1WNY5q1CWzfpK6n0aqv7wvAr+mdsmEyUbe++MqPIoCpanQrnV0Q35oS1jaC2Ek5YvZUcoFWgXmFVsaxBPVUyhBVbChv29uHy4mq24ki2x1/we1hdFd3/hD3YmAn5wjZkutk60eS8JpywMvlb58Kx3rJ6cLOsWl+RrKGbgH3FsFi5sllkSbMks52OvybHeLPVpkVVdEm2sa8fYtuIQMIaKSrmjrjQqlkTu1vWoKYa6cKyPwbCWYbAOt3dfcwp8f2zc272pGM0UbfGtrNHXDsSdViVGT6gsEqz6rmyaxbWzpZVl2FZb5GY/OAurOWbq5d/uhdX+v8Fq50eH5JeAOovoIjTiLG+ISWgsDYWHnckZk0Ja1fLGtRVIX1tbX/Ulls9O/HBtWDSMbL42yeWx3zEeq+RuYj12pKxDymsrE7iX6PLS5wDsduF4f/VVh9dKlNrae4jv06cuRa8fXam+9Vm3deTQm6eneu6SnVE86/1XBXmf5ex0F9pvW5nTQhrJ8uqKYdFeiYnsRc3RtCNG49lv1kyU8VYjFiam6KO/EB0SGFt2iXIJuvceSPC2smyasphFW1XR8TCaDdhJcxrA91JLyh6XiGRNqFONS0EE9amQ35nD5epLmpGWI6WRfVXo2HpU2HZQyC7LbJuva4nDdJBmXiFaBmLem5C59u/niBmYrI2X9f3E3sVf8uq0bB0pZS2f7HaganN6m5LD3mj1/NFWcRXx1KEWM9jMynjTSsiY8/GMpOmhOW9yqrTsKy/TeCQBSKz9otjcWc8K4tfV4oWuim/htCuLJVRDyusbQe+13c8zv8qGhKWt2XVaVi6sOZl5XeZC6e2uN/KtXqtPWnqsGKzPFwYWFi5NaqPqXa2ueGkOWF5WlathqUnEKalAaVjapCmoSSTeeeg1S/Ge0CeloWotYyVFgQWVq7hW3Ka7na2vuk0JyzP9HttSfeMhWUIOErH1GCaxvGCfCfvIsIyfqjhd4l4bbY4oLCKHlm+jjabHuf3HJJOg8Lys6xBvZWZKGMQlZb3G9NVfvdxzO27Sc83KjnCfGwcb7LyIFZbEFpYRPbL15cXFxdXxa2s/A+nEWF5WVbNhqWOqkvzx9L4CbdtkiyMW71vLqVYrb4v/vlR2TFdh/S5kDd8BfRGhRaWZS7PGticsHwsa1BzXZQheusQIFnW+2jBbb6Ww/JLdEary3NprNII83H5r2MnXHnGWgOCC8u4uZk3Ikl3NycsjwvDug3L8h4+iV/ZTnwfsfNTkTk0pFCkfpg/+besJ61yA2SWesmIDTAyKOGFJXTJNh/foLCcLaveS8K82YoblMG5RXqxfSz3bcdcSWXrq4yxHnbTEYR1w1Q7450wScd6/WsQFv887B+kqg0Jy9myas1hZZC/fZeZsMMaTNqHzGz3rojS9uZXUOm+haEG4eoz2YbowrvlHZNJ+dYgLFZZW101KixHy/ppUH9VimfknD6zsEa/pbK8zLYbI6vc6BgzEVu0Vcrv0qUiGR9t+omFtEVs1L4OYTF98lu+q0lhOVpWI+Qj5GhYHb0XP8SbzcYD9DENIrb0IdaPqPzFr31uvOIgceqMvJ5yF0wA06azrwjtWu+K5F3WPjlXnpelLezKRw2Po2U1wXaEfP6onheDSM1HtSz9lv8TJiKvBAl9bx5qgzKv0R9kpNMOIyxnEw7B81xab36rfrRd2SPLyqxEu9dVwvnLNGj54ZKGKS5i2tLjj0ZEEZp71rt0/2xVKpNcWcv0EoG7a+RuwkE4u7h6+frq4qz6kXZnjyyr03v55s2lb1D34vLS6MLCRZbK6jyPsXT68zT2w+aPnROW/jhdN1P3+zg7d8uGtS/skWUFpPt8sUhl9WyH2Ivnl8+izX8YXb0zQ75axOX2TMzF6rTt7miDfbKsoHTPzquvVRn7uSk5qJn5csvLfXN8m5YViLE5rcUlIebkmbTdinb4Zi0rAIxh/V4SYt6wc/hqxLcJLEtkYk6EJRH6b6EdLO6bBZYlwazDk5IQ8xZQmcV9w8CyBMzl0ruSCNOwGruJsofYLKvturWJaVilCSlzsR+33Yo2kS2rgcca9hfTsKYlEeZiv8zivm1kyxq0XbUWMa/vSqc1w7AOMudOOIFhmZiGFZdEmIZ1sKmGDfdgWAZ9f5WYK6xp261omxMYls7EWyXMcw1R261om3swLI2+v0rMHNaB3iWknMCwVCbeKjFzWAe/xOrwljVou1ItwiTdy64Jmd9dzNtuxh5wAsOimIZVZj+MYWHt3uEsa9B2lVqE+521/U3O7C8Qk7bbsQ88hGEVjBmVzO0hMyYkbrsd+8B9GFYO+xqtqTWE/cl03HZD9oKHMKwt7O+fE2vIhAuJ2m7IXnAfhrWBW4eXCIt9h1DpF8APhIcwrAz+TSCxLYRblCE/uuE+DCtj5i0s/m1dENaGhzCsNfyr8axPwIzYkEN+elThPgxrzYQXliWCX5Thjk7OQxiWOq3dLJyERd/19gbCMrkPw1KntWduCyayKLs9grAYHsKw6LT2tuckLLooS8gl5Rf3s37r3IdhEWEsIyIZy0p8TEuRabHhtxftNQ8P3bDo0v1tx0lY1OMSOpPO227LHnH/0A2L3CZcRtS+5AVTX1EfEda07cbsEw8P3LCOFSkduwhrrEhpDGGx3D9sw6IXeLEiLHElTj2uA2GJ/KvtCrQKuQxcL6qeOgiLzITr1TpZoyVttwbsDceq37isxImS4o5ueQCkFLJI7w06zGtkJrxRjwBhgS1kJkwX60RYiRDS17RHcg9R280B+4I2E7osmMaqkEhSC8/5gS0TTRXl8xoRUupx2jUiAB1FFVnaqlxYJDc/Xf/f7e4iOCz09ZLDgmmklTiCsICBrhL1/g5L4WnZzUS329bgsChUks2E5QsmMvPN0w1OdxfBYWGopHzBRC4jY30DhAUyDJX0S4U10QuQ2RTP+YEMQyVEacJXdhe6jMZFCJ7zAyldw2yI/fDzmp5sUBIU87YbBPYDMvEl2ZZJmbD0y0jlA4fTthsE9gNTJcR++AWTnmxQfjuWtN0gsB8sdJXQtxnNuQgio82C6gjCAho9w56o/Uy5kGNDRfSnq3HbLQJ7gbnE6pcJq7gE3OZPyXSKp2ZAClFJlG0ptR9j8qTLfTzcAFKMhbjy0quYiTAnT+XH9m03COwFXatK2HvQ5uRJl/u4Bw3WmLlO+tAMaz9kQRVlW+hyH7cKwZofrCph7Wdi6I4u93GrEKwZGyo5KrOfhbGbXhRO224R2AtmhkroReHfTARZUM03m8hFIfKjIGVhqGRcYj/E0WLjIEhjgZSeaTWzEvvpGyKiqzKkscCaI0MlyndPIiakWO5za3ekscCavmE1R2X2U0yV21UZubJEtgGkHFtVUpJtmBtb+OU+ODyKRME2/0TX7mxOqpBRstlC1+7TtlsE9oKRoQiqkjkXUizu42yD8kmLuO0Wgb1grNuPopKECymEFWUbaOILF4UgZaxbzYgKK+JCFrqIZiQCt6BBii4s5fM4fOpgpu1WPgGGO4UgRRdWv1wlurDoah9rd5Chr7Fm5SrJi2RTofrNwrjtBoH9oBDWfP3fpw4qWahanNAI5N1BRrFWv4073RcrB5UUpnYTdc5fKSHIu4MMchG4/LhQRCItxIlFfdRDpm23B+wJxyuZKR8ysYREbbcH7Al9f5WM5AhkscCGnr9KLCY3b7s5YF/o+qvEYnJx280Be8NCVEkkRMgmh2QDyBn7r5dELc7bbgzYH55KKpmKIRNfjwMHyJEgEsu0Jq3ekR0FBGFis7yiVlpkJW03BewT/CJL+iJFysxzUQYOEX4utL5Tm1+XJW23BOwX3FrcnjhQngbECgvwHPm7D2NZ1rkTHCS/+k2EX+nOfKUIDhBDJu9LQ3r6ZPh7240Ae4imrPdReYimrD/abgLYS7qvvEXSI2Jc/tZ2A8C+8vzPjUg+PHOM6L7YSGv5Om679mB/6Z4/f/ny9aWrrNKQi6s3L68uz9quOthrzi6urq6eRb4hl34hAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEDt/D9x44YmouFomQAAACV0RVh0ZGF0ZTpjcmVhdGUAMjAyMy0wNS0wOVQxNToxMzoyOCswMDowMA1GN2oAAAAldEVYdGRhdGU6bW9kaWZ5ADIwMjMtMDUtMDlUMTU6MTM6MjgrMDA6MDB8G4/WAAAAKHRFWHRkYXRlOnRpbWVzdGFtcAAyMDIzLTA1LTA5VDE1OjEzOjQ4KzAwOjAw7WGnjgAAAABJRU5ErkJggg=="/>
            </defs>
            </svg>
            </div>
            <hr class="manage-horizontal">

            <div>
                <p class="adjust-margin make-text-bold adjust-font-size">
                    Save big on your Med Card 
                </p>
                <p class="adjust-margin make-text-bold adjust-font-size">
                    renewal through NuggMD.
                </p>
            </div>
            <div class="mt-3 ml-2 mr-2 mb-3">
                <p class="nuggmd-highlight-text make-text-bold-1 adjust-font-size-1">
                    Just let NuggMD know when your current card expires, and they will send you a customized Deep Discount Code 30–60 days before expiration.
                </p>
            </div>
            <div class="mt-3 mb-3">
                <button class="learn-more-button-css" @click="handleNuggmdModal()">Learn More
                <svg class="abjust-arrow-position" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 25 42" fill="none">
                <path d="M24.0047 19.3612L5.31415 0.67097C4.88186 0.23834 4.30479 0 3.68948 0C3.07417 0 2.4971 0.23834 2.06481 0.67097L0.688385 2.04706C-0.207266 2.94373 -0.207266 4.40109 0.688385 5.2964L16.3833 20.9913L0.67097 36.7036C0.238681 37.1362 0 37.713 0 38.3279C0 38.9436 0.238681 39.5203 0.67097 39.9533L2.0474 41.329C2.48003 41.7617 3.05676 42 3.67207 42C4.28738 42 4.86445 41.7617 5.29674 41.329L24.0047 22.6218C24.438 22.1878 24.676 21.6083 24.6746 20.9923C24.676 20.3739 24.438 19.7948 24.0047 19.3612Z" fill="white"/>
                </svg>
                </button>
            </div>
            <div style="width:100%;font-weight:bolder;font-size:0.8rem;" class="text-left pb-2 ml-2 mb-2 mr-2 nuggmd-card">
                <p class="nuggmd-note">
                    * Discounts Available to all CanPay Members in Medical markets
                </p>
            </div>
            <div>
            </div>
        </div>
        </div>
    </div>
    <div>
    <b-modal
        ref="member-benfit-modal"
        id="member-benfit-modal"
        size="xl"
        hide-footer
        no-close-on-backdrop
    >
    <div class="dashed-border iframe-sponsor-container">
        <iframe src="https://nugg.typeform.com/to/eEYWRnGb" title="NuggMD Website"></iframe>
    </div>
    </b-modal>
    </div>
</div>
</template>
<script>
import api from "../api/rewardwheel.js";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default { 
components:{
    CanPayLoader
},
data(){
    return {
        isLoading:false,
        coupon_codes:"",
        props: ['params'],
        currentUser:"",
        couponCodeData:[],
        show_copied:false
    }
},
mounted(){
  this.$root.$emit("changeWhiteBackground", [false, false, "LinkAnMemberBenefit"]);
    var element = document.getElementsByClassName("content-wrap");
    if (element[0]) {
        element[0].style.setProperty("background-color", "#149240");
        element[0].style.height = "114vh";
        if(window.innerWidth>1200){
        element[0].style.height = "121vh";
        }
    }
    this.coupon_codes = localStorage.getItem("coupon_code");
    localStorage.removeItem("coupon_code");
    this.getMemberBenefit();
},
methods:{
    showModal(modal){
        this.$refs[modal].show();
    },
    hideModal(modal){
        this.$refs[modal].hide();
    },
    refirectTo(link){
        let self = this;
        window.open(link);
    },
    cpyToClpbrd(value){
        var self = this;
        self.show_copied = true;
        setTimeout(()=>{
            self.show_copied = false;
        },500);
        navigator.clipboard.writeText(value);
    },
    handleNuggmdModal(){
        let self = this;
        api
        .logNugmdSiteVisit()
        .then((response)=>{
            self.showModal('member-benfit-modal');
        })
        .catch((error) => {
            console.log(error);
        })
    },
    getMemberBenefit(){
        let self = this;
        self.isLoading = true;
        this.currentUser = localStorage.getItem("consumer_login_response")
        ? JSON.parse(localStorage.getItem("consumer_login_response"))
        : null;
        const payload = {
            user_id : this.currentUser.user_id
        }
        api
        .getMemberBenefit(payload)
        .then((response)=>{
            self.isLoading = false;
            self.couponCodeData = response.data;
        })
        .catch((error) => {
            self.isLoading = false;
            console.log(error);
        })
    }
}
}
</script>
<style scoped>
.iframe-sponsor-container iframe {
  width: 100vw;
  height: 100vh;
  border: 0;
  margin:0px;
}
.iframe-sponsor-container {
  width: 100vw;
  height: 92vh;
  border: 0;
}
.manage-horizontal{
    margin-top:0px;
    border:0.1px solid rgba(0, 0, 0, 0);
}
.adjust-padding{
    border-radius:8px;
}
.adjust-margin{
    margin:0px!important;
}
.make-text-bold{
    font-weight:700;
    font-family:'Open Sans';
}
.make-text-bold-1{
    font-weight:600;
    font-family:'Open Sans';
}
.adjust-font-size{
    font-size:1.1rem;
}
.adjust-font-size-1{
    font-size:0.8rem;
}
.adjust-font-size-2{
    font-size:1.2rem;
}
.add-decoration-line{
    text-decoration:line-through;
}
/* CSS */
.learn-more-button-css {
    width:200px;
  align-items: center;
  appearance: none;
  background-color: #1e4a7d;
  background-image: linear-gradient(1deg, #1e4a7d, #1e4a7d 99%);
  background-size: calc(100% + 20px) calc(100% + 20px);
  border-radius: 100px;
  border-width: 0;
  box-shadow: none;
  box-sizing: border-box;
  color: #FFFFFF;
  cursor: pointer;
  display: inline-flex;
  font-family: CircularStd,sans-serif;
  font-size: 1rem;
  height: auto;
  justify-content: center;
  line-height: 1.5;
  padding: 6px 20px;
  position: relative;
  text-align: center;
  text-decoration: none;
  transition: background-color .2s,background-position .2s;
  user-select: none;
  -webkit-user-select: none;
  touch-action: manipulation;
  vertical-align: top;
  white-space: nowrap;
}

.learn-more-button-css:active,
.learn-more-button-css:focus {
  outline: none;
}

.learn-more-button-css:hover {
  background-position: -20px -20px;
}

.learn-more-button-css:focus:not(:active) {
  box-shadow: rgba(40, 170, 255, 0.25) 0 0 0 .125em;
}
.abjust-arrow-position{
    position:relative;
    right:-23%;
}
#member-benfit-modal___BV_modal_header_{
  background-color: #ffffff;
  padding:5px;
  border: 0;
}
#member-benfit-modal___BV_modal_content_{
  background-color: transparent!important;
  margin: 0px;
  padding:0px;
  border: 0;
}
.copied-css{
    font-size: 8px;
    position: absolute;
    top: 16.8rem;
    z-index: 9999999;
    font-weight: bold;
    color: #149240;
}
#member-benfit-modal___BV_modal_body_ {
  background-color: #ffffff;
  margin: 0px;
  height:82vh;
  overflow-y: hidden;
  padding:0px;
  border: 0;
}
#member-benfit-modal___BV_modal_content_{
    height:90vh;
}
@media only screen and (min-width: 374px) and (max-width:376px){
  #member-benfit-modal___BV_modal_content_{
      height:87vh;
  }
}
@media only screen and (min-width: 359px) and (max-width:361px){
  #member-benfit-modal___BV_modal_content_{
      height:88vh;
}
}
@media only screen and (min-width: 359px) and (max-width:361px){
  #member-benfit-modal___BV_modal_content_{
      height:88vh;
  }
}
@media only screen and (min-width: 539px) and (max-width:541px){
  #member-benfit-modal___BV_modal_content_{
      height:88vh;
  }
}
@media (max-width: 600px) {
  .nuggmd-note {
    text-align: left !important;
    display: block;
    margin-left: 1rem; /* push whole block in */
    margin-right:1rem;
  }
}
@media (max-width: 600px) {
  .nuggmd-card {
    text-align: left !important;
  }
}
.nuggmd-highlight-text {
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: center;
  line-height: 1.5;   /* Optional: improve readability */
  margin: 0.75rem 0;  /* Optional: vertical spacing */
}

</style>
