<template>
<div>
    <div class="wheel-container">
      <div class="wheel-skeleton"></div>
      <div id="wheelOfFortune" :style="wheelCustomOptions.wheel && wheelCustomOptions.wheel.outer_shadow ? 'box-shadow: 0px 0px 45px 2px #fffeec96; border-radius: 100%;' : ''">
          <div id="triangle">
            <img v-if="wheelCustomOptions.wheel && wheelCustomOptions.wheel.pointer_arrow" style="width: 46px; margin-top: -3px;" :src="wheelCustomOptions.wheel.pointer_arrow" />
          </div>
          <div id="spin" :class="availableSpinCount <= 0 ? 'spin-btn wheel-pointer-block' : 'spin-btn wheel-pointer'" @click="calculatePrizeOnServer()"
          :style="wheelCustomOptions.wheel && wheelCustomOptions.wheel.cp_shadow ?  'box-shadow: 1px 1px 25px 20px #57575754;' : ''"
          >
              <img v-if="wheelCustomOptions.wheel && wheelCustomOptions.wheel.cp_logo" style="width: 50px;" :src="wheelCustomOptions.wheel.cp_logo" />
          </div>
          <canvas style="border-radius: 100%;" :class="availableSpinCount <= 0 ? 'wheel-pointer-block' : 'wheel-pointer'" @click="calculatePrizeOnServer()" id="canvas" width="440" height="440">
              <p style="{color: white}" align="center">Sorry, your browser doesn't support canvas. Please try another.</p>
          </canvas>

          <div v-if="wheelLoader" class="wheelOfFortuneLayer">
            <div class="spinner-border" role="status">
              <span class="sr-only">Loading...</span>
            </div>
          </div>
      </div>
    </div>


    <b-modal
    ref="rw-winprize-modal"
    hide-footer
    v-b-modal.modal-center
    :no-close-on-backdrop="wheelError == true ? false : true"
    hide-header
    id="rw-winprize-modal"
    class="rw-winprize-modal"
    centered
    title="BootstrapVue"
    style
    >
      <div class="rw-winprize-modal-body" :style="wheelError ? 'height: auto;' : ''">
        
          <div class="modal-winprize-layer">
            <svg
            v-if="
            (winValue.segment_value_type == 'jackpot amount') || 
            (winValue.segment_value_type == 'amount') ||
            (winValue.segment_value_type == 'spin') ||
            (winValue.segment_value_type == 'point') ||
            (winValue.segment_value_type == 'transaction amount multiplier')
            "
            width="60px" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 84.42109 85.02992">
              <g id="Layer_2" data-name="Layer 2">
                <g id="Ñëîé_2" data-name="Ñëîé 2">
                  <g>
                    <path id="Shape" d="M81.77808,47.61436c-5.231-21.20567-19.17327-28.995-22.26273-30.48478a7.35637,7.35637,0,0,1-2.49217.97823C61.88794,20.94349,73.00313,29.238,77.54884,47.6594,83.443,71.565,66.55609,74.56465,60.91711,74.62469l4.22923-.045c5.639-.06,22.52591-3.05967,16.63174-26.9653Z" fill="#fec108"/>
                    <path id="Shape-2" data-name="Shape" d="M63.566,1.63389a11.379,11.379,0,0,0-2.26725,1.57786c.01436,3.20174-.62824,9.71953-5.422,13.54771a4.40817,4.40817,0,0,1-.58774.41513A7.61313,7.61313,0,0,1,51.283,18.33107l4.22923-.045a7.61314,7.61314,0,0,0,4.00592-1.15648,4.40742,4.40742,0,0,0,.58773-.41513c4.91821-3.92961,5.46644-10.703,5.41932-13.80429A1.40983,1.40983,0,0,0,63.566,1.63389Z" fill="#fec108"/>
                    <path id="Shape-3" data-name="Shape" d="M33.1886,11.47421l.14981,14.06926a46.24238,46.24238,0,0,0-8.318,13.28529l-.08016-7.528c-.02492-2.34018-4.14739-4.18556-9.20838-4.13167-.23965.00255-.46521.005-.70472.02161l-.16527-15.5213c.02492,2.34018,4.14739,4.18555,9.20838,4.13166S33.21352,13.81439,33.1886,11.47421Z" fill="#fc9206"/>
                    <path id="Shape-4" data-name="Shape" d="M24.94028,31.30072l.24363,22.88016c-3.30614.66967-5.61616,2.20286-5.5974,3.965l.15011,14.09746c-.02492-2.34018-4.14739-4.18556-9.20837-4.13167a18.49422,18.49422,0,0,0-3.52092.36177L6.61359,31.49586c.02492,2.34018,4.1474,4.18555,9.20838,4.13166S24.9652,33.64089,24.94028,31.30072Z" fill="#fc9206"/>
                    <path id="Shape-5" data-name="Shape" d="M19.73662,72.24338l.07506,7.04872c.02492,2.34018-4.05733,4.27292-9.11831,4.32681S1.50991,81.82742,1.485,79.48725l-.07506-7.04873c.02492,2.34018,4.1474,4.18555,9.20838,4.13166S19.76154,74.58355,19.73662,72.24338Z" fill="#f7ca3c"/>
                    <path id="Shape-6" data-name="Shape" d="M65.14635,74.57966l-27.053.28806L37.9132,57.95078c-.02492-2.34018-4.14739-4.18556-9.20838-4.13167a18.49412,18.49412,0,0,0-3.52091.36177l-.16347-15.35212C29.36876,28.3844,36.89387,20.74719,43.61372,17.32711a7.64832,7.64832,0,0,0,4.00114,1.043l7.89458-.08406a7.61313,7.61313,0,0,0,4.00591-1.15648c3.08946,1.4898,17.03177,9.2791,22.26273,30.48477C87.67225,71.52,70.78533,74.51962,65.14635,74.57966Z" fill="#f7ca3c"/>
                    <path id="Shape-7" data-name="Shape" d="M65.52523,2.91014c.04712,3.10129-.50111,9.87468-5.42214,13.80432a4.40752,4.40752,0,0,1-.58774.41513,7.61311,7.61311,0,0,1-4.00591,1.15648l-7.89457.08406a7.64841,7.64841,0,0,1-4.00114-1.043c-.213-.13873-.42592-.27745-.62494-.43042C37.9852,13.07273,37.307,6.3124,37.27394,3.211a1.40982,1.40982,0,0,1,1.93163-1.31769c1.81259.74205,2.43694,2.44139,5.17184,2.41227,3.51027-.03738,3.48025-2.85687,6.99052-2.89425s3.55438,2.782,7.07875,2.74444c2.7349-.02912,3.32278-1.75547,5.1193-2.52185a1.40982,1.40982,0,0,1,1.95925,1.27625Z" fill="#f7ca3c"/>
                    <ellipse id="Oval" cx="24.02525" cy="11.57179" rx="9.16386" ry="4.22948" transform="translate(-0.12185 0.25646) rotate(-0.61007)" fill="#f7ca3c"/>
                    <path id="Shape-8" data-name="Shape" d="M24.94028,31.30072c.02492,2.34017-4.05732,4.27291-9.11831,4.3268S6.63851,33.836,6.61359,31.49586c-.02371-2.2274,3.67865-4.0856,8.41359-4.3052.23951-.01665.46507-.01906.70472-.02161C20.79289,27.11516,24.91536,28.96054,24.94028,31.30072Z" fill="#f7ca3c"/>
                    <path id="Shape-9" data-name="Shape" d="M19.73662,72.24338c.02492,2.34017-4.05732,4.27292-9.11831,4.3268S1.43485,74.7787,1.40993,72.43852c-.01876-1.76218,2.29126-3.29538,5.5974-3.965a18.49422,18.49422,0,0,1,3.52092-.36177C15.58923,68.05782,19.7117,69.9032,19.73662,72.24338Z" fill="#f7ca3c"/>
                    <path id="Shape-10" data-name="Shape" d="M37.9132,57.95078c.02492,2.34018-4.05732,4.27292-9.11831,4.32681s-9.18346-1.79149-9.20838-4.13167c-.01876-1.76218,2.29126-3.29537,5.5974-3.965a18.4942,18.4942,0,0,1,3.52091-.36177C33.76581,53.76522,37.88828,55.6106,37.9132,57.95078Z" fill="#f7ca3c"/>
                    <path id="Shape-11" data-name="Shape" d="M37.9132,57.95078l.19515,18.32669c.02491,2.34018-4.05733,4.27292-9.11832,4.32681s-9.18345-1.79149-9.20837-4.13167l-.19515-18.32669c.02492,2.34018,4.14739,4.18556,9.20838,4.13167S37.93812,60.291,37.9132,57.95078Z" fill="#fc9206"/>
                    <path id="Shape-12" data-name="Shape" d="M66.07876,19.67366c6.99936.45278,9.28171,3.33852,9.3489,3.42522a1.40983,1.40983,0,0,0,2.31771-1.60519c-.10312-.15117-2.091-2.92725-7.79863-4.14667a6.46689,6.46689,0,0,0,2.30682-4.87887A7.23945,7.23945,0,0,0,66.79277,5.959a25.7172,25.7172,0,0,0,.15622-3.071A2.81964,2.81964,0,0,0,63.0009.33857a8.05416,8.05416,0,0,0-1.949,1.27979A3.63457,3.63457,0,0,1,58.42743,2.746a3.63877,3.63877,0,0,1-2.6437-1.07158,6.4852,6.4852,0,0,0-8.79963.0937,3.62608,3.62608,0,0,1-2.62734,1.1277,3.62463,3.62463,0,0,1-2.64229-1.07159A8.067,8.067,0,0,0,39.74435.58761a2.81966,2.81966,0,0,0-3.88727,2.6328c.03735,3.50745.79756,9.75651,5.23365,13.91079a38.89488,38.89488,0,0,0-6.37915,4.96171L34.59835,11.4592C34.56412,8.245,29.99188,5.86863,23.96522,5.9328s-10.54728,2.53735-10.513,5.75157l.15124,14.20318c-4.93873.54606-8.42958,2.80383-8.39956,5.62332l.38181,35.85687c-3.3962.95119-5.60948,2.8302-5.58547,5.08579l.07506,7.04872c.03422,3.21422,4.60647,5.59057,10.63313,5.5264,5.38946-.05739,9.5656-2.04329,10.36426-4.763A15.83657,15.83657,0,0,0,29.00505,82.014c6.02666-.06418,10.54727-2.53735,10.513-5.75157L39.323,57.93576c-.03422-3.21422-4.60647-5.59057-10.63313-5.5264a20.92,20.92,0,0,0-2.11349.12825L26.35,31.2857c-.03287-3.08734-4.25472-5.39411-9.92806-5.51558l-.10874-10.21219a16.0551,16.0551,0,0,0,7.77207,1.65283,16.055,16.055,0,0,0,7.73513-1.81795l.10237,9.61446c-.91655,1.04886-1.83915,2.19084-2.74636,3.45531a1.40983,1.40983,0,0,0,2.29024,1.64072A42.736,42.736,0,0,1,34.33605,26.548c.01114-.01281.02087-.0256.032-.037a37.06148,37.06148,0,0,1,9.27664-7.59806,8.947,8.947,0,0,0,2.18293.7l-3.5982,3.43054a1.41042,1.41042,0,0,0,1.94765,2.04055l5.583-5.31981.44971-.00479.045,4.22924a1.40982,1.40982,0,0,0,2.81949-.03l-.04511-4.23629.44971-.00479,5.695,5.19972a1.41042,1.41042,0,1,0,1.90376-2.08155l-3.69456-3.367a8.88161,8.88161,0,0,0,2.15341-.749c3.74743,1.97627,16.08011,9.81091,20.87474,29.23206,2.085,8.45415,1.4351,14.94522-1.93063,19.28832-4.30595,5.55153-11.8761,5.91412-13.34928,5.9298l-20.00429.213a1.40982,1.40982,0,0,0,.03,2.81949l20.00429-.213c2.39657-.02552,10.57487-.60749,15.547-7.02192,3.91923-5.05958,4.741-12.35754,2.43939-21.69058C79.51694,32.55245,71.70328,24.08176,66.07876,19.67366Zm-55.5355,49.8478c4.662-.04964,7.77161,1.60913,7.78362,2.73693s-3.06155,2.85241-7.72358,2.902-7.77161-1.60913-7.78362-2.73693S5.88123,69.5711,10.54326,69.52146Zm.1351,12.68771c-4.662.04964-7.77162-1.60914-7.78363-2.73693L2.86124,76.3271a16.05514,16.05514,0,0,0,7.77208,1.65283,16.05508,16.05508,0,0,0,7.73512-1.818l.03349,3.14514C18.41394,80.43491,15.34038,82.15952,10.67836,82.20917ZM28.975,79.19453c-4.662.04964-7.77161-1.60913-7.78362-2.73693l-.15358-14.4231a16.05512,16.05512,0,0,0,7.77208,1.65283,16.05518,16.05518,0,0,0,7.73512-1.818l.15358,14.4231C36.71061,77.42027,33.63705,79.14489,28.975,79.19453Zm7.52844-21.22874c.012,1.12779-3.06155,2.85241-7.72358,2.90205s-7.77161-1.60913-7.78362-2.73693,3.06155-2.85241,7.72357-2.90205S36.49145,56.838,36.50346,57.96579Zm-18.32669.19514.10854,10.19387A16.05512,16.05512,0,0,0,10.51323,66.702a20.91707,20.91707,0,0,0-2.11349.12825L8.0649,35.38444A16.05514,16.05514,0,0,0,15.837,37.03727a16.05508,16.05508,0,0,0,7.73512-1.818l.19013,17.85584C20.366,54.02634,18.15275,55.90534,18.17677,58.16093Zm5.35377-26.8452c.012,1.12779-3.06155,2.85241-7.72358,2.902s-7.77161-1.60913-7.78362-2.73693,3.06155-2.85241,7.72358-2.902S23.51853,30.18793,23.53054,31.31573Zm.52474-16.92445c-4.662.04964-7.77162-1.60914-7.78362-2.73693S19.3332,8.80194,23.99523,8.7523s7.77162,1.60913,7.78362,2.73693S28.7173,14.34164,24.05528,14.39128Zm45.37879-1.89311c.0246,2.31058-2.45751,4.23333-5.594,4.28929A7.28082,7.28082,0,0,1,62.279,16.625a16.7163,16.7163,0,0,0,4.03485-7.83974,4.3197,4.3197,0,0,1,3.12018,3.71289ZM39.97288,4.04227a6.36393,6.36393,0,0,0,4.4139,1.67308,6.3625,6.3625,0,0,0,4.38009-1.76672,3.71065,3.71065,0,0,1,5.27245-.05614,6.35688,6.35688,0,0,0,4.41812,1.673,6.3625,6.3625,0,0,0,4.3815-1.76674,5.90435,5.90435,0,0,1,1.28349-.88217c.02788,2.6179-.38957,9.09522-4.894,12.69191a6.0467,6.0467,0,0,1-3.52556,1.24865,30.49289,30.49289,0,0,0,2.80642-6.686,1.41061,1.41061,0,1,0-2.726-.72668,25.863,25.863,0,0,1-3.47629,7.4658l-1.44922.01543a26.10735,26.10735,0,0,1-3.63588-7.39007,1.41012,1.41012,0,1,0-2.70851.786,30.51464,30.51464,0,0,0,2.951,6.63038,6.06231,6.06231,0,0,1-3.62473-1.17816c-4.58-3.49855-5.13524-9.96551-5.163-12.57495a5.51379,5.51379,0,0,1,1.29623.84342Z"/>
                    <path id="Shape-13" data-name="Shape" d="M76.2515,65.51274a13.53438,13.53438,0,0,0,2.50414-7.93059,1.40984,1.40984,0,0,0-1.35639-1.46173,1.42535,1.42535,0,0,0-1.46173,1.35639A10.79638,10.79638,0,0,1,74.021,63.78539c-2.71343,3.5029-7.93043,3.73469-8.94967,3.74554a1.40982,1.40982,0,1,0,.03,2.81949c1.24621-.01327,7.63981-.30834,11.15018-4.83768Z"/>
                    <path id="Shape-14" data-name="Shape" d="M47.67634,47.97748a1.40982,1.40982,0,0,0-2.81949.03,5.6393,5.6393,0,0,0,5.699,5.57893l.015,1.40975a1.40982,1.40982,0,0,0,2.81949-.03l-.015-1.40975a5.6393,5.6393,0,0,0-.12009-11.278l-.06-5.639a2.81965,2.81965,0,0,1,2.84952,2.78946,1.40982,1.40982,0,1,0,2.81949-.03,5.63929,5.63929,0,0,0-5.699-5.57893l-.015-1.40975a1.40982,1.40982,0,0,0-2.81949.03l.015,1.40975a5.6393,5.6393,0,1,0,.12009,11.278l.06,5.639A2.81965,2.81965,0,0,1,47.67634,47.97748ZM47.58628,39.519a2.81964,2.81964,0,0,1,2.78946-2.84951l.06,5.639A2.81965,2.81965,0,0,1,47.58628,39.519Zm5.699,5.57894a2.81965,2.81965,0,0,1,.06005,5.639Z"/>
                  </g>
                </g>
              </g>
            </svg>
            <svg v-else-if="winValue.segment_value_type == 'loser'" width="60px" height="60px" viewBox="0 0 179 217" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M88.4377 143.658C127.25 143.658 158.714 112.194 158.714 73.3808C158.714 34.568 127.25 3.10403 88.4377 3.10403C49.6249 3.10403 18.1609 34.568 18.1609 73.3808C18.1609 112.194 49.6249 143.658 88.4377 143.658Z" fill="#F7CA3C"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M9.4344 144.129C5.89698 144.129 3.06704 146.959 3.06704 150.496L3.5387 207.567C3.5387 211.104 6.36864 213.934 9.90606 213.934L168.618 213.227C172.156 213.227 174.986 210.397 174.986 206.859L174.514 149.553C174.514 146.016 171.684 143.186 168.147 143.186L9.4344 144.129Z" fill="#6AC9EA"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M20.7509 74.5598C20.9868 113.236 52.5877 144.601 91.4993 144.365C93.1501 144.365 94.5651 144.365 96.2159 144.129C57.5401 141.771 28.0616 108.283 30.6557 69.6074C32.7782 34.7048 60.37 6.64123 95.5084 4.04712C56.8326 1.68884 23.345 30.9315 20.9868 69.6074C20.9868 71.2582 20.7509 72.909 20.7509 74.5598Z" fill="#FC9206"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M9.4344 144.129C5.89698 144.129 3.06704 146.959 3.06704 150.496L3.5387 207.567C3.5387 211.104 6.36864 213.934 9.90606 213.934L19.3392 213.934C15.8018 213.934 12.9718 211.104 12.9718 207.567L12.736 150.496C12.736 146.959 15.5659 144.129 19.1033 144.129L9.4344 144.129Z" fill="#007EE5"/>
            <path d="M28.7732 169.834C31.1315 169.363 33.7256 169.834 35.6122 171.485L37.9705 173.608L41.9796 168.655L39.6213 166.533C36.0839 163.703 31.3673 162.76 26.8866 163.703C23.585 164.646 21.2267 167.476 20.7551 171.014C20.2834 174.079 21.6984 177.145 24.2925 178.796C24.5283 178.796 24.5283 179.032 24.7641 179.032C25 179.032 28.3016 180.682 32.3106 182.097C33.4898 182.569 35.8481 183.512 35.6122 185.399C34.9048 187.286 33.254 188.465 31.3673 188.229C29.0091 188.229 26.6508 187.286 25 185.635L22.8775 183.277L18.1609 187.522L20.2834 189.88C23.1133 192.946 27.1224 194.596 31.3673 194.596C36.3197 194.832 40.8005 191.295 41.7438 186.578C42.4513 183.041 40.8005 178.56 34.4331 176.202C31.3673 175.023 28.5374 173.843 27.5941 173.608C27.1224 173.136 26.8866 172.664 27.1224 171.957C27.1224 170.778 27.8299 170.07 28.7732 169.834Z" fill="black"/>
            <path d="M150.929 193.889V179.032L159.891 165.118L154.467 161.581L147.628 172.193L140.553 161.581L135.364 165.118L144.562 179.032V193.889H150.929Z" fill="black"/>
            <path d="M104.471 172.664C104.236 167.24 99.7549 162.995 94.3308 162.995H84.6619L84.8977 194.125H91.2651V183.984L100.462 194.832L105.179 190.823L97.6324 181.862C101.642 180.682 104.471 176.909 104.471 172.664ZM94.5666 175.966H91.0292V169.363H94.5666C96.4533 169.127 98.1041 170.542 98.1041 172.193C98.1041 173.843 96.9249 175.73 95.2741 175.73C95.0383 175.966 94.8025 175.966 94.5666 175.966Z" fill="black"/>
            <path d="M47.1676 178.796C47.1676 187.521 54.2425 194.36 62.7323 194.36C71.458 194.36 78.297 187.285 78.297 178.796C78.297 170.07 71.2221 163.231 62.7323 163.231C54.2425 163.231 47.1676 170.07 47.1676 178.796ZM72.1655 178.56C72.1655 183.748 68.1564 187.757 62.9681 187.757C57.7799 187.757 53.7708 183.748 53.7708 178.56C53.7708 173.372 57.7799 169.363 62.9681 169.363C67.9205 169.363 72.1655 173.607 72.1655 178.56Z" fill="black"/>
            <path d="M131.12 172.429C130.884 167.004 126.403 162.76 120.979 162.76H111.31L111.546 193.889H117.913V183.748L127.111 194.596L131.827 190.587L124.045 181.626C128.054 180.447 130.884 176.673 131.12 172.429ZM120.979 175.73H117.442V169.127H120.979C122.866 168.891 124.517 170.306 124.517 171.957C124.753 173.844 123.338 175.494 121.687 175.494C121.451 175.73 121.215 175.73 120.979 175.73Z" fill="black"/>
            <path d="M168.146 140.12L118.622 140.356C155.647 123.612 172.155 80.2197 155.411 43.1946C138.667 6.40543 95.2746 -10.1025 58.2496 6.40543C51.1748 9.70702 44.5716 13.9519 38.6758 19.376L42.9208 24.0925C70.2768 -0.905251 112.49 0.745548 137.724 28.1016C162.957 55.4577 161.071 97.6709 133.715 122.905C106.359 148.138 64.1453 146.252 38.9117 118.895C15.8005 93.426 15.5647 54.2785 38.6758 28.5733L33.9593 24.3284C6.83902 54.2785 9.19731 100.737 39.3833 127.857C45.279 133.045 51.8822 137.29 58.9571 140.592L9.43313 140.827C4.24491 140.827 0 145.072 0 150.496L0.235828 207.567C0.235828 212.755 4.48074 217 9.90479 217L140.082 216.293V209.925L9.90479 210.633C8.25399 210.633 6.83902 209.218 6.60319 207.567L6.36736 150.496C6.36736 148.846 7.78233 147.431 9.43313 147.195L168.146 146.487C169.796 146.487 171.211 147.902 171.447 149.553L171.683 206.624C171.683 208.274 170.268 209.689 168.617 209.925H159.184V216.293H168.617C173.805 216.293 178.05 212.048 178.05 206.624L177.815 149.553C177.579 144.365 173.334 140.12 168.146 140.12Z" fill="black"/>
            <path d="M146.215 210.161H152.582V216.529H146.215V210.161Z" fill="black"/>
            <path d="M86.0772 104.51C90.7938 104.51 95.0387 108.048 95.5103 113L101.878 112.292C100.934 103.567 92.9162 97.4352 84.1906 98.3785C76.8799 99.3219 71.22 104.982 70.2767 112.292L76.6441 113C77.3516 108.283 81.3607 104.51 86.0772 104.51Z" fill="black"/>
            <path d="M118.857 24.0925H125.225V30.4598H118.857V24.0925Z" fill="black"/>
            <path d="M106.122 17.7252H112.489V24.0925H106.122V17.7252Z" fill="black"/>
            <path d="M50.9402 78.8045L58.4867 68.1922L66.0332 78.8045H72.6364L61.7883 63.2398L72.6364 47.911H66.0332L58.4867 58.5233L50.9402 47.911H44.3371L55.1852 63.2398L44.3371 78.8045H50.9402Z" fill="black"/>
            <path d="M110.13 78.8045L117.441 68.1922L124.987 78.8045H131.826L120.978 63.2398L131.826 47.911H124.987L117.441 58.5233L110.13 47.911H103.291L114.139 63.2398L103.291 78.8045H110.13Z" fill="black"/>
            </svg>
            <svg v-else width="60px" height="60px" viewBox="0 0 213 186" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H213V186H0V0Z" fill="url(#pattern0)"/>
            <defs>
            <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
            <use xlink:href="#image0_10067_408" transform="scale(0.00469484 0.00537634)"/>
            </pattern>
            <image id="image0_10067_408" width="213" height="186" xlink:href="data:image/png;base64,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"/>
            </defs>
            </svg>
          </div>

          <p class="rw-modal-winner" v-if="winValue.segment_value_type == 'spin'">Winner!</p>
          <p class="rw-modal-winner" v-if="winValue.segment_value_type == 'point'">Winner!</p>
          <p class="rw-modal-winner" v-if="winValue.segment_value_type == 'amount'">Winner!</p>
          <p class="rw-modal-winner" v-if="winValue.segment_value_type == 'jackpot amount'">Winner!</p>
          <p class="rw-modal-winner" v-if="winValue.segment_value_type == 'transaction amount multiplier'">Winner!</p>
          <p class="rw-modal-winner" v-if="winValue.segment_value_type == 'loser'">Sorry you didn't win!</p>

          <p v-if="wheelError == false" class="rw-modal-prize d-flex align-items-center mb-0">
            <svg 
            v-if="
            ((winValue.segment_value_type == 'jackpot amount') || 
            (winValue.segment_value_type == 'amount') || 
            (winValue.segment_value_type == 'transaction amount multiplier') || 
            (winValue.segment_value_type == 'point')) && winValue.is_generic_point == 1
            "
            class="mr-2" width="35" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M58.0327 44.4525C60.969 38.2154 59.7673 32.2515 59.4272 30.6694C59.0417 28.8825 58.0667 24.66 54.5183 20.8699C52.8282 19.0889 50.821 17.6413 48.6004 16.6018C45.6075 15.2019 43.0567 14.9287 41.6622 14.7921C35.9144 14.223 31.1643 15.9075 28.5114 17.114C29.8031 15.8716 31.2794 14.8387 32.8875 14.0523C34.3086 13.3704 35.8149 12.8842 37.3656 12.6069C39.9046 12.1197 42.5076 12.0697 45.0633 12.4589C46.2423 12.641 51.1626 13.4946 56.0714 17.3188C63.225 22.8844 66.1613 30.6125 64.4947 39.5016C61.9553 53.1026 48.9632 61.0697 35.7897 57.5641C31.9125 56.5284 27.8312 54.0814 26.0173 51.6912C26.108 51.6912 26.2101 51.6685 26.2667 51.7026C26.7202 51.9985 27.1624 52.3172 27.6272 52.6131C34.9962 57.2568 42.5465 57.291 50.0515 53.1367C51.0019 52.6094 51.9047 52.0001 52.7497 51.3156C56.0034 48.6751 57.4885 45.6021 58.0327 44.4525Z" fill="black"/>
            <path d="M65.1182 45.2722C65.0389 45.7502 64.9028 46.4331 64.6874 47.2412C63.1229 53.023 59.5518 56.6765 58.0213 58.2016C52.4209 63.79 45.9022 65.4062 42.9206 66.1119C36.7873 67.5573 31.8331 66.9427 30.0532 66.6582C23.6479 65.6338 19.2492 63.0388 17.9001 62.1852C15.1477 60.4502 12.6808 58.2961 10.5878 55.8001C8.14327 52.8969 6.22821 49.5834 4.93068 46.012C4.60191 45.1014 3.35485 41.5276 3.03741 36.6335C2.74266 31.9671 3.44554 28.4843 3.6156 27.6762C4.17003 25.0362 5.06567 22.4803 6.27977 20.0733C6.91463 18.8441 10.1456 12.8346 17.2879 8.35027C18.9431 7.30316 23.2738 4.84474 29.407 3.91145C31.0282 3.66105 45.0066 1.72618 55.0171 10.5242C59.6539 14.5988 62.386 20.0505 62.8168 20.9269C63.4801 22.2806 64.0557 23.6759 64.54 25.104C64.3813 24.8877 63.2023 23.226 63.1683 23.1805C58.8149 16.7044 53.7587 13.6541 53.7587 13.6541C51.8468 12.5265 49.7911 11.665 47.6481 11.0932C40.8346 9.24941 35.0981 10.8884 33.7377 11.2981C25.7565 13.6996 21.0291 20.0847 20.757 20.4603C15.9388 27.1299 16.0408 34.0954 16.1315 36.0303C16.4376 42.5406 19.2718 47.1387 20.5642 48.9939C20.6663 49.1191 20.8137 49.3126 20.995 49.5403C21.1311 49.7224 21.2671 49.9045 21.4032 50.0866C24.7816 54.5482 29.2596 57.6553 34.2365 59.2032C37.9454 60.3419 41.8646 60.6138 45.6942 59.9984C49.5239 59.3829 53.1626 57.8962 56.3321 55.6522C59.121 53.6718 60.8102 51.6117 61.8532 50.337C63.2363 48.6525 64.2453 46.9908 64.5741 46.3306C64.6194 46.251 65.0956 45.2722 65.1182 45.2722Z" fill="#007EE5"/>
            <path d="M28.7061 45.6C22.7202 45.6 19.7273 41.6278 19.7273 35.5387C19.7273 29.2447 22.879 25.4546 29.0462 25.4546C31.427 25.4546 33.3429 26.0237 34.8167 27.2301C36.1998 28.4707 36.9707 30.1552 37.1634 32.2949H34.76C33.8984 32.2949 33.2749 31.8966 32.9008 31.1112C32.2432 29.7113 30.9508 28.9943 29.0576 28.9943C25.3617 28.9943 23.7179 31.612 23.7179 35.5501C23.7179 39.3743 25.2937 42.0717 28.9329 42.0717C31.427 42.0717 32.8554 40.6945 33.2862 38.532H37.1521C36.8007 43.0392 33.6377 45.6 28.7061 45.6Z" fill="black"/>
            <path d="M49.3373 25.2747H42.5465C41.4015 25.2747 40.4945 26.2762 40.4945 27.4258V45.3063H44.5758V38.17H49.632C53.634 38.17 55.7766 35.6661 55.7766 31.7166C55.7766 27.551 53.5773 25.2747 49.3373 25.2747ZM48.8725 34.5507H44.8932V28.746H49.0765C50.9811 28.746 51.9561 29.7135 51.9561 31.6597C51.9674 33.606 50.9471 34.5848 48.8725 34.5507H48.8725Z" fill="#007EE5"/>
            </svg>
            <svg 
            v-if="
            ((winValue.segment_value_type == 'jackpot amount') || 
            (winValue.segment_value_type == 'amount') || 
            (winValue.segment_value_type == 'transaction amount multiplier') || 
            (winValue.segment_value_type == 'point')) && winValue.is_generic_point == 0
            " width="35" fill="none" xmlns="http://www.w3.org/2000/svg" class="mr-2" viewBox="0 0 50 50">
              <path data-v-03db4a85="" d="M43.6484 32.2123C45.9758 27.2992 45.0233 22.6013 44.7537 21.3551C44.4482 19.9476 43.6754 16.6214 40.8627 13.6359C39.5231 12.233 37.9321 11.0927 36.1719 10.2738C33.7996 9.17106 31.7777 8.95589 30.6724 8.8483C26.1164 8.40003 22.3512 9.72692 20.2485 10.6773C21.2723 9.69865 22.4425 8.88501 23.7171 8.26555C24.8436 7.72841 26.0375 7.34542 27.2667 7.12693C29.2792 6.74323 31.3425 6.70382 33.3683 7.01038C34.3028 7.15383 38.2028 7.82624 42.0938 10.8386C47.7641 15.2228 50.0915 21.3103 48.7705 28.3123C46.7576 39.0261 36.4595 45.3019 26.0176 42.5406C22.9443 41.7247 19.7093 39.7971 18.2715 37.9144C18.3434 37.9144 18.4243 37.8964 18.4692 37.9233C18.8287 38.1564 19.1791 38.4075 19.5476 38.6406C25.3886 42.2985 31.3733 42.3254 37.3222 39.053C38.0754 38.6376 38.791 38.1577 39.4609 37.6185C42.0399 35.5385 43.2171 33.1178 43.6484 32.2123Z" fill="black"></path>
              <path data-v-03db4a85="" d="M49.2647 32.8578C49.2018 33.2344 49.094 33.7723 48.9232 34.4088C47.6831 38.9633 44.8525 41.8412 43.6394 43.0426C39.2002 47.4446 34.0332 48.7177 31.6698 49.2736C26.8083 50.4122 22.8814 49.9281 21.4706 49.7039C16.3934 48.897 12.9067 46.8529 11.8374 46.1805C9.65575 44.8138 7.70037 43.117 6.04133 41.1509C4.1037 38.8639 2.58573 36.2538 1.55724 33.4406C1.29664 32.7233 0.308168 29.9082 0.0565558 26.053C-0.177084 22.3772 0.380057 19.6337 0.514849 18.9972C0.954323 16.9176 1.66425 14.9043 2.62659 13.0083C3.12982 12.04 5.69087 7.30621 11.3521 3.77381C12.6641 2.94899 16.0968 1.01244 20.9583 0.277275C22.2434 0.0800349 33.3233 -1.4441 41.2581 5.48622C44.9334 8.69586 47.099 12.9903 47.4405 13.6807C47.9662 14.747 48.4225 15.8461 48.8064 16.971C48.6806 16.8006 47.746 15.4917 47.7191 15.4558C44.2684 10.3545 40.2606 7.95172 40.2606 7.95172C38.7451 7.06349 37.1157 6.38487 35.4171 5.93449C30.0164 4.48208 25.4694 5.77311 24.3911 6.09587C18.0648 7.98758 14.3176 13.0172 14.1019 13.3131C10.2828 18.5668 10.3637 24.0537 10.4356 25.5778C10.6782 30.7061 12.9247 34.3281 13.9491 35.7895C14.03 35.8881 14.1468 36.0406 14.2906 36.2199C14.3985 36.3633 14.5063 36.5068 14.6141 36.6502C17.292 40.1647 20.8415 42.6123 24.7864 43.8316C27.7263 44.7285 30.8328 44.9427 33.8684 44.4579C36.9039 43.9731 39.7882 42.802 42.3004 41.0343C44.511 39.4743 45.85 37.8516 46.6767 36.8474C47.773 35.5206 48.5728 34.2116 48.8334 33.6916C48.8693 33.6288 49.2467 32.8578 49.2647 32.8578Z" fill="#ECB800"></path>
              <path data-v-03db4a85="" d="M20.3742 32.9385C15.6295 32.9385 13.2571 29.8096 13.2571 25.0131C13.2571 20.0552 15.7553 17.0697 20.6438 17.0697C22.5309 17.0697 24.0495 17.5179 25.2177 18.4683C26.314 19.4455 26.9251 20.7724 27.0778 22.4579H25.1728C24.4898 22.4579 23.9956 22.1441 23.6991 21.5255C23.1779 20.4227 22.1534 19.8579 20.6527 19.8579C17.7233 19.8579 16.4203 21.92 16.4203 25.022C16.4203 28.0344 17.6693 30.1592 20.5539 30.1592C22.5309 30.1592 23.6631 29.0744 24.0046 27.371H27.0689C26.7903 30.9213 24.2832 32.9385 20.3742 32.9385Z" fill="black"></path>
              <path data-v-03db4a85="" d="M36.756 17.1055H31.3733C30.4657 17.1055 29.7468 17.8944 29.7468 18.7999V32.8847H32.9818V27.2634H36.9896C40.1617 27.2634 41.8601 25.291 41.8601 22.1799C41.8601 18.8986 40.1168 17.1055 36.756 17.1055ZM36.3876 24.4123H33.2334V19.8399H36.5493C38.059 19.8399 38.8318 20.602 38.8318 22.1351C38.8408 23.6682 38.032 24.4392 36.3876 24.4123Z" fill="#ECB800"></path>
            </svg>
            <span v-if="winValue.segment_value_type == 'jackpot amount'">{{pointNumberFormatter(winValue.value)}} {{parseInt(winValue.value) > 1 ? 'Points' : 'Point'}}</span>
            <span v-if="winValue.segment_value_type == 'amount'">{{pointNumberFormatter(winValue.value)}} {{parseInt(winValue.value) > 1 ? 'Points' : 'Point'}}</span>
            <span v-if="winValue.segment_value_type == 'spin'">x{{parseInt(winValue.value)}}</span>
            <span v-if="winValue.segment_value_type == 'point'">{{pointNumberFormatter(winValue.value)}} {{parseInt(winValue.value) > 1 ? 'Points' : 'Point'}}</span>
            <span v-if="winValue.segment_value_type == 'transaction amount multiplier'">{{pointNumberFormatter(winValue.value)}} {{parseInt(winValue.value) > 1 ? 'Points' : 'Point'}}</span>

            <span v-if="winValue.segment_value_type == 'loser'">But, you can still be a winner!</span>
          </p>
          <p v-if="wheelError == false" class="rw-modal-content text-center mb-4">{{winValue.text}}</p>
          <p v-if="wheelError == true" class="rw-modal-content text-center mt-5" v-html="wheelErrorMessage"></p>

          <div v-if="winValue && winValue.used_at" class="spin-time-chip mb-3">Last Spin Date: {{formattedTime(winValue.used_at)}}</div>

          <p class="reward-info"
          v-if="
          ((winValue.segment_value_type == 'jackpot amount') || 
          (winValue.segment_value_type == 'amount') ||
          (winValue.segment_value_type == 'point') ||
          (winValue.segment_value_type == 'transaction amount multiplier')) && winValue.is_merchant_wheel == 1
          "
          >
            Rewards will be pending until transaction clears
          </p>
          <button
          v-if="availableSpinCount > 0 &&  wheelError == false && $online"
          type="button"
          class="rw-modal-close-btn mb-3"
          @click="showModal('rw-spin-again-modal')"
          >
          SPIN AGAIN
          </button>
          <button
          v-if="availableSpinCount > 0 &&  wheelError == false"
          type="button"
          class="rw-modal-spin-btn mb-3"
          @click="hideModal('rw-winprize-modal'), wheelError = false, wheelErrorMessage = ''"
          >
          GOT IT
          </button>
          <button
          v-if="availableSpinCount == 0 &&  wheelError == false"
          type="button"
          class="rw-modal-spin-btn mb-3"
          @click="getLastSpinWheelId()"
          >
          GOT IT
          </button>

        <!-- ////////////////////DO NOT REMOVE THIS CODE ////////////////////////// -->
         <!-- <div class="rw-share-text mb-3 mt-4" v-if="wheelError == false">
            Share on social media and get <br> <span>1000</span> extra points!
          </div>
          <button
          v-if="wheelError == false"
          type="button"
          class="rw-modal-share-btn"
          @click="hideModal('rw-winprize-modal')"
          >
          SHARE
          </button> -->
      </div>
    </b-modal>
        <b-modal 
            ref="winprize-modal"
            hide-header
            hide-footer
            centered
            id="rw-winprize-modal"
            :no-close-on-backdrop="true"
        >
        <div class="text-center mb-3">
            <svg  width="80px" height="80px" viewBox="0 0 213 186" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H213V186H0V0Z" fill="url(#pattern0)"/>
            <defs>
            <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
            <use xlink:href="#image0_10067_408" transform="scale(0.00469484 0.00537634)"/>
            </pattern>
            <image id="image0_10067_408" width="213" height="186" xlink:href="data:image/png;base64,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"/>
            </defs>
            </svg>
        </div>
            <div class="p-1">
                <p class="purchasepower-def-label">{{wheelErrorMessage}}</p>
            </div>   
            <div class="text-center mt-3">
            <div class="row">
                <div class="col-6">
                    <div class="row">

                        <div class="col-12">
                                <button class="button-black" @click="cancel()">CANCEL</button>
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="row">

                        <div class="col-12">
                                <button class="button-green" @click="bankLinkPage()">ADD BANK</button>
                        </div>
                    </div>
                </div>
            </div>

               
            </div>
        </b-modal>
    <b-modal
    ref="rw-spin-again-modal"
    hide-footer
    v-b-modal.modal-center
    modal-backdrop
    hide-header
    id="rw-spin-again-modal"
    class="rw-spin-again-modal"
    centered
    title="BootstrapVue"
    >

        <div class="rw-timer-modal-body">
          <div class="modal-wheel-layer">
              <svg width="80" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
              viewBox="0 0 570.4 570.4" style="enable-background:new 0 0 570.4 570.4;" xml:space="preserve">
              <g>
              <circle class="st0" cx="285.2" cy="285.2" r="285.2"/>
              <g>
              <path d="M285.4,286c-12.9,12.8-25.7,25.6-38.6,38.4c-45.7,45.7-91.5,91.4-137.2,137.2c-1.5,1.5-3.1,2.9-5.3,4.9
              c-6.9-8-13.9-15.4-20.1-23.4c-31.9-40.7-49.9-86.8-54-138.4c-0.2-1.6-0.5-3.2-0.9-4.8v-15c85.4,0,170.7,0,256.1-0.1L285.4,286z"/>
              <path d="M541.4,284.9c-85,0-170.1,0-255.1-0.1c-0.3,0-0.7,0.1-1,0.1c1.3-1.5,2.5-3.1,3.9-4.5c57.5-57.5,115-115,172.5-172.5
              c1.3-1.3,2.6-2.5,4.6-4.3c5.8,6.6,11.7,12.6,17,19.2c31.7,39,50.6,83.4,56.2,133.4c0.6,5.3,1.3,10.5,1.9,15.7L541.4,284.9z"/>
              <path class="st1" d="M466.1,103.3C406,163.4,345.8,223.5,285.7,283.6c-0.2,0.2-0.4,0.5-0.6,0.8c-0.2-2-0.5-4-0.5-6
              c0-81.3,0-162.6,0-243.9c0-1.8,0.1-3.6,0.2-6.3c8.8,0.5,17.2,0.7,25.6,1.5c50,5.1,94.7,23.2,134,54.6c4.1,3.3,8.3,6.5,12.5,9.8
              L466.1,103.3z"/>
              <path class="st2" d="M103.8,103.3c60.1,60.1,120.2,120.3,180.3,180.4c0.2,0.2,0.5,0.4,0.8,0.6c-2,0.2-4,0.5-6,0.5
              c-81.3,0-162.6,0-243.9,0c-1.8,0-3.6-0.1-6.3-0.2c0.5-8.8,0.7-17.2,1.5-25.6c5.1-50,23.2-94.7,54.6-134c3.3-4.1,6.5-8.3,9.8-12.5
              L103.8,103.3z"/>
              <path class="st1" d="M104.3,467c60.1-60.1,120.3-120.2,180.4-180.3c0.2-0.2,0.4-0.5,0.6-0.8c0.2,2,0.5,4,0.5,6
              c0,81.3,0,162.6,0,243.9c0,1.8-0.1,3.6-0.2,6.3c-8.8-0.5-17.2-0.7-25.6-1.5c-50-5.1-94.7-23.2-134-54.6c-4.1-3.3-8.3-6.5-12.5-9.8
              L104.3,467z"/>
              <path class="st2" d="M466.5,466.3C406.4,406.2,346.3,346,286.3,285.8c-0.2-0.2-0.5-0.4-0.8-0.6c2-0.2,4-0.5,6-0.5
              c81.3,0,162.6,0,243.9,0.1c1.8,0,3.6,0.1,6.3,0.2c-0.5,8.8-0.7,17.2-1.5,25.6c-5.1,50-23.2,94.7-54.6,134
              c-3.3,4.1-6.5,8.3-9.8,12.5L466.5,466.3z"/>
              <path class="st3" d="M284.5,28.3c0,85,0,170.1-0.1,255.1c0,0.3,0.1,0.7,0.1,1c-1.5-1.3-3.1-2.5-4.5-3.9
              C222.5,223,165,165.5,107.5,108c-1.3-1.3-2.5-2.6-4.3-4.6c6.6-5.8,12.6-11.7,19.2-17c39-31.7,83.4-50.6,133.4-56.2
              c5.3-0.6,10.5-1.3,15.7-1.9H284.5z"/>
              <path class="st4" d="M285.4,540.9c-0.1-1.7-0.2-3.3-0.2-5c0-81.9,0-163.8,0-245.6c0-1.5,0.1-3,0.2-4.5c1.1,0.9,2.4,1.8,3.4,2.8
              c58,57.9,115.9,115.9,173.8,173.8c0.9,0.9,1.8,1.9,3,3.3c-1,1.1-1.9,2.3-2.9,3.3c-37.9,36.2-82.5,59-134.2,67.7
              c-11,1.8-22.1,2.8-33.1,4.2L285.4,540.9z"/>
              </g>
              </g>
              </svg>

          </div>

          <h5 class="text-center mb-5" style="font-weight: 700;">Do you want to spin again?</h5>
          <div class="row justify-content-between w-100">
            <div class="col-6 p-0" style="max-width: 48%;">
              <button
              type="button"
              class="rw-pending-modal-close-btn"
              @click="calculatePrizeOnServer()"
              >
              YES
              </button>
            </div>
            <div class="col-6 p-0" style="max-width: 48%;">
              <button
              type="button"
              class="rw-pending-modal-spin-btn"
              @click="hideModal('rw-spin-again-modal')"
              >
              NO
              </button>
            </div>
          </div>
      </div>
    </b-modal>

    <b-modal
    ref="connection-lost-modal"
    hide-footer
    v-b-modal.modal-center
    no-close-on-backdrop
    hide-header
    id="connection-lost-modal"
    class="connection-lost-modal"
    centered
    title="BootstrapVue"
    >
        <div class="row">
            <div class="col-12 d-flex justify-content-center">
                <svg v-if="$online" fill="#149240" height="130px" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
                    viewBox="0 0 489.3 489.3" xml:space="preserve">
                <g>
                    <g>
                        <path d="M79.55,229.675c-10.2,10.2-10.2,26.8,0,37.1c10.2,10.2,26.8,10.2,37.1,0c70.6-70.6,185.5-70.6,256.1,0
                            c5.1,5.1,11.8,7.7,18.5,7.7s13.4-2.6,18.5-7.7c10.2-10.2,10.2-26.8,0-37.1C318.75,138.575,170.55,138.575,79.55,229.675z"/>
                        <path d="M150.35,300.475c-10.2,10.2-10.2,26.8,0,37.1c10.2,10.2,26.8,10.2,37.1,0c31.5-31.6,82.9-31.6,114.4,0
                            c5.1,5.1,11.8,7.7,18.5,7.7s13.4-2.6,18.5-7.7c10.2-10.2,10.2-26.8,0-37C286.95,248.475,202.35,248.475,150.35,300.475z"/>
                        <circle cx="244.65" cy="394.675" r="34.9"/>
                        <path d="M481.65,157.675c-130.7-130.6-343.3-130.6-474,0c-10.2,10.2-10.2,26.8,0,37.1c10.2,10.2,26.8,10.2,37.1,0
                            c110.2-110.3,289.6-110.3,399.9,0c5.1,5.1,11.8,7.7,18.5,7.7s13.4-2.6,18.5-7.7C491.85,184.575,491.85,167.975,481.65,157.675z"/>
                    </g>
                </g>
                </svg>
                <svg v-else width="130px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="System / Wifi_Problem">
                <path id="Vector" d="M8.34277 14.5898C8.80861 14.0902 9.37187 13.6914 9.9978 13.418C10.6237 13.1445 11.2995 13.0024 11.9826 13C12.6656 12.9976 13.3419 13.1353 13.9697 13.4044C14.5975 13.6734 15.1637 14.0682 15.633 14.5645M6.14941 11.5439C6.89312 10.7464 7.79203 10.1093 8.79091 9.67188C9.7898 9.23441 10.8678 9.00575 11.9583 9M3.22363 8.81649C4.34177 7.61743 5.69376 6.66021 7.19618 6.00391C8.69859 5.3476 10.3198 5.00558 11.9593 5M16 8.99997L18 6.99998M18 6.99998L20 5M18 6.99998L16 5M18 6.99998L20 8.99997M12 19C11.4477 19 11 18.5523 11 18C11 17.4477 11.4477 17 12 17C12.5523 17 13 17.4477 13 18C13 18.5523 12.5523 19 12 19Z" stroke="#f52323" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </g>
                </svg>
            </div>
            <div class="col-12 my-3">
                <h5 class="connectiontext text-center" v-if="$online" >Wow! You are online!</h5>
                <h5 class="connectiontext text-center" v-else>Oops! You are offline!</h5>
            </div>
            <div class="col-12">
                <div class="row">
                    <div class="col-12 mb-3">
                        <button v-if="$online" type="button" class="connection-modal-btn respin" @click="reSpin()">Show Me Last Spin</button>
                        <button v-else class="connection-modal-btn disabled">Show Me Last Spin</button>
                    </div>
                </div>
            </div>
        </div>                
    </b-modal>
</div>
</template>
        
<script>
import api from "../../api/rewardwheel.js";
import reward_api from "../../api/rewardwheel.js";
import moment from "moment";
import $store from '../../store';
export default {
  name: "Wheel",
  props:{
    value: false,
    callTheSpin:false,
    wheelData:{
      default: [],
      type: Array
    },
    wheelCustomOptions:{
      default: {},
      type: Object
    },
    wheelId: {
      default: "",
      type: String
    },
    availableSpinCount:{
      default: 0,
      type: Number
    },
    userSpinResultId:{
      default: '',
      type: String
    },
    bankAlertModel:{
      type:Function
    }
  },
  data() {
    return {
      theWheel: {},
      wheelSpinning: false,
      wheelLoader: false,
      winValue: {},
      free_spins_available: null,
      wheelError: false,
      wheelErrorMessage: '',
      hasWheelEventData: false,
      isOnline: true,
      userSpinWinID: '',
      lastSpinWheelId: null,
      is_daily_spins_available: false,
      default_wheel_id: null,
      user_any_active_bank_account:false,
    };
  },
  computed: {
    canvasSize(){
      if(this.wheelCustomOptions.wheel && this.wheelCustomOptions.wheel.outer){
            return 440;
        }else{
            return 420
        }
    }
  },
  methods: {
    bankLinkPage(){
        this.$router.push('/banklinking');
    },
    openPopup() {
      this.bankAlertModel()
    },
    cancel() {
        this.$refs['winprize-modal'].hide()
        this.$router.push('/pay');
    },
    calculatePrizeOnServer(){
      var self = this;

      if(!self.isOnline){
        return
      }

      if(self.wheelSpinning == true){
        return
      }

      self.hideModal('rw-winprize-modal')
      self.hideModal('rw-spin-again-modal')
      self.hideModal('connection-lost-modal')
      if(self.availableSpinCount <= 0){
        return;
      }

      

      self.resetWheel();
      let segmentNumber = self.getSegnmentIndexNumber(self.userSpinWinID);   // The segment number should be in response.
      // Get random angle inside specified segment of the wheel.
      let stopAt = self.theWheel.getRandomForSegment(segmentNumber);
      // Important thing is to set the stopAngle of the animation before stating the spin.
      self.theWheel.animation.stopAngle = stopAt;

      self.$root.$emit("rewardBackButtonState", {rewardBackButtonState: false});
      self.wheelSpinning = true
      self.hasWheelEventData = false
      self.$emit('input', true)
      self.winValue = {}

      reward_api.getConsumerBankStatus()
      .then((response)=>{
      const user_any_active_bank_account = response.data;

      if(user_any_active_bank_account){
          self.wheelErrorMessage = response.message
          self.openPopup();
          return;
      }else{
          // Start the spin animation here.
        self.theWheel.startAnimation();
        

      api
        .rewardSpinEvent({id: self.wheelId, winner_segment_id: self.userSpinWinID})
        .then(response => {
          self.getUserSpinResult();
          self.hasWheelEventData = true

          if(response.data){
              self.winValue = response.data.reward_details
              self.free_spins_available = response.data.free_spins_available

              self.wheelError = false
              self.wheelErrorMessage = ''
          }else{
            self.$root.$emit("rewardBackButtonState", {rewardBackButtonState: true});
            self.hasWheelEventData = true
            self.wheelLoader = false
            self.wheelError = true
            self.$emit('input', false)
            self.wheelErrorMessage = response.message
            if(self.isOnline){
              self.showModal("rw-winprize-modal");
              self.wheelSpinning = false
              self.resetWheel();
              self.setwheel(self.wheelData);
              self.$root.$emit("rewardBackButtonState", {rewardBackButtonState: true});
            }
          }
        })
        .catch(function (error) {
          self.getUserSpinResult();
          self.hasWheelEventData = true
          self.wheelLoader = false
          self.wheelError = true

          if(self.isOnline){
            self.wheelErrorMessage = error && error.response && error.response.data ? error.response.data.message : ''
          }else{
            self.wheelErrorMessage = ''
          }
          self.$emit('input', false)
          if(self.isOnline){
            self.showModal("rw-winprize-modal");
            self.wheelSpinning = false
            self.resetWheel();
            self.setwheel(self.wheelData);
            self.$root.$emit("rewardBackButtonState", {rewardBackButtonState: true});
          }
        });
      }
      })
      .catch((error) => {
          error(error.response.data.message);
      })


    },
    spinAgain(data){
      var self = this;

      let segmentNumber = self.getSegnmentIndexNumber(data.reward_id);   // The segment number should be in response.

      self.winValue = data.reward_details
      self.free_spins_available = data.free_spins_available
      self.$root.$emit("spinAvailableCount", {spin_available_count: (parseInt(data.free_spins_available))});

      // Get random angle inside specified segment of the wheel.
      let stopAt = self.theWheel.getRandomForSegment(segmentNumber);
      // Important thing is to set the stopAngle of the animation before stating the spin.
      self.theWheel.animation.stopAngle = stopAt;
      // Start the spin animation here.
      self.theWheel.startAnimation();

      self.wheelError = false
      self.wheelErrorMessage = ''
      self.hideModal('connection-lost-modal')
    },
    reSpin(){
      var self = this;
        reward_api.getConsumerBankStatus()
      .then((response)=>{
      const user_any_active_bank_account = response.data;

      if(user_any_active_bank_account){
          self.wheelErrorMessage = response.message
          self.openPopup();
          return;
      }else{
          api
            .reSpin({id: self.wheelId})
            .then(response => {
              if(response.data != null && response.data && response.data.is_respin){
                self.resetWheel();
                self.$root.$emit("rewardBackButtonState", {rewardBackButtonState: false});
                self.wheelSpinning = true
                self.hasWheelEventData = true
                self.$emit('input', true)
                self.winValue = {}
                self.spinAgain(response.data)
              }else{
                self.wheelSpinning = false
                self.calculatePrizeOnServer()
              }
            })
            .catch(function (error) {
              self.hasWheelEventData = true
              self.wheelLoader = false
              self.wheelError = true

              if(self.isOnline){
                self.wheelErrorMessage = error && error.response && error.response.data ? error.response.data.message : ''
              }else{
                self.wheelErrorMessage = ''
              }
              self.$emit('input', false)
            });
      }
      })
      .catch((error) => {
          error(error.response.data.message);
      })

    },
    getSegnmentIndexNumber(id){
      var self = this;
      var indexNumber = self.wheelData.map(object => object.id).indexOf(id)
      return (indexNumber+1);
    },
    showModal(modal) {
      this.$refs[modal].show()
    },
    hideModal(modal) {
      this.$refs[modal].hide();
    },
    resetWheel()
    {
      let self = this;
        self.theWheel.stopAnimation(false);  // Stop the animation, false as param so does not call callback function.
        self.theWheel.rotationAngle = 0;     // Re-set the wheel angle to 0 degrees.
        self.theWheel.draw();                // Call draw to render changes to the wheel.
    },
    setwheel(props){
      let self = this;

      var segments = [];
      props.forEach((definitions, index) => {
        segments.push({
          id: definitions.id,
          image: definitions.image,
          fillStyle: definitions.color ? definitions.color : '#fff',
        });
      });

      // Create new wheel object specifying the parameters at creation time.
      self.theWheel = new Winwheel({
            'numSegments': segments.length, // Specify number of segments.
            'outerRadius': 200, // Set outer radius so wheel fits inside the background.
            'drawText': true, // Code drawn text can be used with segment images.
            'textAlignment': 'inner',
            'strokeStyle': '#0000',
            'drawMode': 'segmentImage', // Must be segmentImage to draw wheel using one image per segemnt.
            'segments' : segments,
            'pins': {
                'margin': -5,
                // Pins 
                'number': this.wheelCustomOptions.wheel && this.wheelCustomOptions.wheel.pin ? (segments.length * this.wheelCustomOptions.wheel.pin.count) : 1,
                'fillStyle': this.wheelCustomOptions.wheel && this.wheelCustomOptions.wheel.pin ? this.wheelCustomOptions.wheel.pin.color : '#fff',
                'strokeStyle': this.wheelCustomOptions.wheel && this.wheelCustomOptions.wheel.pin ? this.wheelCustomOptions.wheel.pin.color : '#fff',
                'outerRadius': this.wheelCustomOptions.wheel && this.wheelCustomOptions.wheel.pin ? this.wheelCustomOptions.wheel.pin.size : '',
                'pinGlow': this.wheelCustomOptions.wheel && this.wheelCustomOptions.wheel.pin ? this.wheelCustomOptions.wheel.pin.glow : '',

                // Inner 
                'borderLineWidth': this.wheelCustomOptions.wheel && this.wheelCustomOptions.wheel.inner ? this.wheelCustomOptions.wheel.inner.size : '',
                'color_1': this.wheelCustomOptions.wheel && this.wheelCustomOptions.wheel.inner ? this.wheelCustomOptions.wheel.inner.color_1 : '#fff',
                'color_2': this.wheelCustomOptions.wheel && this.wheelCustomOptions.wheel.inner ? this.wheelCustomOptions.wheel.inner.color_2 : '#fff',
                'color_3': this.wheelCustomOptions.wheel && this.wheelCustomOptions.wheel.inner ? this.wheelCustomOptions.wheel.inner.color_3 : '#fff',

                // Outer 
                'outerBorderLineWidth': this.wheelCustomOptions.wheel && this.wheelCustomOptions.wheel.outer ? this.wheelCustomOptions.wheel.outer.size : '',
                'outerBorderStrokeStyle': this.wheelCustomOptions.wheel && this.wheelCustomOptions.wheel.outer ? this.wheelCustomOptions.wheel.outer.color : '#fff',
            },
            'animation': // Specify the animation to use.
            {
                'type': 'spinToStop',
                'duration': 10, // 8 10 // Duration in seconds.
                'spins': 4, // 8 // Number of complete spins.
                'callbackFinished': self.callbackPrize,
            },
            'callbackImageLoaded': self.callbackImageLoaded,
            'inner_shadow': this.wheelCustomOptions.wheel && this.wheelCustomOptions.wheel.inner_shadow ? this.wheelCustomOptions.wheel.inner_shadow : ''
        });

    },
    callbackImageLoaded(value){
      var self = this;
      setTimeout(function(){
        self.$root.$emit("setWheelImageLoader", {image_loaded: true})
      }, 1000)
      ;
    },  
    callbackPrize(val){
      var self = this;
      self.$root.$emit("rewardBackButtonState", {rewardBackButtonState: true});
      if(self.hasWheelEventData && !self.wheelError){

        if(self.isOnline && self.wheelErrorMessage){
          self.$emit('input', false)
          self.showModal("rw-winprize-modal");
          self.wheelSpinning = false
        } else if(self.winValue && self.winValue.segment_value_type){
          self.$root.$emit("spinAvailableCount", {spin_available_count: (parseInt(self.winValue.free_spins_available))});
          self.$emit('input', false)
          self.showModal("rw-winprize-modal");
          self.wheelSpinning = false
        }

        if(self.winValue.jackpot_baseline){
          self.$root.$emit("setJackpotBaseline", {jackpot_value: (parseInt(self.winValue.jackpot_baseline))});
        }
        
        if(self.wheelError == false){
          self.$root.$emit("spinAvailableCount", {spin_available_count: self.free_spins_available});
          self.$root.$emit("rewardAvailablePoint", {available_reward_points: self.winValue.available_reward_points});
        }
      }
    },
    getLastSpinWheelId(){
      var self = this;
      api
      .getLastSpinWheelId()
      .then((response) => {
          self.lastSpinWheelId = response.data;
          self.hideModal('rw-winprize-modal');
          if(self.lastSpinWheelId !== null){
            self.getSpinData();
            if(self.lastSpinWheelId && self.is_daily_spins_available === false){
              $store.commit('setRewardWheelID', self.lastSpinWheelId);
            }else{
              $store.commit('setRewardWheelID', self.default_wheel_id);
            }
            self.$parent.getWheelData();
          }
          else {
            this.$router.push('/pay');
          }
      })
      .catch(function (error) {
          alert(error.respnse.data)
      });
    },
    formattedTime(datetime){
      return moment(datetime).format('DD MMM Y');
    },
    getUserSpinResult(){
      var self = this;
      api
      .getUserSpinResult({id: self.wheelId})
      .then(response => {
        self.userSpinWinID = response.data
      })
      .catch(function (error) {
      });
    },
    getSpinData(){
      var self = this;
      api
      .getSpinData()
      .then((response) => {
          self.is_daily_spins_available = parseInt(response.data.daily_spins) > 0 ? true : false;
          self.default_wheel_id = response.data.default_wheel_id;
      })
      .catch(function (error) {
          
      });
    },
  },
  mounted() {
    var self = this;
    self.userSpinWinID = self.userSpinResultId
    self.setwheel(self.wheelData);
    self.resetWheel();  
  },
  watch: {
    callTheSpin(newval, oldval){
      if(newval == true){
        this.calculatePrizeOnServer();
      }
    },
    wheelData: {
      handler(val) {
        let self = this;
        self.setwheel(self.wheelData);
        self.resetWheel();
      },
      deep: true,
    },
    '$online': {
      handler(val) {
        let self = this;
        self.isOnline = self.$online;
        if(!self.$online){
          self.$refs["connection-lost-modal"].show()
        }
      },
      deep: true,
    },
    'userSpinResultId': {
      handler(val) {
        let self = this;
        self.userSpinWinID = self.userSpinResultId;
      },
      deep: true,
    }
  },
};
</script>

<style lang="scss">
.button-black{
    width:100%;
    padding: 15px 25px;
    background-color: #010002;
    color: snow;
    border-radius: 35px;
    font-size:15px;
    font-weight: 600;
    border:none;
}
.button-green{
     width:100%;
    padding: 15px 25px;
    background-color: #149240;
    color:snow;
    border-radius: 35px;
    font-size:15px;
    font-weight: 600;
    border:none;
}
.wheel-container{
  min-height: auto;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
#wheelOfFortune {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}

#canvas{
  width: 100%;
}

#wheel {
  display: block;
}

#spin {
  width: 50px;
  height: 50px;
  background: #3c3c3c;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
#triangle{
  position: absolute;
  top: 2px;
  left: 50%;
  transform: translateX(-50%);
}

// WIN MODAL
.reward-info{
  font-size: 17px;
  text-align: center;
  font-weight: 500;
}
.rw-winprize-modal{
  background-color: #ffffff;
}
#rw-winprize-modal___BV_modal_body_{
  background-color: #ffffff;
  border-radius: 6px;
  padding: 50px 20px 20px 20px;
  position: relative;
}
#rw-winprize-modal___BV_modal_content_{
  background-color: #ffffff;
  border-radius: 6px;
  margin: 10px;
  margin-bottom: -60px; // 85
}

.modal-winprize-layer{
  position: absolute;
  top: -107px;
  background: white;
  padding: 28px;
  border-radius: 100%;
  border: 7px solid #2ed769;
}
.rw-winprize-modal-body{
    display: flex;
    flex-flow: column;
    align-items: center;
    position: relative;
}
.rw-modal-winner{
  font-size: 33px;
    font-weight: 600;
    margin-top: 35px;
    margin-bottom: 0;
    font-family: 'Circular-Loom';
}
.rw-modal-prize{
  font-size: 22px;
    font-weight: 700;
    margin-bottom: 10px;
    color: #007EE5;
    font-family: 'Open Sans';
}
.rw-modal-content{
  font-size: 16px;
    margin-bottom: 25px;
    font-weight: 600;
}
.wheel-pointer{
  cursor: pointer;
}
.wheel-pointer-block{
  cursor: no-drop;
}
.rw-modal-spin-btn{
  border: 0;
    padding: 17px 30px;
    background: #000000;
    color: white;
    font-weight: bold;
    border-radius: 30px;
    font-size: 19px;
    width: 70%;
}
.rw-modal-close-btn{
  border: 0;
    padding: 17px 30px;
    background: #089338;
    color: white;
    font-weight: bold;
    border-radius: 30px;
    font-size: 19px;
    width: 70%;
}
.rw-share-text{
  text-align: center;
    font-weight: 500;
    font-size: 17px;
    line-height: 17px;
}
.rw-share-text span{
  color: #0dc04c;
  font-weight: bold;
}
.rw-modal-share-btn{
  border: 0;
    padding: 17px 30px;
    background: #d9d9d9;
    color: #000000;
    font-weight: bold;
    border-radius: 30px;
    font-size: 19px;
    width: 70%;
}

// SPIN AGAIN MODAL 
.rw-spin-again-modal{
  background-color: #ffffff;
}
#rw-spin-again-modal___BV_modal_body_{
  background-color: #ffffff;
    border-radius: 6px;
    padding: 50px 30px;
    position: relative;
}
#rw-spin-again-modal___BV_modal_content_{
  background-color: #ffffff;
  border-radius: 6px;
  margin: 10px;
}
.rw-pending-modal-spin-btn{
  border: 0;
  background: #000000;
  color: white;
  font-weight: bold;
  border-radius: 30px;
  font-size: 16px;
  width: 100%;
  padding: 15px;
}
.rw-pending-modal-close-btn{
  border: 0;
  background: #089338;
  color: white;
  font-weight: bold;
  border-radius: 30px;
  font-size: 16px;
  width: 100%;
  padding: 15px;
}
.modal-pending-layer{
  position: absolute;
  top: -73px;
  background: white;
  padding: 3px;
  border-radius: 100%;
  border: 5px solid #0cb346;
  left: 50%;
  transform: translateX(-50%);
}

.wheelOfFortuneLayer{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #75757596;
  border-radius: 100%;
}

#connection-lost-modal___BV_modal_body_{
    background-color: #fff;
    border-radius: 10px;
}
#connection-lost-modal___BV_modal_content_{
    border-radius: 10px;
}
.connection-modal-btn{
    width: 100%;
    border: 0;
    padding: 15px 20px;
    border-radius: 10px;
    font-size: 20px;
}
.connection-modal-btn.exit{
    background: black;
    color: #fff;
}
.connection-modal-btn.respin{
    background: #149240;
    color: #fff;
}
.connection-modal-btn.disabled{
    background: #88d6a3;
    color: #fff;
    cursor: no-drop;
}
.connection-modal-btn.exit-disabled{
    background: #686868;
    color: #fff;
    cursor: no-drop;
}
.connectiontext{
  font-size: 30px;
}

.spin-time-chip{
  color: #000000;
  font-weight: 700;
  background: whitesmoke;
  padding: 2px 15px;
  border-radius: 50px;
  border: 1px solid #dcdcdc;
}

@media only screen and ( min-width:280px) and ( max-width:700px) {
  .modal-winprize-layer svg{
    width: 55px;
  }
  .modal-winprize-layer{
    position: absolute;
    top: -100px;
    background: white;
    padding: 25px;
    border-radius: 100%;
    border: 7px solid #2ed769;
  }
  .rw-winprize-modal-body{
      display: flex;
      flex-flow: column;
      align-items: center;
      position: relative;
  }
  .rw-modal-winner{
    font-size: 21px;
    font-weight: 600;
    margin-top: 25px;
    margin-bottom: 0;
    font-family: 'Circular-Loom';
  }
  .rw-modal-prize{
    font-size: 19px;
      font-weight: 700;
      margin-bottom: 10px;
      color: #007EE5;
      font-family: 'Open Sans';
  }
  .rw-modal-content{
    font-size: 13px;
      margin-bottom: 25px;
      font-weight: 600;
  }
  .wheel-pointer{
    cursor: pointer;
  }
  .wheel-pointer-block{
    cursor: no-drop;
  }
  .rw-modal-spin-btn{
    border: 0;
      padding: 10px 25px;
      background: #000000;
      color: white;
      font-weight: bold;
      border-radius: 30px;
      font-size: 10px;
      width: 70%;
  }
  .rw-modal-close-btn{
    border: 0;
    padding: 10px 25px;
    background: #089338;
    color: white;
    font-weight: bold;
    border-radius: 30px;
    font-size: 10px;
    width: 70%;
  }
  .rw-share-text{
    text-align: center;
    font-weight: 500;
    font-size: 10px;
    line-height: 10px;
  }
  .rw-share-text span{
    color: #0dc04c;
    font-weight: bold;
  }
  .rw-modal-share-btn{
    border: 0;
      padding: 10px 25px;
      background: #d9d9d9;
      color: #000000;
      font-weight: bold;
      border-radius: 30px;
      font-size: 10px;
      width: 70%;
  }

  // SPIN AGAIN MODAL 
  .rw-spin-again-modal{
    background-color: #ffffff;
  }
  #rw-spin-again-modal___BV_modal_body_{
    background-color: #ffffff;
      border-radius: 6px;
      padding: 50px 30px;
      position: relative;
  }
  #rw-spin-again-modal___BV_modal_content_{
    background-color: #ffffff;
    border-radius: 6px;
    margin: 10px;
  }
  .rw-pending-modal-spin-btn{
    border: 0;
    background: #000000;
    color: white;
    font-weight: bold;
    border-radius: 30px;
    font-size: 16px;
    width: 100%;
    padding: 15px;
  }
  .rw-pending-modal-close-btn{
    border: 0;
    background: #089338;
    color: white;
    font-weight: bold;
    border-radius: 30px;
    font-size: 16px;
    width: 100%;
    padding: 15px;
  }
  .modal-pending-layer{
    position: absolute;
    top: -73px;
    background: white;
    padding: 3px;
    border-radius: 100%;
    border: 5px solid #0cb346;
    left: 50%;
    transform: translateX(-50%);
  }
}

@media only screen and ( min-width:320px) and ( max-width:700px) {
  .modal-winprize-layer svg{
    width: 55px;
  }
  .modal-winprize-layer{
    position: absolute;
    top: -100px;
    background: white;
    padding: 25px;
    border-radius: 100%;
    border: 7px solid #2ed769;
  }
  .rw-winprize-modal-body{
      display: flex;
      flex-flow: column;
      align-items: center;
      position: relative;
  }
  .rw-modal-winner{
    font-size: 22px;
    font-weight: 600;
    margin-top: 25px;
    margin-bottom: 0;
    font-family: 'Circular-Loom';
  }
  .rw-modal-prize{
    font-size: 19px;
      font-weight: 700;
      margin-bottom: 10px;
      color: #007EE5;
      font-family: 'Open Sans';
  }
  .rw-modal-content{
    font-size: 13px;
      margin-bottom: 25px;
      font-weight: 600;
  }
  .wheel-pointer{
    cursor: pointer;
  }
  .wheel-pointer-block{
    cursor: no-drop;
  }
  .rw-modal-spin-btn{
    border: 0;
      padding: 15px 25px;
      background: #000000;
      color: white;
      font-weight: bold;
      border-radius: 30px;
      font-size: 15px;
      width: 70%;
  }
  .rw-modal-close-btn{
    border: 0;
    padding: 15px 25px;
    background: #089338;
    color: white;
    font-weight: bold;
    border-radius: 30px;
    font-size: 15px;
    width: 70%;
  }
  .rw-share-text{
    text-align: center;
    font-weight: 500;
    font-size: 13px;
    line-height: 13px;
  }
  .rw-share-text span{
    color: #0dc04c;
    font-weight: bold;
  }
  .rw-modal-share-btn{
    border: 0;
      padding: 15px 25px;
      background: #d9d9d9;
      color: #000000;
      font-weight: bold;
      border-radius: 30px;
      font-size: 15px;
      width: 70%;
  }

  // SPIN AGAIN MODAL 
  .rw-spin-again-modal{
    background-color: #ffffff;
  }
  #rw-spin-again-modal___BV_modal_body_{
    background-color: #ffffff;
      border-radius: 6px;
      padding: 50px 30px;
      position: relative;
  }
  #rw-spin-again-modal___BV_modal_content_{
    background-color: #ffffff;
    border-radius: 6px;
    margin: 10px;
  }
  .rw-pending-modal-spin-btn{
    border: 0;
    background: #000000;
    color: white;
    font-weight: bold;
    border-radius: 30px;
    font-size: 16px;
    width: 100%;
    padding: 15px;
  }
  .rw-pending-modal-close-btn{
    border: 0;
    background: #089338;
    color: white;
    font-weight: bold;
    border-radius: 30px;
    font-size: 16px;
    width: 100%;
    padding: 15px;
  }
  .modal-pending-layer{
    position: absolute;
    top: -73px;
    background: white;
    padding: 3px;
    border-radius: 100%;
    border: 5px solid #0cb346;
    left: 50%;
    transform: translateX(-50%);
  }
}


@media only screen and ( min-width:376px) and ( max-width:800px) {
  .modal-winprize-layer svg{
    width: 65px;
  }
  .modal-winprize-layer{
    position: absolute;
    top: -120px;
    background: white;
    padding: 35px;
    border-radius: 100%;
    border: 7px solid #2ed769;
  }
  .rw-winprize-modal-body{
      display: flex;
      flex-flow: column;
      align-items: center;
      position: relative;
  }
  .rw-modal-winner{
    font-size: 30px;
    font-weight: 600;
    margin-top: 35px;
    margin-bottom: 0;
    font-family: 'Circular-Loom';
  }
  .rw-modal-prize{
    font-size: 21px;
    font-weight: 700;
    margin-bottom: 10px;
    color: #007EE5;
    font-family: 'Open Sans';
  }
  .rw-modal-content{
    font-size: 16px;
      margin-bottom: 7px;
      font-weight: 600;
  }
  .wheel-pointer{
    cursor: pointer;
  }
  .wheel-pointer-block{
    cursor: no-drop;
  }
  .rw-modal-spin-btn{
    border: 0;
      padding: 17px 30px;
      background: #000000;
      color: white;
      font-weight: bold;
      border-radius: 30px;
      font-size: 19px;
      width: 70%;
  }
  .rw-modal-close-btn{
    border: 0;
      padding: 17px 30px;
      background: #089338;
      color: white;
      font-weight: bold;
      border-radius: 30px;
      font-size: 19px;
      width: 70%;
  }
  .rw-share-text{
    text-align: center;
      font-weight: 500;
      font-size: 17px;
      line-height: 17px;
  }
  .rw-share-text span{
    color: #0dc04c;
    font-weight: bold;
  }
  .rw-modal-share-btn{
    border: 0;
      padding: 17px 30px;
      background: #d9d9d9;
      color: #000000;
      font-weight: bold;
      border-radius: 30px;
      font-size: 19px;
      width: 70%;
  }

  // SPIN AGAIN MODAL 
  .rw-spin-again-modal{
    background-color: #ffffff;
  }
  #rw-spin-again-modal___BV_modal_body_{
    background-color: #ffffff;
      border-radius: 6px;
      padding: 50px 30px;
      position: relative;
  }
  #rw-spin-again-modal___BV_modal_content_{
    background-color: #ffffff;
    border-radius: 6px;
    margin: 10px;
  }
  .rw-pending-modal-spin-btn{
    border: 0;
    background: #000000;
    color: white;
    font-weight: bold;
    border-radius: 30px;
    font-size: 16px;
    width: 100%;
    padding: 15px;
  }
  .rw-pending-modal-close-btn{
    border: 0;
    background: #089338;
    color: white;
    font-weight: bold;
    border-radius: 30px;
    font-size: 16px;
    width: 100%;
    padding: 15px;
  }
  .modal-pending-layer{
    position: absolute;
    top: -73px;
    background: white;
    padding: 3px;
    border-radius: 100%;
    border: 5px solid #0cb346;
    left: 50%;
    transform: translateX(-50%);
  }
  
}
</style>