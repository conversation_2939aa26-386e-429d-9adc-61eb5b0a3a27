<template>
<div style="box-shadow: rgb(0, 0, 0) 1px 0px 2px;
    border-radius: 0px 0px 14px 18px;
    padding: 0px 0px 20px 0px;position: relative;z-index: 11;background-color: white;">
  <div class="container">
    <div>
      <div style="display:flex;" id="row-header" >
        <!--Code for ToggleDrawer-->
        <div
          class="canpay-crew-position-for-store-header"
        >
          <a href="javascript:void(0)" v-on:click="changeComponent(1)">
              <svg data-v-5a215274="" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 67 56" height="20" width="20" style="" fill="none"><path data-v-5a215274="" d="M2.38113 25.5833L26.6029 1C27.9167 -0.333333 30.0515 -0.333333 31.3652 1C32.6789 2.33333 32.6789 4.5 31.3652 5.83333L12.8909 24.5833H63.6336C65.5221 24.5833 67 26.0833 67 28C67 29.9167 65.5221 31.4167 63.6336 31.4167H12.8909L31.3652 50.1667C32.6789 51.5 32.6789 53.6667 31.3652 55C30.7083 55.6667 29.8873 56 28.9841 56C28.0809 56 27.2598 55.6667 26.6029 55L2.38113 30.4167L0 28L2.38113 25.5833Z" fill="black"></path></svg>
          </a>
        </div>
        <div class="text-left pr-0" id="col-header">
          <label class="transaction-history-lable" style="font-size:18px!important;margin-left:28px;">Petition a Store</label>
        </div>
        <!--Code for CanPay Logo-->
      </div>
    </div>
  </div>
</div>
</template>
<script>
import DrawerLayout from "vue-drawer-layout";
export default {
  name: "CanPayCrewStoreHeader",
  props:{
    changeComponent:{
      type:Function
    }
  },
  data: () => ({
    open: true,
    showmenuicon: true,
    consumer_type: localStorage.getItem("consumer_login_response")
        ? JSON.parse(localStorage.getItem("consumer_login_response")).consumer_type
        : null,
  }),
  components: {
    DrawerLayout
  },
  mounted() {
    let self = this;
    self.$root.$on("Menu Drawer Close", function(data) {
      self.showmenuicon = true;
    });

    self.$root.$on("Menu Drawer Open", function(data) {
      setTimeout(function() {
        self.showmenuicon = false;
      }, 30);
    });
  },
  methods: {
    showDrawer() {
      this.showmenuicon = false;
      this.$root.$emit("Menu Drawer", [""]);
    }
  }
};
</script>
<style scoped>

</style>
