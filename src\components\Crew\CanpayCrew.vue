<template>
<div>
    <div v-if="isLoading">
        <CanPayLoader/>
    </div>
<div  v-else>
    <div style=" margin-top:55px;position: absolute;width: 100%;" v-if="canPayCrewLanding == 1">
        <div class="canpay-crew-text" id="find-height-for-crew-describe-div">
        <p class="canpay-crew-text-bold canpay-crew-text-font-27 canpay-crew-p-margin-bottom">Welcome to CanPay Crew!</p>
        <p class="canpay-crew-text-700 canpay-crew-text-font-18 canpay-crew-p-margin-bottom">Win Extra Points Every Month</p>
        <div class="canpay-crew-text-font-12">
        <p class="canpay-crew-p-margin-bottom-1  mt-4">Do you wish a store would start accepting CanPay?</p>

        <p class="canpay-crew-p-margin-bottom-1  mt-4">Now you can Create or Join a petition </p>
        <p class="canpay-crew-p-margin-bottom-1 ">for that store and win extra points to spend at that </p>
        <p class="canpay-crew-p-margin-bottom-1 ">store when they do start accepting CanPay.</p>
        </div>
        </div>
        <div>
        <div class="canpay-crew-info-box mt-4" id="find-height-for-petition-div">
            <p class="canpay-crew-info-text" style="margin: 20px 0px 25px 0px;">
            {{
                petitionStatistics.consumer_active_petition_count > petition_per_consumer
                ? `${petitionStatistics.consumer_active_petition_count} of ${petitionStatistics.consumer_active_petition_count}`
                : `${petitionStatistics.consumer_active_petition_count} of ${petition_per_consumer}`
            }} Active Petitions
                <svg v-on:click="viewPetition()" style="margin-left: 10px;cursor: pointer;" xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 91 91" fill="none">
                <circle cx="45.5" cy="45.5" r="45.5" fill="#1B9142"/>
                <path d="M23.5 45.5008H65.4848M65.4848 45.5008L47.0296 27.207M65.4848 45.5008L47.0296 63.7947" stroke="white" stroke-width="5"/>
                </svg>
            </p>
            <div>
            <div>
            <div style="display:flex;justify-content:space-between;">
                <div class="canpay-crew-petition-box-display">
                    <div>
                    <p :class="!petitionStatistics.show_brand_new_total_store_petition_count?'canpay-crew-p-margin-bottom-1 canpay-crew-info-box-header-text':'brand-new-text canpay-crew-p-margin-bottom-1'" style="text-align:left;">{{!petitionStatistics.show_brand_new_total_store_petition_count?petitionStatistics.total_store_petition_count:'New'}}</p>
                    <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-600 canpay-crew-petition-description-text" style="text-align:left;">Total Petitions Created</p>
                    </div>
                </div>
                <div class="canpay-crew-petition-box-display">
                    <div>
                    <p :class="!petitionStatistics.show_brand_new_total_signers?'canpay-crew-p-margin-bottom-1 canpay-crew-info-box-header-text':'brand-new-text  canpay-crew-p-margin-bottom-1'" style="text-align:left;">{{!petitionStatistics.show_brand_new_total_signers?petitionStatistics.total_signers:'New'}}</p>
                    <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-600 canpay-crew-petition-description-text" style="text-align:left;">CanPay Crew Signers</p>
                    </div>
                </div>
            </div>

            <div style="display:flex;justify-content:space-between;margin-top:14px;">
                <div class="canpay-crew-petition-box-display">
                    <div>
                    <p :class="!petitionStatistics.show_brand_new_total_accept_canpay_stores?'canpay-crew-p-margin-bottom-1 canpay-crew-info-box-header-text':'brand-new-text  canpay-crew-p-margin-bottom-1'"  style="text-align:left;">{{!petitionStatistics.show_brand_new_total_accept_canpay_stores?petitionStatistics.total_accept_canpay_stores_via_crew:'New'}}</p>
                    <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-600 canpay-crew-petition-description-text"  style="text-align:left;">Total Crew Stores</p>
                    <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-600 canpay-crew-petition-description-text"  style="text-align:left;">Now Accepting CanPay</p>
                    </div>
                </div>
                <div class="canpay-crew-petition-box-display">
                    <div>
                    <p :class="!petitionStatistics.show_brand_new_lifetime_rewards_from_crew?'canpay-crew-p-margin-bottom-1 canpay-crew-info-box-header-text':'brand-new-text  canpay-crew-p-margin-bottom-1'"  style="text-align:left;">{{!petitionStatistics.show_brand_new_lifetime_rewards_from_crew?petitionStatistics.lifetime_rewards_from_crew:'New'}}</p>
                    <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-600 canpay-crew-petition-description-text"  style="text-align:left;">Total CanPay Crew</p>
                    <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-600 canpay-crew-petition-description-text"  style="text-align:left;">Points Awarded</p>
                    </div>
                </div>
            </div>
            </div>
            <div>
                <button class="canpay-crew-petitions-button canpay-crew-text-600" style="margin-top:35px;" v-on:click="viewPetition()">Review My Petitions</button>
            </div>
            <div>
                <button class="canpay-crew-store-button canpay-crew-text-600" style="margin-top:12px;" v-on:click="storePage()">Sign or Launch Petition</button>
            </div>
            </div>
        </div>
        </div>
    </div>
    <div style="width:100%;" v-else-if="canPayCrewLanding == 2">
        <!---- Show the find a Store header ----->
        <CanPayCrewStoreHeader :changeComponent="changeComponent"/>
        <!---- showing the find a store page Start ----->
        <div style="background-color: white; position: relative; z-index: 10; top: -10px; padding-top: 50px;">
            <!---- find a store logo ---->
        <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 177 166" fill="none">
            <path d="M158.499 1.77686H96.1063V163.795H158.499C168.121 163.795 175.919 156.281 175.919 146.973V18.5992C175.919 9.29152 168.121 1.77686 158.499 1.77686Z" fill="black"/>
            <path d="M156.81 165.57H94.4181C93.4003 165.57 92.5768 164.776 92.5768 163.795V1.77845C92.5768 0.797597 93.4003 0.00390625 94.4181 0.00390625H156.81C167.43 0.00390625 176.072 8.34633 176.072 18.5985V146.972C176.072 157.227 167.43 165.57 156.81 165.57ZM96.2594 162.021H156.81C165.401 162.021 172.389 155.269 172.389 146.972V18.5985C172.389 10.3011 165.401 3.55299 156.81 3.55299H96.2594V162.021Z" fill="black"/>
            <path d="M167.71 18.5963V146.97C167.71 156.278 159.913 163.792 150.291 163.792H26.03C16.3727 163.792 8.57532 156.278 8.57532 146.97V18.5963C8.57532 9.28901 16.3727 1.77393 26.03 1.77393H150.291C159.913 1.77393 167.71 9.28901 167.71 18.5963Z" fill="white"/>
            <path d="M150.292 165.566H26.03C15.3922 165.566 6.73596 157.224 6.73596 146.968V18.5945C6.73596 8.34242 15.3922 0 26.03 0H150.292C160.912 0 169.554 8.34242 169.554 18.5945V146.968C169.554 157.224 160.912 165.566 150.292 165.566ZM26.03 3.54908C17.4205 3.54908 10.4185 10.2972 10.4185 18.5945V146.968C10.4185 155.265 17.4205 162.017 26.03 162.017H150.292C158.884 162.017 165.871 155.265 165.871 146.968V18.5945C165.871 10.2972 158.884 3.54908 150.292 3.54908H26.03Z" fill="black"/>
            <path d="M167.723 19.2993V31.6397H8.5874V19.2993C8.5874 9.64154 16.385 1.84424 26.0426 1.84424H150.303C159.925 1.84424 167.723 9.64154 167.723 19.2993Z" fill="#A8A8A8"/>
            <path d="M167.723 33.4815H8.588C7.57025 33.4815 6.7467 32.6579 6.7467 31.6402V19.2978C6.7467 8.6601 15.4029 0.00390625 26.0443 0.00390625H150.303C160.923 0.00390625 169.564 8.6601 169.564 19.2978V31.6402C169.564 32.6579 168.741 33.4815 167.723 33.4815ZM10.4293 29.7989H165.882V19.2978C165.882 10.6884 158.894 3.68647 150.303 3.68647H26.0443C17.4348 3.68647 10.4293 10.6884 10.4293 19.2978V29.7989Z" fill="black"/>
            <path d="M26.7483 20.8896C29.0526 20.8896 30.9207 19.0216 30.9207 16.7173C30.9207 14.4129 29.0526 12.5449 26.7483 12.5449C24.444 12.5449 22.5759 14.4129 22.5759 16.7173C22.5759 19.0216 24.444 20.8896 26.7483 20.8896Z" fill="white"/>
            <path d="M41.2059 20.8896C43.5103 20.8896 45.3783 19.0216 45.3783 16.7173C45.3783 14.4129 43.5103 12.5449 41.2059 12.5449C38.9016 12.5449 37.0336 14.4129 37.0336 16.7173C37.0336 19.0216 38.9016 20.8896 41.2059 20.8896Z" fill="white"/>
            <path d="M55.6641 20.8896C57.9684 20.8896 59.8364 19.0216 59.8364 16.7173C59.8364 14.4129 57.9684 12.5449 55.6641 12.5449C53.3597 12.5449 51.4917 14.4129 51.4917 16.7173C51.4917 19.0216 53.3597 20.8896 55.6641 20.8896Z" fill="white"/>
            <path d="M98.8075 18.5605H90.0146C88.9969 18.5605 88.1733 17.737 88.1733 16.7192C88.1733 15.7015 88.9969 14.8779 90.0146 14.8779H98.8075C99.8253 14.8779 100.649 15.7015 100.649 16.7192C100.649 17.737 99.8253 18.5605 98.8075 18.5605Z" fill="black"/>
            <path d="M153.73 18.5605H108.794C107.777 18.5605 106.953 17.737 106.953 16.7192C106.953 15.7015 107.777 14.8779 108.794 14.8779H153.73C154.748 14.8779 155.571 15.7015 155.571 16.7192C155.571 17.737 154.748 18.5605 153.73 18.5605Z" fill="black"/>
            <path d="M31.3222 31.6577L26.0314 53.047C25.3413 56.919 21.9674 59.718 18.0186 59.718H10.0055C4.67629 59.718 0.765829 54.6955 2.10761 49.558L8.58698 31.6577H31.3222Z" fill="#179346"/>
            <path d="M18.0173 61.558H10.0048C6.87964 61.558 3.99183 60.141 2.08221 57.6668C0.183376 55.2142 -0.456761 52.0854 0.327227 49.0933L6.85806 31.0329C7.12059 30.3029 7.81108 29.8174 8.58787 29.8174H31.3235C31.8881 29.8174 32.424 30.0763 32.7728 30.5222C33.1217 30.9682 33.2475 31.5508 33.1109 32.101L27.8208 53.488C27.0008 58.1127 22.8651 61.558 18.0173 61.558ZM9.87893 33.5L3.83719 50.183C3.39845 51.9056 3.80123 53.8728 4.99519 55.4156C6.20354 56.9799 8.02685 57.8754 10.0048 57.8754H18.0173C21.0777 57.8754 23.6886 55.7069 24.2209 52.722L28.9716 33.5H9.87893Z" fill="black"/>
            <path d="M54.0576 31.6577L50.9521 52.4718C50.4921 56.6125 47.0033 59.718 42.8242 59.718H34.6195C29.5205 59.718 25.6482 55.1172 26.5683 50.0948L31.3224 31.6577H54.0576Z" fill="#DDDDDD"/>
            <path d="M42.8238 61.558H34.6207C31.6286 61.558 28.8163 60.2417 26.9067 57.9473C25.0043 55.6673 24.2239 52.686 24.7561 49.7658L29.5392 31.1983C29.7514 30.3856 30.4814 29.8174 31.3229 29.8174H54.0586C54.5944 29.8174 55.1051 30.0511 55.454 30.4575C55.8028 30.8639 55.9574 31.3997 55.8783 31.932L52.7711 52.7471C52.2173 57.7387 47.9377 61.558 42.8238 61.558ZM32.7507 33.5L28.3524 50.557C28.0431 52.2688 28.5358 54.1533 29.7334 55.5918C30.9453 57.0411 32.7255 57.8754 34.6207 57.8754H42.8238C46.0605 57.8754 48.7649 55.4659 49.1209 52.2688L51.9224 33.5H32.7507Z" fill="black"/>
            <path d="M76.7929 31.6577L75.7578 51.8585C75.6043 56.2673 72.0004 59.718 67.6299 59.718H59.272C54.4028 59.718 50.6072 55.5005 51.1438 50.67L54.0576 31.6577H76.7929Z" fill="#179346"/>
            <path d="M67.6316 61.558H59.2739C56.422 61.558 53.6961 60.3424 51.7972 58.2206C49.9056 56.106 48.9993 53.2794 49.3122 50.4671L52.2396 31.3782C52.3762 30.4791 53.1494 29.8174 54.0593 29.8174H76.7913C77.2948 29.8174 77.7767 30.026 78.1256 30.3892C78.4744 30.756 78.6542 31.2487 78.629 31.7522L77.5969 51.9524C77.4099 57.3252 73.0332 61.558 67.6316 61.558ZM55.638 33.5L52.9624 50.949C52.7718 52.6716 53.3292 54.4122 54.5412 55.7644C55.7423 57.1058 57.4685 57.8754 59.2739 57.8754H67.6316C71.0373 57.8754 73.7992 55.2034 73.9179 51.7941L74.8529 33.5H55.638Z" fill="black"/>
            <path d="M100.487 51.2833C100.679 55.884 96.9596 59.718 92.3588 59.718H83.9625C79.3235 59.718 75.6429 55.884 75.7964 51.2833L76.793 31.6577H99.5285L100.487 51.2833Z" fill="#DDDDDD"/>
            <path d="M92.3596 61.558H83.9623C81.2219 61.558 78.6649 60.4755 76.7661 58.5119C74.8637 56.5448 73.8639 53.9591 73.9538 51.2223L74.9536 31.5652C75.0003 30.587 75.8095 29.8174 76.7913 29.8174H99.5269C100.509 29.8174 101.318 30.587 101.365 31.5688L102.325 51.1935C102.325 51.1971 102.328 51.2043 102.328 51.2079C102.44 53.9267 101.458 56.5088 99.5665 58.4796C97.6641 60.4647 95.1035 61.558 92.3596 61.558ZM78.5427 33.5L77.6328 51.377C77.5789 53.0708 78.2082 54.7071 79.413 55.9514C80.6141 57.1921 82.2288 57.8754 83.9623 57.8754H92.3596C94.0894 57.8754 95.7077 57.1849 96.9088 55.9298C98.0992 54.6891 98.7178 53.0708 98.6458 51.3662L97.772 33.5H78.5427Z" fill="black"/>
            <path d="M117.049 59.718H108.692C104.283 59.718 100.679 56.2673 100.525 51.8585L99.5286 31.6577H122.264L125.139 50.67C125.676 55.5005 121.919 59.718 117.049 59.718Z" fill="#179346"/>
            <path d="M117.048 61.558H108.69C103.267 61.558 98.8723 57.3252 98.6853 51.9236L97.6891 31.7486C97.6639 31.2451 97.8437 30.756 98.1926 30.3892C98.5414 30.0224 99.0233 29.8174 99.5268 29.8174H122.262C123.172 29.8174 123.946 30.4827 124.082 31.3818L126.959 50.3916C127.283 53.2938 126.377 56.1276 124.489 58.2386C122.6 60.3496 119.889 61.558 117.048 61.558ZM101.462 33.5L102.364 51.7689C102.483 55.2034 105.263 57.8754 108.69 57.8754H117.048C118.868 57.8754 120.536 57.131 121.745 55.7824C122.939 54.4482 123.507 52.6608 123.309 50.8699L120.68 33.5H101.462Z" fill="black"/>
            <path d="M141.702 59.718H133.458C129.279 59.718 125.791 56.6125 125.331 52.4718L122.263 31.6577H144.96L149.714 50.0948C150.634 55.1172 146.8 59.718 141.702 59.718Z" fill="#DDDDDD"/>
            <path d="M141.7 61.558H133.458C128.344 61.558 124.064 57.7387 123.499 52.6788L120.439 31.9284C120.364 31.3961 120.518 30.8603 120.867 30.4539C121.219 30.0511 121.727 29.8174 122.262 29.8174H144.962C145.804 29.8174 146.537 30.3856 146.746 31.1983L151.496 49.6364C152.061 52.6968 151.281 55.6853 149.378 57.9689C147.476 60.2489 144.678 61.558 141.7 61.558ZM124.395 33.5L127.153 52.2041C127.517 55.4659 130.221 57.8754 133.458 57.8754H141.7C143.581 57.8754 145.35 57.0519 146.548 55.6098C147.749 54.1712 148.242 52.2832 147.9 50.4275L143.534 33.5H124.395Z" fill="black"/>
            <path d="M166.277 59.718H158.264C154.354 59.718 150.98 56.919 150.251 53.047L144.96 31.6577H167.696L174.213 49.558C175.517 54.6955 171.606 59.718 166.277 59.718Z" fill="#179346"/>
            <path d="M166.277 61.558H158.265C153.46 61.558 149.331 58.1199 148.443 53.3837L143.175 32.101C143.038 31.5508 143.164 30.9682 143.513 30.5222C143.861 30.0763 144.397 29.8174 144.962 29.8174H167.694C168.467 29.8174 169.158 30.3029 169.424 31.0293L175.944 48.9279C176.76 52.107 176.102 55.2357 174.192 57.6848C172.276 60.1482 169.391 61.558 166.277 61.558ZM147.314 33.5L152.039 52.6033C152.622 55.6997 155.233 57.8754 158.265 57.8754H166.277C168.248 57.8754 170.075 56.9835 171.287 55.4263C172.491 53.8764 172.909 51.902 172.43 50.0104L166.406 33.5H147.314Z" fill="black"/>
            <path d="M117.589 133.067C115.343 135.313 111.664 135.313 109.418 133.067L93.5806 117.23C91.3338 114.961 91.3338 111.305 93.5806 109.058C94.7039 107.912 96.1797 107.362 97.6554 107.362C99.1311 107.362 100.607 107.912 101.752 109.058L117.589 124.895C119.836 127.142 119.836 130.798 117.589 133.067Z" fill="#179346"/>
            <path d="M113.503 135.857C111.742 135.857 109.98 135.186 108.639 133.846L92.8019 118.008C90.1304 115.311 90.1325 110.947 92.8019 108.279C95.338 105.687 99.9197 105.668 102.531 108.279L118.368 124.117C121.037 126.784 121.037 131.149 118.372 133.842C118.37 133.844 118.37 133.844 118.368 133.846C117.027 135.186 115.265 135.857 113.503 135.857ZM97.6546 108.462C96.3974 108.462 95.2304 108.948 94.3679 109.828C92.5449 111.65 92.546 114.621 94.3636 116.455L110.196 132.289C112.018 134.111 114.982 134.111 116.807 132.293C118.622 130.456 118.624 127.488 116.811 125.675L100.974 109.837C100.087 108.95 98.9098 108.462 97.6546 108.462Z" fill="black"/>
            <path d="M100.144 115.599C92.0384 123.705 78.9106 123.705 70.8049 115.599C62.6992 107.515 62.6992 94.3656 70.8049 86.282C78.9106 78.1762 92.0384 78.1762 100.144 86.282C108.228 94.3656 108.228 107.515 100.144 115.599Z" fill="#A8A8A8"/>
            <path d="M85.4742 122.781C79.6406 122.781 74.1545 120.508 70.0256 116.378C65.8978 112.261 63.6241 106.778 63.6241 100.94C63.6241 95.1041 65.8978 89.6212 70.0267 85.502C74.1545 81.3742 79.6406 79.1006 85.4742 79.1006C91.3078 79.1006 96.7939 81.3742 100.923 85.5041C109.434 94.0157 109.434 107.866 100.923 116.378C96.7939 120.508 91.3078 122.781 85.4742 122.781ZM85.4742 81.3032C80.2289 81.3032 75.2956 83.3488 71.5829 87.0615C67.8703 90.7634 65.8268 95.6935 65.8268 100.94C65.8268 106.188 67.8703 111.118 71.5818 114.818C75.2956 118.533 80.2289 120.578 85.4742 120.578C90.7195 120.578 95.6528 118.533 99.3655 114.82C107.019 107.167 107.019 94.7148 99.3655 87.0615C95.6528 83.3488 90.7195 81.3032 85.4742 81.3032Z" fill="black"/>
            <path d="M85.4687 115.627C93.5802 115.627 100.156 109.051 100.156 100.94C100.156 92.8281 93.5802 86.2524 85.4687 86.2524C77.3572 86.2524 70.7815 92.8281 70.7815 100.94C70.7815 109.051 77.3572 115.627 85.4687 115.627Z" fill="white"/>
        </svg>
        <p class="canpay-crew-p-margin-bottom canpay-crew-text-font-27 canpay-crew-text-700">Petition a Store</p>
        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-font-16 canpay-crew-text-600">Petition your favorite stores to accept</p>
        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-font-16 canpay-crew-text-600" style="padding-bottom: 25px;">CanPay, and win points when they do!</p>
        <!------- search input field ----------->
        <div class="container" style="justify-content: center;display: flex;padding-bottom:25px;">
            <div class="canpay-crew-search">
                <div>
                    <div class="text-left canpay-crew-padding-left-none pl-3"
                        style="width: 100%;display: flex;border-radius:10px;position:relative;"
                        v-click-outside="closeDropdown"
                    >
                         <input
                            style="padding: 8px;margin-left: 5px;"
                            class="canpay-crew-input-search"
                            type="text"
                            placeholder="Search Store (min 3 char)"
                            v-model="storeObj.store_name"
                            @input="onSearchInput"
                            @focus="showStoreDropdown = true"
                            @keydown.down.prevent="navigateDropdown(1)"
                            @keydown.up.prevent="navigateDropdown(-1)"
                            @keydown.enter.prevent="selectHighlightedStore"
                            @keydown.esc.prevent="closeDropdown"
                         >
                         <div class="canpay-crew-search-icon-button" v-on:click="handleSearchIconClick">
                            <svg style="position: relative;top: 5.5px;left: 9px;"
                               xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 42 43" fill="#ffffff">
                            <path d="M41.303 38.0504L33.7652 30.5642C39.6509 23.0264 39.0313 12.2876 32.268 5.52428C28.7056 1.96189 23.9557 0 18.8961 0C13.8365 0 9.08666 1.96189 5.52428 5.52428C1.96189 9.08666 0 13.8365 0 18.8961C0 23.9557 1.96189 28.7056 5.52428 32.268C9.08666 35.8304 13.8365 37.7923 18.8961 37.7923C23.0781 37.7923 27.2084 36.3983 30.5126 33.7652L37.9988 41.3546C38.4634 41.8193 39.0313 42.0258 39.6509 42.0258C40.2704 42.0258 40.8384 41.7677 41.303 41.3546C42.2323 40.4769 42.2323 38.9797 41.303 38.0504ZM33.1457 18.8961C33.1457 22.7167 31.6484 26.279 28.9637 28.9637C26.279 31.6484 22.665 33.1457 18.8961 33.1457C15.1272 33.1457 11.5132 31.6484 8.82852 28.9637C6.14382 26.279 4.64659 22.665 4.64659 18.8961C4.64659 15.0756 6.14382 11.5132 8.82852 8.82852C11.5132 6.14382 15.1272 4.64659 18.8961 4.64659C22.7167 4.64659 26.279 6.14382 28.9637 8.82852C31.6484 11.5132 33.1457 15.0756 33.1457 18.8961Z" fill="black"/>
                            </svg>
                        </div>

                        <!-- Store Dropdown -->
                        <div v-if="showStoreDropdown && storeDropdownItems.length > 0"
                            class="store-dropdown"
                            style="position: absolute; top: 100%; left: 0; width: 100%; max-height: 250px; overflow-y: auto; background-color: white; border: 1px solid #ccc; border-radius: 0 0 8px 8px; z-index: 100; box-shadow: 0 4px 8px rgba(0,0,0,0.1); margin-top: 2px;"
                        >
                            <div
                                v-for="(store, index) in storeDropdownItems"
                                :key="index"
                                class="store-dropdown-item"
                                style="padding: 12px; cursor: pointer; border-bottom: 1px solid #eee;"
                                @click="selectStore(store)"
                                @mouseenter="hoveredIndex = index"
                                :style="{ backgroundColor: hoveredIndex === index ? '#f5f5f5' : 'white' }"
                            >
                                <div v-if="store.loading" style="display: flex; align-items: center; justify-content: center;">
                                    <div class="spinner-border spinner-border-sm mr-2" style="width: 1rem; height: 1rem;"></div>
                                    <span>Loading stores...</span>
                                </div>
                                <div v-else-if="store.error || store.noResults" style="color: #666;">
                                    {{ store.store_name }}
                                </div>
                                <div v-else>
                                    <div style="font-weight: 600;">{{ store.store_name }} - {{ store.city }}</div>
                                    <div style="font-size: 12px; color: #666;" v-html="storeAddress(store)"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
        <!---- showing the find a store page End ----->
       <div>
            <div v-if="activeStores.length == 0" class="canpay-crew-petition-info-box">
                <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-700 canpay-crew-text-font-21 pt-3">No Active petitions found</p>
                <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-700 canpay-crew-text-font-21 pb-3">Search for above.</p>
            </div>
            <div v-else class="canpay-crew-petition-info-box pb-3" style="">
                <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-700 canpay-crew-text-font-21 " style="padding-top:12px;">Sign an Active Petition</p>
                <div class="container" v-for="(item, index) in activeStores" :key="index">
                <div v-on:click="gotoDetailsPage(item)" :class="index === 0 ? 'canpay-crew-petition-box mb-3 container mt-4' : 'canpay-crew-petition-box mb-3 container '" style="padding:2px 3px;">
                    <div style="margin:14px 14px 0px 14px;" class="row">
                        <div class="col-10 canpay-crew-padding-left-none canpay-crew-padding-right-none">
                        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-bold canpay-crew-address-text-1 text-left" style="cursor: pointer;" >{{item.store_name}} - {{item.city}}
                        </p>
                        <p class="canpay-crew-p-margin-bottom-1 text-left canpay-crew-address-text-2" v-html="storeAddress(item)"></p>
                        </div>
                        <div  class="col-2 text-right canpay-crew-padding-left-none canpay-crew-padding-right-none">
                                <svg  v-if="item.type == 'mayor' && item.status_code == 202" v-on:click="changeComponent(4,item)" xmlns="http://www.w3.org/2000/svg" class="canpay-crew-icon-size-change" viewBox="0 0 104 104" fill="none">
                                <circle cx="52" cy="52" r="52" fill="black"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M37.6259 31.862C34.4425 31.862 31.8618 34.4426 31.8618 37.6261V66.3743C31.8618 69.5577 34.4425 72.1384 37.6259 72.1384H66.3741C69.5575 72.1384 72.1382 69.5577 72.1382 66.3743V48.4959C72.1382 47.6083 72.8578 46.8887 73.7455 46.8887C74.6331 46.8887 75.3527 47.6083 75.3527 48.4959V66.3743C75.3527 71.3331 71.3329 75.3529 66.3741 75.3529H37.6259C32.6671 75.3529 28.6473 71.3331 28.6473 66.3743V37.6261C28.6473 32.6673 32.6671 28.6475 37.6259 28.6475H55.5043C56.3919 28.6475 57.1115 29.3671 57.1115 30.2547C57.1115 31.1424 56.3919 31.862 55.5043 31.862H37.6259ZM70.2264 33.7739C68.695 32.2425 66.2122 32.2426 64.6809 33.7738L62.6715 35.7832L64.1413 37.253C64.769 37.8807 64.769 38.8984 64.1413 39.5261C63.5136 40.1538 62.4959 40.1538 61.8683 39.5261L60.3985 38.0563L44.6207 53.834C42.329 56.1257 41.2005 59.3085 41.5033 62.4969C44.6917 62.7997 47.8745 61.6712 50.1662 59.3795L70.2263 39.3193C71.7577 37.7879 71.7577 35.3051 70.2264 33.7739ZM42.3477 51.561C39.0324 54.8763 37.5934 59.6291 38.5129 64.2265C38.6401 64.8627 39.1375 65.3601 39.7737 65.4873C44.3711 66.4068 49.1239 64.9678 52.4392 61.6525L72.4994 41.5924C75.2861 38.8056 75.2861 34.2875 72.4993 31.5008C69.7126 28.7142 65.1945 28.7142 62.4078 31.5008L42.3477 51.561Z" fill="white"/>
                                </svg>

                                <svg class="canpay-crew-icon-size-change" viewBox="0 0 103 103" fill="none" xmlns="http://www.w3.org/2000/svg" v-else-if="item.status_code == 217">
                                <circle cx="51.5" cy="51.5" r="51.5" fill="#ECECEC"/>
                                <path d="M51.9679 21.2363C59.926 21.2363 67.4101 24.3357 73.0372 29.9629C78.6642 35.59 81.7637 43.0734 81.7638 51.0312C81.7638 58.7407 78.8546 66.0049 73.5568 71.5674L73.0372 72.1006C67.4101 77.7278 59.926 80.8271 51.9679 80.8271C44.2586 80.8271 36.9951 77.9178 31.4327 72.6201L30.8995 72.1006C25.2724 66.4733 22.173 58.9893 22.173 51.0312C22.1731 43.0733 25.2724 35.59 30.8995 29.9629L31.4327 29.4434C36.9951 24.1456 44.2586 21.2364 51.9679 21.2363ZM53.132 27.7871H50.8038V23.5908C36.5827 24.1869 25.1235 35.6461 24.5275 49.8672H28.7238V52.1953H24.5275C25.1232 66.4167 36.5825 77.8756 50.8038 78.4717V74.2754H53.132V78.4717C67.3536 77.8759 78.8136 66.4169 79.4093 52.1953H75.212V49.8672H79.4093C78.8132 35.6459 67.3534 24.1866 53.132 23.5908V27.7871ZM53.132 31.7461V49.8672H66.465V52.1953H50.8038V31.7461H53.132Z" fill="black" stroke="black" stroke-width="0.6"/>
                                </svg>

                        </div>
                    </div>
                    <div class="text-left" style="margin: 0px 14px 14px 14px;display:flex;justify-content:space-between;" v-if="item.status_code == 202">
                        <div v-if="item.type=='mayor' || item.type=='crew member' || item.type=='crew leader'" class="canpay-crew-signed-button mt-2 canpay-crew-text-font-14" >Already Signed</div>
                        <button v-else-if="petition_per_consumer != consumer_active_petition_count" class="canpay-crew-petition-button mt-2 canpay-crew-text-font-14" v-on:click.stop="showActiveCanPayCrew(item)" >Sign Petition</button>
                        <div v-else></div>
                        <div :class="petition_per_consumer != consumer_active_petition_count || (item.type=='mayor' || item.type=='crew member' || item.type=='crew leader')?'petition-postiton-crew':''">
                            <svg xmlns="http://www.w3.org/2000/svg" class="person-petition-icon" viewBox="0 0 36 42" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M18 4.2C14.4285 4.2 11.5334 7.02062 11.5334 10.5C11.5334 13.9794 14.4285 16.8 18 16.8C21.5713 16.8 24.4666 13.9794 24.4666 10.5C24.4666 7.02062 21.5713 4.2 18 4.2ZM7.22236 10.5C7.22236 4.70102 12.0477 0 18 0C23.9522 0 28.7776 4.70102 28.7776 10.5C28.7776 16.299 23.9522 21 18 21C12.0477 21 7.22236 16.299 7.22236 10.5Z" fill="black"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M18 27.3C10.5797 27.3 6.679 29.8616 4.73304 32.965C4.05582 34.0452 4.22615 35.0639 5.05294 35.9999C5.95485 37.0209 7.58576 37.8 9.37789 37.8H26.6221C28.4142 37.8 30.045 37.0209 30.9469 35.9999C31.7738 35.0639 31.9441 34.0452 31.267 32.965C29.321 29.8616 25.4202 27.3 18 27.3ZM1.0541 30.7755C3.96257 26.137 9.4573 23.1 18 23.1C26.5428 23.1 32.0374 26.137 34.9458 30.7755C36.7511 33.6544 36.06 36.6505 34.2147 38.7395C32.4441 40.7438 29.5917 42 26.6221 42H9.37789C6.40818 42 3.55574 40.7438 1.78539 38.7395C-0.0601066 36.6505 -0.751016 33.6544 1.0541 30.7755Z" fill="black"/>
                            </svg> <span class="petition-count-crew"><b>{{item.signed_users_count}}</b> of <b>{{max_consumer_allowed}}</b> Signed</span>
                        </div>
                    </div>

                    <div class="text-left" style="margin: 0px 14px 14px 14px;display:flex;justify-content:space-between;" v-else>
                        <button :class="item.status_code == 217?'canpay-crew-process-petition-button mt-2 canpay-crew-text-font-14':'canpay-crew-admin-approval-button mt-2 canpay-crew-text-font-14'" style="pointer-events: none;">

                               {{
                                   item.status_code == 217
                                   ? 'CanPay is in discussions with this store'
                                   : 'Pending Admin Approval'
                               }}
                        </button>
                        <div style="position:relative;right:-1px;top:11px">

                        </div>
                    </div>
                </div>
                </div>
                <div v-show="ActiveStoreloading" class="spinner-border"></div>
            </div>
        </div>
    </div>
    <div style="width:100%;"  v-else-if="canPayCrewLanding == 3">
          <CanPayCrewStoreSearchHeader ref="CanPayCrewStoreSearchHeader" :storeObj="storeObj" :fetchStore="initiateSearchingStore" :changeComponent="changeComponent"/>
          <div v-if="storeData.length == 0 && searchedStoreData.length == 0" class="pb-3"
            style="display: flex;
                flex-direction: column;
                align-items: center;"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="250" height="300" viewBox="0 0 653 574" fill="none">
            <path d="M326.521 452.676C329.518 452.676 331.947 450.242 331.947 447.24C331.947 444.237 329.518 441.803 326.521 441.803C323.524 441.803 321.094 444.237 321.094 447.24C321.094 450.242 323.524 452.676 326.521 452.676Z" fill="#CFCFCF"></path>
            <path d="M168.884 43.3816V67.2358" stroke="#9B9B9B" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M156.924 55.3088H180.789" stroke="#9B9B9B" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M447.436 203.363C401.268 199.324 354.435 193.35 311.538 177.34C269.444 161.677 231.858 135.145 198.891 105.373C177.299 85.9828 157.496 70.2822 127.308 69.739C97.8145 68.7486 68.7901 77.1814 44.4992 93.7983C3.42444 123.662 3.77778 185.694 15.289 230.096C32.5451 297.14 98.4119 348.561 155.198 383.067C220.784 423.09 294.4 449.462 368.92 467.41C434.234 483.228 518.656 497.901 579.774 460.019C635.907 425.123 658.239 336.312 650.653 274.365C649.093 256.124 640.88 239.07 627.558 226.41C592.9 196.27 536.114 209.41 494.448 206.648C478.975 205.626 463.221 204.634 447.436 203.363Z" fill="#C9C9C9"></path>
            <path d="M364.947 10.8731C367.944 10.8731 370.373 8.43907 370.373 5.43655C370.373 2.43402 367.944 0 364.947 0C361.95 0 359.521 2.43402 359.521 5.43655C359.521 8.43907 361.95 10.8731 364.947 10.8731Z" fill="#9B9B9B"></path>
            <path d="M83.0056 434.203V458.057" stroke="#9B9B9B" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M71.1016 446.13H94.9104" stroke="#9B9B9B" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M328.736 574C440.994 574 531.998 568.312 531.998 561.296C531.998 554.28 440.994 548.593 328.736 548.593C216.478 548.593 125.475 554.28 125.475 561.296C125.475 568.312 216.478 574 328.736 574Z" fill="#9B9B9B"></path>
            <path d="M492.13 425.216H141.032C138.683 425.214 136.362 424.701 134.229 423.713C132.097 422.724 130.204 421.283 128.683 419.489C127.161 417.695 126.046 415.592 125.416 413.324C124.785 411.056 124.654 408.678 125.031 406.355L169.935 133.528C170.542 129.731 172.482 126.276 175.405 123.784C178.328 121.293 182.044 119.928 185.882 119.937H536.98C539.328 119.937 541.647 120.45 543.778 121.439C545.908 122.429 547.797 123.871 549.315 125.666C550.833 127.461 551.942 129.565 552.565 131.833C553.189 134.101 553.312 136.478 552.926 138.799L508.243 411.625C507.621 415.445 505.654 418.917 502.699 421.41C499.743 423.903 495.994 425.253 492.13 425.216Z" fill="#9B9B9B"></path>
            <path d="M496.457 112.802L288.053 19.5758C281.91 16.8278 274.706 19.5895 271.964 25.7443L205.913 173.962C203.17 180.116 205.927 187.333 212.07 190.081L420.474 283.308C426.617 286.056 433.82 283.294 436.563 277.139L502.614 128.922C505.356 122.767 502.6 115.55 496.457 112.802Z" fill="white" stroke="#7F7F7F" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M318.603 141.517C324.089 133.805 332.067 128.231 341.185 125.738C350.304 123.246 360.002 123.988 368.637 127.84C377.272 131.693 384.311 138.417 388.563 146.875C392.815 155.332 394.018 165.003 391.968 174.247" stroke="#7F7F7F" stroke-opacity="0.9694" stroke-width="0.68" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M418.268 133.14C421.418 133.14 423.971 130.582 423.971 127.426C423.971 124.27 421.418 121.712 418.268 121.712C415.118 121.712 412.565 124.27 412.565 127.426C412.565 130.582 415.118 133.14 418.268 133.14Z" fill="#9B9B9B"></path>
            <path d="M335.657 96.1937C338.807 96.1937 341.36 93.6355 341.36 90.4798C341.36 87.3241 338.807 84.7659 335.657 84.7659C332.507 84.7659 329.954 87.3241 329.954 90.4798C329.954 93.6355 332.507 96.1937 335.657 96.1937Z" fill="#9B9B9B"></path>
            <path d="M490.581 425.105H137.767C133.473 425.09 129.361 423.372 126.331 420.325C123.3 417.278 121.599 413.152 121.599 408.851V159.602C121.547 157.439 121.927 155.289 122.715 153.275C123.504 151.262 124.686 149.426 126.192 147.876C127.697 146.326 129.497 145.093 131.484 144.249C133.472 143.404 135.608 142.966 137.767 142.959H266.279C269.261 142.97 272.182 143.804 274.722 145.37C277.262 146.936 279.322 149.173 280.675 151.835L299.002 187.783C300.351 190.449 302.41 192.69 304.951 194.256C307.491 195.823 310.415 196.654 313.399 196.659H490.581C494.869 196.659 498.981 198.366 502.013 201.404C505.045 204.441 506.749 208.562 506.749 212.858V408.851C506.749 413.152 505.047 417.278 502.017 420.325C498.986 423.372 494.874 425.09 490.581 425.105Z" fill="white" stroke="#7F7F7F" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M436.263 205.147C415.88 205.136 395.953 211.181 379 222.519C362.048 233.856 348.832 249.976 341.024 268.839C333.217 287.703 331.168 308.463 335.138 328.493C339.107 348.523 348.917 366.923 363.326 381.367C377.734 395.811 396.094 405.649 416.084 409.637C436.074 413.625 456.795 411.584 475.627 403.771C494.459 395.959 510.555 382.727 521.88 365.748C533.205 348.769 539.25 328.807 539.25 308.386C539.25 281.015 528.401 254.764 509.089 235.404C489.777 216.045 463.582 205.161 436.263 205.147Z" fill="white" stroke="#7F7F7F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M509.572 383.277L537.036 410.793" stroke="#9B9B9B" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M533.931 397.424L525.399 406.366C521.574 410.374 521.717 416.73 525.718 420.562L599.762 491.48C603.763 495.312 610.107 495.169 613.931 491.16L622.464 482.218C626.288 478.209 626.145 471.853 622.144 468.021L548.101 397.104C544.1 393.272 537.756 393.415 533.931 397.424Z" fill="#9B9B9B"></path>
            <path d="M588.751 5.43677V29.3465" stroke="#9B9B9B" stroke-linecap="round" stroke-linejoin="round"></path>
            <path d="M576.79 17.4192H600.655" stroke="#9B9B9B" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </div>
          <div v-if="storeData.length != 0 && searchedStoreData.length == 0" style="width:100%;">
          <div  class="canpay-crew-all-info-div pb-2" style="top: -11px;">
            <div class="container" style="display: flex; justify-content: center; flex-direction: column;align-items:center;">
            <div class="w-100" v-for="(item, index) in storeData" :key="index">
                <div v-on:click="gotoDetailsPage(item)" :class="index === 0 ? 'canpay-crew-petition-box mb-3 container mt-3' : 'canpay-crew-petition-box mb-3 container '" style="padding:2px 3px;"  v-if="item.pending_petition == 1">
                    <div style="margin:14px 14px 0px 14px;" class="row">
                        <div class="col-10 canpay-crew-padding-left-none canpay-crew-padding-right-none">
                        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-bold canpay-crew-address-text-1 text-left" style="cursor: pointer;">{{item.store_name}} - {{item.city}}
                        </p>
                        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-address-text-2 text-left" v-html="storeAddress(item)"></p>
                        </div>
                        <div class="col-2 text-right canpay-crew-padding-left-none canpay-crew-padding-right-none">
                            <svg  v-if="item.type == 'mayor'" v-on:click="changeComponent(4,item)" xmlns="http://www.w3.org/2000/svg" class="canpay-crew-icon-size-change" viewBox="0 0 104 104" fill="none">
                            <circle cx="52" cy="52" r="52" fill="black"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M37.6259 31.862C34.4425 31.862 31.8618 34.4426 31.8618 37.6261V66.3743C31.8618 69.5577 34.4425 72.1384 37.6259 72.1384H66.3741C69.5575 72.1384 72.1382 69.5577 72.1382 66.3743V48.4959C72.1382 47.6083 72.8578 46.8887 73.7455 46.8887C74.6331 46.8887 75.3527 47.6083 75.3527 48.4959V66.3743C75.3527 71.3331 71.3329 75.3529 66.3741 75.3529H37.6259C32.6671 75.3529 28.6473 71.3331 28.6473 66.3743V37.6261C28.6473 32.6673 32.6671 28.6475 37.6259 28.6475H55.5043C56.3919 28.6475 57.1115 29.3671 57.1115 30.2547C57.1115 31.1424 56.3919 31.862 55.5043 31.862H37.6259ZM70.2264 33.7739C68.695 32.2425 66.2122 32.2426 64.6809 33.7738L62.6715 35.7832L64.1413 37.253C64.769 37.8807 64.769 38.8984 64.1413 39.5261C63.5136 40.1538 62.4959 40.1538 61.8683 39.5261L60.3985 38.0563L44.6207 53.834C42.329 56.1257 41.2005 59.3085 41.5033 62.4969C44.6917 62.7997 47.8745 61.6712 50.1662 59.3795L70.2263 39.3193C71.7577 37.7879 71.7577 35.3051 70.2264 33.7739ZM42.3477 51.561C39.0324 54.8763 37.5934 59.6291 38.5129 64.2265C38.6401 64.8627 39.1375 65.3601 39.7737 65.4873C44.3711 66.4068 49.1239 64.9678 52.4392 61.6525L72.4994 41.5924C75.2861 38.8056 75.2861 34.2875 72.4993 31.5008C69.7126 28.7142 65.1945 28.7142 62.4078 31.5008L42.3477 51.561Z" fill="white"/>
                            </svg>
                        </div>
                    </div>
                    <div class="text-left" style="margin: 0px 14px 14px 14px;display:flex;justify-content:space-between;">
                        <div v-if="item.type=='mayor' || item.type=='crew member' || item.type=='crew leader'" class="canpay-crew-signed-button mt-2 canpay-crew-text-font-14" >Already Signed</div>
                        <button v-else-if="petition_per_consumer != consumer_active_petition_count" class="canpay-crew-petition-button mt-2 canpay-crew-text-font-14" v-on:click.stop="showActiveCanPayCrew(item)">Sign Petition</button>
                        <div v-else></div>
                          <div :style="petition_per_consumer != consumer_active_petition_count || (item.type=='mayor' || item.type=='crew member' || item.type=='crew leader')?'position:relative;right:-1px;top:11px':''">
                            <svg xmlns="http://www.w3.org/2000/svg" class="person-petition-icon" viewBox="0 0 36 42" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M18 4.2C14.4285 4.2 11.5334 7.02062 11.5334 10.5C11.5334 13.9794 14.4285 16.8 18 16.8C21.5713 16.8 24.4666 13.9794 24.4666 10.5C24.4666 7.02062 21.5713 4.2 18 4.2ZM7.22236 10.5C7.22236 4.70102 12.0477 0 18 0C23.9522 0 28.7776 4.70102 28.7776 10.5C28.7776 16.299 23.9522 21 18 21C12.0477 21 7.22236 16.299 7.22236 10.5Z" fill="black"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M18 27.3C10.5797 27.3 6.679 29.8616 4.73304 32.965C4.05582 34.0452 4.22615 35.0639 5.05294 35.9999C5.95485 37.0209 7.58576 37.8 9.37789 37.8H26.6221C28.4142 37.8 30.045 37.0209 30.9469 35.9999C31.7738 35.0639 31.9441 34.0452 31.267 32.965C29.321 29.8616 25.4202 27.3 18 27.3ZM1.0541 30.7755C3.96257 26.137 9.4573 23.1 18 23.1C26.5428 23.1 32.0374 26.137 34.9458 30.7755C36.7511 33.6544 36.06 36.6505 34.2147 38.7395C32.4441 40.7438 29.5917 42 26.6221 42H9.37789C6.40818 42 3.55574 40.7438 1.78539 38.7395C-0.0601066 36.6505 -0.751016 33.6544 1.0541 30.7755Z" fill="black"/>
                            </svg> <span class="petition-count-crew"><b>{{item.total_signer}}</b> of <b>{{max_consumer_allowed}}</b> Signed</span>
                        </div>
                    </div>
                </div>
                <div v-if="item.accept_canpay == 1" :class="index === 0 ? 'canpay-crew-petition-box mb-3 container mt-3' : 'canpay-crew-petition-box mb-3 container '" style="padding:2px 3px;">
                    <div style="margin:14px 14px 7px 14px;" class="row">
                        <div class="col-10 canpay-crew-padding-left-none canpay-crew-padding-right-none">
                        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-address-text-1 canpay-crew-text-bold text-left">{{item.store_name}} - {{item.city}}
                        </p>
                        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-address-text-2 text-left" v-html="storeAddress(item)"></p>
                        <div class="text-left">
                            <button class="canpay-crew-accpeted-petition-button mt-2 canpay-crew-text-font-14">CanPay Already Accepted</button>
                        </div>
                        </div>
                        <div class="col-2 text-right canpay-crew-padding-left-none canpay-crew-padding-right-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="canpay-crew-icon-size-change" viewBox="0 0 110 110" fill="none">
                            <circle cx="55" cy="55" r="55" fill="#D5EFDF"/>
                            <path d="M67.1681 46.7563L50.6806 63.2437L42.8318 55.3951M84 55C84 71.0162 71.0162 84 55 84C38.9838 84 26 71.0162 26 55C26 38.9838 38.9838 26 55 26C71.0162 26 84 38.9838 84 55Z" stroke="black" stroke-width="3.6" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                    </div>
                    <div style="margin:0px 14px 14px 14px;" class="row">
                        <div class="col-12 text-left canpay-crew-padding-left-none canpay-crew-padding-right-none">
                            <span class="canpay-crew-accepted-text canpay-crew-address-text-2">You can already use CanPay at this store.</span>
                        </div>
                    </div>
                </div>

                <div v-on:click="gotoDetailsPage(item)" v-if="item.in_discussion == 1 || item.in_admin_approval == 1"  :class="index === 0 ? 'canpay-crew-petition-box mb-3 container mt-3' : 'canpay-crew-petition-box mb-3 container '" style="padding:2px 3px;">
                    <div style="margin:14px 14px 7px 14px;" class="row">
                        <div class="col-10 canpay-crew-padding-left-none canpay-crew-padding-right-none">
                        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-bold canpay-crew-address-text-1 text-left" style="cursor: pointer;">{{item.store_name}} - {{item.city}}
                        </p>
                        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-address-text-2 text-left" v-html="storeAddress(item)"></p>
                        </div>
                        <div class="col-2 text-right canpay-crew-padding-left-none canpay-crew-padding-right-none">
                            <svg  class="canpay-crew-icon-size-change" viewBox="0 0 103 103" fill="none" xmlns="http://www.w3.org/2000/svg" v-if="item.in_discussion == 1">
                            <circle cx="51.5" cy="51.5" r="51.5" fill="#ECECEC"/>
                            <path d="M51.9679 21.2363C59.926 21.2363 67.4101 24.3357 73.0372 29.9629C78.6642 35.59 81.7637 43.0734 81.7638 51.0312C81.7638 58.7407 78.8546 66.0049 73.5568 71.5674L73.0372 72.1006C67.4101 77.7278 59.926 80.8271 51.9679 80.8271C44.2586 80.8271 36.9951 77.9178 31.4327 72.6201L30.8995 72.1006C25.2724 66.4733 22.173 58.9893 22.173 51.0312C22.1731 43.0733 25.2724 35.59 30.8995 29.9629L31.4327 29.4434C36.9951 24.1456 44.2586 21.2364 51.9679 21.2363ZM53.132 27.7871H50.8038V23.5908C36.5827 24.1869 25.1235 35.6461 24.5275 49.8672H28.7238V52.1953H24.5275C25.1232 66.4167 36.5825 77.8756 50.8038 78.4717V74.2754H53.132V78.4717C67.3536 77.8759 78.8136 66.4169 79.4093 52.1953H75.212V49.8672H79.4093C78.8132 35.6459 67.3534 24.1866 53.132 23.5908V27.7871ZM53.132 31.7461V49.8672H66.465V52.1953H50.8038V31.7461H53.132Z" fill="black" stroke="black" stroke-width="0.6"/>
                            </svg>

                        </div>
                    </div>
                    <div style="margin:0px 14px 14px 14px;" class="row">
                        <div class="text-left col-12 canpay-crew-padding-left-none canpay-crew-padding-right-none">
                            <button :class="item.in_discussion == 1?'canpay-crew-process-petition-button mt-2 canpay-crew-text-font-14':'canpay-crew-admin-approval-button mt-2 canpay-crew-text-font-14'" style="pointer-events: none;">

                                {{
                                    item.in_discussion == 1
                                    ? 'CanPay is in discussions with this store'
                                    : 'Pending Admin Approval'
                                }}
                            </button>
                        </div>
                        <div style="margin:14px 14px 0px 0px;" class="col-12 text-left canpay-crew-padding-left-none canpay-crew-padding-right-none">
                            <span class="canpay-crew-accepted-text-1 canpay-crew-address-text-2">No new Petitions can be added at this time.</span>
                        </div>
                    </div>
                </div>
                <div  v-on:click="gotoDetailsPage(item)" v-else-if="item.in_discussion == 0 && item.in_admin_approval == 0 && item.accept_canpay == 0 && item.pending_petition == 0 " :class="index === 0 ? 'canpay-crew-petition-box mb-3 container mt-3' : 'canpay-crew-petition-box mb-3 container '" style="padding:2px 3px;">
                    <div style="margin:14px;" class="row">
                        <div class="col-10 canpay-crew-padding-left-none canpay-crew-padding-right-none">
                        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-bold canpay-crew-address-text-1 text-left" style="cursor: pointer;">{{item.store_name}} - {{item.city}}
                        </p>
                        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-address-text-2 text-left" v-html="storeAddress(item)"></p>
                        <div class="text-left">
                            <button class="canpay-crew-new-petition-button mt-2 canpay-crew-text-font-14" v-on:click="createPetition(item)">Create new Petition</button>
                        </div>
                        </div>
                        <div class="col-2 text-right canpay-crew-padding-left-none canpay-crew-padding-right-none">
                                <svg v-on:click="createPetition(item)" xmlns="http://www.w3.org/2000/svg" class="canpay-crew-icon-size-change" viewBox="0 0 94 94" fill="none">
                                <circle cx="47" cy="47" r="47" fill="#179346"/>
                                <path d="M47 74.2593C31.9684 74.2593 19.74 62.0309 19.74 46.9993C19.74 31.9677 31.9684 19.7393 47 19.7393C62.0316 19.7393 74.26 31.9677 74.26 46.9993C74.26 62.0309 62.0316 74.2593 47 74.2593ZM47 22.011C33.2224 22.011 22.0117 33.2216 22.0117 46.9993C22.0117 60.777 33.2224 71.9876 47 71.9876C60.7777 71.9876 71.9884 60.777 71.9884 46.9993C71.9884 33.2216 60.7777 22.011 47 22.011Z" fill="white"/>
                                <path d="M59.4942 48.1351H34.5059C33.8789 48.1351 33.37 47.6263 33.37 46.9993C33.37 46.3723 33.8789 45.8635 34.5059 45.8635H59.4942C60.1212 45.8635 60.63 46.3723 60.63 46.9993C60.63 47.6263 60.1212 48.1351 59.4942 48.1351Z" fill="white"/>
                                <path d="M47 60.6293C46.373 60.6293 45.8642 60.1204 45.8642 59.4935V34.5051C45.8642 33.8782 46.373 33.3693 47 33.3693C47.627 33.3693 48.1359 33.8782 48.1359 34.5051V59.4935C48.1359 60.1204 47.627 60.6293 47 60.6293Z" fill="white"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M47 75.0113C31.5531 75.0113 18.988 62.4462 18.988 46.9993C18.988 31.5524 31.5531 18.9873 47 18.9873C62.447 18.9873 75.012 31.5524 75.012 46.9993C75.012 62.4462 62.447 75.0113 47 75.0113ZM47 22.763C33.6377 22.763 22.7637 33.637 22.7637 46.9993C22.7637 60.3616 33.6377 71.2356 47 71.2356C60.3624 71.2356 71.2364 60.3616 71.2364 46.9993C71.2364 33.637 60.3624 22.763 47 22.763ZM59.4942 45.8635C60.1212 45.8635 60.63 46.3723 60.63 46.9993C60.63 47.6263 60.1212 48.1351 59.4942 48.1351L48.1359 48.1351L48.1359 59.4935C48.1359 60.1204 47.627 60.6293 47 60.6293C46.373 60.6293 45.8642 60.1204 45.8642 59.4935L45.8642 48.1351L34.5059 48.1351C33.8789 48.1351 33.37 47.6263 33.37 46.9993C33.37 46.3723 33.8789 45.8635 34.5059 45.8635L45.8642 45.8635L45.8642 34.5051C45.8642 33.8782 46.373 33.3693 47 33.3693C47.627 33.3693 48.1359 33.8782 48.1359 34.5051L48.1359 45.8635L59.4942 45.8635ZM48.8879 45.1115H59.4942C60.5365 45.1115 61.382 45.957 61.382 46.9993C61.382 48.0416 60.5365 48.8871 59.4942 48.8871H48.8879V59.4935C48.8879 60.5358 48.0423 61.3813 47 61.3813C45.9577 61.3813 45.1122 60.5358 45.1122 59.4935V48.8871H34.5059C33.4636 48.8871 32.618 48.0416 32.618 46.9993C32.618 45.957 33.4636 45.1115 34.5059 45.1115H45.1122V34.5051C45.1122 33.4628 45.9577 32.6173 47 32.6173C48.0423 32.6173 48.8879 33.4628 48.8879 34.5051V45.1115ZM47 74.2593C31.9684 74.2593 19.74 62.0309 19.74 46.9993C19.74 31.9677 31.9684 19.7393 47 19.7393C62.0316 19.7393 74.26 31.9677 74.26 46.9993C74.26 62.0309 62.0316 74.2593 47 74.2593ZM47 22.011C33.2224 22.011 22.0117 33.2216 22.0117 46.9993C22.0117 60.777 33.2224 71.9876 47 71.9876C60.7777 71.9876 71.9884 60.777 71.9884 46.9993C71.9884 33.2216 60.7777 22.011 47 22.011Z" fill="white"/>
                                </svg>
                        </div>
                    </div>
                    <div style="margin:0px 14px 14px 14px;" class="row">
                        <div class="col-12 text-left canpay-crew-padding-left-none canpay-crew-padding-right-none">
                            <span class="canpay-crew-success-text">Become the Mayor of this location!</span>
                        </div>
                    </div>
                </div>
            </div>
            <div v-show="loading" class="spinner-border"></div>
            </div>
          </div>
          </div>
          <div v-else-if="searchedStoreData.length != 0" style="width:100%;">
          <div  class="canpay-crew-all-info-div pb-2" style="top: -11px;">
            <div class="container" style="display: flex; justify-content: center; flex-direction: column;align-items:center;">
            <div class="w-100" v-for="(item, index) in searchedStoreData" :key="index">
                <div  v-on:click="gotoDetailsPage(item)" :class="index === 0 ? 'canpay-crew-petition-box mb-3 container mt-4' : 'canpay-crew-petition-box mb-3 container '" style="padding:2px 3px;"  v-if="item.pending_petition == 1">
                    <div style="margin:14px;" class="row">
                        <div class="col-10 canpay-crew-padding-left-none canpay-crew-padding-right-none">
                        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-bold canpay-crew-address-text-1 text-left" style="cursor: pointer;">{{item.store_name}} - {{item.city}}
                        </p>
                        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-address-text-1 text-left" v-html="storeAddress(item)"></p>
                        <div class="text-left">
                        <div v-if="item.type=='mayor' || item.type=='crew member' || item.type=='crew leader'" class="canpay-crew-signed-button mt-2 canpay-crew-text-font-14" >Already Signed</div>
                        <button v-else-if="petition_per_consumer != consumer_active_petition_count" class="canpay-crew-petition-button mt-2 canpay-crew-text-font-14" v-on:click.stop="showActiveCanPayCrew(item)">Sign Petition</button>
                        </div>
                        </div>
                        <div class="col-2 text-right canpay-crew-padding-left-none canpay-crew-padding-right-none">
                            <svg  v-if="item.type == 'mayor'" v-on:click="changeComponent(4,item)" xmlns="http://www.w3.org/2000/svg" class="canpay-crew-icon-size-change" viewBox="0 0 104 104" fill="none">
                            <circle cx="52" cy="52" r="52" fill="black"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M37.6259 31.862C34.4425 31.862 31.8618 34.4426 31.8618 37.6261V66.3743C31.8618 69.5577 34.4425 72.1384 37.6259 72.1384H66.3741C69.5575 72.1384 72.1382 69.5577 72.1382 66.3743V48.4959C72.1382 47.6083 72.8578 46.8887 73.7455 46.8887C74.6331 46.8887 75.3527 47.6083 75.3527 48.4959V66.3743C75.3527 71.3331 71.3329 75.3529 66.3741 75.3529H37.6259C32.6671 75.3529 28.6473 71.3331 28.6473 66.3743V37.6261C28.6473 32.6673 32.6671 28.6475 37.6259 28.6475H55.5043C56.3919 28.6475 57.1115 29.3671 57.1115 30.2547C57.1115 31.1424 56.3919 31.862 55.5043 31.862H37.6259ZM70.2264 33.7739C68.695 32.2425 66.2122 32.2426 64.6809 33.7738L62.6715 35.7832L64.1413 37.253C64.769 37.8807 64.769 38.8984 64.1413 39.5261C63.5136 40.1538 62.4959 40.1538 61.8683 39.5261L60.3985 38.0563L44.6207 53.834C42.329 56.1257 41.2005 59.3085 41.5033 62.4969C44.6917 62.7997 47.8745 61.6712 50.1662 59.3795L70.2263 39.3193C71.7577 37.7879 71.7577 35.3051 70.2264 33.7739ZM42.3477 51.561C39.0324 54.8763 37.5934 59.6291 38.5129 64.2265C38.6401 64.8627 39.1375 65.3601 39.7737 65.4873C44.3711 66.4068 49.1239 64.9678 52.4392 61.6525L72.4994 41.5924C75.2861 38.8056 75.2861 34.2875 72.4993 31.5008C69.7126 28.7142 65.1945 28.7142 62.4078 31.5008L42.3477 51.561Z" fill="white"/>
                            </svg>
                        </div>
                    </div>

                </div>
                <div v-if="item.accept_canpay == 1" :class="index === 0 ? 'canpay-crew-petition-box mb-3 container mt-4' : 'canpay-crew-petition-box mb-3 container '" style="padding:2px 3px;">
                    <div style="margin:14px 14px 7px 14px;" class="row">
                        <div class="col-10 canpay-crew-padding-left-none canpay-crew-padding-right-none">
                        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-bold canpay-crew-address-text-1 text-left">{{item.store_name}} - {{item.city}}
                        </p>
                        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-address-text-2 text-left" v-html="storeAddress(item)"></p>
                        <div class="text-left">
                            <button class="canpay-crew-accpeted-petition-button mt-2 canpay-crew-text-font-14">CanPay Already Accepted</button>
                        </div>
                        </div>
                        <div class="col-2 text-right canpay-crew-padding-left-none canpay-crew-padding-right-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="canpay-crew-icon-size-change" viewBox="0 0 110 110" fill="none">
                            <circle cx="55" cy="55" r="55" fill="#D5EFDF"/>
                            <path d="M67.1681 46.7563L50.6806 63.2437L42.8318 55.3951M84 55C84 71.0162 71.0162 84 55 84C38.9838 84 26 71.0162 26 55C26 38.9838 38.9838 26 55 26C71.0162 26 84 38.9838 84 55Z" stroke="black" stroke-width="3.6" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                    </div>
                    <div style="margin:0px 14px 14px 14px;" class="row">
                        <div class="col-12 text-left canpay-crew-padding-left-none canpay-crew-padding-right-none">
                            <span class="canpay-crew-accepted-text canpay-crew-address-text-2">You can already use CanPay at this store.</span>
                        </div>
                    </div>
                </div>

                <div v-on:click="gotoDetailsPage(item)" v-if="item.in_discussion == 1"  :class="index === 0 ? 'canpay-crew-petition-box mb-3 container mt-4' : 'canpay-crew-petition-box mb-3 container '" style="padding:2px 3px;">
                    <div style="margin:14px 14px 7px 14px;" class="row">
                        <div class="col-10 canpay-crew-padding-left-none canpay-crew-padding-right-none">
                        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-bold canpay-crew-address-text-1 text-left" style="cursor: pointer;">{{item.store_name}} - {{item.city}}
                        </p>
                        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-address-text-2 text-left" v-html="storeAddress(item)"></p>
                        </div>
                        <div class="col-2 text-right canpay-crew-padding-left-none canpay-crew-padding-right-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="canpay-crew-icon-size-change" viewBox="0 0 110 110" fill="none">
                            <circle cx="55" cy="55" r="55" fill="#D5EFDF"/>
                            <path d="M26.1708 53.2771C26.7974 38.0618 39.0618 25.7974 54.2771 25.1708V29.3557V29.6557H54.5771H56.4229H56.7229V29.3557V25.1708C71.9382 25.7974 84.2026 38.0618 84.8292 53.2771H80.6443H80.3443V53.5771V55.4229V55.7229H80.6443H84.8292C84.2026 70.9382 71.9382 83.2026 56.7229 83.8292V79.6443V79.3443H56.4229H54.5771H54.2771V79.6443V83.8292C39.0618 83.2026 26.7974 70.9382 26.1708 55.7229H30.3557H30.6557V55.4229V53.5771V53.2771H30.3557H26.1708ZM70.6616 55.7229H70.9616V55.4229V53.5772V53.2772H70.6616H56.7229V34.2242V33.9242H56.4229H54.5771H54.2771V34.2242V55.4229V55.7229H54.5771H70.6616ZM33.014 76.986C39.0198 82.9918 47.0065 86.3 55.5 86.3C63.9935 86.3 71.9803 82.9918 77.986 76.986C83.9918 70.9801 87.3 62.9935 87.3 54.5C87.3 46.0065 83.9918 38.0198 77.986 32.014C71.9803 26.0082 63.9935 22.7 55.5 22.7C47.0065 22.7 39.0198 26.0082 33.014 32.014C27.0082 38.0197 23.7 46.0065 23.7 54.5C23.7 62.9935 27.0082 70.9801 33.014 76.986Z" fill="black" stroke="black" stroke-width="0.6"/>
                            </svg>
                        </div>
                    </div>
                    <div style="margin:0px 14px 14px 14px;" class="row">
                        <div class="text-left col-12 canpay-crew-padding-left-none canpay-crew-padding-right-none">
                            <button class="canpay-crew-process-petition-button mt-2 canpay-crew-text-font-14">CanPay is in discussions with this store</button>
                        </div>
                        <div style="margin:14px 14px 0px 0px;" class="col-12 text-left canpay-crew-padding-left-none canpay-crew-padding-right-none">
                            <span class="canpay-crew-accepted-text-1 canpay-crew-address-text-2">No new Petitions can be added at this time.</span>
                        </div>
                    </div>
                </div>
                <div v-on:click="gotoDetailsPage(item)" v-if="item.in_discussion == 0 && item.accept_canpay == 0 && item.pending_petition == 0 " :class="index === 0 ? 'canpay-crew-petition-box mb-3 container mt-4' : 'canpay-crew-petition-box mb-3 container '" style="padding:2px 3px;">
                    <div style="margin:14px;" class="row">
                        <div class="col-10 canpay-crew-padding-left-none canpay-crew-padding-right-none">
                        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-bold canpay-crew-address-text-1 text-left"  style="cursor: pointer;">{{item.store_name}} - {{item.city}}
                        </p>
                        <p class="canpay-crew-p-margin-bottom-1 canpay-crew-address-text-2 text-left" v-html="storeAddress(item)"></p>
                        <div class="text-left">
                            <button class="canpay-crew-new-petition-button mt-2 canpay-crew-text-font-14" v-on:click="createPetition(item)">Create new Petition</button>
                        </div>
                        </div>
                        <div class="col-2 text-right canpay-crew-padding-left-none canpay-crew-padding-right-none">
                                <svg v-on:click="createPetition(item)" xmlns="http://www.w3.org/2000/svg" class="canpay-crew-icon-size-change" viewBox="0 0 94 94" fill="none">
                                <circle cx="47" cy="47" r="47" fill="#179346"/>
                                <path d="M47 74.2593C31.9684 74.2593 19.74 62.0309 19.74 46.9993C19.74 31.9677 31.9684 19.7393 47 19.7393C62.0316 19.7393 74.26 31.9677 74.26 46.9993C74.26 62.0309 62.0316 74.2593 47 74.2593ZM47 22.011C33.2224 22.011 22.0117 33.2216 22.0117 46.9993C22.0117 60.777 33.2224 71.9876 47 71.9876C60.7777 71.9876 71.9884 60.777 71.9884 46.9993C71.9884 33.2216 60.7777 22.011 47 22.011Z" fill="white"/>
                                <path d="M59.4942 48.1351H34.5059C33.8789 48.1351 33.37 47.6263 33.37 46.9993C33.37 46.3723 33.8789 45.8635 34.5059 45.8635H59.4942C60.1212 45.8635 60.63 46.3723 60.63 46.9993C60.63 47.6263 60.1212 48.1351 59.4942 48.1351Z" fill="white"/>
                                <path d="M47 60.6293C46.373 60.6293 45.8642 60.1204 45.8642 59.4935V34.5051C45.8642 33.8782 46.373 33.3693 47 33.3693C47.627 33.3693 48.1359 33.8782 48.1359 34.5051V59.4935C48.1359 60.1204 47.627 60.6293 47 60.6293Z" fill="white"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M47 75.0113C31.5531 75.0113 18.988 62.4462 18.988 46.9993C18.988 31.5524 31.5531 18.9873 47 18.9873C62.447 18.9873 75.012 31.5524 75.012 46.9993C75.012 62.4462 62.447 75.0113 47 75.0113ZM47 22.763C33.6377 22.763 22.7637 33.637 22.7637 46.9993C22.7637 60.3616 33.6377 71.2356 47 71.2356C60.3624 71.2356 71.2364 60.3616 71.2364 46.9993C71.2364 33.637 60.3624 22.763 47 22.763ZM59.4942 45.8635C60.1212 45.8635 60.63 46.3723 60.63 46.9993C60.63 47.6263 60.1212 48.1351 59.4942 48.1351L48.1359 48.1351L48.1359 59.4935C48.1359 60.1204 47.627 60.6293 47 60.6293C46.373 60.6293 45.8642 60.1204 45.8642 59.4935L45.8642 48.1351L34.5059 48.1351C33.8789 48.1351 33.37 47.6263 33.37 46.9993C33.37 46.3723 33.8789 45.8635 34.5059 45.8635L45.8642 45.8635L45.8642 34.5051C45.8642 33.8782 46.373 33.3693 47 33.3693C47.627 33.3693 48.1359 33.8782 48.1359 34.5051L48.1359 45.8635L59.4942 45.8635ZM48.8879 45.1115H59.4942C60.5365 45.1115 61.382 45.957 61.382 46.9993C61.382 48.0416 60.5365 48.8871 59.4942 48.8871H48.8879V59.4935C48.8879 60.5358 48.0423 61.3813 47 61.3813C45.9577 61.3813 45.1122 60.5358 45.1122 59.4935V48.8871H34.5059C33.4636 48.8871 32.618 48.0416 32.618 46.9993C32.618 45.957 33.4636 45.1115 34.5059 45.1115H45.1122V34.5051C45.1122 33.4628 45.9577 32.6173 47 32.6173C48.0423 32.6173 48.8879 33.4628 48.8879 34.5051V45.1115ZM47 74.2593C31.9684 74.2593 19.74 62.0309 19.74 46.9993C19.74 31.9677 31.9684 19.7393 47 19.7393C62.0316 19.7393 74.26 31.9677 74.26 46.9993C74.26 62.0309 62.0316 74.2593 47 74.2593ZM47 22.011C33.2224 22.011 22.0117 33.2216 22.0117 46.9993C22.0117 60.777 33.2224 71.9876 47 71.9876C60.7777 71.9876 71.9884 60.777 71.9884 46.9993C71.9884 33.2216 60.7777 22.011 47 22.011Z" fill="white"/>
                                </svg>
                            </svg>
                        </div>
                    </div>
                    <div style="margin:0px 14px 14px 14px;" class="row">
                        <div class="col-12 text-left canpay-crew-padding-left-none canpay-crew-padding-right-none">
                            <span class="canpay-crew-success-text">Become the Mayor of this location!</span>
                        </div>
                    </div>
                </div>
            </div>
            </div>
          </div>
          </div>

    </div>
    <div style="width:100%" v-else-if="canPayCrewLanding == 4">
        <div style="background-color:#ffffff">
            <svg style="margin-top:95px;" xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 152 167" fill="none">
            <mask id="path-1-outside-1_16407_57" maskUnits="userSpaceOnUse" x="0" y="0" width="152" height="167" fill="black">
            <rect fill="white" width="152" height="167"/>
            <path d="M45.8462 26.2273C30.8928 26.2273 18.7273 38.3928 18.7273 53.3462C18.7273 68.2996 30.8928 80.4651 45.8462 80.4651C60.7996 80.4651 72.9653 68.2996 72.9653 53.3462C72.9653 38.3928 60.7996 26.2273 45.8462 26.2273ZM45.8462 76.8643C32.8784 76.8643 22.3281 66.3142 22.3281 53.3462C22.3281 40.3782 32.8784 29.8281 45.8462 29.8281C58.8142 29.8281 69.3645 40.3782 69.3645 53.3462C69.3645 66.3142 58.8142 76.8643 45.8462 76.8643ZM51.628 51.6344C53.1213 50.1484 54.0474 48.0926 54.0474 45.8245C54.0474 41.3025 50.3684 37.6235 45.8464 37.6235C41.3244 37.6235 37.6457 41.3025 37.6457 45.8245C37.6457 48.0926 38.5716 50.1484 40.0648 51.6344C34.9622 53.8699 31.3867 58.966 31.3867 64.8842C31.3867 65.8785 32.1929 66.6846 33.1872 66.6846H58.5057C59.5 66.6846 60.3061 65.8785 60.3061 64.8842C60.3059 58.966 56.7307 53.8697 51.628 51.6344ZM41.2463 45.8242C41.2463 43.2877 43.3098 41.2241 45.8462 41.2241C48.3825 41.2241 50.4463 43.2877 50.4463 45.8242C50.4463 48.3608 48.3825 50.4243 45.8462 50.4243C43.3098 50.4243 41.2463 48.3608 41.2463 45.8242ZM35.1366 63.0836C35.9967 57.9499 40.4719 54.0252 45.8462 54.0252C51.2205 54.0252 55.6956 57.9499 56.5558 63.0836H35.1366ZM45.8462 87.2167C30.8928 87.2167 18.7273 99.3822 18.7273 114.336C18.7273 129.289 30.8928 141.455 45.8462 141.455C60.7996 141.455 72.9653 129.289 72.9653 114.336C72.9653 99.3822 60.7996 87.2167 45.8462 87.2167ZM45.8462 137.854C32.8784 137.854 22.3281 127.303 22.3281 114.336C22.3281 101.368 32.8784 90.8176 45.8462 90.8176C58.8142 90.8176 69.3645 101.368 69.3645 114.336C69.3645 127.303 58.8142 137.854 45.8462 137.854ZM51.628 112.624C53.1213 111.138 54.0474 109.082 54.0474 106.814C54.0474 102.291 50.3684 98.6127 45.8464 98.6127C41.3244 98.6127 37.6457 102.292 37.6457 106.814C37.6457 109.082 38.5716 111.138 40.0648 112.624C34.9622 114.859 31.3867 119.955 31.3867 125.873C31.3867 126.868 32.1929 127.674 33.1872 127.674H58.5057C59.5 127.674 60.3061 126.868 60.3061 125.873C60.3059 119.955 56.7307 114.859 51.628 112.624ZM41.2463 106.814C41.2463 104.277 43.3098 102.214 45.8462 102.214C48.3825 102.214 50.4463 104.277 50.4463 106.814C50.4463 109.35 48.3825 111.414 45.8462 111.414C43.3098 111.414 41.2463 109.35 41.2463 106.814ZM35.1366 124.073C35.9967 118.939 40.4719 115.015 45.8462 115.015C51.2205 115.015 55.6956 118.939 56.5558 124.073H35.1366ZM84.1325 53.3462C84.1325 52.3519 84.9386 51.5458 85.9329 51.5458H132.154C133.148 51.5458 133.955 52.3519 133.955 53.3462C133.955 54.3405 133.148 55.1466 132.154 55.1466H85.9329C84.9386 55.1466 84.1325 54.3407 84.1325 53.3462ZM133.955 65.3303C133.955 66.3246 133.148 67.1307 132.154 67.1307H85.9329C84.9386 67.1307 84.1325 66.3246 84.1325 65.3303C84.1325 64.336 84.9386 63.5299 85.9329 63.5299H132.154C133.148 63.5299 133.955 64.336 133.955 65.3303ZM84.1325 41.3621C84.1325 40.3678 84.9386 39.5617 85.9329 39.5617H117.256C118.25 39.5617 119.056 40.3678 119.056 41.3621C119.056 42.3564 118.25 43.1625 117.256 43.1625H85.9329C84.9386 43.1625 84.1325 42.3566 84.1325 41.3621ZM133.955 114.336C133.955 115.33 133.148 116.136 132.154 116.136H85.9329C84.9386 116.136 84.1325 115.33 84.1325 114.336C84.1325 113.341 84.9386 112.535 85.9329 112.535H132.154C133.148 112.535 133.955 113.341 133.955 114.336ZM133.955 126.32C133.955 127.314 133.148 128.12 132.154 128.12H85.9329C84.9386 128.12 84.1325 127.314 84.1325 126.32C84.1325 125.325 84.9386 124.519 85.9329 124.519H132.154C133.148 124.519 133.955 125.325 133.955 126.32ZM84.1325 102.352C84.1325 101.357 84.9386 100.551 85.9329 100.551H117.256C118.25 100.551 119.056 101.357 119.056 102.352C119.056 103.346 118.25 104.152 117.256 104.152H85.9329C84.9386 104.152 84.1325 103.346 84.1325 102.352Z"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M136.526 5.09091H15.4737C9.66016 5.09091 4.94737 9.97507 4.94737 16V151C4.94737 157.025 9.66016 161.909 15.4737 161.909H136.526C142.34 161.909 147.053 157.025 147.053 151V16C147.053 9.97507 142.34 5.09091 136.526 5.09091ZM15.4737 1C7.48009 1 1 7.71572 1 16V151C1 159.284 7.48009 166 15.4737 166H136.526C144.52 166 151 159.284 151 151V16C151 7.71573 144.52 1 136.526 1H15.4737Z"/>
            </mask>
            <path d="M45.8462 26.2273C30.8928 26.2273 18.7273 38.3928 18.7273 53.3462C18.7273 68.2996 30.8928 80.4651 45.8462 80.4651C60.7996 80.4651 72.9653 68.2996 72.9653 53.3462C72.9653 38.3928 60.7996 26.2273 45.8462 26.2273ZM45.8462 76.8643C32.8784 76.8643 22.3281 66.3142 22.3281 53.3462C22.3281 40.3782 32.8784 29.8281 45.8462 29.8281C58.8142 29.8281 69.3645 40.3782 69.3645 53.3462C69.3645 66.3142 58.8142 76.8643 45.8462 76.8643ZM51.628 51.6344C53.1213 50.1484 54.0474 48.0926 54.0474 45.8245C54.0474 41.3025 50.3684 37.6235 45.8464 37.6235C41.3244 37.6235 37.6457 41.3025 37.6457 45.8245C37.6457 48.0926 38.5716 50.1484 40.0648 51.6344C34.9622 53.8699 31.3867 58.966 31.3867 64.8842C31.3867 65.8785 32.1929 66.6846 33.1872 66.6846H58.5057C59.5 66.6846 60.3061 65.8785 60.3061 64.8842C60.3059 58.966 56.7307 53.8697 51.628 51.6344ZM41.2463 45.8242C41.2463 43.2877 43.3098 41.2241 45.8462 41.2241C48.3825 41.2241 50.4463 43.2877 50.4463 45.8242C50.4463 48.3608 48.3825 50.4243 45.8462 50.4243C43.3098 50.4243 41.2463 48.3608 41.2463 45.8242ZM35.1366 63.0836C35.9967 57.9499 40.4719 54.0252 45.8462 54.0252C51.2205 54.0252 55.6956 57.9499 56.5558 63.0836H35.1366ZM45.8462 87.2167C30.8928 87.2167 18.7273 99.3822 18.7273 114.336C18.7273 129.289 30.8928 141.455 45.8462 141.455C60.7996 141.455 72.9653 129.289 72.9653 114.336C72.9653 99.3822 60.7996 87.2167 45.8462 87.2167ZM45.8462 137.854C32.8784 137.854 22.3281 127.303 22.3281 114.336C22.3281 101.368 32.8784 90.8176 45.8462 90.8176C58.8142 90.8176 69.3645 101.368 69.3645 114.336C69.3645 127.303 58.8142 137.854 45.8462 137.854ZM51.628 112.624C53.1213 111.138 54.0474 109.082 54.0474 106.814C54.0474 102.291 50.3684 98.6127 45.8464 98.6127C41.3244 98.6127 37.6457 102.292 37.6457 106.814C37.6457 109.082 38.5716 111.138 40.0648 112.624C34.9622 114.859 31.3867 119.955 31.3867 125.873C31.3867 126.868 32.1929 127.674 33.1872 127.674H58.5057C59.5 127.674 60.3061 126.868 60.3061 125.873C60.3059 119.955 56.7307 114.859 51.628 112.624ZM41.2463 106.814C41.2463 104.277 43.3098 102.214 45.8462 102.214C48.3825 102.214 50.4463 104.277 50.4463 106.814C50.4463 109.35 48.3825 111.414 45.8462 111.414C43.3098 111.414 41.2463 109.35 41.2463 106.814ZM35.1366 124.073C35.9967 118.939 40.4719 115.015 45.8462 115.015C51.2205 115.015 55.6956 118.939 56.5558 124.073H35.1366ZM84.1325 53.3462C84.1325 52.3519 84.9386 51.5458 85.9329 51.5458H132.154C133.148 51.5458 133.955 52.3519 133.955 53.3462C133.955 54.3405 133.148 55.1466 132.154 55.1466H85.9329C84.9386 55.1466 84.1325 54.3407 84.1325 53.3462ZM133.955 65.3303C133.955 66.3246 133.148 67.1307 132.154 67.1307H85.9329C84.9386 67.1307 84.1325 66.3246 84.1325 65.3303C84.1325 64.336 84.9386 63.5299 85.9329 63.5299H132.154C133.148 63.5299 133.955 64.336 133.955 65.3303ZM84.1325 41.3621C84.1325 40.3678 84.9386 39.5617 85.9329 39.5617H117.256C118.25 39.5617 119.056 40.3678 119.056 41.3621C119.056 42.3564 118.25 43.1625 117.256 43.1625H85.9329C84.9386 43.1625 84.1325 42.3566 84.1325 41.3621ZM133.955 114.336C133.955 115.33 133.148 116.136 132.154 116.136H85.9329C84.9386 116.136 84.1325 115.33 84.1325 114.336C84.1325 113.341 84.9386 112.535 85.9329 112.535H132.154C133.148 112.535 133.955 113.341 133.955 114.336ZM133.955 126.32C133.955 127.314 133.148 128.12 132.154 128.12H85.9329C84.9386 128.12 84.1325 127.314 84.1325 126.32C84.1325 125.325 84.9386 124.519 85.9329 124.519H132.154C133.148 124.519 133.955 125.325 133.955 126.32ZM84.1325 102.352C84.1325 101.357 84.9386 100.551 85.9329 100.551H117.256C118.25 100.551 119.056 101.357 119.056 102.352C119.056 103.346 118.25 104.152 117.256 104.152H85.9329C84.9386 104.152 84.1325 103.346 84.1325 102.352Z" fill="#1B9146"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M136.526 5.09091H15.4737C9.66016 5.09091 4.94737 9.97507 4.94737 16V151C4.94737 157.025 9.66016 161.909 15.4737 161.909H136.526C142.34 161.909 147.053 157.025 147.053 151V16C147.053 9.97507 142.34 5.09091 136.526 5.09091ZM15.4737 1C7.48009 1 1 7.71572 1 16V151C1 159.284 7.48009 166 15.4737 166H136.526C144.52 166 151 159.284 151 151V16C151 7.71573 144.52 1 136.526 1H15.4737Z" fill="#179346"/>
            <path d="M45.8462 26.2273C30.8928 26.2273 18.7273 38.3928 18.7273 53.3462C18.7273 68.2996 30.8928 80.4651 45.8462 80.4651C60.7996 80.4651 72.9653 68.2996 72.9653 53.3462C72.9653 38.3928 60.7996 26.2273 45.8462 26.2273ZM45.8462 76.8643C32.8784 76.8643 22.3281 66.3142 22.3281 53.3462C22.3281 40.3782 32.8784 29.8281 45.8462 29.8281C58.8142 29.8281 69.3645 40.3782 69.3645 53.3462C69.3645 66.3142 58.8142 76.8643 45.8462 76.8643ZM51.628 51.6344C53.1213 50.1484 54.0474 48.0926 54.0474 45.8245C54.0474 41.3025 50.3684 37.6235 45.8464 37.6235C41.3244 37.6235 37.6457 41.3025 37.6457 45.8245C37.6457 48.0926 38.5716 50.1484 40.0648 51.6344C34.9622 53.8699 31.3867 58.966 31.3867 64.8842C31.3867 65.8785 32.1929 66.6846 33.1872 66.6846H58.5057C59.5 66.6846 60.3061 65.8785 60.3061 64.8842C60.3059 58.966 56.7307 53.8697 51.628 51.6344ZM41.2463 45.8242C41.2463 43.2877 43.3098 41.2241 45.8462 41.2241C48.3825 41.2241 50.4463 43.2877 50.4463 45.8242C50.4463 48.3608 48.3825 50.4243 45.8462 50.4243C43.3098 50.4243 41.2463 48.3608 41.2463 45.8242ZM35.1366 63.0836C35.9967 57.9499 40.4719 54.0252 45.8462 54.0252C51.2205 54.0252 55.6956 57.9499 56.5558 63.0836H35.1366ZM45.8462 87.2167C30.8928 87.2167 18.7273 99.3822 18.7273 114.336C18.7273 129.289 30.8928 141.455 45.8462 141.455C60.7996 141.455 72.9653 129.289 72.9653 114.336C72.9653 99.3822 60.7996 87.2167 45.8462 87.2167ZM45.8462 137.854C32.8784 137.854 22.3281 127.303 22.3281 114.336C22.3281 101.368 32.8784 90.8176 45.8462 90.8176C58.8142 90.8176 69.3645 101.368 69.3645 114.336C69.3645 127.303 58.8142 137.854 45.8462 137.854ZM51.628 112.624C53.1213 111.138 54.0474 109.082 54.0474 106.814C54.0474 102.291 50.3684 98.6127 45.8464 98.6127C41.3244 98.6127 37.6457 102.292 37.6457 106.814C37.6457 109.082 38.5716 111.138 40.0648 112.624C34.9622 114.859 31.3867 119.955 31.3867 125.873C31.3867 126.868 32.1929 127.674 33.1872 127.674H58.5057C59.5 127.674 60.3061 126.868 60.3061 125.873C60.3059 119.955 56.7307 114.859 51.628 112.624ZM41.2463 106.814C41.2463 104.277 43.3098 102.214 45.8462 102.214C48.3825 102.214 50.4463 104.277 50.4463 106.814C50.4463 109.35 48.3825 111.414 45.8462 111.414C43.3098 111.414 41.2463 109.35 41.2463 106.814ZM35.1366 124.073C35.9967 118.939 40.4719 115.015 45.8462 115.015C51.2205 115.015 55.6956 118.939 56.5558 124.073H35.1366ZM84.1325 53.3462C84.1325 52.3519 84.9386 51.5458 85.9329 51.5458H132.154C133.148 51.5458 133.955 52.3519 133.955 53.3462C133.955 54.3405 133.148 55.1466 132.154 55.1466H85.9329C84.9386 55.1466 84.1325 54.3407 84.1325 53.3462ZM133.955 65.3303C133.955 66.3246 133.148 67.1307 132.154 67.1307H85.9329C84.9386 67.1307 84.1325 66.3246 84.1325 65.3303C84.1325 64.336 84.9386 63.5299 85.9329 63.5299H132.154C133.148 63.5299 133.955 64.336 133.955 65.3303ZM84.1325 41.3621C84.1325 40.3678 84.9386 39.5617 85.9329 39.5617H117.256C118.25 39.5617 119.056 40.3678 119.056 41.3621C119.056 42.3564 118.25 43.1625 117.256 43.1625H85.9329C84.9386 43.1625 84.1325 42.3566 84.1325 41.3621ZM133.955 114.336C133.955 115.33 133.148 116.136 132.154 116.136H85.9329C84.9386 116.136 84.1325 115.33 84.1325 114.336C84.1325 113.341 84.9386 112.535 85.9329 112.535H132.154C133.148 112.535 133.955 113.341 133.955 114.336ZM133.955 126.32C133.955 127.314 133.148 128.12 132.154 128.12H85.9329C84.9386 128.12 84.1325 127.314 84.1325 126.32C84.1325 125.325 84.9386 124.519 85.9329 124.519H132.154C133.148 124.519 133.955 125.325 133.955 126.32ZM84.1325 102.352C84.1325 101.357 84.9386 100.551 85.9329 100.551H117.256C118.25 100.551 119.056 101.357 119.056 102.352C119.056 103.346 118.25 104.152 117.256 104.152H85.9329C84.9386 104.152 84.1325 103.346 84.1325 102.352Z" stroke="#179346" stroke-width="2" mask="url(#path-1-outside-1_16407_57)"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M136.526 5.09091H15.4737C9.66016 5.09091 4.94737 9.97507 4.94737 16V151C4.94737 157.025 9.66016 161.909 15.4737 161.909H136.526C142.34 161.909 147.053 157.025 147.053 151V16C147.053 9.97507 142.34 5.09091 136.526 5.09091ZM15.4737 1C7.48009 1 1 7.71572 1 16V151C1 159.284 7.48009 166 15.4737 166H136.526C144.52 166 151 159.284 151 151V16C151 7.71573 144.52 1 136.526 1H15.4737Z" stroke="#179346" stroke-width="2" mask="url(#path-1-outside-1_16407_57)"/>
            </svg>
            <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-bold canpay-crew-text-font-21  mt-4 pb-4">Summary of contacts added</p>
        </div>
        <div style="background-color:#ECECEC;margin-top: 22px;" >
        <div>
            <div class="container">
            <div style="margin-bottom:20px;display:flex;">
                    <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 104 104" fill="none">
                    <circle cx="52" cy="52" r="52" fill="#179346"/>
                    <path d="M59.1975 55.2093C58.1131 55.2205 57.0398 54.9913 56.0546 54.5381C55.0695 54.085 54.197 53.4191 53.5 52.5884C52.7978 53.413 51.9243 54.0747 50.9403 54.5274C49.9563 54.98 48.8855 55.2127 47.8025 55.2093C46.7181 55.2205 45.6448 54.9913 44.6596 54.5381C43.6745 54.085 42.802 53.4191 42.105 52.5884C41.8948 52.8405 41.6663 53.0766 41.4213 53.2949C40.654 53.993 39.7475 54.5205 38.7615 54.8426C37.7755 55.1648 36.7324 55.2742 35.701 55.1637C33.8342 54.9337 32.1181 54.0226 30.8816 52.6053C29.6451 51.1879 28.9753 49.364 29.0007 47.4833V45.8651C28.9988 44.7036 29.1835 43.5494 29.5477 42.4465L33.1257 31.7349C33.389 30.9394 33.8963 30.247 34.5753 29.7561C35.2544 29.2652 36.0709 29.0006 36.9088 29H70.0911C70.929 29.0006 71.7455 29.2652 72.4246 29.7561C73.1037 30.247 73.6109 30.9394 73.8742 31.7349L77.4523 42.4465C77.8164 43.5494 78.0011 44.7036 77.9992 45.8651V47.4833C78.0259 49.3571 77.3625 51.1753 76.1353 52.5916C74.9082 54.0079 73.203 54.9234 71.3445 55.1637C70.313 55.2772 69.2691 55.1691 68.2826 54.8469C67.2962 54.5246 66.3899 53.9955 65.6243 53.2949C65.3736 53.067 65.1229 52.7935 64.895 52.5428C64.1961 53.3746 63.3243 54.0441 62.3404 54.5046C61.3564 54.9651 60.2839 55.2056 59.1975 55.2093ZM36.9088 32.4186C36.7892 32.4204 36.6729 32.4584 36.5753 32.5276C36.4777 32.5967 36.4033 32.6938 36.3619 32.806L32.8066 43.5177C32.5597 44.2526 32.429 45.0215 32.4192 45.7967V47.4149C32.3858 48.4437 32.7319 49.449 33.3917 50.2391C34.0515 51.0292 34.9789 51.549 35.9972 51.6995C36.5468 51.7564 37.1022 51.6986 37.6282 51.5295C38.1542 51.3605 38.6394 51.084 39.0529 50.7176C39.4664 50.3512 39.7993 49.9028 40.0305 49.401C40.2616 48.8992 40.386 48.3548 40.3957 47.8023C40.3957 47.349 40.5758 46.9142 40.8963 46.5937C41.2169 46.2731 41.6516 46.093 42.105 46.093C42.5583 46.093 42.993 46.2731 43.3136 46.5937C43.6341 46.9142 43.8142 47.349 43.8142 47.8023C43.7881 48.333 43.8734 48.8632 44.0646 49.3589C44.2558 49.8546 44.5487 50.3048 44.9243 50.6805C45.3 51.0562 45.7502 51.3491 46.2459 51.5403C46.7416 51.7315 47.2718 51.8168 47.8025 51.7907C48.8584 51.7847 49.8693 51.3626 50.616 50.6159C51.3626 49.8692 51.7847 48.8583 51.7907 47.8023C51.7907 47.349 51.9708 46.9142 52.2913 46.5937C52.6119 46.2731 53.0466 46.093 53.5 46.093C53.9533 46.093 54.388 46.2731 54.7086 46.5937C55.0291 46.9142 55.2092 47.349 55.2092 47.8023C55.1831 48.333 55.2684 48.8632 55.4596 49.3589C55.6508 49.8546 55.9437 50.3048 56.3194 50.6805C56.695 51.0562 57.1452 51.3491 57.6409 51.5403C58.1366 51.7315 58.6668 51.8168 59.1975 51.7907C60.2534 51.7847 61.2643 51.3626 62.011 50.6159C62.7576 49.8692 63.1797 48.8583 63.1857 47.8023C63.1857 47.349 63.3658 46.9142 63.6863 46.5937C64.0069 46.2731 64.4416 46.093 64.895 46.093C65.3483 46.093 65.7831 46.2731 66.1036 46.5937C66.4241 46.9142 66.6042 47.349 66.6042 47.8023C66.6045 48.3605 66.7219 48.9125 66.9488 49.4225C67.1758 49.9325 67.5073 50.3891 67.9219 50.7629C68.3365 51.1367 68.825 51.4193 69.3556 51.5924C69.8863 51.7655 70.4474 51.8253 71.0027 51.7679C72.021 51.6174 72.9484 51.0976 73.6082 50.3075C74.268 49.5173 74.6142 48.5121 74.5807 47.4833V45.8651C74.5709 45.0899 74.4402 44.321 74.1933 43.586L70.6381 32.8744C70.5967 32.7622 70.5222 32.6651 70.4246 32.5959C70.327 32.5268 70.2107 32.4888 70.0911 32.487L36.9088 32.4186Z" fill="white"/>
                    <path d="M71.732 78H35.2679C34.212 77.994 33.2011 77.5719 32.4544 76.8252C31.7078 76.0786 31.2857 75.0676 31.2797 74.0116V52.3149C31.2797 51.8615 31.4598 51.4268 31.7803 51.1062C32.1009 50.7857 32.5356 50.6056 32.9889 50.6056C33.4423 50.6056 33.877 50.7857 34.1976 51.1062C34.5181 51.4268 34.6982 51.8615 34.6982 52.3149V74.0116C34.6982 74.1627 34.7582 74.3077 34.8651 74.4145C34.9719 74.5214 35.1168 74.5814 35.2679 74.5814H71.732C71.8831 74.5814 72.028 74.5214 72.1348 74.4145C72.2417 74.3077 72.3017 74.1627 72.3017 74.0116V52.2921C72.3017 51.8388 72.4818 51.404 72.8023 51.0834C73.1229 50.7629 73.5577 50.5828 74.011 50.5828C74.4643 50.5828 74.8991 50.7629 75.2196 51.0834C75.5401 51.404 75.7202 51.8388 75.7202 52.2921V74.0116C75.7142 75.0676 75.2921 76.0786 74.5455 76.8252C73.7988 77.5719 72.7879 77.994 71.732 78Z" fill="white"/>
                    <path d="M61.4765 78H45.5235C45.072 77.9941 44.6406 77.8121 44.3214 77.4928C44.0021 77.1735 43.8201 76.7422 43.8142 76.2907V64.8953C43.8202 63.8394 44.2423 62.8284 44.9889 62.0818C45.7356 61.3351 46.7466 60.913 47.8025 60.907H59.1975C60.2534 60.913 61.2643 61.3351 62.011 62.0818C62.7576 62.8284 63.1797 63.8394 63.1857 64.8953V76.2907C63.1798 76.7422 62.9978 77.1735 62.6786 77.4928C62.3593 77.8121 61.928 77.9941 61.4765 78ZM47.2327 74.5814H59.7672V64.8953C59.7672 64.7442 59.7072 64.5993 59.6003 64.4925C59.4935 64.3856 59.3486 64.3256 59.1975 64.3256H47.8025C47.6513 64.3256 47.5064 64.3856 47.3996 64.4925C47.2927 64.5993 47.2327 64.7442 47.2327 64.8953V74.5814Z" fill="white"/>
                    </svg>
                    <div class="canpay-crew-p-margin-bottom-1 canpay-crew-text-font-16 canpay-crew-text-700"
                    style="width: 100%;
                    display: flex;
                    align-items: center;
                    text-align: left;
                    margin-left:12px;">
                    {{contactDetails.store_name}} - {{contactDetails.city}}
                    </div>

            </div>
            </div>
            <div class="container">
            <div class="canpay-crew-petition-box mb-3 conainter"  style="padding: 20px 30px;width:100%;" v-if="contactDetails.primary_contact_person_firstname != null">
                <div class="row">
                    <div class="col-10 canpay-crew-padding-left-none canpay-crew-padding-right-none">
                    <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-bold text-left" style="word-break:break-all">
                        <svg class="canpay-crew-participant-logo" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 44 51" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M22 5.1C17.6348 5.1 14.0964 8.52503 14.0964 12.75C14.0964 16.975 17.6348 20.4 22 20.4C26.3649 20.4 29.9036 16.975 29.9036 12.75C29.9036 8.52503 26.3649 5.1 22 5.1ZM8.82733 12.75C8.82733 5.70838 14.7249 0 22 0C29.275 0 35.1726 5.70838 35.1726 12.75C35.1726 19.7916 29.275 25.5 22 25.5C14.7249 25.5 8.82733 19.7916 8.82733 12.75Z" fill="black"/>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M22 33.15C12.9307 33.15 8.16322 36.2605 5.78483 40.0289C4.95711 41.3406 5.16529 42.5776 6.17582 43.7141C7.27815 44.9539 9.27149 45.9 11.4619 45.9H32.5381C34.7284 45.9 36.7217 44.9539 37.824 43.7141C38.8346 42.5776 39.0427 41.3406 38.2152 40.0289C35.8368 36.2605 31.0691 33.15 22 33.15ZM1.28835 37.3703C4.84314 31.7378 11.5589 28.05 22 28.05C32.4411 28.05 39.1568 31.7378 42.7116 37.3703C44.918 40.866 44.0734 44.5041 41.8179 47.0409C39.6539 49.4746 36.1677 51 32.5381 51H11.4619C7.83222 51 4.3459 49.4746 2.18214 47.0409C-0.0734636 44.5041 -0.917908 40.866 1.28835 37.3703Z" fill="black"/>
                        </svg>
                        <span class="ml-2">{{contactDetails.primary_contact_person_firstname+" "+contactDetails.primary_contact_person_lastname}}</span>
                    </p>

                    <p class="canpay-crew-p-margin-bottom-1 text-left canpay-crew-text-font-16" style="word-break:break-all;">{{contactDetails.primary_contact_person_title}}</p>

                    <p class="canpay-crew-p-margin-bottom-1 text-left canpay-crew-text-font-16" style="word-break:break-all;">{{contactDetails.primary_contact_person_email}}</p>
                    </div>
                    <div class="col-2 text-right canpay-crew-padding-left-none canpay-crew-padding-right-none canpay-crew-text-font-16 " v-on:click="openEditContactDetail('primary')">
                            Edit
                    </div>
                </div>

            </div>
            <div class="canpay-crew-petition-box mb-3 conainter" style="padding: 20px 30px;width:100%;" v-if="contactDetails.secondary_contact_person_firstname != null">
                <div class="row">
                    <div class="col-10 canpay-crew-padding-left-none canpay-crew-padding-right-none">
                    <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-bold text-left" style="word-break:break-all">
                        <svg class="canpay-crew-participant-logo" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 44 51" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M22 5.1C17.6348 5.1 14.0964 8.52503 14.0964 12.75C14.0964 16.975 17.6348 20.4 22 20.4C26.3649 20.4 29.9036 16.975 29.9036 12.75C29.9036 8.52503 26.3649 5.1 22 5.1ZM8.82733 12.75C8.82733 5.70838 14.7249 0 22 0C29.275 0 35.1726 5.70838 35.1726 12.75C35.1726 19.7916 29.275 25.5 22 25.5C14.7249 25.5 8.82733 19.7916 8.82733 12.75Z" fill="black"/>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M22 33.15C12.9307 33.15 8.16322 36.2605 5.78483 40.0289C4.95711 41.3406 5.16529 42.5776 6.17582 43.7141C7.27815 44.9539 9.27149 45.9 11.4619 45.9H32.5381C34.7284 45.9 36.7217 44.9539 37.824 43.7141C38.8346 42.5776 39.0427 41.3406 38.2152 40.0289C35.8368 36.2605 31.0691 33.15 22 33.15ZM1.28835 37.3703C4.84314 31.7378 11.5589 28.05 22 28.05C32.4411 28.05 39.1568 31.7378 42.7116 37.3703C44.918 40.866 44.0734 44.5041 41.8179 47.0409C39.6539 49.4746 36.1677 51 32.5381 51H11.4619C7.83222 51 4.3459 49.4746 2.18214 47.0409C-0.0734636 44.5041 -0.917908 40.866 1.28835 37.3703Z" fill="black"/>
                        </svg>
                        <span class="ml-2">{{contactDetails.secondary_contact_person_firstname+" "+contactDetails.secondary_contact_person_lastname}}</span>
                    </p>
                    <p class="canpay-crew-p-margin-bottom-1 text-left canpay-crew-text-font-16" style="word-break:break-all;">{{contactDetails.secondary_contact_person_title}}</p>
                    <p class="canpay-crew-p-margin-bottom-1 text-left canpay-crew-text-font-16" style="word-break:break-all;">{{contactDetails.secondary_contact_person_email}}</p>
                    </div>
                    <div class="col-2 text-right canpay-crew-padding-left-none canpay-crew-padding-right-none canpay-crew-text-font-16 " v-on:click="openEditContactDetail('secondary')">
                            Edit
                    </div>
                </div>

            </div>
            </div>
        <div class="container" style="margin-top:7rem;">
            <button class="canpay-crew-sign-petition-modal-button" v-if="contactDetails.secondary_contact_person_firstname == null || contactDetails.secondary_contact_person_firstname == ''" v-on:click="provideAdditionalContact(contactDetails)">Add Additional Contact</button>
            <button class="canpay-crew-sign-petition-not-ok-modal-button mt-2" v-on:click="changeComponent(2)">{{(contactDetails.secondary_contact_person_firstname == null || contactDetails.secondary_contact_person_firstname == '')?'Not Right Now':'Go Back'}}</button>
        </div>
        </div>
        </div>

    </div>

</div>
<div>
    <canpay-crew-active-petition ref="CanpayCrewActivePetition"
        :passPetitionStatistics="passPetitionStatistics"
        :storeAddress="storeAddress"
        :signPetition="signPetition"
        :fetchPetition="fetchPetition"
        :shareThePetition="shareThePetition"></canpay-crew-active-petition>
    <canpay-crew-create-petition ref="CanPayCrewCreatePetition"
       :canPayCrewLanding="canPayCrewLanding"
       :oldCanPayCrewLanding="oldCanPayCrewLanding"
       :changeComponent="changeComponent"
       :getPetitionId="getPetitionId"
       :storeAddress="storeAddress"
       :createThePetition="createThePetition"
       :addAdditionalContact="addAdditionalContact"></canpay-crew-create-petition>
    <canpay-crew-edit-conatct-person-detail
    :shareThePetition="shareThePetition"
    :editContact="editContact" ref="CanpayCrewEditConatctPersonDetail"></canpay-crew-edit-conatct-person-detail>
    <crew-error
        ref="CrewError"
        :textMessage="textMessage"
    >
    </crew-error>
    <b-modal
      ref="canpay-crew-address-modal"
      hide-footer
      hide-header
      centered
      id="canpay-crew-address-modal"
      title="BootstrapVue"
    >
      <!------ initiating the canpay crew fill up end -------->
      <!------ Missing Details in Adress Start ---------------------->
      <div class="text-center mt-4" v-if="storeAddressModelStep == 1">
        <svg xmlns="http://www.w3.org/2000/svg" width="90" height="90" viewBox="0 0 104 104" fill="none">
        <circle cx="52" cy="52" r="52" fill="#179346"/>
        <path d="M58.1975 55.2093C57.1131 55.2205 56.0398 54.9913 55.0546 54.5381C54.0695 54.085 53.197 53.4191 52.5 52.5884C51.7978 53.413 50.9243 54.0747 49.9403 54.5274C48.9563 54.98 47.8855 55.2127 46.8025 55.2093C45.7181 55.2205 44.6448 54.9913 43.6596 54.5381C42.6745 54.085 41.802 53.4191 41.105 52.5884C40.8948 52.8405 40.6663 53.0766 40.4213 53.2949C39.654 53.993 38.7475 54.5205 37.7615 54.8426C36.7755 55.1648 35.7324 55.2742 34.701 55.1637C32.8342 54.9337 31.1181 54.0226 29.8816 52.6053C28.6451 51.1879 27.9753 49.364 28.0007 47.4833V45.8651C27.9988 44.7036 28.1835 43.5494 28.5477 42.4465L32.1257 31.7349C32.389 30.9394 32.8963 30.247 33.5753 29.7561C34.2544 29.2652 35.0709 29.0006 35.9088 29H69.0911C69.929 29.0006 70.7455 29.2652 71.4246 29.7561C72.1037 30.247 72.6109 30.9394 72.8742 31.7349L76.4523 42.4465C76.8164 43.5494 77.0011 44.7036 76.9992 45.8651V47.4833C77.0259 49.3571 76.3625 51.1753 75.1353 52.5916C73.9082 54.0079 72.203 54.9234 70.3445 55.1637C69.313 55.2772 68.2691 55.1691 67.2826 54.8469C66.2962 54.5246 65.3899 53.9955 64.6243 53.2949C64.3736 53.067 64.1229 52.7935 63.895 52.5428C63.1961 53.3746 62.3243 54.0441 61.3404 54.5046C60.3564 54.9651 59.2839 55.2056 58.1975 55.2093ZM35.9088 32.4186C35.7892 32.4204 35.6729 32.4584 35.5753 32.5276C35.4777 32.5967 35.4033 32.6938 35.3619 32.806L31.8066 43.5177C31.5597 44.2526 31.429 45.0215 31.4192 45.7967V47.4149C31.3858 48.4437 31.7319 49.449 32.3917 50.2391C33.0515 51.0292 33.9789 51.549 34.9972 51.6995C35.5468 51.7564 36.1022 51.6986 36.6282 51.5295C37.1542 51.3605 37.6394 51.084 38.0529 50.7176C38.4664 50.3512 38.7993 49.9028 39.0305 49.401C39.2616 48.8992 39.386 48.3548 39.3957 47.8023C39.3957 47.349 39.5758 46.9142 39.8963 46.5937C40.2169 46.2731 40.6516 46.093 41.105 46.093C41.5583 46.093 41.993 46.2731 42.3136 46.5937C42.6341 46.9142 42.8142 47.349 42.8142 47.8023C42.7881 48.333 42.8734 48.8632 43.0646 49.3589C43.2558 49.8546 43.5487 50.3048 43.9243 50.6805C44.3 51.0562 44.7502 51.3491 45.2459 51.5403C45.7416 51.7315 46.2718 51.8168 46.8025 51.7907C47.8584 51.7847 48.8693 51.3626 49.616 50.6159C50.3626 49.8692 50.7847 48.8583 50.7907 47.8023C50.7907 47.349 50.9708 46.9142 51.2913 46.5937C51.6119 46.2731 52.0466 46.093 52.5 46.093C52.9533 46.093 53.388 46.2731 53.7086 46.5937C54.0291 46.9142 54.2092 47.349 54.2092 47.8023C54.1831 48.333 54.2684 48.8632 54.4596 49.3589C54.6508 49.8546 54.9437 50.3048 55.3194 50.6805C55.695 51.0562 56.1452 51.3491 56.6409 51.5403C57.1366 51.7315 57.6668 51.8168 58.1975 51.7907C59.2534 51.7847 60.2643 51.3626 61.011 50.6159C61.7576 49.8692 62.1797 48.8583 62.1857 47.8023C62.1857 47.349 62.3658 46.9142 62.6863 46.5937C63.0069 46.2731 63.4416 46.093 63.895 46.093C64.3483 46.093 64.7831 46.2731 65.1036 46.5937C65.4241 46.9142 65.6042 47.349 65.6042 47.8023C65.6045 48.3605 65.7219 48.9125 65.9488 49.4225C66.1758 49.9325 66.5073 50.3891 66.9219 50.7629C67.3365 51.1367 67.825 51.4193 68.3556 51.5924C68.8863 51.7655 69.4474 51.8253 70.0027 51.7679C71.021 51.6174 71.9484 51.0976 72.6082 50.3075C73.268 49.5173 73.6142 48.5121 73.5807 47.4833V45.8651C73.5709 45.0899 73.4402 44.321 73.1933 43.586L69.6381 32.8744C69.5967 32.7622 69.5222 32.6651 69.4246 32.5959C69.327 32.5268 69.2107 32.4888 69.0911 32.487L35.9088 32.4186Z" fill="white"/>
        <path d="M70.732 78H34.2679C33.212 77.994 32.2011 77.5719 31.4544 76.8252C30.7078 76.0786 30.2857 75.0676 30.2797 74.0116V52.3149C30.2797 51.8615 30.4598 51.4268 30.7803 51.1062C31.1009 50.7857 31.5356 50.6056 31.9889 50.6056C32.4423 50.6056 32.877 50.7857 33.1976 51.1062C33.5181 51.4268 33.6982 51.8615 33.6982 52.3149V74.0116C33.6982 74.1627 33.7582 74.3077 33.8651 74.4145C33.9719 74.5214 34.1168 74.5814 34.2679 74.5814H70.732C70.8831 74.5814 71.028 74.5214 71.1348 74.4145C71.2417 74.3077 71.3017 74.1627 71.3017 74.0116V52.2921C71.3017 51.8388 71.4818 51.404 71.8023 51.0834C72.1229 50.7629 72.5577 50.5828 73.011 50.5828C73.4643 50.5828 73.8991 50.7629 74.2196 51.0834C74.5401 51.404 74.7202 51.8388 74.7202 52.2921V74.0116C74.7142 75.0676 74.2921 76.0786 73.5455 76.8252C72.7988 77.5719 71.7879 77.994 70.732 78Z" fill="white"/>
        <path d="M60.4765 78H44.5235C44.072 77.9941 43.6406 77.8121 43.3214 77.4928C43.0021 77.1735 42.8201 76.7422 42.8142 76.2907V64.8953C42.8202 63.8394 43.2423 62.8284 43.9889 62.0818C44.7356 61.3351 45.7466 60.913 46.8025 60.907H58.1975C59.2534 60.913 60.2643 61.3351 61.011 62.0818C61.7576 62.8284 62.1797 63.8394 62.1857 64.8953V76.2907C62.1798 76.7422 61.9978 77.1735 61.6786 77.4928C61.3593 77.8121 60.928 77.9941 60.4765 78ZM46.2327 74.5814H58.7672V64.8953C58.7672 64.7442 58.7072 64.5993 58.6003 64.4925C58.4935 64.3856 58.3486 64.3256 58.1975 64.3256H46.8025C46.6513 64.3256 46.5064 64.3856 46.3996 64.4925C46.2927 64.5993 46.2327 64.7442 46.2327 64.8953V74.5814Z" fill="white"/>
        </svg>

        <p class="canpay-crew-text-font-18 canpay-crew-p-margin-bottom-1 canpay-crew-text-700 mt-3">Enter Store Name</p>
        <p v-if="storeData.length == 0"  class="canpay-crew-p-margin-bottom-1 canpay-crew-text-font-16 mt-4">We couldn’t find your store. Want to start a new petition?</p>

        <div class="mt-3 row">
            <div class="col-12 text-center">
              <gmap-autocomplete
                ref="storeAddressRef"
                :value="storeAddressModel"
                placeholder="Store Name"
                @place_changed="setStorePlace"
                id="findStoreAddress"
                class="canpay-crew-general-input-box"
                :style="storeAddressError == true?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}"
                :options="autocompleteOptions">
              </gmap-autocomplete>
               <!-- Optional Error Message -->
                <div v-if="storeAddressError" class="text-red-crew">
                    Please provide a complete US store address.
                </div>

            </div>
        </div>

        <div class="row mt-3">
            <div class="col-12">
                <button class="canpay-crew-sign-petition-modal-button" v-on:click="continueStorePlace()">
                Continue
                </button>
            </div>
            <div class="col-12">
                <button class="canpay-crew-sign-petition-not-ok-modal-button mt-2" v-on:click="hideModal('canpay-crew-address-modal')">
                    Not Right Now
                </button>
            </div>

        </div>
      </div>

      <!------ Missing Details in Adress Start ---------------------->
      <div class="text-center mt-4" v-if="storeAddressModelStep == 2">
        <svg xmlns="http://www.w3.org/2000/svg" width="104" height="104" viewBox="0 0 104 104" fill="none">
        <circle cx="52" cy="52" r="52" fill="#179346"/>
        <path d="M58.1975 55.2093C57.1131 55.2205 56.0398 54.9913 55.0546 54.5381C54.0695 54.085 53.197 53.4191 52.5 52.5884C51.7978 53.413 50.9243 54.0747 49.9403 54.5274C48.9563 54.98 47.8855 55.2127 46.8025 55.2093C45.7181 55.2205 44.6448 54.9913 43.6596 54.5381C42.6745 54.085 41.802 53.4191 41.105 52.5884C40.8948 52.8405 40.6663 53.0766 40.4213 53.2949C39.654 53.993 38.7475 54.5205 37.7615 54.8426C36.7755 55.1648 35.7324 55.2742 34.701 55.1637C32.8342 54.9337 31.1181 54.0226 29.8816 52.6053C28.6451 51.1879 27.9753 49.364 28.0007 47.4833V45.8651C27.9988 44.7036 28.1835 43.5494 28.5477 42.4465L32.1257 31.7349C32.389 30.9394 32.8963 30.247 33.5753 29.7561C34.2544 29.2652 35.0709 29.0006 35.9088 29H69.0911C69.929 29.0006 70.7455 29.2652 71.4246 29.7561C72.1037 30.247 72.6109 30.9394 72.8742 31.7349L76.4523 42.4465C76.8164 43.5494 77.0011 44.7036 76.9992 45.8651V47.4833C77.0259 49.3571 76.3625 51.1753 75.1353 52.5916C73.9082 54.0079 72.203 54.9234 70.3445 55.1637C69.313 55.2772 68.2691 55.1691 67.2826 54.8469C66.2962 54.5246 65.3899 53.9955 64.6243 53.2949C64.3736 53.067 64.1229 52.7935 63.895 52.5428C63.1961 53.3746 62.3243 54.0441 61.3404 54.5046C60.3564 54.9651 59.2839 55.2056 58.1975 55.2093ZM35.9088 32.4186C35.7892 32.4204 35.6729 32.4584 35.5753 32.5276C35.4777 32.5967 35.4033 32.6938 35.3619 32.806L31.8066 43.5177C31.5597 44.2526 31.429 45.0215 31.4192 45.7967V47.4149C31.3858 48.4437 31.7319 49.449 32.3917 50.2391C33.0515 51.0292 33.9789 51.549 34.9972 51.6995C35.5468 51.7564 36.1022 51.6986 36.6282 51.5295C37.1542 51.3605 37.6394 51.084 38.0529 50.7176C38.4664 50.3512 38.7993 49.9028 39.0305 49.401C39.2616 48.8992 39.386 48.3548 39.3957 47.8023C39.3957 47.349 39.5758 46.9142 39.8963 46.5937C40.2169 46.2731 40.6516 46.093 41.105 46.093C41.5583 46.093 41.993 46.2731 42.3136 46.5937C42.6341 46.9142 42.8142 47.349 42.8142 47.8023C42.7881 48.333 42.8734 48.8632 43.0646 49.3589C43.2558 49.8546 43.5487 50.3048 43.9243 50.6805C44.3 51.0562 44.7502 51.3491 45.2459 51.5403C45.7416 51.7315 46.2718 51.8168 46.8025 51.7907C47.8584 51.7847 48.8693 51.3626 49.616 50.6159C50.3626 49.8692 50.7847 48.8583 50.7907 47.8023C50.7907 47.349 50.9708 46.9142 51.2913 46.5937C51.6119 46.2731 52.0466 46.093 52.5 46.093C52.9533 46.093 53.388 46.2731 53.7086 46.5937C54.0291 46.9142 54.2092 47.349 54.2092 47.8023C54.1831 48.333 54.2684 48.8632 54.4596 49.3589C54.6508 49.8546 54.9437 50.3048 55.3194 50.6805C55.695 51.0562 56.1452 51.3491 56.6409 51.5403C57.1366 51.7315 57.6668 51.8168 58.1975 51.7907C59.2534 51.7847 60.2643 51.3626 61.011 50.6159C61.7576 49.8692 62.1797 48.8583 62.1857 47.8023C62.1857 47.349 62.3658 46.9142 62.6863 46.5937C63.0069 46.2731 63.4416 46.093 63.895 46.093C64.3483 46.093 64.7831 46.2731 65.1036 46.5937C65.4241 46.9142 65.6042 47.349 65.6042 47.8023C65.6045 48.3605 65.7219 48.9125 65.9488 49.4225C66.1758 49.9325 66.5073 50.3891 66.9219 50.7629C67.3365 51.1367 67.825 51.4193 68.3556 51.5924C68.8863 51.7655 69.4474 51.8253 70.0027 51.7679C71.021 51.6174 71.9484 51.0976 72.6082 50.3075C73.268 49.5173 73.6142 48.5121 73.5807 47.4833V45.8651C73.5709 45.0899 73.4402 44.321 73.1933 43.586L69.6381 32.8744C69.5967 32.7622 69.5222 32.6651 69.4246 32.5959C69.327 32.5268 69.2107 32.4888 69.0911 32.487L35.9088 32.4186Z" fill="white"/>
        <path d="M70.732 78H34.2679C33.212 77.994 32.2011 77.5719 31.4544 76.8252C30.7078 76.0786 30.2857 75.0676 30.2797 74.0116V52.3149C30.2797 51.8615 30.4598 51.4268 30.7803 51.1062C31.1009 50.7857 31.5356 50.6056 31.9889 50.6056C32.4423 50.6056 32.877 50.7857 33.1976 51.1062C33.5181 51.4268 33.6982 51.8615 33.6982 52.3149V74.0116C33.6982 74.1627 33.7582 74.3077 33.8651 74.4145C33.9719 74.5214 34.1168 74.5814 34.2679 74.5814H70.732C70.8831 74.5814 71.028 74.5214 71.1348 74.4145C71.2417 74.3077 71.3017 74.1627 71.3017 74.0116V52.2921C71.3017 51.8388 71.4818 51.404 71.8023 51.0834C72.1229 50.7629 72.5577 50.5828 73.011 50.5828C73.4643 50.5828 73.8991 50.7629 74.2196 51.0834C74.5401 51.404 74.7202 51.8388 74.7202 52.2921V74.0116C74.7142 75.0676 74.2921 76.0786 73.5455 76.8252C72.7988 77.5719 71.7879 77.994 70.732 78Z" fill="white"/>
        <path d="M60.4765 78H44.5235C44.072 77.9941 43.6406 77.8121 43.3214 77.4928C43.0021 77.1735 42.8201 76.7422 42.8142 76.2907V64.8953C42.8202 63.8394 43.2423 62.8284 43.9889 62.0818C44.7356 61.3351 45.7466 60.913 46.8025 60.907H58.1975C59.2534 60.913 60.2643 61.3351 61.011 62.0818C61.7576 62.8284 62.1797 63.8394 62.1857 64.8953V76.2907C62.1798 76.7422 61.9978 77.1735 61.6786 77.4928C61.3593 77.8121 60.928 77.9941 60.4765 78ZM46.2327 74.5814H58.7672V64.8953C58.7672 64.7442 58.7072 64.5993 58.6003 64.4925C58.4935 64.3856 58.3486 64.3256 58.1975 64.3256H46.8025C46.6513 64.3256 46.5064 64.3856 46.3996 64.4925C46.2927 64.5993 46.2327 64.7442 46.2327 64.8953V74.5814Z" fill="white"/>
        </svg>

        <p class="canpay-crew-text-font-18 canpay-crew-p-margin-bottom-1 canpay-crew-text-700 mt-3">The store address is incomplete. Please add the missing details below</p>

        <div class="mt-3 row">
            <div class="col-12">
                <input type="text"
                    autocomplete="off" autocorrect="off" autocapitalize="off"
                    class="canpay-crew-general-input-box-1 canpay-crew-p-margin-bottom"
                    required placeholder="Store Name" v-model="googleStoreData.store_name"
                    :style="googleStoreDataError.error_store_name != ''?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}">
                <p class="text-red-crew" v-if="googleStoreDataError.error_store_name != ''">{{googleStoreDataError.error_store_name}}</p>
                <input type="text"
                    autocomplete="off" autocorrect="off" autocapitalize="off"
                    class="canpay-crew-general-input-box-1 canpay-crew-p-margin-bottom"
                    required placeholder="Street Address" v-model="googleStoreData.street_address"
                    :style="googleStoreDataError.error_street_address != ''?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}">
                <p class="text-red-crew" v-if="googleStoreDataError.error_street_address != ''">{{googleStoreDataError.error_street_address}}</p>
                <input type="text"
                    autocomplete="off" autocorrect="off" autocapitalize="off"
                    class="canpay-crew-general-input-box-1 canpay-crew-p-margin-bottom"
                    required placeholder="City" v-model="googleStoreData.city"
                    :style="googleStoreDataError.error_city != ''?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}">
                <p class="text-red-crew" v-if="googleStoreDataError.error_city != ''">{{googleStoreDataError.error_city}}</p>
                <input type="text"
                    autocomplete="off" autocorrect="off" autocapitalize="off"
                    class="canpay-crew-general-input-box-1 canpay-crew-p-margin-bottom"
                    required :maxlength="2" placeholder="State (e.g., CA, NY)" v-model="googleStoreData.state"
                    :style="googleStoreDataError.error_state != ''?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}">
                <p class="text-red-crew" v-if="googleStoreDataError.error_state != ''">{{googleStoreDataError.error_state}}</p>
                <input type="text"
                    autocomplete="off" autocorrect="off" autocapitalize="off"
                    class="canpay-crew-general-input-box-1 canpay-crew-p-margin-bottom"
                    required placeholder="Zipcode" v-on:keypress="zipisNumber($event)" v-model="googleStoreData.zipcode"
                    :style="googleStoreDataError.error_zipcode != ''?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}">
                <p class="text-red-crew" v-if="googleStoreDataError.error_zipcode != ''">{{googleStoreDataError.error_zipcode}}</p>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <button class="canpay-crew-sign-petition-modal-button" v-on:click="updateTheGoogleAddress()">
                    Update the Address
                </button>
            </div>

        </div>
      </div>
      <!------ Missing Details in Adress End ------------------------->
      <!------ Missing Details in Adress End ------------------------->
    </b-modal>
</div>
</div>
</template>
<script>
import CrewError from "./Modal/CrewError.vue";
import CanPayCrewStoreHeader from "../Layouts/Headers/CanPayCrewStoreHeader.vue";
import CanPayCrewStoreSearchHeader from "../Layouts/Headers/CanPayCrewStoreSearchHeader.vue";
import CanpayCrewActivePetition from "./Modal/CanpayCrewActivePetition.vue";
import CanpayCrewCreatePetition from "./Modal/CanpayCrewCreatePetition.vue";
import CanpayCrewEditConatctPersonDetail from "./Modal/CanpayCrewEditConatctPersonDetail.vue";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue";
import petitionApi from "../../api/petition.js";
import shortUrlApi from "../../api/redirect.js";
import Loading from "vue-loading-overlay";
export default {
    name:"Crew",
    directives: {
        'click-outside': {
            bind(el, binding, vnode) {
                el.clickOutsideEvent = function(event) {
                    // Check if the click was outside the el and its children
                    if (!(el == event.target || el.contains(event.target))) {
                        // Call the provided method
                        vnode.context[binding.expression](event);
                    }
                };
                document.body.addEventListener('click', el.clickOutsideEvent);
            },
            unbind(el) {
                document.body.removeEventListener('click', el.clickOutsideEvent);
            }
        }
    },
    created(){
        this.currentUser = localStorage.getItem("consumer_login_response")
		? JSON.parse(localStorage.getItem("consumer_login_response"))
		: null;
    },
    data(){
        return {
            currentUser:null,
            canPayCrewLanding:0,
            oldCanPayCrewLanding:0,
            storeData:[],
            searchedStoreData:[],
            isLoading:false,
            storeObj:{
                store_name:"",
                page:1,
                per_page:10
            },
            activeStoresObj:{
                page:1,
                per_page:5
            },
            activeStores:[],
            petitionStatistics:{
                consumer_active_petition_count :0,
                total_store_petition_count:0,
                total_signers: 0,
                total_accept_canpay_stores_via_crew : 0,
                lifetime_rewards_from_crew: 0,
                show_brand_new_total_store_petition_count:false,
                show_brand_new_total_signers:false,
                show_brand_new_total_accept_canpay_stores:false,
                show_brand_new_lifetime_rewards_from_crew:false
            },
            selectedPetition:{},
            petition_id:null,
            contactDetails:{},
            showLoadMore:true,
            currentUser:null,
            fixedHeight:"",
            petitionLink:"",
            textMessage:"",
            max_consumer_allowed:process.env.VUE_APP_MAX_CONSUMER_ALLOWED_TO_SIGN,
            loading: false,
            ActiveStoreloading:false,
            showActiveLoader: true,
            googleAutoSearch: false,
            storeAddressModel: '',
            autocompleteOptions: {
                types: ['establishment'], // Only search businesses like stores
                componentRestrictions: { country: 'us' }
            },
            googleStoreData:{},
            storeAddressModelStep: 1, // Flag for error handling
            storeAddressError: false, // Flag for error handling
            googleStoreDataError:{
                error_store_name: '',
                error_street_address: '',
                error_city: '',
                error_state: '',
                error_zipcode: ''
            },
            usaState: [
                "AA","AE","AK","AL","AP","AR","AS",
                "AZ","CA","CO","CT","DC","DE","FL","FM","GA",
                "GU","HI","IA","ID","IL","IN","KS",
                "KY","LA","MA","MD","ME","MH","MI","MN","MO","MP",
                "MS","MT","NC","ND","NE","NH","NJ","NM","NV","NY",
                "OH","OK","OR","PA","PR","PW","RI","SC","SD","TN",
                "TX","UT","VA","VI", "VT","WA","WI", "WV", "WY"
            ],
            petition_per_consumer:process.env.VUE_APP_SIGN_MAX_PETITION_PER_CONSUMER,
            consumer_active_petition_count:0,
            displayJustLaunched:false,
            isScrollLoading: false,
            stopScrolling:true,
            // Store dropdown properties
            showStoreDropdown: false,
            storeDropdownItems: [],
            hoveredIndex: -1,
            searchPlaceholder: '',
            searchTimeout: null,
            selectedStore: null
        }
    },
    components: {
        CanPayCrewStoreHeader,
        CanPayCrewStoreSearchHeader,
        CanpayCrewActivePetition,
        CanpayCrewCreatePetition,
        CanpayCrewEditConatctPersonDetail,
        CanPayLoader,
        CrewError,
        Loading
    },
    created(){
        this.currentUser = localStorage.getItem("consumer_login_response")
        ? JSON.parse(localStorage.getItem("consumer_login_response"))
        : null;
    },
    watch:{
        /**
         * This watch function is used to change the background
         * color and height of content-wrap when the canPayCrewLanding
         * variable is changed. The canPayCrewLanding variable is used to
         * switch between different components in the CanPayCrew
         * component.
         * @param {Number} newValue - The new value of canPayCrewLanding.
         * @param {Number} oldValue - The old value of canPayCrewLanding.
         */
        isLoading(newValue,oldValue){
            if(this.canPayCrewLanding == 1){
                this.$root.$emit("crewLaodingOn",[newValue])
            }
            if(newValue == false){
                if(this.canPayCrewLanding == 1){
                    var element = document.getElementsByClassName("content-wrap");
                    if (element[0]) {
                    element[0].style.setProperty("background", "#149240")
                        element[0].style.height = "";
                        if(window.innerWidth>1200){
                            element[0].style.height = "";
                        }
                    }
                    setTimeout(() => {
                        const totalScreenHeight = document.getElementById('nav-drawer').scrollHeight;
                        console.log(totalScreenHeight);
                        const petitionTextDiv = document.getElementById('find-height-for-crew-describe-div');

                        const clientDetails = petitionTextDiv.getBoundingClientRect();
                        console.log(clientDetails);
                        const increaseDivHeight = totalScreenHeight - 125 - clientDetails.height;

                        const petitionDetail = document.getElementById('find-height-for-petition-div');
                        petitionDetail.style.height = increaseDivHeight + "px";

                },100);
                }else{
                        var element = document.getElementsByClassName("content-wrap");
                        if (element[0]) {
                        this.$root.$emit("changeWhiteBackground", [false, false, "none", true]);
                        element[0].style.height = "114vh";
                        if(window.innerWidth>1200){
                            element[0].style.height = "121vh";
                        }
                        }
                }
            }
        },
        canPayCrewLanding(newValue, oldValue){
            let self = this;
            self.loading = false;
            self.stopScrolling = true;
            self.showActiveLoader = true;
            self.oldCanPayCrewLanding = oldValue;
            self.storeObj.page = 1;
            self.showLoadMore = true;
            if(newValue == 1){
                var element = document.getElementsByClassName("content-wrap");
                if (element[0]) {

                element[0].style.height = "none";
                if(window.innerWidth>1200){
                    element[0].style.height = "none";
                }
                }
            }
            if(newValue != 1){
            var element = document.getElementsByClassName("content-wrap");
            if (element[0]) {
            element[0].style.height = "114vh";
            if(window.innerWidth>1200){
                element[0].style.height = "121vh";
            }
            }
            }
            if(newValue == 2){
                self.storeData = [];
                self.searchedStoreData = [];
                self.resetSearch();
            }
            if(newValue == 2 && oldValue == 1){
                self.storeObj.store_name = "";
            }
            if(newValue == 1 && oldValue != 1){
                self.getPetitionStatistics();
            }else if(newValue == 2){
                self.getCurrentLocation();
            }else if(newValue == 4){
                self.fetchContact();
            }
            if(newValue == 2 || newValue == 3){
                this.$root.$emit("customIntercomButton", {
                    show: true,
                    type: "launch_petition"
                });
            } else {
                this.$root.$emit("customIntercomButton", {
                    show: false,
                    type: ""
                });
            }
        },
        googleStoreData: {
            handler(newData) {
                if (this.storeAddressModelStep == 2) {
                    this.validateTheGoogleAddress(newData);
                }
            },
            deep: true // This ensures it watches all properties within googleStoreData
        }
        // googleStoreData(newState) {
        //     this.validateTheGoogleAddress(newState);
        // }
    },
    mounted(){
        const backStep = localStorage.getItem("crewBackButtonStep");

        if (backStep !== null && !isNaN(Number(backStep))) {
            localStorage.removeItem("crewBackButtonStep");
            this.canPayCrewLanding = Math.min(Number(backStep), 2);
        } else {
            this.canPayCrewLanding = 1;
        }

        var element = document.getElementsByClassName("content-wrap");
        if (element[0]) {
        element[0].style.setProperty("background", "#149240");
        }
        this.$root.$emit("loginapp", [""]);
        this.$root.$emit("changeWhiteBackground", [false, false, "CanPayCrew"]);
        document.getElementById('nav-drawer').addEventListener('scroll', this.handleScroll);
    },

    beforeDestroy() {
    // Your existing beforeDestroy code...

    // Remove event listener
    window.addEventListener('scroll', this.handleScroll);
    },
    methods:{
    // Store dropdown methods
    closeDropdown() {
        this.showStoreDropdown = false;
        this.hoveredIndex = -1;
    },

    navigateDropdown(direction) {
        // Only navigate if dropdown is open and has items
        if (!this.showStoreDropdown || this.storeDropdownItems.length === 0) return;

        // Calculate new index
        if (direction > 0) {
            // Moving down
            if (this.hoveredIndex < this.storeDropdownItems.length - 1) {
                this.hoveredIndex++;
            } else {
                this.hoveredIndex = 0; // Wrap to top
            }
        } else {
            // Moving up
            if (this.hoveredIndex > 0) {
                this.hoveredIndex--;
            } else {
                this.hoveredIndex = this.storeDropdownItems.length - 1; // Wrap to bottom
            }
        }

        // Ensure the highlighted item is visible in the dropdown
        this.$nextTick(() => {
            const dropdown = document.querySelector('.store-dropdown');
            const highlightedItem = dropdown.querySelectorAll('.store-dropdown-item')[this.hoveredIndex];
            if (dropdown && highlightedItem) {
                // Check if item is outside visible area
                const dropdownRect = dropdown.getBoundingClientRect();
                const itemRect = highlightedItem.getBoundingClientRect();

                if (itemRect.bottom > dropdownRect.bottom) {
                    // Item is below visible area
                    dropdown.scrollTop += (itemRect.bottom - dropdownRect.bottom);
                } else if (itemRect.top < dropdownRect.top) {
                    // Item is above visible area
                    dropdown.scrollTop -= (dropdownRect.top - itemRect.top);
                }
            }
        });
    },

    selectHighlightedStore() {
        // Only proceed if dropdown is open and an item is highlighted
        if (this.showStoreDropdown && this.hoveredIndex >= 0 && this.hoveredIndex < this.storeDropdownItems.length) {
            const selectedStore = this.storeDropdownItems[this.hoveredIndex];
            this.selectStore(selectedStore);
        }
    },

    handleSearchIconClick() {
        // If we have a selected store and no text in the input, clear the selection
        if (this.selectedStore && !this.storeObj.store_name) {
            this.resetSearch();
            return;
        }

        // Otherwise, perform the search
        this.fetchStore(this.storeObj);
    },

    onSearchInput() {
        // Clear any existing timeout
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // If we have a selected store and user starts typing, clear the selection
        if (this.selectedStore && this.storeObj.store_name) {
            this.selectedStore = null;
            this.searchPlaceholder = '';
        }

        // Hide dropdown if input is empty
        if (!this.storeObj.store_name || this.storeObj.store_name.length < 3) {
            this.storeDropdownItems = [];
            this.showStoreDropdown = false;
            return;
        }

        // Set a timeout to fetch stores for dropdown
        this.searchTimeout = setTimeout(() => {
            this.fetchStoresForDropdown();
        }, 300); // 300ms debounce
    },

    fetchStoresForDropdown() {
        let self = this;
        // Only search if we have at least 3 characters
        if (self.storeObj.store_name.length < 3) return;

        // Create a temporary object for the search to avoid modifying the main storeObj
        const searchObj = {
            store_name: self.storeObj.store_name,
            page: 1,
            per_page: 5 // Limit to 5 results for dropdown
        };

        // Show loading indicator in dropdown
        self.storeDropdownItems = [{ loading: true, store_name: 'Loading...', city: '' }];
        self.showStoreDropdown = true;

        petitionApi.searchStore(searchObj)
            .then((res) => {
                if (res.data && res.data.length > 0) {
                    self.storeDropdownItems = res.data;
                }else{
                    self.showStoreDropdown = false;
                }
            })
            .catch((err) => {
                console.error('Error fetching stores for dropdown:', err);
                self.storeDropdownItems = [{ store_name: 'Error loading stores', city: '', error: true }];
            });
    },

    selectStore(store) {
        // Don't do anything for loading or error items
        if (store.loading || store.error || store.noResults) return;

        // Set the selected store
        this.selectedStore = store;

        // Update the placeholder with the store name
        this.searchPlaceholder = `${store.store_name} - ${store.city}`;

        // Hide the dropdown
        this.showStoreDropdown = false;

        // if the store status is pending 
        if(store.pending_petition == 1){
            this.gotoDetailsPage(store)
        }
        else{
            // Automatically trigger search with the selected store
            this.searchWithSelectedStore(store);
        }

    },

    searchWithSelectedStore(store) {
        // Create a temporary object with the store data for search
        const searchObj = {
            store_name: store.store_name,
            page: 1,
            per_page: this.storeObj.per_page
        };

        // Trigger the search
        this.fetchStore(searchObj);
    },

    // Original scroll handler
    handleScroll() {
        // Get the scrollable container - nav-drawer is the correct element to track
        const navDrawer = document.getElementById('nav-drawer');
        if (!navDrawer) return;

        // Get scroll position within the nav-drawer
        const scrollTop = navDrawer.scrollTop;
        const viewportHeight = navDrawer.clientHeight;
        const scrollHeight = navDrawer.scrollHeight;

        // Calculate distance from bottom
        const distanceFromBottom = scrollHeight - (scrollTop + viewportHeight);
        // If we're near the bottom (within 50px), load more data
        if (Number(distanceFromBottom) < 50 && !this.loading && !this.isScrollLoading && this.stopScrolling) {
            if (this.$route.name === 'canpaycrew' && this.canPayCrewLanding == 2) {
                // Prevent multiple calls by setting a loading flag
                this.isScrollLoading = true;
                this.fetchPetition(true,false,true);
            }
        }
        if(Number(distanceFromBottom) < 50 && !this.ActiveStoreloading && !this.isScrollLoading && this.stopScrolling){
            if (this.$route.name === 'canpaycrew' && this.canPayCrewLanding == 3) {
                // Prevent multiple calls by setting a loading flag
                this.isScrollLoading = true;
                this.fetchStoreDetails();
            }
        }
    },
        zipisNumber: function (evt) {
            evt = evt ? evt : window.event;
            var charCode = evt.which ? evt.which : evt.keyCode;
            if (charCode < 48 || charCode > 57) {
                evt.preventDefault();
            } else {
                return true;
            }
        },
        clearErrors() {
            let self = this;
            self.googleStoreDataError = {
                error_store_name: '',
                error_street_address: '',
                error_city: '',
                error_state: '',
                error_zipcode: ''
            };
        },
        validateTheGoogleAddress(googleStoreData){
            let self = this;
            let hasError = false;
            // Reset all errors first
            self.clearErrors();

            // Validate each field
            if (!googleStoreData.store_name) {
                self.googleStoreDataError.error_store_name = 'Store Name is required';
                hasError = true;
            }
            if (!googleStoreData.street_address) {
                self.googleStoreDataError.error_street_address = 'Street Address is required';
                hasError = true;
            }
            if (!googleStoreData.city) {
                self.googleStoreDataError.error_city = 'City is required';
                hasError = true;
            }
            // State validation (case-insensitive)
            let state = self.googleStoreData.state || '';  // Ensure state is a string, fallback to empty string if null or undefined

            state = state.trim();  // Now it's safe to use trim()
            if (!state) {
                self.googleStoreDataError.error_state = 'State is required';
                hasError = true;
            } else if (state.length === 1) {
                self.googleStoreDataError.error_state = 'State must be exactly 2 characters';
                hasError = true;
            } else if (!this.usaState.map(s => s.toUpperCase()).includes(state.toUpperCase())) {
                // self.googleStoreDataError.error_state = 'Invalid state. Please enter a valid US state code';
                self.googleStoreDataError.error_state = `Invalid state. Please enter a valid US state code. Valid states: ${this.usaState.join(', ')}`;

                hasError = true;
            }
            if (!googleStoreData.zipcode) {
                self.googleStoreDataError.error_zipcode = 'Zipcode is required';
                hasError = true;
            } else if (!/^\d+$/.test(googleStoreData.zipcode)) {
                self.googleStoreDataError.error_zipcode = 'Zipcode must be numbers only';
                hasError = true;
            }

            if (hasError) {
                // There are validation errors, don't continue
                console.log('Validation failed');
                return false;
            } else {
                console.log('Validation passed');
                return true;
            }
        },
        updateTheGoogleAddress(){
            let self = this;
            if(self.validateTheGoogleAddress(self.googleStoreData)){
                self.hideModal("canpay-crew-address-modal");
                self.createPetition(self.googleStoreData);
            }
        },
        setStorePlace(place) {
            var self = this;
            //Need to remove this console
            console.log('place');
            console.log(place);
            const getAddressComponent = (components, type, useShortName = false) => {
                const comp = components.find(c => c.types.includes(type));
                return comp ? (useShortName ? comp.short_name : comp.long_name) : null;
            };

            const formattedAddress = place.formatted_address || '';
            const streetNumber = getAddressComponent(place.address_components, 'street_number');
            const route = getAddressComponent(place.address_components, 'route');
            const streetAddress = [streetNumber, route].filter(Boolean).join(' ') || null;
            const aptNumber = null; // Autocomplete won't provide this — you can ask the user separately
            const city = getAddressComponent(place.address_components, 'locality') ||
                        getAddressComponent(place.address_components, 'sublocality') ||
                        getAddressComponent(place.address_components, 'administrative_area_level_2');
            let state = getAddressComponent(place.address_components, 'administrative_area_level_1', true); // short_name here
            // Make comparison case-insensitive by converting both to uppercase
            if (state && !self.usaState.map(s => s.toUpperCase()).includes(state.toUpperCase())) {
                state = null;
            }

            const zipcode = getAddressComponent(place.address_components, 'postal_code');
            const lat = place.geometry?.location?.lat?.();
            const lng = place.geometry?.location?.lng?.();

            self.googleStoreData = {
                store_name: place.name || null,
                formatted_address: formattedAddress,
                street_address: streetAddress,
                apt_number: aptNumber,
                city: city,
                state: state,
                zipcode: zipcode,
                place_id: place.place_id || null,
                latitude: lat ?? null,
                longitude: lng ?? null
            };
            self.storeAddressError = false;

            self.storeAddressModel = `${place.name || ''}, ${place.formatted_address || ''}`;

        },
        continueStorePlace() {
            var self = this;
            if (Object.keys(self.googleStoreData).length === 0) {
                // Show error if any required field is missing
                self.storeAddressError = true;
                return false;
            } else {
                // 🔍 Validate required fields
                const { store_name, street_address, city, state, zipcode } = this.googleStoreData;

                if (!store_name || !street_address || !city || !state || !zipcode) {
                    // Reset all errors first
                    self.clearErrors();
                    self.storeAddressModelStep = 2; // Switch to manual entry
                    self.storeAddressError = false;
                    return false;
                } else {
                    self.storeAddressError = false;
                    self.hideModal("canpay-crew-address-modal");
                    self.createPetition(self.googleStoreData);

                    return true;
                }
            }
        },
        hideModal(modal) {
            this.$refs[modal].hide();
        },
        provideAdditionalContact(item){
            let self = this;
            self.$refs.CanPayCrewCreatePetition.provideAdditionalContacts(item,false);
        },
        passPetitionStatistics(){
            let self = this;
            return self.petitionStatistics;
        },
        gotoDetailsPage(item) {
            this.setBackButtonStep();
            this.$root.$emit("Show_current_petition_store_name", [item.store_name, item.logo_url, item.store_short_name]);
            this.$router.push({ name: 'PetitionDetails', params: { encodedData: btoa(item.id) } });
        },
        toBase64(str) {
          return btoa(str);
        },
        shareThePetition(payload,currStoreData){
            let self = this;
            const baseUrl = window.location.origin;
            // Combine them with a colon (or another delimiter)
            const combined = `${currStoreData.id}:${self.currentUser.user_id}`;

            // Encode the full string
            self.petitionLink = `${baseUrl}/petitiondetails/${btoa(combined)}`;
            self.isLoading = true;
            // Step 1: Call your short URL API
            shortUrlApi.createShortUrl(self.petitionLink)
            .then((res) => {
                const shortCode = res.data;
                self.petitionLink = `${baseUrl}/s/${shortCode}`; // Use your route for short URL redirection
            })
            .finally(() =>{
                petitionApi
                .petitionShare(payload)
                .then((res) => {
                    const storeName = currStoreData.store_name;
                    const subjectMessage = `Let's bring CanPay to ${storeName} through signing a petition`;

                    const message = `Hi,\r\n\r\n` +
                    `${self.currentUser.first_name} here. I have been using CanPay for a while now and would like to introduce it to ${storeName}. I need your support by signing the petition.\r\n\r\n` +
                    `Click on the link below and sign the petition to help bring CanPay here:\r\n` +
                    `${self.petitionLink}\r\n\r\n` +
                    `Also, please share it with others to increase the reach.\r\n\r\n` +
                    `Thank you,\r\n\r\n` +
                    `${self.currentUser.first_name} ${self.currentUser.last_name}`;

                    if (payload.type === 'email') {
                        const email = encodeURIComponent(payload.email);
                        const subject = encodeURIComponent(subjectMessage);
                        const body = encodeURIComponent(message);

                        window.location.href = `mailto:${email}?subject=${subject}&body=${body}`;
                    } else if (payload.type === 'phone') {
                        const phone = payload.email.replace(/\D/g, ''); // Remove non-numeric characters
                        const smsBody = encodeURIComponent(message);

                        // iOS vs Android handling
                        const smsUrl = /iPhone|iPad|iPod/i.test(navigator.userAgent)
                            ? `sms:${phone}&body=${smsBody}`
                            : `sms:${phone}?body=${smsBody}`;

                        window.location.href = smsUrl;
                    }

                })
                .catch((err) => {
                    this.textMessage = err.response.data.message;
                    this.$refs.CrewError.showErrorModal();
                })
                .finally(() =>{
                    self.isLoading = false;
                })
            })
        },
        signPetition(payload){
            let self = this;
            self.isLoading = true;
            petitionApi
            .petitionSign(payload)
            .then((res) => {
                this.$refs.CanpayCrewActivePetition.showSignSharingPetition(res.data.type);
                self.fetchPetition();
            })
            .catch((err) => {
                this.textMessage = err.response.data.message;
                this.$refs.CrewError.showErrorModal();
            })
            .finally(() =>{
                self.isLoading = false;
            })
        },
        addAdditionalContact(payload){
            let self = this;
            self.isLoading = true;
            petitionApi
            .addAdditionalContact(payload)
            .then((res) => {
                self.$refs.CanPayCrewCreatePetition.closeCanPayCrewCreatePetition()
            })
            .catch((err) => {
                this.textMessage = err.response.data.message;
                this.$refs.CrewError.showErrorModal();
            })
            .finally(() =>{
                self.isLoading = false;
            })
        },
        getPetitionId(){
            let self = this;
            return self.petition_id;
        },
        createThePetition(payload){
            let self = this;
            self.petition_id = null;
            self.isLoading = true;
            this.$refs.CanPayCrewCreatePetition.closeCanPayCrewCreatePetition(false);
            petitionApi
            .createPetition(payload)
            .then((res) => {
                self.isLoading = false;
                self.petition_id = res.data.petition_id;
                this.$refs.CanPayCrewCreatePetition.showFormSucessMessage();
            })
            .catch((err) => {
                this.textMessage = err.response.data.message;
                this.$refs.CrewError.showErrorModal();
                if(err && err.response.data.code == 598){
                    setTimeout(()=>{
                        self.setBackButtonStep();
                        this.$router.push({ name: 'PetitionDetails', params: { encodedData: btoa(err.response.data.data) } });
                    },3010);
                }


                self.isLoading = false;
            })

        },
        storeAddress(storeAdd) {
        var full_address = "";
        full_address =
            full_address +
            (storeAdd.street_address != null ? storeAdd.street_address + "<br>" : "");
        full_address =
            full_address + (storeAdd.city != null ? storeAdd.city + ", " : "");
        full_address =
            full_address + (storeAdd.state != null ? storeAdd.state + " " : "");
        full_address =
            full_address + (storeAdd.zipcode != null ? storeAdd.zipcode + " " : "");
        return full_address;
        },
        fetchContact(){
            let self = this;
            const payload = {
                petition_id: self.selectedPetition.id
            }
            self.isLoading = true;
            petitionApi
            .getContact(payload)
            .then((res)=>{
                self.contactDetails = res.data;
            })
            .catch((err) => {
                this.textMessage = err.response.data.message;
                this.$refs.CrewError.showErrorModal();
            })
            .finally(() => {
                self.isLoading = false;
            })
        },
        editContact(paylaod){
            let self = this;
            self.isLoading = true;
            this.$refs.CanpayCrewEditConatctPersonDetail.closeEditContact();
            petitionApi
            .editContact(paylaod)
            .then((res)=>{
                self.isLoading = false;
                self.fetchContact();
                this.$refs.CanpayCrewEditConatctPersonDetail.openSuccessEditModal();
                setTimeout(() => {
                    this.$refs.CanpayCrewEditConatctPersonDetail.closeEditContact();
                },2000);
            })
            .catch((err)=>{
                this.textMessage = err.response.data.message;
                this.$refs.CrewError.showErrorModal();
                self.isLoading = false;
            })
            .finally(()=>{
            })
        },
        changeComponent(val, item = null){
            let self = this;
            if(val == 4){
                self.selectedPetition = item;
            }

            // Reset search state when navigating away from search page
            if (self.canPayCrewLanding === 2 && val !== 2) {
                self.resetSearch();
            }

            self.canPayCrewLanding = val;
        },

        resetSearch() {
            this.storeObj.store_name = '';
            this.searchPlaceholder = '';
            this.selectedStore = null;
            this.storeDropdownItems = [];
            this.showStoreDropdown = false;
        },
        getPetitionStatistics(){
            let self = this;
            self.isLoading = true;
            petitionApi
            .getPetitionStatistics()
            .then((res)=>{

                self.petitionStatistics.consumer_active_petition_count = res.data.consumer_active_petition_count;
                self.consumer_active_petition_count = res.data.consumer_active_petition_count;
                self.petitionStatistics.lifetime_rewards_from_crew = res.data.lifetime_rewards_from_crew;
                self.petitionStatistics.total_accept_canpay_stores_via_crew = res.data.total_accept_canpay_stores_via_crew;
                self.petitionStatistics.total_signers = res.data.total_signers;
                self.petitionStatistics.total_store_petition_count = res.data.total_store_petition_count;
                self.petitionStatistics.show_brand_new_total_store_petition_count = res.data.show_brand_new_total_store_petition_count;
                self.petitionStatistics.show_brand_new_total_signers = res.data.show_brand_new_total_signers;
                self.petitionStatistics.show_brand_new_total_accept_canpay_stores = res.data.show_brand_new_total_accept_canpay_stores;
                self.petitionStatistics.show_brand_new_lifetime_rewards_from_crew = res.data.show_brand_new_lifetime_rewards_from_crew;

            })
            .catch((err) => {

            })
            .finally(() => {
                self.isLoading = false;
                this.$root.$emit("changeWhiteBackground", [false, false, "CanPayCrew"]);
            })
        },
        triggerGoogleAutocomplete() {
            // const autocompleteInput = this.$refs.storeAddressRef?.$el?.querySelector('input');
            const autocompleteInput = document.getElementById('findStoreAddress');
            if (autocompleteInput) {
                autocompleteInput.focus();

                // Programmatically set input value
                const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
                    window.HTMLInputElement.prototype,
                    'value'
                ).set;
                nativeInputValueSetter.call(autocompleteInput, this.storeAddressModel);

                // Dispatch input event to trigger suggestions
                autocompleteInput.dispatchEvent(new Event('input', { bubbles: true }));

                // Optional: simulate keypress to help trigger the dropdown
                autocompleteInput.dispatchEvent(new KeyboardEvent('keydown', { bubbles: true, keyCode: 40 }));
            }
        },
        findStoreToCreatePetition(){
            let self = this;
            self.storeAddressError = false;
            self.googleStoreData = {};
            self.storeAddressModelStep = 1; // 1 for google place serch, 2 for manual entry
            self.storeAddressModel = self.storeObj.store_name;
            this.$refs["canpay-crew-address-modal"].show();
            // Wait for DOM to update, then focus
            this.$nextTick(() => {
                setTimeout(() => {
                    this.triggerGoogleAutocomplete();
                }, 300); // Delay slightly to allow rendering
            });
        },
        initiateSearchingStore(item){
            let self = this;
            self.storeData = [];
            self.fetchStore(item,true,true,false);
        },
        /**
         * Fetch the list of stores based on the store name
         * @param {Object} storeObj - The object containing the store name, page and per page
         */
        fetchStoreDetails(){
            let self = this;
            self.storeObj.page++;
            self.fetchStore(self.storeObj, false, false, true);
        },
        fetchStore(storeObj, initial = true, allowLoading = true, activateSpinLoader = false){
            let self = this;
            if (initial) {
                self.storeObj.page = 1;
                self.showLoadMore = true;
                // Clear existing store data when starting a new search
                self.storeData = [];
            }
            // this variable tell wheather to use global CanPay Loader or not
            // false when clicking on the load more store.
            if(allowLoading)
                self.isLoading = true
            // This variable tell weather to active the spin loader or not
            // this is to show thing spin animation for lazy loading in store Search.
            if(activateSpinLoader)
                self.loading = true

            petitionApi
            .searchStore(storeObj)
            .then((res)=>{
                self.searchedStoreData = []
                if(allowLoading){
                    setTimeout(()=>{
                        self.isLoading = false;
                    },500);
                }
                if(activateSpinLoader)
                    self.loading = false

                res.data.forEach( currentStore => {
                    self.storeData.push(currentStore)
                })
                if(res.pagination.total_results <= self.storeData.length){
                    self.stopScrolling = false;
                }
                if (self.storeData.length == 0) {
                    self.findStoreToCreatePetition();
                } else {
                    self.canPayCrewLanding = 3;
                }

                // If this was triggered by a store selection, update the placeholder
                if (self.selectedStore) {
                    self.searchPlaceholder = `${self.selectedStore.store_name} - ${self.selectedStore.city}`;
                }
            })
            .catch((err) =>{
                self.canPayCrewLanding = 3;
                if(allowLoading)
                    self.isLoading = false;
                if(activateSpinLoader)
                    self.loading = false
            }).finally(()=>{
                this.isScrollLoading = false;
            })
        },
        fetchPetition(nextPageData = false,allowLoading = true, activateSpinLoader = false){
            let self = this;
            if(nextPageData){
                self.activeStoresObj.page++;
            }else{
                self.activeStores = [];
                self.activeStoresObj.page = 1;
            }
            const payload = {
                lat: parseFloat(localStorage.getItem("current_lat")),
                long: parseFloat(localStorage.getItem("current_long")),
                page:self.activeStoresObj.page,
                per_page:self.activeStoresObj.per_page,
                status: "pending",
                top_five: true
            }

            if(allowLoading)
                self.isLoading = true;
            if(activateSpinLoader)
                self.ActiveStoreloading = true;

            petitionApi
            .petitionList(payload)
            .then((res)=>{
                self.consumer_active_petition_count = res.data.consumer_active_petition_count;
                res.data.data.forEach((currELement) => {
                    self.activeStores.push(currELement);
                })
                if(self.activeStores.length>=res.data.total){
                    self.showActiveLoader = false;
                    self.stopScrolling = false;
                }
            })
            .catch((err) => {
                this.textMessage = err.response.data.message;
                this.$refs.CrewError.showErrorModal();
            })
            .finally(()=>{
                if(allowLoading)
                    self.isLoading = false;
                if(activateSpinLoader)
                    self.ActiveStoreloading = false;
                this.isScrollLoading = false;
            })
        },
        storePage(){
            let self = this;
            this.$root.$emit("changeWhiteBackground", [false, false, "none", true]);
            self.canPayCrewLanding = 2;
            self.activeStores = [];
            self.activeStoresObj.page = 1;
        },
        getCurrentLocation(){
            let self = this;
            if (!navigator.geolocation) {
                console.warn("Geolocation not supported by this browser.");
                self.fetchPetition();
                return;
            }
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    self.center = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude,
                    };
                    self.currentLat = self.center.lat;
                    self.currentLong = self.center.lng;
                    localStorage.setItem("current_lat", parseFloat(self.center.lat));
                    localStorage.setItem("current_long", parseFloat(self.center.lng));
                    self.fetchPetition();
                },
                (error) => {
                    console.warn("Geolocation error:", error.message);
                    // Optional: set fallback coords here
                    self.fetchPetition();
                }
            );
        },
        viewPetition(){
            let self = this;
            self.$router.push("/canpaycrewpetition")
        },
        setBackButtonStep(){
            let self = this;
            localStorage.setItem("crewBackButtonStep", self.canPayCrewLanding);
        },
        showActiveCanPayCrew(item){
            this.$refs.CanpayCrewActivePetition.showCanPayCrewActivePetition(item);
        },
        createPetition(item){
            let self = this;
            self.petition_id = null;
            self.isLoading = true;
            self.$refs.CanPayCrewCreatePetition.closeCanPayCrewCreatePetition(false);
            petitionApi
            .petitionExistsCheck(item)
            .then((res) => {
                self.isLoading = false;
                self.$refs.CanPayCrewCreatePetition.showCanpayCrewCreatePetition(item);
            })
            .catch((err) => {
                self.textMessage = err.response.data.message;
                self.$refs.CrewError.showErrorModal();
                if(err && err.response.data.code == 598){
                    setTimeout(()=>{
                        self.setBackButtonStep();
                        this.$router.push({ name: 'PetitionDetails', params: { encodedData: btoa(err.response.data.data) } });
                    },3010);
                }


                self.isLoading = false;
            })
        },
        openEditContactDetail(contactDetailState){
            let self = this;
            this.$refs.CanpayCrewEditConatctPersonDetail.openEditContact(self.contactDetails,contactDetailState);
        }

    }
}
</script>
<style scoped>
</style>
