.btn-register {
    width: 100px;
    height: 40px;
}

.otp-curser {
    text-align: center !important;
}

.otp-curser::placeholder {
    text-align: center !important;
}

.quick-pin-created-style {
    font-family: $cp-font;
    font-size: 14px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #000000;
    margin-top: 15px;
}

.enter-quick-pin {
    font-family: $cp-font;
    font-size: 17px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-white;
}

.heading-row {
    margin-top: 10px;
    margin-bottom: 10px;
}

.sub-heading-row {
    margin-bottom: 20px;
}

.success-popup-style {
    font-family: $cp-font;
    font-size: 22px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #000000;
}

.success-top-spacing {
    height: 55px !important;
}

.pin-success-top-spacing {
    height: 35px !important;
}

.success-bottom-spacing {
    height: 35px !important;
}

.btn-ResendCode {
    height: 55px !important;
    width: 100%;
    border-radius: 6px !important;
    border-color: transparent !important;
    background-color: $cp-secondary !important;
    font-family: $cp-font;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: 1px !important;
    text-align: center;
    color: $cp-white;
    cursor: pointer;
}

.btn-verify {
    height: 55px !important;
    border-radius: 6px !important;
    width: 100% !important;
    border-color: transparent !important;
    background-color: #000000 !important;
    font-family: $cp-font-secondary;
    font-size: 14px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-white;
    margin-top: 5px !important;
    cursor: pointer;
}

.register-label {
    font-weight: bold;
    color: $cp-white;
    text-align: left;
}

.left-padding {
    padding-left: 20px !important;
}

.rounting-form-group {
    width: 100% !important;
    margin-bottom: 0px !important;
    padding-left: 10px !important;
}

.form-group {
    width: 100% !important;
    margin-bottom: 0px !important;
}

.form-group .form-control {
    padding-left: 2.575rem !important;
    height: 50px !important;
    border-radius: 6px !important;
    margin-left: 0px !important;
}

.hyperlink {
    color: $cp-black;
    text-decoration: none !important;
}

.pin-text-style {
    font-family: $cp-font;
    font-size: 11px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: $cp-white;
    float: left !important;
    margin-left: -102px;
    margin-bottom: 15px;
}

.reenter-pin-text-style {
    font-family: $cp-font;
    font-size: 11px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: $cp-white;
    float: left !important;
    margin-left: -132px;
    margin-top: 15px;
    margin-bottom: 15px;
}

.btn-next {
    height: 55px !important;
    border-radius: 8px !important;
    width: 100% !important;
    border-color: transparent !important;
    background-color: #000000 !important;
    font-family: $cp-font-secondary;
    font-size: 14px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-white;
    cursor: pointer;
    margin-left: 5px !important;
    margin-right: 5px !important;
}

.col-padding {
    padding: 30px !important;
}

.access-pin-description {
    font-family: $cp-font;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-white;
}

.btn-reg-month-style {
    background-color: $cp-white !important;
    height: 50px !important;
    border-radius: 8px 0px 0px 8px !important;
    border: solid white;
    border-width: 1px 0 1px 1px;
}

.btn-reg-day-style {
    background-color: $cp-white !important;
    height: 50px !important;
    border-radius: 8px !important;
    border-width: 1px;
}

.input-reg-day-style {
    height: 50px !important;
    background-color: $cp-white !important;
    color: #000000 !important;
    text-align: center !important;
    border-radius: 8px !important;
}

.btn-reg-dropdown-style {
    color: $cp-black !important;
    width: 100% !important;
    background-color: $cp-white !important;
    height: 50px !important;
    border-radius: 6px !important;
    border: solid $cp-white;
    border-width: 1px 1px 1px 1px;
}

.digit-text-style {
    font-family: $cp-font;
    font-size: 13px;
    color: $cp-white;
    margin-left: -139px;
    margin-bottom: 14px;
    margin-top: 20px;
}

.pin-text-style {
    font-family: $cp-font;
    font-size: 13px;
    color: white;
    margin-left: 296px;
    margin-bottom: 14px;
    margin-top: 20px;
}

.reenter-digit-text-style {
    margin-left: -115px;
    margin-bottom: 14px;
    margin-top: 10px;
    font-family: $cp-font;
    font-size: 13px;
    color: $cp-white;
}

.pin-link-text-style {
    margin-top: 10px;
    font-family: $cp-font;
    font-size: 13px;
    color: $cp-white;
    text-decoration: underline;
}

.pin-link-text-style-black {
    margin-top: 10px;
    font-family: $cp-font;
    font-size: 13px;
    color: $cp-black;
    text-decoration: underline;
}

.reenter-digit-text-style-new {
    margin-left: -80px;
    margin-bottom: 14px;
    margin-top: 10px;
    font-family: $cp-font;
    font-size: 13px;
    color: $cp-white;
}

.ssn-input-box-padding {
    display: table-cell !important;
    width: 55px !important;
    border-radius: 8px !important;
    height: 55px !important;
    margin-left: 5px !important;
}

.ssn-text {
    font-weight: 900 !important;
    font-size: 23px !important;
    color: $cp-black !important;
    text-align: center !important;
}

.ssn-text::placeholder {
    text-align: center !important;
}

.ssn-heading {
    font-family: $cp-font;
    font-size: 15px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-white;
}

.ssn-row {
    margin-top: 27px !important;
    margin-bottom: 30px !important;
}

.profile-btn-cancel {
    border: 0;
    background: #000;
    padding: 10px 25px;
    margin: 0;
    border-radius: 3px;
}

.profile-btn-ok {
    border: 0;
    background: #149240;
    color: #fff;
    padding: 10px 25px;
    margin: 0;
    border-radius: 3px;
}

@media only screen and (min-width: 369px) and (max-width: 375px) {
    .reenter-digit-text-style {
        margin-left: -120px;
        margin-bottom: 14px;
        margin-top: 10px;
        font-family: "Open Sans";
        font-size: 13px;
        color: white;
    }
    .pin-link-text-style {
        margin-top: 10px;
        font-family: "Open Sans";
        font-size: 13px;
        color: white;
        text-decoration: underline;
    }
    .pin-link-text-style-black {
        margin-top: 10px;
        font-family: "Open Sans";
        font-size: 13px;
        color: black;
        text-decoration: underline;
    }
}

@media only screen and (min-width: 360px) and (max-width: 368px) {
    .reenter-digit-text-style {
        margin-left: -115px;
        margin-bottom: 14px;
        margin-top: 10px;
        font-family: "Open Sans";
        font-size: 13px;
        color: white;
    }
    .pin-link-text-style {
        margin-top: 10px;
        font-family: "Open Sans";
        font-size: 13px;
        color: white;
        text-decoration: underline;
    }
    .pin-link-text-style-black {
        margin-top: 10px;
        font-family: "Open Sans";
        font-size: 13px;
        color: black;
        text-decoration: underline;
    }
}

@media only screen and (min-width: 385px) {
    .reenter-digit-text-style {
        margin-left: -112px;
        margin-bottom: 14px;
        margin-top: 10px;
        font-family: "Open Sans";
        font-size: 13px;
        color: white;
    }
    .pin-link-text-style {
        margin-top: 10px;
        font-family: "Open Sans";
        font-size: 13px;
        color: white;
        text-decoration: underline;
    }
    .pin-link-text-style-black {
        margin-top: 10px;
        font-family: "Open Sans";
        font-size: 13px;
        color: black;
        text-decoration: underline;
    }
}

@media only screen and (min-width: 369px) and (max-width: 375px) {
    .reenter-digit-text-style-new {
        margin-left: -120px;
        margin-bottom: 14px;
        margin-top: 10px;
        font-family: "Open Sans";
        font-size: 13px;
        color: white;
    }
}

@media only screen and (min-width: 360px) and (max-width: 368px) {
    .reenter-digit-text-style-new {
        margin-left: -80px;
        margin-bottom: 14px;
        margin-top: 10px;
        font-family: "Open Sans";
        font-size: 13px;
        color: white;
    }
}

@media only screen and (min-width: 385px) {
    .reenter-digit-text-style-new {
        margin-left: -81px;
        margin-bottom: 14px;
        margin-top: 10px;
        font-family: "Open Sans";
        font-size: 13px;
        color: white;
    }
}

// --------------------------------------300px-600px----------------------
@media only screen and (min-width: 300px) and (max-width: 359px) {
    .enter-quick-pin {
        font-family: $cp-font;
        font-size: 13px;
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: center;
        color: $cp-white;
    }
    .heading-row {
        margin-top: 5px;
        margin-bottom: 5px;
    }
    .sub-heading-row {
        margin-bottom: 5px;
    }
    .ssn-heading {
        font-family: $cp-font;
        font-size: 13px;
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: center;
        color: $cp-white;
    }
    .digit-text-style {
        font-family: $cp-font;
        font-size: 12px;
        font-style: Opensans;
        color: $cp-white;
        margin-left: -154px;
        margin-bottom: 14px;
        margin-top: 20px;
    }
    .pin-text-style {
        font-family: $cp-font;
        font-size: 12px;
        color: white;
        margin-left: -4px;
        margin-bottom: 14px;
        margin-top: 20px;
    }
    .reenter-digit-text-style {
        margin-left: -115px;
        margin-bottom: 14px;
        margin-top: 10px;
        font-family: $cp-font;
        font-size: 12px;
        font-style: Opensans;
        color: $cp-white;
    }
    .pin-link-text-style {
        margin-top: 10px;
        font-family: $cp-font;
        font-size: 12px;
        font-style: Opensans;
        color: $cp-white;
        text-decoration: underline;
    }
    .pin-link-text-style-black {
        margin-top: 10px;
        font-family: $cp-font;
        font-size: 12px;
        font-style: Opensans;
        color: $cp-black;
        text-decoration: underline;
    }
    .reenter-digit-text-style-new {
        margin-left: -80px;
        margin-bottom: 14px;
        margin-top: 10px;
        font-family: $cp-font;
        font-size: 12px;
        font-style: Opensans;
        color: $cp-white;
    }
    .ssn-input-box-padding {
        display: table-cell !important;
        width: 48px !important;
        border-radius: 8px !important;
        // padding-left: 22px !important;
        height: 48px !important;
        margin-left: 5px !important;
        float: left;
    }
    .eye-icon {
        color: $cp-white;
        margin-right: -15px;
        margin-left: 15px;
        margin-top: 13px;
        // float: left;
    }
    .eye-icon-div {
        margin-left: -10px;
        // margin-top: 15px;
    }
    .m-l-7 {
        margin-left: 7px;
    }
    .enter-otp-title-style {
        font-family: $cp-font;
        font-size: 11px;
        font-weight: 500;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: center;
        color: $cp-white;
    }
    .error-div {
        margin-left: 31px !important;
        margin-right: 31px !important;
        background-color: $cp-red !important;
        color: $cp-white !important;
        height: 30px !important;
        border-radius: 0px 0px 8px 8px !important;
    }
    .text-msg-label {
        // margin-top: 15px !important;
        margin-bottom: 1.5rem !important;
    }
    .enter-otp-input {
        margin-left: 16px !important;
        margin-right: 16px !important;
        margin-top: 15px;
    }
    .btn-resend-code {
        margin-left: 30px !important;
        margin-right: 30px !important;
        margin-top: 10px;
    }
    .middile-space {
        height: 25px !important;
    }
    .moredetails-bottom-space {
        height: 55px !important;
    }
    .emailsent-bottom-space {
        height: 65px !important;
    }
    .emailsent-top-space {
        height: 50px !important;
    }
    .quick-access-top-space {
        height: 55px !important;
    }
    .quick-access-botton-space {
        height: 30px !important;
    }
    .no-account-top-space {
        height: 30px !important;
    }
    .no-account-bottom-space {
        height: 30px !important;
    }
    .succes-svg-padding {
        margin-left: -13px;
    }
}

@media only screen and (min-width: 360px) and (max-width: 369px) {
    .pin-text-style {
        font-family: $cp-font;
        font-size: 12px;
        color: white;
        margin-left: -4px;
        margin-bottom: 14px;
        margin-top: 20px;
    }
    .heading-row {
        margin-top: 10px;
        margin-bottom: 10px;
    }
    .sub-heading-row {
        margin-bottom: 10px;
    }
    .eye-icon {
        color: $cp-white;
        margin-right: -30px;
        margin-left: 15px;
        //float: left;
    }
    .eye-icon-div {
        margin-left: -10px;
        // margin-top: 15px;
    }
    .enter-otp-title-style {
        font-family: $cp-font;
        font-size: 13px;
        font-weight: 500;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: center;
        color: $cp-white;
    }
    .error-div {
        margin-left: 31px !important;
        margin-right: 31px !important;
        background-color: $cp-red !important;
        color: $cp-white !important;
        height: 30px !important;
        border-radius: 0px 0px 8px 8px !important;
    }
    .text-msg-label {
        margin-top: 10px !important;
    }
    .enter-otp-input {
        margin-left: 16px !important;
        margin-right: 16px !important;
        margin-top: 15px;
    }
    .btn-resend-code {
        margin-left: 30px !important;
        margin-right: 30px !important;
        margin-top: 10px;
    }
    .middile-space {
        height: 35px !important;
    }
    .moredetails-bottom-space {
        height: 85px !important;
    }
    .emailsent-bottom-space {
        height: 55px !important;
    }
    .emailsent-top-space {
        height: 50px !important;
    }
    .verify-otp-bottom-space {
        height: 45px !important;
    }
    .quick-access-top-space {
        height: 85px !important;
    }
    .quick-access-botton-space {
        height: 60px !important;
    }
    .no-account-top-space {
        height: 40px !important;
    }
    .no-account-bottom-space {
        height: 40px !important;
    }
}

@media only screen and (min-width: 370px) and (max-width: 770px) {
    .pin-text-style {
        font-family: $cp-font;
        font-size: 12px;
        color: white;
        margin-left: 0px;
        margin-bottom: 14px;
        margin-top: 20px;
    }
    .eye-icon {
        color: $cp-white;
        // float: left;
        margin-right: -30px;
        margin-left: 15px;
    }
    .eye-icon-div {
        margin-left: -10px;
        // margin-top: 15px;
    }
    .enter-otp-title-style {
        font-family: $cp-font;
        font-size: 15px;
        font-weight: 500;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: center;
        color: $cp-white;
    }
    .error-div {
        margin-left: 31px !important;
        margin-right: 31px !important;
        background-color: $cp-red !important;
        color: $cp-white !important;
        height: 30px !important;
        border-radius: 0px 0px 8px 8px !important;
    }
    .text-msg-label {
        margin-top: 1.5rem !important;
        margin-bottom: 3.5rem !important;
    }
    .enter-otp-input {
        margin-left: 16px !important;
        margin-right: 16px !important;
        margin-top: 20px;
    }
    .btn-resend-code {
        margin-left: 30px !important;
        margin-right: 30px !important;
        margin-top: 20px;
    }
    .middile-space {
        height: 55px !important;
    }
    .moredetails-bottom-space {
        height: 85px !important;
    }
    .emailsent-bottom-space {
        height: 85px !important;
    }
    .emailsent-top-space {
        height: 70px !important;
    }
    .verify-otp-bottom-space {
        height: 55px !important;
    }
    .quick-access-top-space {
        height: 95px !important;
    }
    .quick-access-botton-space {
        height: 60px !important;
    }
    .no-account-top-space {
        height: 60px !important;
    }
    .no-account-bottom-space {
        height: 60px !important;
    }
}

@media only screen and (min-width: 400px) and (max-width: 1000px) {
    .pin-text-style {
        font-family: $cp-font;
        font-size: 12px;
        color: white;
        margin-left: 30px;
        margin-bottom: 14px;
        margin-top: 20px;
    }
    .heading-row {
        margin-top: 10px;
        margin-bottom: 10px;
    }
    .sub-heading-row {
        margin-bottom: 10px;
    }
    .btn-space {
        margin-top: 30px;
    }
    .enter-otp-title-style {
        font-family: $cp-font;
        font-size: 15px;
        font-weight: 500;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: center;
        color: $cp-white;
    }
    .eye-icon {
        color: $cp-white;
        margin-right: -30px;
        margin-left: 15px;
        // float: left;
    }
    .eye-icon-div {
        // margin-top: 15px;
    }
    .text-msg-label {
        margin-top: 1.5rem !important;
        margin-bottom: 3.5rem !important;
    }
    .enter-otp-input {
        margin-left: 16px !important;
        margin-right: 16px !important;
        margin-top: 20px;
    }
    .btn-resend-code {
        margin-left: 30px !important;
        margin-right: 30px !important;
        margin-top: 20px;
    }
    .moredetails-bottom-space {
        height: 130px !important;
    }
    .emailsent-bottom-space {
        height: 65px !important;
    }
    .emailsent-top-space {
        height: 50px !important;
    }
    .verify-otp-bottom-space {
        height: 60px !important;
    }
    .quick-access-top-space {
        height: 135px !important;
    }
    .quick-access-botton-space {
        height: 60px !important;
    }
    .no-account-top-space {
        height: 80px !important;
    }
    .no-account-bottom-space {
        height: 80px !important;
    }
    .middile-space {
        height: 55px !important;
    }
}

@media only screen and (width: 800px) {
    .eye-icon {
        color: $cp-white;
        margin-right: -30px;
        margin-left: 15px;
        // float: left;
    }
    .eye-icon-div {
        // margin-top: 15px;
        margin-left: -175px;
    }
    .pin-text-style {
        font-family: $cp-font;
        font-size: 12px;
        color: white;
        margin-left: 174px;
        margin-bottom: 14px;
        margin-top: 20px;
    }
}

@media only screen and (width: 768px) {
    .succes-svg-padding {
        margin-left: 20px;
    }
    .middile-space {
        height: 75px !important;
    }
    .pin-text-style {
        font-family: $cp-font;
        font-size: 12px;
        color: white;
        margin-left: 174px;
        margin-bottom: 14px;
        margin-top: 20px;
    }
}

//for tablets and ipad pro
@media only screen and (width: 1024px) {
    .eye-icon {
        color: $cp-white;
        margin-right: -30px;
        margin-left: 15px;
        // float: left;
    }
    .eye-icon-div {
        // margin-top: 15px;
        margin-left: -291px;
    }
    .col-padding {
        padding: 100px !important;
    }
    .enter-otp-title-style {
        font-family: $cp-font;
        font-size: 20px;
        font-weight: 500;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: center;
        color: $cp-white;
    }
    .error-div {
        margin-left: 64px !important;
        margin-right: 64px !important;
        background-color: $cp-red !important;
        color: $cp-white !important;
        height: 30px !important;
        border-radius: 0px 0px 8px 8px !important;
    }
    .text-msg-label {
        margin-top: 1.5rem !important;
        margin-bottom: 3.5rem !important;
    }
    .enter-otp-input {
        margin-left: 50px !important;
        margin-right: 50px !important;
        margin-top: 40px;
    }
    .btn-resend-code {
        margin-left: 66px !important;
        margin-right: 66px !important;
        margin-top: 30px;
    }
    .succes-svg-padding {
        margin-left: 20px;
    }
    .code-sent-top-margin {
        margin-top: 6px;
    }
    .quick-access-top-space {
        height: 85px !important;
    }
    .quick-access-botton-space {
        height: 60px !important;
    }
    .middile-space {
        height: 75px !important;
    }
}

//for desktop and larger screens
@media (min-width: 1281px) {
    .eye-icon {
        color: $cp-white;
        // margin-top: 23px;
        margin-right: -30px;
        margin-left: 15px;
    }
    .enter-otp-title-style {
        font-family: $cp-font;
        font-size: 15px;
        font-weight: 500;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: center;
        color: $cp-white;
    }
    .error-div {
        margin-left: 66px !important;
        margin-right: 66px !important;
        background-color: $cp-red !important;
        color: $cp-white !important;
        height: 30px !important;
        border-radius: 0px 0px 8px 8px !important;
    }
    .text-msg-label {
        margin-top: 1.5rem !important;
        margin-bottom: 3.5rem !important;
    }
    .enter-otp-input {
        margin-left: 51px !important;
        margin-right: 51px !important;
        margin-top: 28px;
    }
    .btn-resend-code {
        margin-left: 67px !important;
        margin-right: 67px !important;
        margin-top: 10px;
    }
    .succes-svg-padding {
        margin-left: 20px;
    }
    .quick-access-top-space {
        height: 85px !important;
    }
    .quick-access-botton-space {
        height: 60px !important;
    }
    .middile-space {
        height: 55px !important;
    }
}

// for long heights
@media (min-height: 700px) {
    .button-space {
        margin-top: 30px !important;
    }
    .col-pad {
        padding: 30px !important;
    }
}

@media only screen and (min-height: 650px) and (max-height: 667px) {
    .text-msg-label {
        margin-top: 5px !important;
    }
}

@media (min-height: 500px) and (max-height: 599px) {
    .container .email-row-space {
        margin-top: 10px !important;
    }
}

@media (max-width: 360px) and (max-height: 640px) {
    .margin-button {
        margin-top: 1rem!important;
    }
}

@media (min-width: 361px) {
    .margin-button {
        margin-top: 3rem!important;
    }
}

.margin-button {
    margin-top: 3rem;
}

.margin-pin-asterisk {
    padding-top: 10px!important;
}

#pin-textbox-modal___BV_modal_body_ {
    background-color: #ffffff;
    border-radius: 12px;
    margin: 10px;
}

#ssn-textbox-modal___BV_modal_body_ {
    background-color: #ffffff;
    border-radius: 12px;
    margin: 10px;
}