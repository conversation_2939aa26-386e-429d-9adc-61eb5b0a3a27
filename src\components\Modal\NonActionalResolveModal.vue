<template>
    <div>
        <div>
            <b-modal
                ref="non-actionable-status-resolve-modal"
                hide-footer
                no-close-on-backdrop
                no-close-on-esc
                modal-backdrop
                hide-header
                id="non-actionable-status-resolve-modal"
                centered
            >
                <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="110" height="110" viewBox="0 0 197 197" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H197V197H0V0Z" fill="url(#pattern0_0_557)"/>
                        <defs>
                        <pattern id="pattern0_0_557" patternContentUnits="objectBoundingBox" width="1" height="1">
                        <use xlink:href="#image0_0_557" transform="scale(0.00507614)"/>
                        </pattern>
                        <image id="image0_0_557" width="197" height="197" xlink:href="data:image/png;base64,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"/>
                        </defs>
                        </svg>
                        <p class="mt-3 mb-2 non-actionable-status-css">
                            CanPay’s connection to your bank has restored and your Purchase Power is updated. Thank you.
                        </p>
                        <button class="ok-button-non-actional" @click="hideModal('non-actionable-status-resolve-modal')">
                            OK
                        </button>
                </div>
            </b-modal>
        </div>
    </div>
</template>
<script>
export default{
    name:"NonActionalActionStatusModal",
    data(){
        return{

        }
    },
    methods:{
        hideModal(){
            let self = this;
             self.$bvModal.hide("non-actionable-status-resolve-modal");
             if(self.$route.name == 'pay')
                self.$router.go();
            else
                self.$router.push("/pay");
        },
        showModal(){
            let self = this;
             self.$bvModal.show("non-actionable-status-resolve-modal");    
        }
    }

}
</script>
<style scoped>
.non-actionable-status-css{
    font-size:15px;
    font-family:'Montserrat';
    color:#000000;
    font-weight:600;
}
#non-actionable-status-resolve-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#non-actionable-status-resolve-modal___BV_modal_content_{
    background-color: #ffffff;

}
.ok-button-non-actional{
    border:none;
    width:100%;
    padding:12px;
    border-radius:5px;
    background-color:#000000;
    font-family:"Montserrat";
    font-size:18px;
    font-weight:bolder;
    color:#ffffff;
}
</style>