# CanPay Consumer App

## Purpose

The README file serves as a comprehensive guide to understanding, setting up, and contributing to this project. Its primary purposes include:

- **Introduction:** Provide a brief overview of the project, its goals, and its intended audience.

- **Installation Instructions:** Guide users through the process of installing and configuring the necessary dependencies, tools, and services to get the project up and running.

- **Configuration Details:** Detail any required configuration settings, environment variables, or external services necessary for the project's proper functioning.

- **Usage Guidelines:** Explain how to use the software, including essential commands, features, and functionalities. This section should help users navigate and interact with the project effectively.

- **Project Architecture:** Offer a high-level view of the project's structure and key components. This section helps users and contributors understand how different parts of the system work together.

- **API Documentation (if applicable):** If the project includes an API, provide details on available endpoints, request/response formats, and authentication mechanisms.

- **Changelog:** Provide a history of notable changes to the project, including new features, bug fixes, and improvements. This helps users stay informed about the project's evolution.

# Introduction
CanPay offers payment solutions for the State Regulated Cannabis Industry and other emerging markets.
CanPay started with one goal – to bring traditional and legitimate electronic payment solutions to highly-regulated industries. We’ve spent years learning about the unique challenges that face consumers and retailers in these industries and used that experience to design CanPay as the stable payment solution they can rely on.

With 15 years of industry experience, the CanPay team of payments professionals is building a proprietary network of consumers, retailers, financial institutions, and specialized technology providers that make payments at regulated businesses just like payments everywhere else.

As a CanPay Mobile Debit App user, consumers can make debit purchases with no convenience fees. By accepting CanPay, retailers can offer their customers the convenience of a debit payment while mitigating the risks and costs associated with cash and increasing consumer spending. Financial Institutions who participate in the CanPay Closed-Banking Feedback Loop give their highly-regulated business clients access to a legitimate payment tool while also ensuring only compliant transactions flow through the CanPay network.

CanPay – The Better Way!



# Project Information

* [Project Requirement Document](https://drive.google.com/drive/u/1/folders/1vONIYz_RCIey9Q43soHZaLxCRwcGyPJA)
* [Project Management Plan](https://docs.google.com/document/d/1tvuheOb8xqZJxpyqKxR3PckYYNqfTK2nW3ajs2DPw7E/edit?usp=drive_link)
* [Architechture Document](https://docs.google.com/presentation/d/1p8Lrydn1mjKRBBaGdDtUh5tAa0v4BjIX/edit)
* [Test Plan](https://docs.google.com/document/d/1VPc8fH24WOowrkvAZZSWAkq5Jey7tnfft5bEO66wRMQ/edit)
* [Test Report](https://docs.google.com/spreadsheets/d/1eq19za1SMZyNa4PiP6QZFNlBcO-0ymqep4wl3Kbo2ns/edit#gid=**********)
* [Task/Issue Tracking](https://optimore.int.bluecoppertech.com/app/page/board.php?project_id=163)
* [API Documentation](https://documenter.getpostman.com/view/********/2sA2rDwLJU)
* [Changelog](https://docs.google.com/spreadsheets/d/1HZNdo0JzERmxamH13jPUAmf21kto_RENRCi0SvMXlp0/edit#gid=*********)

**Notes**: 
* All the documents are accessible through Rivethammer account only.
* Log all feature, requests and bugs in Optimore.

## Architecture
The project has multiple servers running behind an Application Load Balancer and database maintained in RDS.

![Architechture Overview](contrib/architecture/images/architechture.png "Architechture Overview")

# Running on Linux/Windows

### Vhost configuration
The sample vhost configuration can be found [here](contrib/architecture/vhosts/example.vhost.conf)

## Prerequisites

* Node JS 14.15
* NPM 6.14
* Vue JS 2.7
* Git & Git Bash (Windows/Linux)
* Firebase account

## Running it for first time in developer system
Checkout the appropriate branch at a known location.

## Configuration (local/staging/dev/prod) - First time

1. Go to the project root directory and run the following command

2. ```cp .env .env.local```

3. Change the .env configuration accordingly.

4. ```sh setup.sh```

## First time run for dev

  Open a new terminal and  run 

 ```npm run serve```


# Building/Deploying in production

* Fetch the latest tag that needs to be released

    ``` git fetch --tags ```

* Checkout the specific tag that need to be deployed

    ``` git checkout <tag_version> ```

* Recheck the tag that just checked out
  
   ```git describe --tags```

* Copy any new changes from .env.example to .env file

* Run the setup.sh file to install any external dependencies using the following command

    ```sh setup.sh```
