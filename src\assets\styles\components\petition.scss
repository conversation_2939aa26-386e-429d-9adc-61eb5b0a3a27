.petition-flow {
  min-height: 100vh;
  background-color: #008F39;
  padding: 24px;
}

.petition-container {
  max-width: 480px;
  margin: 0 auto;
}

.logo-section {
  display: flex;
  gap: 16px;
  margin-bottom: 30px;

  .logo {
    width: 230px;
    height: 55px;
    background: white;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    
    &.canpay {
      img {
        height: 62px;
      }
    }

    &.herbn {
      font-family: 'Open Sans';
      font-size: 18px;
      font-weight: 800;
    }
  }
}

.content-section {
  color: white;
  text-align: center;

  .icon-container {
    margin: 0 auto 24px;
    font-size: 48px;
    transform: rotate(-8deg);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  }

  h1 {
    font-size: 28px;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 16px;
  }

  .subtitle {
    font-size: 16px;
    line-height: 1.5;
    opacity: 0.9;
    margin-bottom: 32px;
  }
}

.progress-info {
  .count-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 12px;
    font-size: 16px;

    i {
      font-size: 18px;
    }
  }

  .progress-bar {
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    margin-bottom: 8px;
    overflow: hidden;

    .progress {
      height: 100%;
      background: white;
      border-radius: 2px;
      transition: width 0.3s ease;
    }
  }

  .progress-numbers {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    opacity: 0.8;
  }
}

.why-accept-section {
  background-color: #f5f5f5;
  border-radius: 12px;
  padding: 20px 0px;

  h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 32px;
  }

  .features-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 40px;
  }

  .feature-item {
    background: white;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    align-items: center;

    .feature-icon {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;

      svg {
        color: #008F39;
        width: 24px;
        height: 24px;
      }
    }

    .feature-text {
      h3 {
        font-size: 16px;
        font-weight: 500;
        color: #000000;
        margin: 0;
      }

      p {
        font-size: 12px;
        color: #000000;
        margin: 4px 0 0;
      }
    }
  }

  .coverage-info {
    text-align: center;
    margin-top: 40px;

    h3 {
      font-size: 18px;
      font-weight: 500;
      color: #333;
      margin-bottom: 8px;
    }

    .states-count {
      font-weight: bold;
      margin-bottom: 24px;
    }

    .us-map {
      border-radius: 8px;
      padding: 15px 24px 24px 24px ;
      
      img {
        width: 100%;
        height: auto;
        max-width: 400px;
      }
    }
  }
} 


.logo-section-padded{
  display: flex;
  justify-content: center;
  padding-top: 5px;
}

.key-features-section {
  background-color: #000;
  color: white;
  padding: 32px 0px;

  h2 {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 32px;
    text-align: left;
  }

  .features-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin: 0 auto;
  }

  .feature-card {
    background: white;
    border-radius: 8px;
    padding: 20px 15px;
    display: flex;
    gap: 16px;
    align-items: flex-start;

    .feature-icon {
      width: 40px;
      height: 40px;
      background-color: #E8F5E9;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      svg {
        width: 24px;
        height: 24px;
        color: #008F39;
      }
    }

    .feature-content {
      h3 {
        color: #333;
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 8px 0;
      }

      p {
        color: #000000;
        font-size: 14px;
        line-height: 1.5;
        margin: 0;
      }
    }
  }
}
.consumer-section-for-merchant-crew{
  background-color: white;
  padding: 16px 24px;
  margin: 0px 0px 20px 0px;
  border-radius:8px;

  h2 {
    font-size: 18px;
    font-weight: bold;
    color: #000000;
    margin-bottom: 16px;
  }

  p {
    font-size: 14px;
    line-height: 1.6;
    font-weight: 500;
  }
}
.why-consumers-section {
  background-color: white;
  padding: 32px 24px;
  max-width: 480px;
  margin: 0px 20px 20px 20px;
  border-radius:8px;

  h2 {
    font-size: 18px;
    font-weight: bold;
    color: #000000;
    margin-bottom: 16px;
  }

  p {
    font-size: 14px;
    line-height: 1.6;
    font-weight: 500;
  }
}

.signers-section {
  width: 100% !important;

  h2 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    margin-top: -30px;
  }

  .signers-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 24px;
  }

  .signer-row {
    background: #f5f5f5;
    border-radius: 8px;
    padding: 12px 16px;

    .signer-info {
      display: flex;
      align-items: center;
      gap: 12px;

      svg {
        color: #666;
      }

      span {
        color: #333;
        font-size: 14px;
      }
    }
  }

  .view-all-btn {
    width: 100%;
    padding: 12px;
    background: #f5f5f5;
    border: none;
    border-radius: 8px;
    color: #666;
    font-size: 14px;
    cursor: pointer;
    margin-top: 8px;

    &:hover {
      background: #e0e0e0;
    }
  }

  .mayor-info {
    display: flex;
    align-items: center;
    gap: 16px;
    margin: 32px 0;
    padding: 16px;
    background: #f5f5f5;
    border-radius: 8px;

    .mayor-avatar {
      width: 48px;
      height: 48px;
      background: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      svg {
        color: #666;
      }
    }

    .mayor-details {
      h3 {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0;
      }

      p {
        font-size: 14px;
        color: #666;
        margin: 4px 0 0;
      }
    }
  }

  .accept-button-petition {
    width: 100%;
    padding: 16px;
    background: #008F39;
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background: darken(#008F39, 5%);
    }
  }
}
/* Styles for buttons that need to coexist with Intercom */
.intercom-button-row {
    position: fixed;
    bottom: 27px;
    left: 0;
    width: 100%;
    margin-top: 20px;
    z-index: 100;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding-right:18px;
}
.intercom-aligned-button {
  margin-bottom: 0 !important;
  background-color: #179346 !important; /* Keep the green color */
  color: white !important;
  border-radius: 30px !important;
  border: none !important;
  font-weight: 600 !important;
  text-align: center;
  padding:4px 10px 5px 10px;
}

.intercom-schedule-call-button{
  margin-bottom: 0 !important;
  background-color: #179346 !important; /* Keep the green color */
  color: white !important;
  border-radius: 30px !important;
  border: none !important;
  font-weight: 600 !important;
  text-align: center;
  padding: 4px 10px 5px 10px;
  margin-left: 20px;
}

/* Add padding to the bottom of the content to ensure space for Intercom */
.petition-content-container {
  padding-bottom: 80px;
}

.floating-accept-button {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
  background-color: #008F39;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  font-weight: bold;
}


.floating-accept-button:hover {
  transform: translateY(-2px);
}

.floating-accept-button {
  z-index: 9999;
}

.canpay-crew-petition-list {
  padding-left: 20px;
  margin: 0;
  text-align: left;
}

.canpay-crew-petition-list li {
  margin-bottom: 8px;
}

.mail-button-wrapper {
  background-color: #008F39;
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 16px;
  color: white;
  max-width: 360px;
  cursor: pointer;
}

.mail-icon-container {
  background-color: white;         /* white square */
  width: 55px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-grey-circle {
  background-color: #D9D9D9;       /* grey circle */
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-grey-circle svg {
  width: 18px;
  height: 18px;
}

.mail-text {
  display: flex;
  flex-direction: column;
}

.mail-header {
  font-weight: 600;
  font-size: 14px;
  margin: 0;
  line-height: 1.2;
  text-align: left;
}

.mail-subtext {
  font-size: 12px;
  margin: 4px 0 0;
  line-height: 1.2;
  text-align: left;
}

.crew-slider-container {
  position: relative;
  background-color: #d5d5d5;
  padding: 15px;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 1rem;
  width: 100%;
}

.crew-slider-track {
  display: flex;
  width: 100%;
  transition: transform 0.5s ease-in-out;
}

.crew-slide {
  width: 100%;
  flex-shrink: 0;
  padding: 10px;
  box-sizing: border-box;
}

input[type="radio"] {
  display: none;
}

#crew-slide1:checked ~ .crew-slider-track {
  transform: translateX(0%);
}
#crew-slide2:checked ~ .crew-slider-track {
  transform: translateX(-50%);
}

.crew-card {
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.crew-mail-button {
  background-color: #008f39;
  border-radius: 16px;
  color: white;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  cursor: pointer;
}

.crew-edit-button {
  color: white;
  cursor: pointer;
}

.mail-header {
  margin: 0;
  font-weight: 600;
}

.mail-subtext {
  margin: 0;
  font-size: 13px;
}

/* Arrows */
.arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: black;
  color: white;
  border-radius: 50%;
  font-size: 18px;
  padding: 0px 10px;
  cursor: pointer;
  z-index: 1;
}
.arrow-left {
  left: 10px;
}
.arrow-right {
  right: 10px;
}

@media (max-width: 768px) {
  .crew-slider-container {
    padding: 12px;
  }

  .crew-slider-track {
    width: 100%;
  }

  .crew-slide {
    width: 100% !important;
    padding: 10px 5px;
  }

  #crew-slide1:checked ~ .crew-slider-track {
    transform: translateX(0%);
  }

  #crew-slide2:checked ~ .crew-slider-track {
    transform: translateX(-100%);
  }

  .crew-card {
    padding: 15px;
    width: 100%;
    box-sizing: border-box;
  }

  .crew-card .row {
    flex-direction: column;
  }

  .crew-card .col-8,
  .crew-card .col-4 {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0 !important;
  }

  .crew-mail-button {
    width: 100%;
    margin-top: 12px;
    justify-content: flex-start;
    align-items: center;
    text-align: left;
    flex-direction: row; /* side by side */
    gap: 10px;
    padding: 10px;
    font-size: 14px;
    border-radius: 12px;
  }

  .crew-edit-button {
    margin-top: 12px;
    font-size: 14px;
  }

  .mail-icon-container {
    width: 40px;
  }

  .mail-header {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
  }

  .mail-subtext {
    margin: 0;
    font-size: 12px;
  }

  .arrow {
    font-size: 20px;
    padding: 0px 10px;
  }

  .arrow-left {
    left: 5px;
  }

  .arrow-right {
    right: 5px;
  }
}


/* Responsive adjustments */
@media only screen and (max-width: 375px) {
  
  .intercom-aligned-button {
    font-size: 12px !important;
  }
  
  .petition-content-container {
    padding-bottom: 70px;
  }

  .intercom-schedule-call-button{
    padding-right:50px !important;
    margin-left:12px !important;
  }
  
  .crew-mail-button {
    align-items: center;
    text-align: center;
  }
}

@media only screen and (min-width: 768px) {

  .petition-content-container {
    padding-bottom: 90px;
  }

  .intercom-schedule-call-button{
    margin-left:12px !important;
  }

  .crew-mail-button-desktop {
    width: 100%;
    padding: 16px;
    border-radius: 12px;
    transition: all 0.2s ease;
  }

    .mail-icon-container {
      width: 50px;
      height: 45px;
      margin-right: 4px;
    }

    .mail-text-content {
      flex: 1;
    }

    .mail-header {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .mail-subtext {
      font-size: 14px;
      opacity: 0.9;
      line-height: 1.3;
    }
  }


