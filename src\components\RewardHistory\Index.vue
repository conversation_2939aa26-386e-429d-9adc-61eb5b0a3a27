<template>
<div>
    <div v-if="isLoading">
        <CanPayLoader/>
    </div>
  <div class="container min-h py-3" v-else>


    <div >
        
        <RwDatePicker @change="onSelectDate" class="mb-3" v-model="filter"/>

        <div v-if="rewardPointHistories.length > 0" class="historyScrollArea" id="historyScrollArea">
            <h5 class="text-left text-white mb-3">History</h5>
            <a v-for="history in rewardPointHistories" :key="history.id" class="rp-detail-card-link" data-toggle="collapse" href="#rpDetailCollapse1" role="button" aria-expanded="false" aria-controls="rpDetailCollapse1">
            <div class="rp-detail-card mb-3 pb-1" style="position: relative;">
                <div class="rp-detail-card-header">
                    <div v-if="history.sponsor_link_id || history.is_brand" class="tran-title mb-2">+${{ pointNumberFormatter(amountNumberFormatter(history.reward_amount)) }}</div>
                    <div v-if="history.sponsor_link_id" class="tran-store-name mb-0">CannaPlan Funds to Points</div>
                    <div v-if="history.is_brand && history.reason == 'Brand converted to points'" class="tran-store-name mb-0">{{history.retailer}} Funds to Points</div>
                    <hr v-if="history.sponsor_link_id || history.is_brand" class="my-2">
                    <div v-if="!history.sponsor_link_id && history.entry_type == 'Cr' && !history.is_brand" >
                        <div>
                            <div class="tran-title mb-2">+${{ pointNumberFormatter(amountNumberFormatter(history.reward_amount)) }}</div>
                            <div class="tran-store-name mb-0">{{history.retailer}}</div>
                        </div>
                        <div class="tran-store-logo">
                            <img v-bind:src="history.logo_url" v-if="history.logo_url != null && history.logo_url != ''" alt="" style="width:100%; height: 90%;">
                        </div>
                        <hr class="my-2">
                    </div>
                    <div class="row justify-content-between m-0">
                        <div class="rp-title">
                            <svg class="mr-2" width="30" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg" v-if="(history.segment_name!= 'Loser' && history.is_generic_point == 1 && history.reason != 'transaction') || (history.reason == 'transaction' && isRewardUsed(history.generic_reward_amount_used))">
                            <path d="M58.0327 44.4525C60.969 38.2154 59.7673 32.2515 59.4272 30.6694C59.0417 28.8825 58.0667 24.66 54.5183 20.8699C52.8282 19.0889 50.821 17.6413 48.6004 16.6018C45.6075 15.2019 43.0567 14.9287 41.6622 14.7921C35.9144 14.223 31.1643 15.9075 28.5114 17.114C29.8031 15.8716 31.2794 14.8387 32.8875 14.0523C34.3086 13.3704 35.8149 12.8842 37.3656 12.6069C39.9046 12.1197 42.5076 12.0697 45.0633 12.4589C46.2423 12.641 51.1626 13.4946 56.0714 17.3188C63.225 22.8844 66.1613 30.6125 64.4947 39.5016C61.9553 53.1026 48.9632 61.0697 35.7897 57.5641C31.9125 56.5284 27.8312 54.0814 26.0173 51.6912C26.108 51.6912 26.2101 51.6685 26.2667 51.7026C26.7202 51.9985 27.1624 52.3172 27.6272 52.6131C34.9962 57.2568 42.5465 57.291 50.0515 53.1367C51.0019 52.6094 51.9047 52.0001 52.7497 51.3156C56.0034 48.6751 57.4885 45.6021 58.0327 44.4525Z" fill="black"/>
                            <path d="M65.1182 45.2722C65.0389 45.7502 64.9028 46.4331 64.6874 47.2412C63.1229 53.023 59.5518 56.6765 58.0213 58.2016C52.4209 63.79 45.9022 65.4062 42.9206 66.1119C36.7873 67.5573 31.8331 66.9427 30.0532 66.6582C23.6479 65.6338 19.2492 63.0388 17.9001 62.1852C15.1477 60.4502 12.6808 58.2961 10.5878 55.8001C8.14327 52.8969 6.22821 49.5834 4.93068 46.012C4.60191 45.1014 3.35485 41.5276 3.03741 36.6335C2.74266 31.9671 3.44554 28.4843 3.6156 27.6762C4.17003 25.0362 5.06567 22.4803 6.27977 20.0733C6.91463 18.8441 10.1456 12.8346 17.2879 8.35027C18.9431 7.30316 23.2738 4.84474 29.407 3.91145C31.0282 3.66105 45.0066 1.72618 55.0171 10.5242C59.6539 14.5988 62.386 20.0505 62.8168 20.9269C63.4801 22.2806 64.0557 23.6759 64.54 25.104C64.3813 24.8877 63.2023 23.226 63.1683 23.1805C58.8149 16.7044 53.7587 13.6541 53.7587 13.6541C51.8468 12.5265 49.7911 11.665 47.6481 11.0932C40.8346 9.24941 35.0981 10.8884 33.7377 11.2981C25.7565 13.6996 21.0291 20.0847 20.757 20.4603C15.9388 27.1299 16.0408 34.0954 16.1315 36.0303C16.4376 42.5406 19.2718 47.1387 20.5642 48.9939C20.6663 49.1191 20.8137 49.3126 20.995 49.5403C21.1311 49.7224 21.2671 49.9045 21.4032 50.0866C24.7816 54.5482 29.2596 57.6553 34.2365 59.2032C37.9454 60.3419 41.8646 60.6138 45.6942 59.9984C49.5239 59.3829 53.1626 57.8962 56.3321 55.6522C59.121 53.6718 60.8102 51.6117 61.8532 50.337C63.2363 48.6525 64.2453 46.9908 64.5741 46.3306C64.6194 46.251 65.0956 45.2722 65.1182 45.2722Z" fill="#007EE5"/>
                            <path d="M28.7061 45.6C22.7202 45.6 19.7273 41.6278 19.7273 35.5387C19.7273 29.2447 22.879 25.4546 29.0462 25.4546C31.427 25.4546 33.3429 26.0237 34.8167 27.2301C36.1998 28.4707 36.9707 30.1552 37.1634 32.2949H34.76C33.8984 32.2949 33.2749 31.8966 32.9008 31.1112C32.2432 29.7113 30.9508 28.9943 29.0576 28.9943C25.3617 28.9943 23.7179 31.612 23.7179 35.5501C23.7179 39.3743 25.2937 42.0717 28.9329 42.0717C31.427 42.0717 32.8554 40.6945 33.2862 38.532H37.1521C36.8007 43.0392 33.6377 45.6 28.7061 45.6Z" fill="black"/>
                            <path d="M49.3373 25.2747H42.5465C41.4015 25.2747 40.4945 26.2762 40.4945 27.4258V45.3063H44.5758V38.17H49.632C53.634 38.17 55.7766 35.6661 55.7766 31.7166C55.7766 27.551 53.5773 25.2747 49.3373 25.2747ZM48.8725 34.5507H44.8932V28.746H49.0765C50.9811 28.746 51.9561 29.7135 51.9561 31.6597C51.9674 33.606 50.9471 34.5848 48.8725 34.5507H48.8725Z" fill="#007EE5"/>
                            </svg>
                            
                            <span v-if="history.entry_type == 'Dr' && history.reason == 'transaction' && isRewardUsed(history.generic_reward_amount_used)">${{amountDecimalNumberFormatter(amountNumberFormatter(history.generic_reward_amount_used))}}</span>
                            <span v-if="history.entry_type == 'Dr' && history.reason == 'transaction' && isRewardUsed(history.generic_reward_amount_used) && isRewardUsed(history.merchant_reward_amount_used)" class="ml-2 mr-2"> + </span>
                            
                            <svg class="mr-2" width="30" height="30" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg" v-if="(history.segment_name!= 'Loser' && history.is_generic_point == 0) || (history.reason == 'transaction' && isRewardUsed(history.merchant_reward_amount_used))">
                            <path d="M43.6484 32.2123C45.9758 27.2992 45.0233 22.6013 44.7537 21.3551C44.4482 19.9476 43.6754 16.6214 40.8627 13.6359C39.5231 12.233 37.9321 11.0927 36.1719 10.2738C33.7996 9.17106 31.7777 8.95589 30.6724 8.8483C26.1164 8.40003 22.3512 9.72692 20.2485 10.6773C21.2723 9.69865 22.4425 8.88501 23.7171 8.26555C24.8436 7.72841 26.0375 7.34542 27.2667 7.12693C29.2792 6.74323 31.3425 6.70382 33.3683 7.01038C34.3028 7.15383 38.2028 7.82624 42.0938 10.8386C47.7641 15.2228 50.0915 21.3103 48.7705 28.3123C46.7576 39.0261 36.4595 45.3019 26.0176 42.5406C22.9443 41.7247 19.7093 39.7971 18.2715 37.9144C18.3434 37.9144 18.4243 37.8964 18.4692 37.9233C18.8287 38.1564 19.1791 38.4075 19.5476 38.6406C25.3886 42.2985 31.3733 42.3254 37.3222 39.053C38.0754 38.6376 38.791 38.1577 39.4609 37.6185C42.0399 35.5385 43.2171 33.1178 43.6484 32.2123Z" fill="black"/>
                            <path d="M49.2647 32.8578C49.2018 33.2344 49.094 33.7723 48.9232 34.4088C47.6831 38.9633 44.8525 41.8412 43.6394 43.0426C39.2002 47.4446 34.0332 48.7177 31.6698 49.2736C26.8083 50.4122 22.8814 49.9281 21.4706 49.7039C16.3934 48.897 12.9067 46.8529 11.8374 46.1805C9.65575 44.8138 7.70037 43.117 6.04133 41.1509C4.1037 38.8639 2.58573 36.2538 1.55724 33.4406C1.29664 32.7233 0.308168 29.9082 0.0565558 26.053C-0.177084 22.3772 0.380057 19.6337 0.514849 18.9972C0.954323 16.9176 1.66425 14.9043 2.62659 13.0083C3.12982 12.04 5.69087 7.30621 11.3521 3.77381C12.6641 2.94899 16.0968 1.01244 20.9583 0.277275C22.2434 0.0800349 33.3233 -1.4441 41.2581 5.48622C44.9334 8.69586 47.099 12.9903 47.4405 13.6807C47.9662 14.747 48.4225 15.8461 48.8064 16.971C48.6806 16.8006 47.746 15.4917 47.7191 15.4558C44.2684 10.3545 40.2606 7.95172 40.2606 7.95172C38.7451 7.06349 37.1157 6.38487 35.4171 5.93449C30.0164 4.48208 25.4694 5.77311 24.3911 6.09587C18.0648 7.98758 14.3176 13.0172 14.1019 13.3131C10.2828 18.5668 10.3637 24.0537 10.4356 25.5778C10.6782 30.7061 12.9247 34.3281 13.9491 35.7895C14.03 35.8881 14.1468 36.0406 14.2906 36.2199C14.3985 36.3633 14.5063 36.5068 14.6141 36.6502C17.292 40.1647 20.8415 42.6123 24.7864 43.8316C27.7263 44.7285 30.8328 44.9427 33.8684 44.4579C36.9039 43.9731 39.7882 42.802 42.3004 41.0343C44.511 39.4743 45.85 37.8516 46.6767 36.8474C47.773 35.5206 48.5728 34.2116 48.8334 33.6916C48.8693 33.6288 49.2467 32.8578 49.2647 32.8578Z" fill="#ECB800"/>
                            <path d="M20.3742 32.9385C15.6295 32.9385 13.2571 29.8096 13.2571 25.0131C13.2571 20.0552 15.7553 17.0697 20.6438 17.0697C22.5309 17.0697 24.0495 17.5179 25.2177 18.4683C26.314 19.4455 26.9251 20.7724 27.0778 22.4579H25.1728C24.4898 22.4579 23.9956 22.1441 23.6991 21.5255C23.1779 20.4227 22.1534 19.8579 20.6527 19.8579C17.7233 19.8579 16.4203 21.92 16.4203 25.022C16.4203 28.0344 17.6693 30.1592 20.5539 30.1592C22.5309 30.1592 23.6631 29.0744 24.0046 27.371H27.0689C26.7903 30.9213 24.2832 32.9385 20.3742 32.9385Z" fill="black"/>
                            <path d="M36.756 17.1055H31.3733C30.4657 17.1055 29.7468 17.8944 29.7468 18.7999V32.8847H32.9818V27.2634H36.9896C40.1617 27.2634 41.8601 25.291 41.8601 22.1799C41.8601 18.8986 40.1168 17.1055 36.756 17.1055ZM36.3876 24.4123H33.2334V19.8399H36.5493C38.059 19.8399 38.8318 20.602 38.8318 22.1351C38.8408 23.6682 38.032 24.4392 36.3876 24.4123Z" fill="#ECB800"/>
                            </svg>
                            <span v-if="history.entry_type == 'Dr' && history.reason == 'transaction' && isRewardUsed(history.merchant_reward_amount_used)">${{amountDecimalNumberFormatter(amountNumberFormatter(history.merchant_reward_amount_used))}}</span>
                            <span v-if="history.entry_type == 'Cr'">
                                <span v-if="history.segment_name != null">
                                    <span v-if="history.segment_name == 'Amount'">{{pointNumberFormatter(history.reward_point)}} {{ parseInt(history.reward_point) > 1 ? 'pts' : 'pt'}}</span>
                                    <span v-if="history.segment_name == 'Jackpot'">{{pointNumberFormatter(history.reward_point)}} {{ parseInt(history.reward_point) > 1 ? 'pts' : 'pt'}}</span>
                                    <span v-if="history.segment_name == 'Free Spin'">{{pointNumberFormatter(history.reward_point)}} spin</span>
                                    <span v-if="history.segment_name == 'Reward Points'">{{pointNumberFormatter(history.reward_point)}} {{ parseInt(history.reward_point) > 1 ? 'pts' : 'pt'}}</span>
                                    <span v-if="history.segment_name == 'Multiplier'">{{pointNumberFormatter(history.reward_point)}} {{ parseInt(history.reward_point) > 1 ? 'pts' : 'pt'}}</span>
                                    <span v-if="history.segment_name == 'Loser'">{{pointNumberFormatter(history.reward_point)}} {{ parseInt(history.reward_point) > 1 ? 'pts' : 'pt'}}</span>
                                </span>
                                <span style="font-weight: 700; color: #007ee5;" v-else-if="history.sponsor_link_id">{{pointNumberFormatter(history.reward_point)}} {{ parseInt(history.reward_point) > 1 ? 'pts' : 'pt'}}</span>
                                <span v-else>{{pointNumberFormatter(history.reward_point)}} {{ parseInt(history.reward_point) > 1 ? 'pts' : 'pt'}}</span>
                                <span v-if="history.status && history.status.toLowerCase() == 'pending' && history.segment_name!= 'Loser'" class="font-weight-bold ml-2"><small style="font-weight: 700; color: #007ee5;">(Pending)</small></span>
                            </span>
                            <span v-if="history.segment_name == 'Loser'" class="rp-text" style="text-transform:none;">Sorry! This spin didn’t win. <br/>
                                But it did gain you entry into the Daily 2nd Chance Drawing. Good luck!</span>
                        </div>
                        <span v-if="history.segment_name">
                            <div v-if="history.entry_type == 'Cr'" class="rp-text" style="text-transform: capitalize;"><span class="text-dark">Win:</span> {{history.segment_name}}</div>
                            <!-- <div v-if="history.entry_type == 'Cr' && history.segment_name == 'Loser'" class="rp-text" style="text-transform: capitalize;">{{history.reason}}</div> -->
                            <div v-if="history.entry_type == 'Dr'" class="rp-text" style="text-transform: capitalize; color: #007EE5!important;">Paid with Points</div>
                        </span>
                        <div v-else-if="history.sponsor_link_id" class="rp-text" style="font-weight: 700; color: #007ee5;">Canpay Points</div>
                        <div v-else-if="history.is_brand" class="rp-text" style="font-weight: 700; color: #007ee5;">Brand Points</div>
                        <div v-else class="rp-text" style="text-transform: capitalize;">{{history.reason}}</div>
                    </div>
                    <div v-if="history.entry_type == 'Cr' || history.segment_name== 'Loser'" class="font-weight-bold mt-2">{{history.reward_wheel_name}}</div>
                    <div v-if="history.entry_type == 'Dr'" class="font-weight-bold mt-2">{{history.retailer}}</div>
                    <hr class="my-2 mb-1">
                    <ul class="rp-timing">
                        <li class="d-flex align-items-center">
                        <svg
                        class="mr-2"
                        version="1.1"
                        id="Layer_1"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        x="0px"
                        y="0px"
                        viewBox="0 0 90 90"
                        style="
                            enable-background: new 0 0 90 90;
                            margin-left: -8px;
                            margin-right: -10px;
                            float: left;
                        "
                        xml:space="preserve"
                        width="30"
                        fill="#7f7f7f"
                        >
                        <path
                            d="M63,25h-2v-2c0-1.1-0.9-2-2-2s-2,0.9-2,2v2H33v-2c0-1.1-0.9-2-2-2s-2,0.9-2,2v2h-2c-3.3,0-6,2.7-6,6v32c0,3.3,2.7,6,6,6h36
                        c3.3,0,6-2.7,6-6V31C69,27.7,66.3,25,63,25z M65,63c0,1.1-0.9,2-2,2H27c-1.1,0-2-0.9-2-2V31c0-1.1,0.9-2,2-2h2v2c0,1.1,0.9,2,2,2
                    c1.1,0,2-0.9,2-2v-2h24v2c0,1.1,0.9,2,2,2s2-0.9,2-2v-2h2c1.1,0,2,0.9,2,2V63z"
                        />
                        <path
                            d="M59,39H31c-1.1,0-2,0.9-2,2s0.9,2,2,2h28c1.1,0,2-0.9,2-2S60.1,39,59,39z"
                        />
                        </svg> {{formatedTime(history.rewards_time)}} - {{ history.timezone_name }}</li>
                    </ul>
                </div>
            </div>
            </a>
            <div v-show="loading" class="spinner-border"></div>
            <div v-if="!noMoredata" class="text-center"><button class="load-more-button-style" @click="loadMoreData()">Load more</button></div>
            <p v-if="noMoredata" >No more data found</p>
        </div>
        <div v-else class="text-white mt-5">
            <small>No history found!</small>
        </div>
    </div>
  </div>
</div>
</template>
<script>
import moment from "moment";
import rewardwheelapi from "../../api/rewardwheel.js";
import RwDatePicker from "./RwDatePicker.vue";
import Loading from "vue-loading-overlay";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
    name: "Transactions",
    data() {
        return {
            isLoading: true,
            rewardPointHistories: [],
            filter: {
                tenure: '',
                from_date: '',
                to_date: ''
            },
            formDateValidate: null,
            toDateValidate: null,
            pagination:{
                total: null,
                page: 1,
                current_page: 1,
                rowsPerPage: process.env.VUE_APP_REWARD_HISTORY_PER_PAGE,
                to: process.env.VUE_APP_REWARD_HISTORY_PER_PAGE
            },
            loading: false,
            noMoredata: false
        };
    },
    watch: {
        
    },
    components: {
        Loading,
        RwDatePicker,
        CanPayLoader
    },
    mounted() {
        var element = document.getElementsByClassName("content-wrap");
        if (element[0]) {
            element[0].style.setProperty("background", "linear-gradient(0deg, #148f3f, #00ae44)");
            element[0].style.height = "114vh";
      if(window.innerWidth>1200){
        element[0].style.height = "121vh";
      }
        }
        this.$root.$emit("loginapp", [""]);
        this.$root.$emit("changeWhiteBackground", [
            false,
            false,
            "RewardPointHeader"
        ]);
        
        this.filter.tenure = 'last-week'
        this.filter.from_date = moment().subtract(6, 'd').format('YYYY-MM-DD')
        this.filter.to_date = moment().format('YYYY-MM-DD')
        this.getRwardHistory({
            from_date: this.filter.from_date, 
            to_date: this.filter.to_date,
            page: this.pagination.page,
            rowsPerPage: this.pagination.rowsPerPage
        });

        var app = document.getElementById('nav-drawer');

        app.scrollTop = 0; // For Safari
        document.documentElement.scrollTop = 0; // For Chrome, Firefox, IE and Opera
    },
    methods: {
        isRewardUsed(point){
            if(point){
                if(point == '' || point == '0.00'){
                return false
                }
                return true
            }else{
                return false
            }
        },
        formatedTime(data){
            return moment(data).format('DD MMM Y | hh:mm A');
        }, 
        loadMoreData(){
            var self = this;
            if(!self.noMoredata){
                rewardwheelapi
                .rewardPointHistory({
                    from_date: self.filter.from_date, 
                    to_date: self.filter.to_date,
                    page: self.pagination.page+1,
                    rowsPerPage: self.pagination.rowsPerPage
                })
                .then((response) => {
                    self.pushToHistoryArray(response.data)
                    
                    if(response.data.total<=response.data.per_page || response.data.to == response.data.total || response.data.to==null){
                        self.noMoredata = true
                    }

                })
                .catch(function (error) {
                    
                })
            }

        },
        getRwardHistory(params){
            var self = this;
            rewardwheelapi
            .rewardPointHistory(params)
            .then((response) => {

                // Set Pagination 
                self.pagination.rowsPerPage = response.data.per_page
                self.pagination.current_page = response.data.current_page
                self.pagination.total = response.data.total
                self.pagination.to = response.data.to

                self.rewardPointHistories = response.data.data;
                if(response.data.total<=response.data.per_page || response.data.to == response.data.total || response.data.to==null){
                    self.noMoredata = true
                }else{
                    self.noMoredata = false;
                }
                self.isLoading = false
            })
            .catch(function (error) {
                self.isLoading = false
            })
        },
        routePush(url){
            this.$router.push("/"+url).catch((err) => {});
        },
        pushToHistoryArray(payload){

            var self = this
            
            self.rewardPointHistories.push(...payload.data) 

            self.pagination.rowsPerPage = payload.per_page
            self.pagination.current_page = payload.current_page
            self.pagination.page += 1
            self.pagination.total = payload.total
            self.pagination.to = payload.to
            self.loading = false
        },
        onSelectDate(){
            let self = this;
            if(this.rewardPointHistories.length > 0){
                document.getElementById('historyScrollArea').scroll({top:0});
            }

            this.pagination.rowsPerPage = process.env.VUE_APP_REWARD_HISTORY_PER_PAGE
            this.pagination.current_page = 1
            this.pagination.page = 1
            this.pagination.total = null
            this.pagination.to = process.env.VUE_APP_REWARD_HISTORY_PER_PAGE

            this.getRwardHistory({
                from_date: this.filter.from_date, 
                to_date: this.filter.to_date,
                page: this.pagination.page,
                rowsPerPage: this.pagination.rowsPerPage
            });
        },
        amountNumberFormatter(amount) {
            return parseFloat(amount).toFixed(2);
        }
    }
};
</script>


<style scoped>
.min-h{
    min-height: calc(100vh - 115px)!important;
}
.load-more-button-style{
    width:50%;
    border:none;
    border-radius: 6px;
    padding:15px;
    background-color: #000;
    font-weight:600;
    color:#fff;
}
.rp-available-card{
    background: linear-gradient(45deg, #242424, black);
    padding: 20px;
    border-radius: 6px;
    position: relative;
}
.rp-available-title{
    text-align: left;
    color: #dddddd;
}
.rp-available-value{
    text-align: left;
    color: #199448;
    font-size: 40px;
}
.rp-icon-avatar{
    border-radius: 100%;
    background: #1e1e1e;
    position: absolute;
    top: 15px;
    right: 15px;
}

.rp-pending-card{
    padding: 20px;
    border-radius: 6px;
    position: relative;
    background: linear-gradient(45deg, #0e8740, #006b2c);

}
.rp-pending-text{
    padding: 0;
    margin: 0;
    list-style: none;
    text-align: left;
}
.rp-pending-text .text{
    color: #fff;
    margin-left: 10px;
}
.rp-pending-text .value{
    color: #fff;
    font-size: 30px;
}
.rp-imark-avatar{
    position: absolute;
    top: 4px;
    right: 7px;
}
.rp-invite-card{
    background: #fff;
    border-radius: 6px;
}
.rp-invite-card-header{
    background: #e7e8e7;
    padding: 20px;
    font-size: 23px;
    color: #00943f;
    text-align: left;
    font-weight: bold;
    border-radius: 6px 6px 0 0;
}
.rp-invite-card-body{
    background: #ffffff;
    padding: 20px;
    border-radius: 0 0 6px 6px;
    text-align: left;
}
.rp-invite-card-body .content{
    background: #ffffff;
    text-align: left;
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 10px;
}
.rp-invite-card-body .link{
    background: #ffffff;
    color: #00943f;
    text-align: left;
    font-size: 15px;
    font-weight: 500;
    text-decoration: underline;
    font-weight: bold;
}

.rp-detail-card-link, .rp-detail-card-link:hover{
    text-decoration: none;
    color: unset;
}
.rp-detail-card{
    background: white;
    border-radius: 7px;
    text-align: left;
}

.rp-detail-card{
    padding: 10px 20px;
}
.rp-title{
    font-weight: 800;
    font-size: 19px;
}
.rp-text{
    font-weight: 500;
    font-size: 15px;
    color: #00943f;
    font-weight: 700;
    text-transform: capitalize;
}
.rp-timing{
    list-style: none;
    margin: 0;
    padding: 0;
    color: #919090;
    font-size: 13px;
    font-weight: 600;
}

.rp-history-link{
    color: #fff;
    text-decoration: underline;
}

.btn-submit-filter{
    background-color: #000;
    color: #fff;
    border: 0;
    padding: 14px 34px;
    border-radius: 8px;
}
.vdate-picker{
    border-radius: 8px;
}

.rw-loading{ 
    position: absolute;
    bottom: 5%;
    padding: 10px 10px;
    color: #fff;
    text-align: center;
    background: #000;
    border-radius: 4px;
    width: auto;
    left: 50%;
    transform: translateX(-50%);
}
.fade-enter-active,
.fade-leave-active{
    transition: opacity .25s;
}
.fade-enter,
.fade-leave-to{
    opacity: 0;
}

.tran-store-name{
    font-weight: 600;
    font-size: 16px;
}

.tran-title {
    font-weight: 600;
    font-size: 25px;
}
.tran-store-logo{
        position: absolute;
        width: 80px;
        top: 10px;
        right: 10px;
    }
</style>