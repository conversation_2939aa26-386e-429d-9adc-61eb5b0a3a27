<template>
    <b-modal
        ref="merchant-point-available-modal"
        hide-footer
        no-close-on-backdrop
        no-close-on-esc
        modal-backdrop
        centered
        fade
        hide-header
        id="merchant-point-available-modal"
    >
        <div class="text-center" style="font-family:'Open Sans'">
            <svg class="mt-2" xmlns="http://www.w3.org/2000/svg" width="45" height="45" viewBox="0 0 141 141" fill="none">
            <path d="M141 70.5C141 109.436 109.436 141 70.5 141C31.5639 141 0 109.436 0 70.5C0 31.5639 31.5639 0 70.5 0C109.436 0 141 31.5639 141 70.5Z" fill="black"/>
            <path d="M116.894 89.5408C122.809 76.9775 120.388 64.9643 119.703 61.7777C118.927 58.1783 116.963 49.6729 109.815 42.0386C106.411 38.4512 102.368 35.5353 97.8951 33.4414C91.8665 30.6215 86.7284 30.0713 83.9196 29.7962C72.3419 28.6499 62.7737 32.0429 57.4302 34.4731C60.0318 31.9706 63.0056 29.8901 66.2448 28.306C69.1073 26.9325 72.1414 25.9532 75.2649 25.3945C80.3792 24.4133 85.6224 24.3125 90.7703 25.0964C93.1453 25.4632 103.056 27.1827 112.944 34.8857C127.353 46.0964 133.268 61.663 129.911 79.5681C124.796 106.964 98.6258 123.012 72.0907 115.951C64.2809 113.865 56.06 108.936 52.4063 104.122C52.589 104.122 52.7945 104.076 52.9087 104.144C53.8221 104.741 54.7127 105.382 55.649 105.979C70.4922 115.332 85.7008 115.401 100.818 107.033C102.732 105.971 104.551 104.744 106.253 103.365C112.807 98.0462 115.798 91.8563 116.894 89.5408Z" fill="white"/>
            <path d="M131.166 91.1911C131.006 92.154 130.732 93.5296 130.298 95.1573C127.147 106.804 119.954 114.163 116.871 117.235C105.59 128.491 92.4597 131.747 86.4539 133.168C74.0998 136.08 64.1205 134.842 60.5353 134.269C47.6332 132.205 38.7729 126.978 36.0554 125.259C30.5114 121.764 25.5424 117.425 21.3264 112.398C16.4024 106.55 12.545 99.8751 9.93137 92.6813C9.26913 90.8473 6.7572 83.6486 6.1178 73.7905C5.52407 64.3909 6.93988 57.3756 7.28242 55.7479C8.39922 50.4303 10.2033 45.2819 12.6488 40.4335C13.9276 37.9575 20.4358 25.8527 34.8223 16.8199C38.1563 14.7108 46.8796 9.75881 59.2337 7.87889C62.4992 7.37452 90.6557 3.47714 110.82 21.1988C120.159 29.4062 125.663 40.3876 126.531 42.1529C127.867 44.8797 129.026 47.6901 130.002 50.5667C129.682 50.1311 127.307 46.7839 127.239 46.6922C118.47 33.6475 108.285 27.5034 108.285 27.5034C104.434 25.232 100.293 23.4967 95.9764 22.3451C82.2521 18.6311 70.6972 21.9324 67.957 22.7577C51.8806 27.5951 42.3581 40.4564 41.81 41.213C32.1049 54.6475 32.3104 68.678 32.4931 72.5754C33.1096 85.689 38.8186 94.951 41.4218 98.6879C41.6274 98.9401 41.9242 99.3298 42.2896 99.7883C42.5636 100.155 42.8377 100.522 43.1117 100.889C49.9167 109.876 58.9368 116.134 68.9617 119.252C76.4325 121.546 84.3268 122.094 92.0408 120.854C99.7548 119.614 107.084 116.62 113.469 112.099C119.086 108.11 122.489 103.961 124.59 101.393C127.376 98.0001 129.408 94.6529 130.07 93.3233C130.161 93.1628 131.121 91.1911 131.166 91.1911Z" fill="#FF9F20"/>
            <path d="M57.749 91.3977C45.6917 91.3977 39.6631 83.3966 39.6631 71.1313C39.6631 58.4533 46.0114 50.8191 58.4341 50.8191C63.2296 50.8191 67.0888 51.9654 70.0575 54.3955C72.8434 56.8944 74.3963 60.2874 74.7845 64.5975H69.9433C68.2078 64.5975 66.9518 63.7951 66.1982 62.2132C64.8738 59.3933 62.2705 57.949 58.4569 57.949C51.0125 57.949 47.7013 63.2219 47.7013 71.1542C47.7013 78.8573 50.8754 84.2907 58.2057 84.2907C63.2296 84.2907 66.1069 81.5167 66.9746 77.1608H74.7616C74.0537 86.2394 67.6826 91.3977 57.749 91.3977Z" fill="white"/>
            <path d="M99.3789 50.9105H85.7003C83.3939 50.9105 81.567 52.928 81.567 55.2435V91.2599H89.7879V76.8854H99.9726C108.034 76.8854 112.35 71.8418 112.35 63.8865C112.35 55.4957 107.919 50.9105 99.3789 50.9105ZM98.4427 69.595H90.4273V57.9029H98.8537C102.69 57.9029 104.654 59.8516 104.654 63.7719C104.677 67.6922 102.622 69.6638 98.4427 69.595Z" fill="#FF9F20"/>
            </svg>
            <p class="mt-3 fontWeight-700 fontSize-18" style="margin-bottom:0px;">You have Merchant</p>
            <p class="fontWeight-700 fontSize-18">Points available</p>
            <button class="mt-2 select-merchant-point-button" v-on:click="selectMerchant()">Select Merchant</button>
            <button class="mt-3 close-merchant-point-button" v-on:click="hideMerchantPointAvailable()">Close</button>
        </div>
    </b-modal>
</template>
<script>
import reward_api from "../../api/rewardwheel"
export default {
name: "MerchantPointAvailableModal",
data(){
    return {

    }
},
props: {
  changeMerchant: {
    type: Function,
    required: true
  }
},
methods:{
    updateMerchantPointUsed(){
        let self = this;
        reward_api
        .updateMerchantPointUsed()
        .then((res)=>{

        })
        .catch((err)=>{
        })
    },      
    showMerchantPointAvailable(){
        let self = this;
         self.$bvModal.show("merchant-point-available-modal");
    },
    hideMerchantPointAvailable(){
        let self = this;
         self.$bvModal.hide("merchant-point-available-modal");
         self.updateMerchantPointUsed();
    },
    selectMerchant(){
        let self = this;
        self.hideMerchantPointAvailable();
        self.changeMerchant();
        self.updateMerchantPointUsed();
    }
},
mounted(){

}
}
</script>
<style scoped>
.fontWeight-700{
    font-weight:700
}
.select-merchant-point-button{
    background-color:#1B9146;
    font-family:'Montserrat';
    font-size:16px;
    border:none;
    border-radius:8px;
    color:#ffffff;
    width:100%;
    padding:15px 20px;
    font-weight:600;
}
.fontSize-18{
    font-size:18px;
}
.close-merchant-point-button{
    font-family:'Montserrat';
    background-color: #D8D8D8;
    color: #000000;
    font-size:16px;
    border:none;
    border-radius:8px;
    width:100%;
    padding:15px 20px;
    font-weight:600;
}
</style>