<template>
<div>
  <div class="bg-grey" style="margin-bottom:-20px;padding-bottom:13px;">
   <div class="container width-fix">
    <div class="row">
      <div class="col-12 text-center padding-zero bg-color-black add-payment-border-radius mt-3">
        <div class="row mt-3">
          <div class="col-12 padding-zero">
            <span class="pymnt-appvd-style">{{approve_label}}</span>
            <span style="position:absolute;left:20px;top:-8px;">
            <svg class="ml-2" xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 94 98" fill="none">
              <mask id="path-1-outside-1_13454_706" maskUnits="userSpaceOnUse" x="0" y="0" width="94" height="98" fill="black">
                <rect fill="white" width="94" height="98"/>
                <path d="M91.2705 57.2388H89.8078V50.1441C89.8064 46.8849 88.5022 43.7594 86.1812 41.4532C83.8601 39.147 80.7118 37.8485 77.427 37.8425H72.7619V27.7773C72.7599 25.9574 72.0304 24.2126 70.7334 22.9257C69.4364 21.6388 67.6778 20.915 65.8436 20.913H58.2408C55.2994 -5.60143 17.5102 -5.67286 14.628 20.9099L11.2501 20.9097C9.89227 20.9108 8.54815 21.1793 7.29569 21.6998C6.04323 22.2202 4.90737 22.9821 3.95397 23.9414C3.00058 24.9007 2.24861 26.0383 1.74169 27.2881C1.23476 28.538 0.982964 29.8752 1.00089 31.2224V84.6976C1.00452 87.9593 2.31205 91.0864 4.6366 93.3928C6.96116 95.6991 10.1129 96.9964 13.4003 97H77.4203C80.7077 96.9964 83.8594 95.6991 86.1839 93.3927C88.5084 91.0864 89.816 87.9593 89.8196 84.6976C89.8067 84.6322 89.8445 77.6639 89.8063 77.6088L91.2705 77.6087C91.4976 77.6088 91.7226 77.5645 91.9325 77.4783C92.1424 77.3922 92.3331 77.2658 92.4937 77.1064C92.6543 76.947 92.7817 76.7578 92.8686 76.5496C92.9555 76.3413 93.0001 76.1181 93 75.8927V58.9548C93.0001 58.7295 92.9555 58.5063 92.8686 58.298C92.7818 58.0897 92.6544 57.9005 92.4937 57.7411C92.3331 57.5818 92.1424 57.4554 91.9325 57.3692C91.7226 57.283 91.4977 57.2387 91.2705 57.2388ZM58.2531 24.3452H65.8435C66.7606 24.3462 67.6399 24.7082 68.2884 25.3516C68.9369 25.995 69.3017 26.8674 69.3027 27.7773V37.8466H52.0362C55.6366 34.2064 57.8366 29.4287 58.2531 24.3452ZM23.6595 9.43682C34.8864 -1.5444 54.2968 6.4831 54.8629 21.9549C55.6091 36.3914 38.8381 45.9111 26.6371 38.1094C16.6026 32.2585 15.061 17.2114 23.6595 9.43682ZM11.2501 24.3418H14.6512C15.032 29.4296 17.2253 34.2168 20.8386 37.8466H11.2501C9.447 37.8438 7.71875 37.1311 6.44479 35.8651C5.17084 34.5991 4.45531 32.8832 4.45531 31.0942C4.45531 29.3052 5.17084 27.5893 6.44479 26.3233C7.71875 25.0573 9.447 24.3447 11.2501 24.3418ZM86.3605 84.6976C86.358 87.0494 85.4153 89.3042 83.7392 90.9671C82.0631 92.6301 79.7906 93.5654 77.4203 93.5678H13.4003C11.03 93.5654 8.75742 92.6301 7.08134 90.9671C5.40527 89.3041 4.46258 87.0493 4.46015 84.6975V38.708C6.32886 40.3625 8.74554 41.2775 11.2501 41.2788H25.2552C28.6434 43.2602 32.5045 44.3051 36.4374 44.3051C40.3703 44.3051 44.2315 43.2602 47.6197 41.2788C47.6403 41.2735 71.0291 41.2876 71.0464 41.2746H77.4271C79.7948 41.2794 82.0639 42.216 83.7365 43.8788C85.4091 45.5416 86.3485 47.7948 86.3486 50.1441V57.2388H78.8881C77.5389 57.237 76.2026 57.4992 74.9556 58.0103C73.7087 58.5213 72.5755 59.2713 71.6209 60.2172C70.6662 61.1631 69.9089 62.2865 69.3922 63.5231C68.8755 64.7596 68.6095 66.0851 68.6095 67.4237C68.6095 68.7624 68.8755 70.0879 69.3922 71.3244C69.9089 72.561 70.6662 73.6844 71.6209 74.6303C72.5755 75.5762 73.7087 76.3262 74.9556 76.8372C76.2026 77.3483 77.5389 77.6105 78.8881 77.6087C78.8646 77.6617 86.5416 77.4942 86.3604 77.6741L86.3605 84.6976ZM89.5409 74.1766H78.8881C77.0935 74.1608 75.3778 73.4423 74.1144 72.1777C72.851 70.913 72.1422 69.2044 72.1422 67.4237C72.1422 65.643 72.8511 63.9345 74.1145 62.6698C75.3779 61.4051 77.0936 60.6867 78.8882 60.6709H89.5409L89.5409 74.1766Z"/>
                <path d="M80.8458 69.139C81.2987 69.1302 81.73 68.9456 82.0471 68.6247C82.3643 68.3038 82.5419 67.8723 82.5419 67.4229C82.5419 66.9734 82.3642 66.542 82.0471 66.2211C81.7299 65.9002 81.2986 65.7156 80.8457 65.7069C80.3928 65.7156 79.9614 65.9003 79.6443 66.2212C79.3271 66.542 79.1495 66.9735 79.1495 67.423C79.1495 67.8724 79.3272 68.3039 79.6444 68.6247C79.9615 68.9456 80.3929 69.1302 80.8458 69.139Z"/>
                <path d="M31.7551 30.6933C31.9157 30.8527 32.1063 30.9791 32.3162 31.0654C32.526 31.1517 32.7509 31.1961 32.978 31.1961C33.2051 31.1961 33.43 31.1517 33.6399 31.0654C33.8497 30.9791 34.0403 30.8527 34.2009 30.6933L48.0376 16.9647C48.3514 16.6408 48.5246 16.2077 48.5201 15.7584C48.5156 15.3092 48.3337 14.8795 48.0135 14.5619C47.6933 14.2442 47.2603 14.0638 46.8075 14.0593C46.3547 14.0549 45.9182 14.2268 45.5917 14.5382L32.978 27.0534L27.2825 21.4024C26.9561 21.0909 26.5195 20.9189 26.0667 20.9233C25.6138 20.9277 25.1807 21.1081 24.8605 21.4259C24.5402 21.7436 24.3584 22.1733 24.354 22.6227C24.3496 23.072 24.5229 23.5051 24.8369 23.829L31.7551 30.6933Z"/>
              </mask>
              <path d="M91.2705 57.2388H89.8078V50.1441C89.8064 46.8849 88.5022 43.7594 86.1812 41.4532C83.8601 39.147 80.7118 37.8485 77.427 37.8425H72.7619V27.7773C72.7599 25.9574 72.0304 24.2126 70.7334 22.9257C69.4364 21.6388 67.6778 20.915 65.8436 20.913H58.2408C55.2994 -5.60143 17.5102 -5.67286 14.628 20.9099L11.2501 20.9097C9.89227 20.9108 8.54815 21.1793 7.29569 21.6998C6.04323 22.2202 4.90737 22.9821 3.95397 23.9414C3.00058 24.9007 2.24861 26.0383 1.74169 27.2881C1.23476 28.538 0.982964 29.8752 1.00089 31.2224V84.6976C1.00452 87.9593 2.31205 91.0864 4.6366 93.3928C6.96116 95.6991 10.1129 96.9964 13.4003 97H77.4203C80.7077 96.9964 83.8594 95.6991 86.1839 93.3927C88.5084 91.0864 89.816 87.9593 89.8196 84.6976C89.8067 84.6322 89.8445 77.6639 89.8063 77.6088L91.2705 77.6087C91.4976 77.6088 91.7226 77.5645 91.9325 77.4783C92.1424 77.3922 92.3331 77.2658 92.4937 77.1064C92.6543 76.947 92.7817 76.7578 92.8686 76.5496C92.9555 76.3413 93.0001 76.1181 93 75.8927V58.9548C93.0001 58.7295 92.9555 58.5063 92.8686 58.298C92.7818 58.0897 92.6544 57.9005 92.4937 57.7411C92.3331 57.5818 92.1424 57.4554 91.9325 57.3692C91.7226 57.283 91.4977 57.2387 91.2705 57.2388ZM58.2531 24.3452H65.8435C66.7606 24.3462 67.6399 24.7082 68.2884 25.3516C68.9369 25.995 69.3017 26.8674 69.3027 27.7773V37.8466H52.0362C55.6366 34.2064 57.8366 29.4287 58.2531 24.3452ZM23.6595 9.43682C34.8864 -1.5444 54.2968 6.4831 54.8629 21.9549C55.6091 36.3914 38.8381 45.9111 26.6371 38.1094C16.6026 32.2585 15.061 17.2114 23.6595 9.43682ZM11.2501 24.3418H14.6512C15.032 29.4296 17.2253 34.2168 20.8386 37.8466H11.2501C9.447 37.8438 7.71875 37.1311 6.44479 35.8651C5.17084 34.5991 4.45531 32.8832 4.45531 31.0942C4.45531 29.3052 5.17084 27.5893 6.44479 26.3233C7.71875 25.0573 9.447 24.3447 11.2501 24.3418ZM86.3605 84.6976C86.358 87.0494 85.4153 89.3042 83.7392 90.9671C82.0631 92.6301 79.7906 93.5654 77.4203 93.5678H13.4003C11.03 93.5654 8.75742 92.6301 7.08134 90.9671C5.40527 89.3041 4.46258 87.0493 4.46015 84.6975V38.708C6.32886 40.3625 8.74554 41.2775 11.2501 41.2788H25.2552C28.6434 43.2602 32.5045 44.3051 36.4374 44.3051C40.3703 44.3051 44.2315 43.2602 47.6197 41.2788C47.6403 41.2735 71.0291 41.2876 71.0464 41.2746H77.4271C79.7948 41.2794 82.0639 42.216 83.7365 43.8788C85.4091 45.5416 86.3485 47.7948 86.3486 50.1441V57.2388H78.8881C77.5389 57.237 76.2026 57.4992 74.9556 58.0103C73.7087 58.5213 72.5755 59.2713 71.6209 60.2172C70.6662 61.1631 69.9089 62.2865 69.3922 63.5231C68.8755 64.7596 68.6095 66.0851 68.6095 67.4237C68.6095 68.7624 68.8755 70.0879 69.3922 71.3244C69.9089 72.561 70.6662 73.6844 71.6209 74.6303C72.5755 75.5762 73.7087 76.3262 74.9556 76.8372C76.2026 77.3483 77.5389 77.6105 78.8881 77.6087C78.8646 77.6617 86.5416 77.4942 86.3604 77.6741L86.3605 84.6976ZM89.5409 74.1766H78.8881C77.0935 74.1608 75.3778 73.4423 74.1144 72.1777C72.851 70.913 72.1422 69.2044 72.1422 67.4237C72.1422 65.643 72.8511 63.9345 74.1145 62.6698C75.3779 61.4051 77.0936 60.6867 78.8882 60.6709H89.5409L89.5409 74.1766Z" fill="white"/>
              <path d="M80.8458 69.139C81.2987 69.1302 81.73 68.9456 82.0471 68.6247C82.3643 68.3038 82.5419 67.8723 82.5419 67.4229C82.5419 66.9734 82.3642 66.542 82.0471 66.2211C81.7299 65.9002 81.2986 65.7156 80.8457 65.7069C80.3928 65.7156 79.9614 65.9003 79.6443 66.2212C79.3271 66.542 79.1495 66.9735 79.1495 67.423C79.1495 67.8724 79.3272 68.3039 79.6444 68.6247C79.9615 68.9456 80.3929 69.1302 80.8458 69.139Z" fill="white"/>
              <path d="M31.7551 30.6933C31.9157 30.8527 32.1063 30.9791 32.3162 31.0654C32.526 31.1517 32.7509 31.1961 32.978 31.1961C33.2051 31.1961 33.43 31.1517 33.6399 31.0654C33.8497 30.9791 34.0403 30.8527 34.2009 30.6933L48.0376 16.9647C48.3514 16.6408 48.5246 16.2077 48.5201 15.7584C48.5156 15.3092 48.3337 14.8795 48.0135 14.5619C47.6933 14.2442 47.2603 14.0638 46.8075 14.0593C46.3547 14.0549 45.9182 14.2268 45.5917 14.5382L32.978 27.0534L27.2825 21.4024C26.9561 21.0909 26.5195 20.9189 26.0667 20.9233C25.6138 20.9277 25.1807 21.1081 24.8605 21.4259C24.5402 21.7436 24.3584 22.1733 24.354 22.6227C24.3496 23.072 24.5229 23.5051 24.8369 23.829L31.7551 30.6933Z" fill="white"/>
              <path d="M91.2705 57.2388H89.8078V50.1441C89.8064 46.8849 88.5022 43.7594 86.1812 41.4532C83.8601 39.147 80.7118 37.8485 77.427 37.8425H72.7619V27.7773C72.7599 25.9574 72.0304 24.2126 70.7334 22.9257C69.4364 21.6388 67.6778 20.915 65.8436 20.913H58.2408C55.2994 -5.60143 17.5102 -5.67286 14.628 20.9099L11.2501 20.9097C9.89227 20.9108 8.54815 21.1793 7.29569 21.6998C6.04323 22.2202 4.90737 22.9821 3.95397 23.9414C3.00058 24.9007 2.24861 26.0383 1.74169 27.2881C1.23476 28.538 0.982964 29.8752 1.00089 31.2224V84.6976C1.00452 87.9593 2.31205 91.0864 4.6366 93.3928C6.96116 95.6991 10.1129 96.9964 13.4003 97H77.4203C80.7077 96.9964 83.8594 95.6991 86.1839 93.3927C88.5084 91.0864 89.816 87.9593 89.8196 84.6976C89.8067 84.6322 89.8445 77.6639 89.8063 77.6088L91.2705 77.6087C91.4976 77.6088 91.7226 77.5645 91.9325 77.4783C92.1424 77.3922 92.3331 77.2658 92.4937 77.1064C92.6543 76.947 92.7817 76.7578 92.8686 76.5496C92.9555 76.3413 93.0001 76.1181 93 75.8927V58.9548C93.0001 58.7295 92.9555 58.5063 92.8686 58.298C92.7818 58.0897 92.6544 57.9005 92.4937 57.7411C92.3331 57.5818 92.1424 57.4554 91.9325 57.3692C91.7226 57.283 91.4977 57.2387 91.2705 57.2388ZM58.2531 24.3452H65.8435C66.7606 24.3462 67.6399 24.7082 68.2884 25.3516C68.9369 25.995 69.3017 26.8674 69.3027 27.7773V37.8466H52.0362C55.6366 34.2064 57.8366 29.4287 58.2531 24.3452ZM23.6595 9.43682C34.8864 -1.5444 54.2968 6.4831 54.8629 21.9549C55.6091 36.3914 38.8381 45.9111 26.6371 38.1094C16.6026 32.2585 15.061 17.2114 23.6595 9.43682ZM11.2501 24.3418H14.6512C15.032 29.4296 17.2253 34.2168 20.8386 37.8466H11.2501C9.447 37.8438 7.71875 37.1311 6.44479 35.8651C5.17084 34.5991 4.45531 32.8832 4.45531 31.0942C4.45531 29.3052 5.17084 27.5893 6.44479 26.3233C7.71875 25.0573 9.447 24.3447 11.2501 24.3418ZM86.3605 84.6976C86.358 87.0494 85.4153 89.3042 83.7392 90.9671C82.0631 92.6301 79.7906 93.5654 77.4203 93.5678H13.4003C11.03 93.5654 8.75742 92.6301 7.08134 90.9671C5.40527 89.3041 4.46258 87.0493 4.46015 84.6975V38.708C6.32886 40.3625 8.74554 41.2775 11.2501 41.2788H25.2552C28.6434 43.2602 32.5045 44.3051 36.4374 44.3051C40.3703 44.3051 44.2315 43.2602 47.6197 41.2788C47.6403 41.2735 71.0291 41.2876 71.0464 41.2746H77.4271C79.7948 41.2794 82.0639 42.216 83.7365 43.8788C85.4091 45.5416 86.3485 47.7948 86.3486 50.1441V57.2388H78.8881C77.5389 57.237 76.2026 57.4992 74.9556 58.0103C73.7087 58.5213 72.5755 59.2713 71.6209 60.2172C70.6662 61.1631 69.9089 62.2865 69.3922 63.5231C68.8755 64.7596 68.6095 66.0851 68.6095 67.4237C68.6095 68.7624 68.8755 70.0879 69.3922 71.3244C69.9089 72.561 70.6662 73.6844 71.6209 74.6303C72.5755 75.5762 73.7087 76.3262 74.9556 76.8372C76.2026 77.3483 77.5389 77.6105 78.8881 77.6087C78.8646 77.6617 86.5416 77.4942 86.3604 77.6741L86.3605 84.6976ZM89.5409 74.1766H78.8881C77.0935 74.1608 75.3778 73.4423 74.1144 72.1777C72.851 70.913 72.1422 69.2044 72.1422 67.4237C72.1422 65.643 72.8511 63.9345 74.1145 62.6698C75.3779 61.4051 77.0936 60.6867 78.8882 60.6709H89.5409L89.5409 74.1766Z" stroke="white" stroke-width="1.2" mask="url(#path-1-outside-1_13454_706)"/>
              <path d="M80.8458 69.139C81.2987 69.1302 81.73 68.9456 82.0471 68.6247C82.3643 68.3038 82.5419 67.8723 82.5419 67.4229C82.5419 66.9734 82.3642 66.542 82.0471 66.2211C81.7299 65.9002 81.2986 65.7156 80.8457 65.7069C80.3928 65.7156 79.9614 65.9003 79.6443 66.2212C79.3271 66.542 79.1495 66.9735 79.1495 67.423C79.1495 67.8724 79.3272 68.3039 79.6444 68.6247C79.9615 68.9456 80.3929 69.1302 80.8458 69.139Z" stroke="white" stroke-width="1.2" mask="url(#path-1-outside-1_13454_706)"/>
              <path d="M31.7551 30.6933C31.9157 30.8527 32.1063 30.9791 32.3162 31.0654C32.526 31.1517 32.7509 31.1961 32.978 31.1961C33.2051 31.1961 33.43 31.1517 33.6399 31.0654C33.8497 30.9791 34.0403 30.8527 34.2009 30.6933L48.0376 16.9647C48.3514 16.6408 48.5246 16.2077 48.5201 15.7584C48.5156 15.3092 48.3337 14.8795 48.0135 14.5619C47.6933 14.2442 47.2603 14.0638 46.8075 14.0593C46.3547 14.0549 45.9182 14.2268 45.5917 14.5382L32.978 27.0534L27.2825 21.4024C26.9561 21.0909 26.5195 20.9189 26.0667 20.9233C25.6138 20.9277 25.1807 21.1081 24.8605 21.4259C24.5402 21.7436 24.3584 22.1733 24.354 22.6227C24.3496 23.072 24.5229 23.5051 24.8369 23.829L31.7551 30.6933Z" stroke="white" stroke-width="1.2" mask="url(#path-1-outside-1_13454_706)"/>
            </svg>
            </span>
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-12 padding-zero trnsctn-style">
            ${{finaltransactionAmount}}
          </div>
        </div>

      </div>
    </div>
  </div>
  <div class="mt-3" v-if="!show_cashback  && (rwInvited != 1 && rwState == 'allowed')" >
    <div class="row bg-white">
      <div class="col-12 text-center payment-approved-text-div" v-if="(transactiondetails.free_spin_wheel_id && transactiondetails.free_spin_wheel_id != null )">
        <div class="row justify-content-center">
          <div :class="cashback_points_earned==0?'spin-rward-card  mb-4':'spin-rward-card  mb-3'" @click="redirectToWheel" style="padding:3px; width:300px">
            <svg width="50" viewBox="0 0 141 141" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M71.1435 70.5881C73.1964 75.098 75.2586 79.5824 77.3114 84.0924C84.6743 100.103 92.0278 116.139 99.4163 132.159C99.6579 132.685 99.8646 133.226 100.168 133.978C97.4714 134.99 94.9187 136.085 92.2883 136.917C78.8721 141.246 65.389 141.507 51.8134 137.69C51.3855 137.591 50.9482 137.517 50.5015 137.468L46.6677 136.054C54.7357 114.189 62.7943 92.3493 70.8368 70.4749L71.1435 70.5881Z" fill="black"/>
            <path d="M95.0486 4.94183C87.0184 26.7044 78.9787 48.4925 70.9229 70.2456C70.8945 70.3224 70.8823 70.4342 70.8539 70.5111C70.5934 70.0367 70.2978 69.5786 70.0722 69.0881C60.8082 48.9436 51.5441 28.799 42.2801 8.65443C42.0706 8.19899 41.8867 7.75299 41.6156 7.07117C43.8504 6.20865 45.9414 5.26393 48.129 4.52942C61.0917 0.0913902 74.2254 -0.560182 87.5338 2.72158C88.9451 3.06781 90.3403 3.37899 91.726 3.71579L95.0486 4.94183Z" fill="black"/>
            <path d="M41.5178 7.0943C51.2008 28.1498 60.8743 49.2308 70.5572 70.2863C70.5894 70.3563 70.6472 70.4358 70.705 70.5153C70.1749 70.3779 69.6354 70.2661 69.1242 70.0775C48.345 62.41 27.5657 54.7426 6.78639 47.0751C6.32633 46.9054 5.87572 46.71 5.19508 46.4298C6.15424 44.2239 6.99894 42.0921 7.99699 40.0169C14.0242 27.6964 22.8733 17.9589 34.6116 10.8582C35.8423 10.1197 37.057 9.34622 38.2972 8.58211L41.5178 7.0943Z" fill="#0DD668"/>
            <path d="M7.29107 99.8554C28.3298 90.1361 49.394 80.4262 70.4327 70.7069C70.5027 70.6745 70.5822 70.6166 70.6616 70.5586C70.5238 71.0896 70.4115 71.6299 70.2226 72.142C62.5419 92.9572 54.8612 113.772 47.1804 134.588C47.0104 135.048 46.8148 135.5 46.5341 136.182C44.3322 135.224 42.2042 134.38 40.1328 133.383C27.8352 127.362 18.1204 118.512 11.0423 106.767C10.3062 105.535 9.53502 104.32 8.77331 103.079L7.29107 99.8554Z" fill="#D3FCC8"/>
            <path d="M100.292 134.024C90.6092 112.968 80.9357 91.8872 71.2528 70.8318C71.2205 70.7617 71.1628 70.6822 71.105 70.6027C71.635 70.7401 72.1746 70.8519 72.6857 71.0406C93.465 78.708 114.244 86.3755 135.024 94.0429C135.484 94.2127 135.934 94.408 136.615 94.6883C135.656 96.8942 134.811 99.026 133.813 101.101C127.786 113.422 118.937 123.159 107.198 130.26C105.968 130.998 104.753 131.772 103.513 132.536L100.292 134.024Z" fill="#0DD668"/>
            <path d="M134.331 41.2265C113.293 50.9458 92.2284 60.6557 71.1736 70.34C71.1036 70.3724 71.0242 70.4303 70.9447 70.4882C71.0825 69.9573 71.1948 69.417 71.3838 68.9049C79.0645 48.0897 86.7452 27.2745 94.4514 6.46867C94.6215 6.00782 94.8171 5.5564 95.0978 4.87454C97.2997 5.8325 99.4277 6.67591 101.499 7.67295C113.797 13.6942 123.511 22.544 130.59 34.2898C131.326 35.5213 132.097 36.7368 132.859 37.9778L134.331 41.2265Z" fill="#D3FCC8"/>
            <path d="M5.1902 46.5153C26.9151 54.5317 48.6657 62.5575 70.3812 70.5995C70.4578 70.6278 70.5695 70.6399 70.6462 70.6682C70.1722 70.9297 69.7144 71.2261 69.2243 71.4526C49.0957 80.7514 28.9672 90.0503 8.83869 99.3491C8.38361 99.5593 7.93798 99.744 7.25675 100.016C6.39787 97.7794 5.45675 95.6868 4.72566 93.4972C0.308002 80.5224 -0.327974 67.3722 2.96442 54.0425C3.31178 52.629 3.62413 51.2316 3.96204 49.8437L5.1902 46.5153Z" fill="#007EE5"/>
            <path d="M136.29 94.6281C135.846 94.4933 135.428 94.368 134.993 94.2077C114.061 86.4837 93.128 78.7596 72.2209 71.045C71.8375 70.9036 71.4636 70.7365 71.0896 70.5694C71.4236 70.3727 71.7764 70.1247 72.1265 69.963C92.4045 60.5739 112.699 51.2197 132.967 41.8562C133.282 41.7107 133.623 41.5745 134.094 41.3993C134.281 41.7591 134.502 42.1027 134.663 42.453C140.335 55.5706 141.949 69.1398 139.288 83.1971C138.709 86.1832 137.916 89.1194 137.235 92.0678L136.29 94.6281Z" fill="#29576C"/>
            </svg>
            <span style="font-size:1rem;">FREE SPIN AWARDED</span>
            <svg width="17" style="margin-right:20px;" viewBox="0 0 49 41" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M46.6265 22.0146L29.509 39.4962C28.5806 40.4444 27.0559 40.4604 26.1077 39.532C25.1596 38.6036 25.1435 37.0789 26.0719 36.1308L39.1277 22.7973L2.88655 23.1787C1.53777 23.1929 0.471099 22.1484 0.456906 20.7997C0.442712 19.4509 1.48717 18.3842 2.83595 18.37L39.0771 17.9886L25.7436 4.9329C24.7955 4.00449 24.7794 2.47979 25.7078 1.53163C26.172 1.05756 26.756 0.816816 27.4011 0.810027C28.0461 0.803239 28.635 1.03164 29.1091 1.49584L46.5907 18.6134L48.3092 20.2961L46.6265 22.0146Z" fill="white"/>
            </svg>
          </div>
        </div>
      </div>
      <div :class="(transactiondetails.free_spin_wheel_id && transactiondetails.free_spin_wheel_id != null )?'col-12 text-center mb-4':'col-12 text-center mt-4 mb-4'" v-if="cashback_points_earned > 0">
        <span class="text-bold" >
        <span >+{{pointNumberFormatter(cashback_points_earned)}} </span>
        <svg xmlns="http://www.w3.org/2000/svg" v-if="transactiondetails.is_generic_cashback_point == 0" width="20" height="20" viewBox="0 0 50 50" fill="none">
          <path d="M43.6484 32.2123C45.9758 27.2992 45.0233 22.6013 44.7537 21.3551C44.4482 19.9476 43.6754 16.6214 40.8627 13.6359C39.5231 12.233 37.9321 11.0927 36.1719 10.2738C33.7996 9.17106 31.7777 8.95589 30.6724 8.8483C26.1164 8.40003 22.3512 9.72692 20.2485 10.6773C21.2723 9.69865 22.4425 8.88501 23.7171 8.26555C24.8436 7.72841 26.0375 7.34542 27.2667 7.12693C29.2792 6.74323 31.3425 6.70382 33.3683 7.01038C34.3028 7.15383 38.2028 7.82624 42.0938 10.8386C47.7641 15.2228 50.0915 21.3103 48.7705 28.3123C46.7576 39.0261 36.4595 45.3019 26.0176 42.5406C22.9443 41.7247 19.7093 39.7971 18.2715 37.9144C18.3434 37.9144 18.4243 37.8964 18.4692 37.9233C18.8287 38.1564 19.1791 38.4075 19.5476 38.6406C25.3886 42.2985 31.3733 42.3254 37.3222 39.053C38.0754 38.6376 38.791 38.1577 39.4609 37.6185C42.0399 35.5385 43.2171 33.1178 43.6484 32.2123Z" fill="black"/>
          <path d="M49.2647 32.8578C49.2018 33.2344 49.094 33.7723 48.9232 34.4088C47.6831 38.9633 44.8525 41.8412 43.6394 43.0426C39.2002 47.4446 34.0332 48.7177 31.6698 49.2736C26.8083 50.4122 22.8814 49.9281 21.4706 49.7039C16.3934 48.897 12.9067 46.8529 11.8374 46.1805C9.65575 44.8138 7.70037 43.117 6.04133 41.1509C4.1037 38.8639 2.58573 36.2538 1.55724 33.4406C1.29664 32.7233 0.308168 29.9082 0.0565558 26.053C-0.177084 22.3772 0.380057 19.6337 0.514849 18.9972C0.954323 16.9176 1.66425 14.9043 2.62659 13.0083C3.12982 12.04 5.69087 7.30621 11.3521 3.77381C12.6641 2.94899 16.0968 1.01244 20.9583 0.277275C22.2434 0.0800349 33.3233 -1.4441 41.2581 5.48622C44.9334 8.69586 47.099 12.9903 47.4405 13.6807C47.9662 14.747 48.4225 15.8461 48.8064 16.971C48.6806 16.8006 47.746 15.4917 47.7191 15.4558C44.2684 10.3545 40.2606 7.95172 40.2606 7.95172C38.7451 7.06349 37.1157 6.38487 35.4171 5.93449C30.0164 4.48208 25.4694 5.77311 24.3911 6.09587C18.0648 7.98758 14.3176 13.0172 14.1019 13.3131C10.2828 18.5668 10.3637 24.0537 10.4356 25.5778C10.6782 30.7061 12.9247 34.3281 13.9491 35.7895C14.03 35.8881 14.1468 36.0406 14.2906 36.2199C14.3985 36.3633 14.5063 36.5068 14.6141 36.6502C17.292 40.1647 20.8415 42.6123 24.7864 43.8316C27.7263 44.7285 30.8328 44.9427 33.8684 44.4579C36.9039 43.9731 39.7882 42.802 42.3004 41.0343C44.511 39.4743 45.85 37.8516 46.6767 36.8474C47.773 35.5206 48.5728 34.2116 48.8334 33.6916C48.8693 33.6288 49.2467 32.8578 49.2647 32.8578Z" fill="#ECB800"/>
          <path d="M20.3742 32.9385C15.6295 32.9385 13.2571 29.8096 13.2571 25.0131C13.2571 20.0552 15.7553 17.0697 20.6438 17.0697C22.5309 17.0697 24.0495 17.5179 25.2177 18.4683C26.314 19.4455 26.9251 20.7724 27.0778 22.4579H25.1728C24.4898 22.4579 23.9956 22.1441 23.6991 21.5255C23.1779 20.4227 22.1534 19.8579 20.6527 19.8579C17.7233 19.8579 16.4203 21.92 16.4203 25.022C16.4203 28.0344 17.6693 30.1592 20.5539 30.1592C22.5309 30.1592 23.6631 29.0744 24.0046 27.371H27.0689C26.7903 30.9213 24.2832 32.9385 20.3742 32.9385Z" fill="black"/>
          <path d="M36.756 17.1055H31.3733C30.4657 17.1055 29.7468 17.8944 29.7468 18.7999V32.8847H32.9818V27.2634H36.9896C40.1617 27.2634 41.8601 25.291 41.8601 22.1799C41.8601 18.8986 40.1168 17.1055 36.756 17.1055ZM36.3876 24.4123H33.2334V19.8399H36.5493C38.059 19.8399 38.8318 20.602 38.8318 22.1351C38.8408 23.6682 38.032 24.4392 36.3876 24.4123Z" fill="#ECB800"/>
        </svg>
        <svg style="margin-left: 1px;" v-else width="20" height="20" viewBox="0 0 47 47" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M40.7744 30.2796C42.9485 25.6613 42.0587 21.2453 41.8069 20.0739C41.5215 18.7507 40.7995 15.6241 38.1721 12.8178C36.9207 11.499 35.4344 10.4271 33.7902 9.65742C31.574 8.62083 29.6853 8.41857 28.6528 8.31744C24.3968 7.89607 20.8795 9.14334 18.9152 10.0367C19.8716 9.11677 20.9648 8.35195 22.1555 7.76965C23.2077 7.26475 24.3231 6.90473 25.4713 6.69935C27.3513 6.33868 29.2787 6.30163 31.1711 6.5898C32.0441 6.72464 35.6873 7.3567 39.3221 10.1884C44.619 14.3094 46.7932 20.0317 45.5592 26.6136C43.6788 36.6846 34.0588 42.5838 24.3044 39.9882C21.4335 39.2212 18.4115 37.4093 17.0684 35.6395C17.1356 35.6395 17.2111 35.6227 17.2531 35.648C17.5889 35.8671 17.9163 36.1031 18.2604 36.3222C23.7168 39.7606 29.3075 39.7859 34.8647 36.7098C35.5683 36.3194 36.2368 35.8682 36.8625 35.3614C39.2717 33.4062 40.3714 31.1308 40.7744 30.2796Z" fill="black"/>
              <path d="M46.0208 30.8863C45.9621 31.2403 45.8613 31.746 45.7018 32.3443C44.5434 36.6255 41.8992 39.3307 40.7659 40.46C36.6191 44.598 31.7922 45.7947 29.5845 46.3172C25.0431 47.3875 21.3747 46.9324 20.0568 46.7217C15.3139 45.9632 12.0569 44.0417 11.058 43.4097C9.01996 42.125 7.19333 40.53 5.64353 38.6818C3.83349 36.5321 2.41547 34.0786 1.4547 31.4341C1.21127 30.7599 0.287876 28.1137 0.0528318 24.4898C-0.165424 21.0345 0.355032 18.4557 0.480949 17.8574C0.891485 15.9026 1.55466 14.01 2.45364 12.2278C2.92373 11.3176 5.31615 6.86784 10.6047 3.54738C11.8302 2.77205 15.0369 0.951698 19.5783 0.260639C20.7787 0.0752328 31.1291 -1.35745 38.5414 5.15704C41.9747 8.17411 43.9978 12.2109 44.3168 12.8598C44.8079 13.8622 45.2341 14.8953 45.5927 15.9527C45.4752 15.7926 44.6022 14.5622 44.577 14.5285C41.3535 9.7332 37.6096 7.47462 37.6096 7.47462C36.1939 6.63968 34.6718 6.00177 33.085 5.57842C28.0399 4.21316 23.7923 5.42673 22.785 5.73012C16.8753 7.50833 13.3748 12.2362 13.1734 12.5143C9.60571 17.4528 9.68126 22.6105 9.74842 24.0432C9.97507 28.8637 12.0737 32.2685 13.0307 33.6422C13.1062 33.7349 13.2153 33.8781 13.3496 34.0467C13.4504 34.1815 13.5511 34.3164 13.6518 34.4512C16.1534 37.7548 19.4692 40.0555 23.1544 41.2017C25.9006 42.0448 28.8026 42.2462 31.6383 41.7904C34.474 41.3347 37.1683 40.2339 39.5151 38.5723C41.5802 37.1059 42.8309 35.5805 43.6032 34.6366C44.6273 33.3893 45.3745 32.1589 45.6179 31.6701C45.6515 31.6111 46.004 30.8863 46.0208 30.8863Z" fill="#007EE5"/>
              <path d="M19.0326 30.9623C14.6004 30.9623 12.3842 28.0211 12.3842 23.5123C12.3842 18.8519 14.7179 16.0455 19.2845 16.0455C21.0473 16.0455 22.466 16.4669 23.5572 17.3602C24.5814 18.2788 25.1522 19.5261 25.2949 21.1105H23.5153C22.8773 21.1105 22.4156 20.8155 22.1386 20.234C21.6517 19.1974 20.6947 18.6665 19.2929 18.6665C16.5563 18.6665 15.3391 20.6048 15.3391 23.5208C15.3391 26.3524 16.5059 28.3497 19.2005 28.3497C21.0473 28.3497 22.105 27.33 22.424 25.7288H25.2865C25.0263 29.0661 22.6842 30.9623 19.0326 30.9623Z" fill="black"/>
              <path d="M34.3357 16.0792H29.3075C28.4596 16.0792 27.7881 16.8208 27.7881 17.672V30.9116H30.8101V25.6276H34.554C37.5172 25.6276 39.1038 23.7735 39.1038 20.8491C39.1038 17.7647 37.4753 16.0792 34.3357 16.0792ZM33.9916 22.9476H31.0451V18.6496H34.1427C35.5529 18.6496 36.2748 19.3659 36.2748 20.807C36.2832 22.2481 35.5277 22.9729 33.9916 22.9476Z" fill="#007EE5"/>
        </svg>
        Points
        </span>
        </div>
      </div>
    </div>
  </div>
   <div class="mt-5" style="padding-left:20px; padding-right:20px;" v-if="!show_cashback">
    <div class="row justify-content-center">
      <div class="col-12 col-md-6">
        <div class="tn-details">
          <div class="d-flex justify-content-between">
            <div class="tn-detail-label total" style="font-size:23px!important;">Transaction Total:</div>
            <div class="tn-detail-value total" style="font-size:23px!important;">${{ finaltransactionAmount }}</div>  
          </div>
          <hr style="margin:0px!important;margin-top:15px;" class="hr-style mt-4" >
          <div v-if="rwState == 'allowed' || rwState == 'partially_allowed' || cashback_points_earned > 0" class="text-left mt-4 mb-3" style="font-family:Montserrat!important;">
            <span v-if="(generic_point_used > 0)||(merchant_point_used > 0)" style="font-weight:500;font-weight:bold;font-size:20px;">Paid With CanPay Points:
            </span>
          </div>
          <div class=" mt-2 mb-2">
          <div v-if="(rwState == 'allowed' || rwState == 'partially_allowed') && generic_point_used > 0" class="d-flex justify-content-between my-2"  style="font-family:Montserrat!important;">
            <div class="tn-detail-label point row mx-0 text-left">
              <span style="color:#000000!important;"></span>
              <span style="color:#000000!important;margin-left:25px">
              <svg style="margin-left: 1px;" width="25" viewBox="0 0 47 47" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M40.7744 30.2796C42.9485 25.6613 42.0587 21.2453 41.8069 20.0739C41.5215 18.7507 40.7995 15.6241 38.1721 12.8178C36.9207 11.499 35.4344 10.4271 33.7902 9.65742C31.574 8.62083 29.6853 8.41857 28.6528 8.31744C24.3968 7.89607 20.8795 9.14334 18.9152 10.0367C19.8716 9.11677 20.9648 8.35195 22.1555 7.76965C23.2077 7.26475 24.3231 6.90473 25.4713 6.69935C27.3513 6.33868 29.2787 6.30163 31.1711 6.5898C32.0441 6.72464 35.6873 7.3567 39.3221 10.1884C44.619 14.3094 46.7932 20.0317 45.5592 26.6136C43.6788 36.6846 34.0588 42.5838 24.3044 39.9882C21.4335 39.2212 18.4115 37.4093 17.0684 35.6395C17.1356 35.6395 17.2111 35.6227 17.2531 35.648C17.5889 35.8671 17.9163 36.1031 18.2604 36.3222C23.7168 39.7606 29.3075 39.7859 34.8647 36.7098C35.5683 36.3194 36.2368 35.8682 36.8625 35.3614C39.2717 33.4062 40.3714 31.1308 40.7744 30.2796Z" fill="black"/>
              <path d="M46.0208 30.8863C45.9621 31.2403 45.8613 31.746 45.7018 32.3443C44.5434 36.6255 41.8992 39.3307 40.7659 40.46C36.6191 44.598 31.7922 45.7947 29.5845 46.3172C25.0431 47.3875 21.3747 46.9324 20.0568 46.7217C15.3139 45.9632 12.0569 44.0417 11.058 43.4097C9.01996 42.125 7.19333 40.53 5.64353 38.6818C3.83349 36.5321 2.41547 34.0786 1.4547 31.4341C1.21127 30.7599 0.287876 28.1137 0.0528318 24.4898C-0.165424 21.0345 0.355032 18.4557 0.480949 17.8574C0.891485 15.9026 1.55466 14.01 2.45364 12.2278C2.92373 11.3176 5.31615 6.86784 10.6047 3.54738C11.8302 2.77205 15.0369 0.951698 19.5783 0.260639C20.7787 0.0752328 31.1291 -1.35745 38.5414 5.15704C41.9747 8.17411 43.9978 12.2109 44.3168 12.8598C44.8079 13.8622 45.2341 14.8953 45.5927 15.9527C45.4752 15.7926 44.6022 14.5622 44.577 14.5285C41.3535 9.7332 37.6096 7.47462 37.6096 7.47462C36.1939 6.63968 34.6718 6.00177 33.085 5.57842C28.0399 4.21316 23.7923 5.42673 22.785 5.73012C16.8753 7.50833 13.3748 12.2362 13.1734 12.5143C9.60571 17.4528 9.68126 22.6105 9.74842 24.0432C9.97507 28.8637 12.0737 32.2685 13.0307 33.6422C13.1062 33.7349 13.2153 33.8781 13.3496 34.0467C13.4504 34.1815 13.5511 34.3164 13.6518 34.4512C16.1534 37.7548 19.4692 40.0555 23.1544 41.2017C25.9006 42.0448 28.8026 42.2462 31.6383 41.7904C34.474 41.3347 37.1683 40.2339 39.5151 38.5723C41.5802 37.1059 42.8309 35.5805 43.6032 34.6366C44.6273 33.3893 45.3745 32.1589 45.6179 31.6701C45.6515 31.6111 46.004 30.8863 46.0208 30.8863Z" fill="#007EE5"/>
              <path d="M19.0326 30.9623C14.6004 30.9623 12.3842 28.0211 12.3842 23.5123C12.3842 18.8519 14.7179 16.0455 19.2845 16.0455C21.0473 16.0455 22.466 16.4669 23.5572 17.3602C24.5814 18.2788 25.1522 19.5261 25.2949 21.1105H23.5153C22.8773 21.1105 22.4156 20.8155 22.1386 20.234C21.6517 19.1974 20.6947 18.6665 19.2929 18.6665C16.5563 18.6665 15.3391 20.6048 15.3391 23.5208C15.3391 26.3524 16.5059 28.3497 19.2005 28.3497C21.0473 28.3497 22.105 27.33 22.424 25.7288H25.2865C25.0263 29.0661 22.6842 30.9623 19.0326 30.9623Z" fill="black"/>
              <path d="M34.3357 16.0792H29.3075C28.4596 16.0792 27.7881 16.8208 27.7881 17.672V30.9116H30.8101V25.6276H34.554C37.5172 25.6276 39.1038 23.7735 39.1038 20.8491C39.1038 17.7647 37.4753 16.0792 34.3357 16.0792ZM33.9916 22.9476H31.0451V18.6496H34.1427C35.5529 18.6496 36.2748 19.3659 36.2748 20.807C36.2832 22.2481 35.5277 22.9729 33.9916 22.9476Z" fill="#007EE5"/>
              </svg>
              {{pointNumberFormatter(generic_point_used)}} 
              </span></div>
            <div class="tn-detail-value point">${{generic_amount_used}}</div>
          </div>
          <div class="d-flex justify-content-between my-2" v-if="merchant_point_used > 0" >
            <div class="tn-detail-label point row mx-0 text-left">
              <span style="color:#000000!important; font-family:Montserrat;"></span>
              <span style="color:#000000!important;font-family:Montserrat;margin-left:25px;">
                <svg xmlns="http://www.w3.org/2000/svg" width="25" viewBox="0 0 50 50" fill="none">
                  <path d="M43.6484 32.2123C45.9758 27.2992 45.0233 22.6013 44.7537 21.3551C44.4482 19.9476 43.6754 16.6214 40.8627 13.6359C39.5231 12.233 37.9321 11.0927 36.1719 10.2738C33.7996 9.17106 31.7777 8.95589 30.6724 8.8483C26.1164 8.40003 22.3512 9.72692 20.2485 10.6773C21.2723 9.69865 22.4425 8.88501 23.7171 8.26555C24.8436 7.72841 26.0375 7.34542 27.2667 7.12693C29.2792 6.74323 31.3425 6.70382 33.3683 7.01038C34.3028 7.15383 38.2028 7.82624 42.0938 10.8386C47.7641 15.2228 50.0915 21.3103 48.7705 28.3123C46.7576 39.0261 36.4595 45.3019 26.0176 42.5406C22.9443 41.7247 19.7093 39.7971 18.2715 37.9144C18.3434 37.9144 18.4243 37.8964 18.4692 37.9233C18.8287 38.1564 19.1791 38.4075 19.5476 38.6406C25.3886 42.2985 31.3733 42.3254 37.3222 39.053C38.0754 38.6376 38.791 38.1577 39.4609 37.6185C42.0399 35.5385 43.2171 33.1178 43.6484 32.2123Z" fill="black"/>
                  <path d="M49.2647 32.8578C49.2018 33.2344 49.094 33.7723 48.9232 34.4088C47.6831 38.9633 44.8525 41.8412 43.6394 43.0426C39.2002 47.4446 34.0332 48.7177 31.6698 49.2736C26.8083 50.4122 22.8814 49.9281 21.4706 49.7039C16.3934 48.897 12.9067 46.8529 11.8374 46.1805C9.65575 44.8138 7.70037 43.117 6.04133 41.1509C4.1037 38.8639 2.58573 36.2538 1.55724 33.4406C1.29664 32.7233 0.308168 29.9082 0.0565558 26.053C-0.177084 22.3772 0.380057 19.6337 0.514849 18.9972C0.954323 16.9176 1.66425 14.9043 2.62659 13.0083C3.12982 12.04 5.69087 7.30621 11.3521 3.77381C12.6641 2.94899 16.0968 1.01244 20.9583 0.277275C22.2434 0.0800349 33.3233 -1.4441 41.2581 5.48622C44.9334 8.69586 47.099 12.9903 47.4405 13.6807C47.9662 14.747 48.4225 15.8461 48.8064 16.971C48.6806 16.8006 47.746 15.4917 47.7191 15.4558C44.2684 10.3545 40.2606 7.95172 40.2606 7.95172C38.7451 7.06349 37.1157 6.38487 35.4171 5.93449C30.0164 4.48208 25.4694 5.77311 24.3911 6.09587C18.0648 7.98758 14.3176 13.0172 14.1019 13.3131C10.2828 18.5668 10.3637 24.0537 10.4356 25.5778C10.6782 30.7061 12.9247 34.3281 13.9491 35.7895C14.03 35.8881 14.1468 36.0406 14.2906 36.2199C14.3985 36.3633 14.5063 36.5068 14.6141 36.6502C17.292 40.1647 20.8415 42.6123 24.7864 43.8316C27.7263 44.7285 30.8328 44.9427 33.8684 44.4579C36.9039 43.9731 39.7882 42.802 42.3004 41.0343C44.511 39.4743 45.85 37.8516 46.6767 36.8474C47.773 35.5206 48.5728 34.2116 48.8334 33.6916C48.8693 33.6288 49.2467 32.8578 49.2647 32.8578Z" fill="#ECB800"/>
                  <path d="M20.3742 32.9385C15.6295 32.9385 13.2571 29.8096 13.2571 25.0131C13.2571 20.0552 15.7553 17.0697 20.6438 17.0697C22.5309 17.0697 24.0495 17.5179 25.2177 18.4683C26.314 19.4455 26.9251 20.7724 27.0778 22.4579H25.1728C24.4898 22.4579 23.9956 22.1441 23.6991 21.5255C23.1779 20.4227 22.1534 19.8579 20.6527 19.8579C17.7233 19.8579 16.4203 21.92 16.4203 25.022C16.4203 28.0344 17.6693 30.1592 20.5539 30.1592C22.5309 30.1592 23.6631 29.0744 24.0046 27.371H27.0689C26.7903 30.9213 24.2832 32.9385 20.3742 32.9385Z" fill="black"/>
                  <path d="M36.756 17.1055H31.3733C30.4657 17.1055 29.7468 17.8944 29.7468 18.7999V32.8847H32.9818V27.2634H36.9896C40.1617 27.2634 41.8601 25.291 41.8601 22.1799C41.8601 18.8986 40.1168 17.1055 36.756 17.1055ZM36.3876 24.4123H33.2334V19.8399H36.5493C38.059 19.8399 38.8318 20.602 38.8318 22.1351C38.8408 23.6682 38.032 24.4392 36.3876 24.4123Z" fill="#ECB800"/>
              </svg>
              {{pointNumberFormatter(merchant_point_used)}}
              </span></div>
            <div class="tn-detail-value point" style="color:#ebb700!important;font-family:Montserrat;">${{merchant_amount_used}}</div>
          </div>
          </div>
          <div class="d-flex justify-content-between my-2" v-if="consumer_bank_posting_amount > 0" >
            <div class="tn-detail-label point row mx-0 text-left">
              <span style="color:#000000!important;">Paid by Bank:</span></div>
            <div class="tn-detail-value point" style="color:#000000!important;font-size:21px!important;font-family:Montserrat;">${{consumer_bank_posting_amount}}</div>
          </div>
          <hr style="margin:0px!important;margin-top:15px;" class="hr-style mt-4" v-if="rwState == 'allowed' || rwState == 'partially_allowed' || cashback_points_earned > 0">
        </div>
      </div>
    </div>

    <div class="row justify-content-center store-name-row mt-2">
      <div class="col-12 row col-md-6 p-0">
      <div class="col-6 padding-zero text-left" >
        <div class="padding-zero row" style="padding-top:20px!important;">
          <div class="col-3 padding-zero text-right">
        <svg style="margin-right: 5px;" xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 43 55" fill="none">
          <path d="M6.62518 28.3098L6.6181 28.2947L6.61003 28.2802C5.36521 26.0378 4.43714 23.2806 4.43714 20.8045C4.43714 11.7239 12.0445 4.34112 21.4723 4.34112C30.7228 4.34112 38.3307 11.7214 38.2883 20.7598V20.7621C38.2883 23.4752 37.0691 26.3497 35.7969 28.9385L35.7969 28.9385C32.9578 34.7186 29.3345 40.5029 25.792 46.1585C25.6866 46.3267 25.5813 46.4948 25.4762 46.6628L25.476 46.6627L25.4699 46.6731L23.9408 49.2511C22.7324 51.1019 19.9834 51.0992 18.7794 49.2432C18.7794 49.2431 18.7793 49.243 18.7793 49.243L16.9424 46.4083C16.9417 46.4072 16.941 46.4062 16.9404 46.4051C13.3012 40.5942 9.54736 34.512 6.62518 28.3098ZM27.3284 51.3127L27.3335 51.3048L27.3382 51.2968L28.8668 48.7197C32.6855 42.9017 36.3317 36.7822 39.4091 30.6583C40.9563 27.6642 42.2692 24.1875 42.2692 20.8045C42.2692 9.53184 32.9142 0.499756 21.4723 0.499756C9.85789 0.499756 0.5 9.52908 0.5 20.8045C0.5 23.8828 1.67676 27.2281 2.91167 29.9587L2.91562 29.9675L2.9199 29.9761C5.66901 35.4823 9.0309 40.9875 12.2737 46.2978C12.7162 47.0224 13.1565 47.7434 13.5927 48.4603L13.5964 48.4664L13.6002 48.4723L15.4387 51.3094C15.4392 51.3101 15.4397 51.3109 15.4402 51.3117C18.1378 55.5659 24.5912 55.5587 27.3284 51.3127ZM32.1865 20.8045C32.1865 14.9628 27.3057 10.2537 21.4723 10.2537C15.4691 10.2537 10.5827 14.9574 10.5827 20.8045C10.5827 26.4874 15.4748 31.1857 21.4723 31.1857C27.3001 31.1857 32.1865 26.482 32.1865 20.8045ZM14.5637 20.7621C14.5637 17.1503 17.6577 14.0951 21.4723 14.0951C25.1076 14.0951 28.2494 17.1465 28.2494 20.7621C28.2494 24.2448 25.1135 27.3019 21.4723 27.3019C17.6937 27.3019 14.5637 24.2392 14.5637 20.7621Z" fill="black" stroke="black"/>
        </svg> 
          </div>
        <div class="col-9 padding-zero text-left">
        <span>{{transactiondetails.store_name}}</span>
        </div>
        </div>
      </div>
      <div  class="col-6 text-right px-5" style="padding-right:22px!important;padding-top:15px!important;">
        <img v-if="transactiondetails.store_logo_url!=null && transactiondetails.store_logo_url!=''" style="width:100%!important;background-repeat: cover;" :src="transactiondetails.store_logo_url" alt="Store Image">
      </div>
      </div>
    </div>
    <!-- Tip options section -->
    <div class="row justify-content-center" v-if="enableTip==1">
      <div class="col-12 col-md-6">
        <!-- Leave a tip heading -->
        <p class="text-left mb-3" style="font-weight: bold; color: #333;">Leave a tip</p>

          <!-- Tip percentage options -->
          <div class="row mb-3 tip-percentage-row">
            <div v-for="(percentage, index) in tipPercentage" :key="index" class="col tip-percentage-col">
              <div
                class="tip-option"
                @click="selectTip(percentage)"
                :class="{ 'tip-selected': selected == index + 1 }"
              >
                <div class="tip-percentage">{{percentage}}%</div>
                <div class="tip-amount">${{ calculateTipAmount(percentage) }}</div>
              </div>
            </div>
          </div>

          <!-- Custom tip and No tip options -->
          <div class="row mb-3 tip-bottom-row">
            <div class="col-6">
              <div
                class="tip-option no-tip-option"
                @click="selectNoTip()"
                :class="{ 'tip-selected': selected == 'no-tip' }"
              >
                <div class="tip-no-tip-text">No Tip</div>
              </div>
            </div>
            <div class="col-6">
              <div class="d-flex align-items-center custom-tip-wrapper">
                <div
                  class="tip-option custom-tip-container flex-grow-1"
                  :class="{ 'tip-selected': selected == 'custom' }"
                >
                  <input
                    class="form-control custom-tip-input"
                    v-model="customTipAmount"
                    inputmode="numeric"
                    placeholder="Custom Tip"
                    @click="clickCustomTipAmount()"
                    @keypress="isNumber($event)"
                    @keyup.enter="processCustomTip()"
                  />
                </div>
                <button
                  v-if="selected === 'custom' && customTipAmount > 0"
                  @click="processCustomTip()"
                  class="custom-tip-add-btn ml-2"
                >
                  Add
                </button>
              </div>
            </div>
          </div>
      </div>
    </div>

    <!-- OK button when tip is not enabled -->
    <div class="row justify-content-center" style="padding-left:20px;padding-right:20px;" v-else>
      <div class="col-12 col-md-6 p-0">
        <div class="row tip-btn-row">
          <div class="col-12 text-center">
            <button
              @click="clickOK"
              class="btn btn-danger btn-md center-block tip-ok-btn w-100 tip-btn mt-4"
            >
              <span class="forgetpassword-ok-label">OK</span>
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Keep the invitation modal -->
    <b-modal
    ref="invitation-modal"
    hide-footer
    v-b-modal.modal-center
    no-close-on-backdrop
    modal-backdrop
    hide-header
    id="invitation-modal"
    centered
    title="BootstrapVue"
    >
    <InvitationComponent :modal="$refs['invitation-modal']">
      <template v-slot:closeModal="">
        <a class="close-modal" @click="hideInviteModal('invitation-modal')" href="javascript:void(0)">
          <svg fill="#000000" width="20px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M4.293,18.293,10.586,12,4.293,5.707A1,1,0,0,1,5.707,4.293L12,10.586l6.293-6.293a1,1,0,1,1,1.414,1.414L13.414,12l6.293,6.293a1,1,0,1,1-1.414,1.414L12,13.414,5.707,19.707a1,1,0,0,1-1.414-1.414Z"/></svg>
        </a>
      </template>
    </InvitationComponent>
    </b-modal>

    <!-- MODAL FOR TRANSACTION ERROR MESSAGES -->
    <b-modal
      ref="transaction-error-modal"
      hide-footer
      v-b-modal.modal-center
      modal-backdrop
      hide-header
      id="transaction-error-modal"
      centered
    >
      <div class="color">
        <div class="purchaserpower-modal-text">
          <div class="d-block text-center">
            <label class="purchasepower-def-label">
              {{ error_message }}
            </label>
          </div>
          <br />
          <br />
          <div class="text-center">
            <button
              type="button"
              class="mx-auto col-10 offset-1 btn-black"
              style="height: 60px"
              @click="hideTransactionErrorModal"
            >
              <label class="purchasepower-modal-ok-label">OK</label>
            </button>
          </div>
        </div>
      </div>
    </b-modal>

    <!-- MODAL FOR TERMINAL TIP NOT ALLOWED -->
    <b-modal
      ref="tip-not-allowed-modal"
      hide-footer
      v-b-modal.modal-center
      modal-backdrop
      hide-header
      id="tip-not-allowed-modal"
      centered
    >
      <div class="color">
        <div class="purchaserpower-modal-text">
          <div class="d-block text-center">
            <label class="purchasepower-def-label">
              {{ tip_error_message }}
            </label>
          </div>
          <br />
          <br />
          <div class="text-center">
            <button
              type="button"
              class="mx-auto col-10 offset-1 btn-black"
              style="height: 60px"
              @click="hideTipErrorModal"
            >
              <label class="purchasepower-modal-ok-label">OK</label>
            </button>
          </div>
        </div>
      </div>
    </b-modal>

    <!-- MODAL FOR TIP CONFIRMATION -->
    <b-modal
      ref="tip-confirmation-modal"
      hide-footer
      v-b-modal.modal-center
      modal-backdrop
      hide-header
      id="tip-confirmation-modal"
      centered
      size="sm"
    >
      <div class="color">
        <div class="tip-confirmation-modal-content">
          <div class="d-block text-center mb-4">
            <!-- Confirmation Icon -->
            <div class="tip-confirmation-icon mb-3">
              <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 264 263" fill="none">
                <path d="M147.384 94.7847H36.6079C19.6135 94.7847 5.83679 108.561 5.83679 125.556V204.535C5.83679 221.529 19.6135 235.306 36.6079 235.306H69.9325L69.4704 235.346L91.9958 257.871L92.0117 235.306H147.384C164.378 235.306 178.155 221.529 178.155 204.535V125.556C178.155 108.561 164.378 94.7847 147.384 94.7847Z" fill="white"/>
                <path d="M91.997 263C90.662 263 89.3507 262.479 88.3696 261.498L67.3063 240.434H36.608C16.8129 240.434 0.708374 224.33 0.708374 204.535V125.556C0.708374 105.761 16.8129 89.656 36.608 89.656H88.406C91.238 89.656 93.5345 91.952 93.5345 94.7845C93.5345 97.617 91.238 99.913 88.406 99.913H36.608C22.4687 99.913 10.9654 111.416 10.9654 125.556V204.535C10.9654 218.674 22.4687 230.177 36.608 230.177H69.9326C72.0301 230.177 73.8436 231.439 74.6359 233.259L86.8767 245.499L86.8838 235.302C86.8859 232.471 89.1809 230.177 92.0124 230.177H147.384C161.523 230.177 173.026 218.674 173.026 204.535V169.148C173.026 166.316 175.323 164.019 178.155 164.019C180.987 164.019 183.284 166.316 183.284 169.148V204.535C183.284 224.33 167.179 240.434 147.384 240.434H97.1368L97.1245 257.875C97.1229 259.948 95.8731 261.817 93.9566 262.61C93.3232 262.872 92.657 263 91.997 263Z" fill="black"/>
                <path d="M188.748 5.54858H175.768C137.433 5.54858 106.356 36.6253 106.356 74.961C106.356 113.296 137.432 144.373 175.768 144.373H188.748C195.853 144.373 202.708 143.304 209.162 141.32L230.466 162.612V130.439C247.284 117.772 258.16 97.6372 258.16 74.9605C258.16 36.6253 227.083 5.54858 188.748 5.54858Z" fill="#E4E4E4"/>
                <path d="M230.467 167.74C229.132 167.74 227.822 167.219 226.841 166.239L207.667 147.076C201.512 148.686 195.163 149.501 188.747 149.501H175.768C134.666 149.501 101.227 116.063 101.227 74.9608C101.227 33.8589 134.666 0.419922 175.768 0.419922H188.748C229.849 0.419922 263.288 33.8589 263.288 74.9608C263.288 86.8672 260.564 98.2468 255.188 108.784C250.456 118.061 243.712 126.366 235.594 132.94V162.612C235.594 164.686 234.345 166.556 232.429 167.349C231.795 167.613 231.128 167.74 230.467 167.74ZM209.162 136.191C210.501 136.191 211.811 136.717 212.787 137.692L225.337 150.236V130.44C225.337 128.829 226.094 127.312 227.38 126.343C243.682 114.065 253.031 95.3369 253.031 74.9608C253.031 39.5146 224.194 10.677 188.748 10.677H175.768C140.322 10.677 111.484 39.5146 111.484 74.9608C111.484 110.407 140.321 139.245 175.768 139.245H188.748C195.19 139.245 201.551 138.294 207.656 136.418C208.151 136.266 208.659 136.191 209.162 136.191Z" fill="black"/>
                <path d="M183.694 96.2074C180.862 96.2074 178.565 93.9114 178.565 91.0789V78.7576C178.565 74.164 181.709 70.252 186.211 69.2447C191.767 68.0016 195.515 62.9541 195.121 57.2425C194.731 51.5872 190.172 47.0285 184.516 46.6387C181.291 46.4162 178.225 47.4998 175.879 49.6892C173.531 51.8806 172.238 54.8557 172.238 58.0656C172.238 60.8981 169.941 63.1941 167.109 63.1941C164.277 63.1941 161.981 60.8981 161.981 58.0656C161.981 52.0698 164.495 46.2839 168.88 42.1908C173.326 38.0403 179.135 35.9873 185.221 36.4053C195.958 37.1454 204.613 45.8002 205.353 56.5368C206.089 67.2149 199.156 76.6708 188.822 79.1674V91.0789C188.822 93.9114 186.526 96.2074 183.694 96.2074Z" fill="black"/>
                <path d="M183.694 116.493C182.345 116.493 181.022 115.95 180.068 114.996C179.115 114.042 178.565 112.719 178.565 111.365C178.565 110.016 179.114 108.693 180.068 107.739C181.022 106.785 182.345 106.236 183.694 106.236C185.043 106.236 186.366 106.785 187.32 107.739C188.273 108.693 188.822 110.016 188.822 111.365C188.822 112.719 188.274 114.037 187.32 114.996C186.366 115.95 185.043 116.493 183.694 116.493Z" fill="black"/>
                <path d="M141.743 169.661H33.5309C30.6989 169.661 28.4023 167.365 28.4023 164.532C28.4023 161.7 30.6989 159.404 33.5309 159.404H141.743C144.575 159.404 146.871 161.7 146.871 164.532C146.871 167.365 144.575 169.661 141.743 169.661Z" fill="black"/>
                <path d="M141.743 198.381C140.394 198.381 139.071 197.832 138.117 196.878C137.163 195.924 136.614 194.601 136.614 193.252C136.614 191.903 137.162 190.58 138.117 189.626C139.071 188.672 140.394 188.124 141.743 188.124C143.091 188.124 144.414 188.672 145.368 189.626C146.322 190.58 146.871 191.903 146.871 193.252C146.871 194.606 146.322 195.924 145.368 196.878C144.414 197.832 143.091 198.381 141.743 198.381Z" fill="black"/>
                <path d="M121.571 198.381H33.5309C30.6989 198.381 28.4023 196.085 28.4023 193.252C28.4023 190.42 30.6989 188.124 33.5309 188.124H121.571C124.403 188.124 126.699 190.42 126.699 193.252C126.699 196.085 124.403 198.381 121.571 198.381Z" fill="black"/>
                <path d="M108.407 140.941H33.5309C30.6989 140.941 28.4023 138.645 28.4023 135.813C28.4023 132.98 30.6989 130.684 33.5309 130.684H108.407C111.239 130.684 113.536 132.98 113.536 135.813C113.536 138.645 111.24 140.941 108.407 140.941Z" fill="black"/>
              </svg>
            </div>
            <!-- Confirmation Message -->
            <div class="tip-confirmation-message">
              {{ confirmationMessage }}
            </div>
          </div>
          <div class="text-center">
            <div class="row">
              <div class="col-6">
                <button
                  type="button"
                  class="btn w-100 d-flex align-items-center justify-content-center"
                  style="height: 50px; border-radius: 7px; background-color: #000000; border-color: #000000; color: white;"
                  @click="cancelTipConfirmation"
                >
                  <label style="font-size: 16px; font-weight: 500; color: white; margin: 0; cursor: pointer;">Cancel</label>
                </button>
              </div>
              <div class="col-6">
                <button
                  type="button"
                  class="btn btn-success w-100 d-flex align-items-center justify-content-center"
                  style="height: 50px; border-radius: 7px; background-color: #089338; border-color: #089338;"
                  @click="confirmTipSelection"
                >
                  <label style="font-size: 16px; font-weight: 500; color: white; margin: 0; cursor: pointer;">Confirm</label>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </b-modal>

  </div>
  <!-- cash back earned start -->
  <div v-if="show_cashback" style="height:72vh;margin-top:40px; padding-top:41px;background-color:#ffffff;position:relative;top:-18px;" class="merchant-cashback">   
    <div style="border:1px solid #000000; border-radius:30px;padding:20px;margin:calc(9vh - 20px) auto;width:80%;" class="bg-transparent-opac">
      <div> 
      <p class="padding-zero margin-zero text-bold" style="font-size:30px; font-family:Montserrat">Congrats!</p>
      <p class="padding-zero margin-zero" style="font-size:20px; font-family:Montserrat">You Received</p>
      <p class="padding-zero margin-zero text-bold" style="font-size:40px;color:#179346; font-family:Montserrat;padding:10px 0px 10px 0px!important;">${{cashback_amount_earned}}</p>
      <p v-if="transactiondetails.is_generic_cashback_point == 0" class="padding-zero margin-zero" style="font-size:20px; font-family:Montserrat">In Merchant Points</p>
      <p v-if="transactiondetails.is_generic_cashback_point == 1" class="padding-zero margin-zero" style="font-size:20px; font-family:Montserrat">In CanPay Points</p>
      <p class="padding-zero margin-zero" style="font-size:20px; font-family:Montserrat">towards your next</p>
      <p v-if="transactiondetails.is_generic_cashback_point == 0" class="padding-zero margin-zero" style="font-size:20px; font-family:Montserrat"><span class="text-bold">CanPay</span><span> purchase at</span></p>
      <p v-if="transactiondetails.is_generic_cashback_point == 1" class="padding-zero margin-zero" style="font-size:20px; font-family:Montserrat"><span class="text-bold">CanPay</span><span> purchase.</span></p>
      <p v-if="transactiondetails.is_generic_cashback_point == 0" class="padding-zero margin-zero text-bold" style="font-size:20px; font-family:Montserrat">{{transactiondetails.store_name}}!</p>
      </div>
    </div>
  </div>
  <!-- cash back eraned end -->
</div>
</template>

<style scoped>
.tip-option {
  border: 1px solid #089338;
  border-radius: 8px;
  padding: 5px 10px;
  text-align: center;
  cursor: pointer;
  background-color: white;
  transition: all 0.3s ease;
  margin: 0;
  min-height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.tip-option:hover {
  border-color: #089338;
  box-shadow: 0 2px 8px rgba(8, 147, 56, 0.2);
}

.tip-selected {
  border-color: #089338 !important;
  background-color: #e8f5e8 !important;
  box-shadow: 0 2px 8px rgba(8, 147, 56, 0.3);
}

.tip-percentage {
  font-weight: 500;
  font-size: 30px;
  color: #28a745;
  margin-bottom: 4px;
  font-family: Montserrat;
}

.tip-amount {
  font-size: 16px;
  color: #000;
  font-weight:500;
  font-family: Montserrat;
}

.no-tip-option {
  min-height: 60px;
  border-color: #000000 !important;
}

.tip-no-tip-text {
  font-weight: bold;
  font-size: 18px;
  color: #333;
  font-family: Montserrat;
}

.custom-tip-container {
  border: 1px solid #089338;
  border-radius: 8px;
  background-color: #089338;
  transition: all 0.3s ease;
  padding: 12px;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white !important;
  font-weight: bold;
  font-size: 18px;
  font-family: Montserrat;
}

.custom-tip-input {
  border: none;
  text-align: center;
  font-weight: bold;
  font-size: 18px !important;
  padding: 0;
  color: white !important;
  background-color: transparent !important;
  font-family: Montserrat;
  width: 100%;
  height: 100%;
}

.custom-tip-input::placeholder {
  color: white !important;
  font-weight: bold !important;
  font-size: 18px !important;
  opacity: 1;
}

.custom-tip-input::-webkit-input-placeholder {
  color: white !important;
  font-weight: bold !important;
  font-size: 18px !important;
}

.custom-tip-input::-moz-placeholder {
  color: white !important;
  font-weight: bold !important;
  font-size: 18px !important;
  opacity: 1;
}

.custom-tip-input:-ms-input-placeholder {
  color: white !important;
  font-weight: bold !important;
  font-size: 18px !important;
}

.custom-tip-input:focus {
  outline: none;
  box-shadow: none;
}

.custom-tip-container.tip-selected {
  border-color: #089338 !important;
  background-color: #e8f5e8 !important;
}

.custom-tip-container.tip-selected .custom-tip-input {
  color: #089338 !important;
  font-weight: bold;
  background-color: transparent !important;
}

.custom-tip-container.tip-selected .custom-tip-input::placeholder {
  color: #089338 !important;
  font-weight: bold !important;
  font-size: 18px !important;
  opacity: 0.7;
}

.custom-tip-container.tip-selected .custom-tip-input::-webkit-input-placeholder {
  color: #089338 !important;
  font-weight: bold !important;
  font-size: 18px !important;
}

.custom-tip-container.tip-selected .custom-tip-input::-moz-placeholder {
  color: #089338 !important;
  font-weight: bold !important;
  font-size: 18px !important;
  opacity: 0.7;
}

.custom-tip-container.tip-selected .custom-tip-input:-ms-input-placeholder {
  color: #089338 !important;
  font-weight: bold !important;
  font-size: 18px !important;
}

.custom-tip-add-btn {
  background-color: #089338;
  border: none;
  padding: 18px 16px;
  font-size: 14px;
  border-radius: 8px;
  color: white;
  font-weight: bold;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 60px;
}

.custom-tip-add-btn:hover {
  background-color: #067a2e;
}

.custom-tip-wrapper {
  gap: 8px;
}

.custom-tip-wrapper .custom-tip-add-btn {
  margin-left: 8px !important;
  flex-shrink: 0;
}

.tip-percentage-row {
  margin-left: -7.5px;
  margin-right: -7.5px;
}

.tip-percentage-col {
  padding-left: 7.5px;
  padding-right: 7.5px;
}

.tip-bottom-row {
  margin-left: -7.5px;
  margin-right: -7.5px;
}

.tip-bottom-row .col-6 {
  padding-left: 7.5px;
  padding-right: 7.5px;
}
</style>

<script>
import api from "../../api/payment.js";
import InvitationComponent from '../InvitationComponent.vue';
import rewardwheelapi from "../../api/rewardwheel.js";
import $store from '../../store'

export default {
  name: "Tip",
  data() {
    return {
      customplaceholder: "Custom TIP",
      show: 0,
      refreshing: false,
      registration: null,
      transactionAmount: "",
      finaltransactionAmount: "",
      reward_point_used: 0,
      reward_amount_used: 0,
      cashback_points_earned: 0,
      cashback_amount_earned: 0,
      consumer_bank_posting_amount: 0,
      generic_amount_used: 0,
      generic_point_used: 0,
      merchant_amount_used: 0,
      merchant_point_used: 0,
      transactionID: "",
      tipamountFor15: "",
      tipamountFor20: "",
      tipamountFor25: "",
      selected: -1,
      tipamount: "",
      customTipAmount: "",
      disableButton: false,
      showTipTransaction: false,
      finalTipamount: "",
      enableTip: 0,
      tipPercentage: [],
      approve_label: "",
      error_message: "",
      show_cashback:false,
      tip_error_message: "",
      currentUser: localStorage.getItem("consumer_login_response")
      ? JSON.parse(localStorage.getItem("consumer_login_response"))
      : null,
      rwState: '',
      rwInvited: 0,
      invitationModalShown: false,
      ignoreInvitation: false,
      // Confirmation modal data
      confirmationMessage: "",
      pendingTipAction: null,
      pendingTipAmount: 0
    };
  },
  props: {
    transactiondetails: {},
  },
  components: {
    InvitationComponent
  },
  watch: {
    tipamount: function (newval, oldval) {
      if (this.tipamount.length > 0) {
        this.disableButton = true;
      }
    },
    customTipAmount: function (newval, oldval) {
      if (this.selected === 'custom') {
        this.tipamount = this.customTipAmount;
      }
    },
  },
  methods: {
    showTipModal() {
      this.$refs.tipModal.showModal();
    },
    hideInviteModal(modal) {
      this.$refs[modal].hide();
    },
    toggleModal() {
      // We pass the ID of the button that we want to return focus to
      // when the modal has hidden
      this.$refs["my-modal"].toggle("#toggle-btn");
    },
    selectNoTip() {
      this.selected = 'no-tip';
      this.tipamount = 0;
      this.customTipAmount = '';
      // Show confirmation for no tip
      this.showTipConfirmation('no-tip', 0);
    },
    clickCustomTipAmount() {
      this.selected = 'custom';
      this.tipamount = 0;
    },
    processCustomTip() {
      if (this.customTipAmount > 0) {
        this.tipamount = this.customTipAmount;
        // Show confirmation for custom tip
        this.showTipConfirmation('custom', this.customTipAmount);
      }
    },
    clickOK() {

      const consumer_login_response = JSON.parse(localStorage.getItem("consumer_login_response"));

      if(consumer_login_response.rw_state  == 'not_allowed' 
      && consumer_login_response.rw_invited  == 0
      && consumer_login_response.rw_complete_shutdown == 0
      && consumer_login_response.rw_partial_shutdown == 0
      && consumer_login_response.rw_invitation_state == 1
      && this.ignoreInvitation == false
      ){
        rewardwheelapi
        .inviteConusmerForRewardWheelAfterTransaction()
        .then((response) => {

          if(response == 1){

            if(this.invitationModalShown == false){
              consumer_login_response.rw_invited = 1
              localStorage.setItem(
                "consumer_login_response",
                JSON.stringify(consumer_login_response)
              );
              const event = new CustomEvent('rwStateCahnged', {detail:consumer_login_response});
              document.dispatchEvent(event);

              this.invitationModalShown == true
            }
          }else{
            this.$root.$emit("showSpinInfo", {show_spin_info: true});
            this.$router
            .push({ path: '/pay' })
            // .then(() => { this.$router.go() })
          }
        })
        .catch(function (error) {
        });
      }else{
        this.$root.$emit("showSpinInfo", {show_spin_info: true});
        this.$router.push("/pay");
      }
    },
    redirectToWheel(){
      this.$root.$emit("showSpinInfo", {show_spin_info: true});
      if(this.transactiondetails.free_spin_wheel_id && this.transactiondetails.free_spin_wheel_id != ''){
        if(this.rwInvited == 1){
            this.$refs['invitation-modal'].show();
        }else{
          $store.commit('setRewardWheelID', this.transactiondetails.free_spin_wheel_id)
          this.$router.push("/rewardWheel");
        }
      }else{
        this.$router.push("/pay");
      }
    },
    clickADD() {
      this.payTip();
    },
    payTip() {
      if (this.tipamount.length > 0) {
        this.makeTransactionForTip();
      }
    },
    isNumber: function (evt) {
      evt = evt ? evt : window.event;
      var charCode = evt.which ? evt.which : evt.keyCode;
      // first number should't be dot
      if(this.customTipAmount.length == 0 && charCode == 46){
        this.customTipAmount = 0.00;
      }
      // only allow positive number
      if ((charCode < 48 || charCode > 57) && (charCode !== 46 || this.customTipAmount.indexOf('.') !== -1)) { // 46 is dot
          evt.preventDefault();
      }
      // restrict to 2 decimal place
      else if (this.customTipAmount !== null && this.customTipAmount.indexOf('.') > -1 && (this.customTipAmount.split('.')[1].length > 1)) {
        evt.preventDefault();
      }
      else {
        return true;
      }
    },
    calculateTipAmount(percentage) {
      return ((this.transactionAmount * percentage) / 100).toFixed(2);
    },
    selectTip(percentage) {
      // Find the index of the percentage in the array
      const index = this.tipPercentage.indexOf(percentage);
      if (index !== -1) {
        this.selected = index + 1;
        this.tipamount = this.calculateTipAmount(percentage);
        this.customTipAmount = '';
        console.log("this.tipamount", this.tipamount);
        // Show confirmation for percentage tip
        this.showTipConfirmation('percentage', this.tipamount, percentage);
      }
    },
    makeTransactionForTip(tipAmount) {
      let self = this;
      var request = {
        transaction_id: this.transactionID,
        tip_amount: tipAmount,
      };
      api
        .insertTip(request)
        .then((response) => {
          self.showTransactionErrorModal(response.message);
          if (response.code == 200) {
            this.finaltransactionAmount = (
              parseFloat(response.data.amount) +
              parseFloat(response.data.tip_amount)
            ).toFixed(2);
            this.consumer_bank_posting_amount = parseFloat(response.data.consumer_bank_posting_amount).toFixed(2);
            this.finalTipamount = response.data.tip_amount;
            this.showTipTransaction = true;
            this.enableTip = 0;
            this.tipPercentage = [];
          }
        })
        .catch((err) => {
          self.showTipErrorModal(err.response.data.message);
        });
    },
    showTransactionErrorModal(msg) {
      this.error_message = msg;
      this.$refs["transaction-error-modal"].show();
    },
    hideTransactionErrorModal() {
      this.error_message = "";
      this.$refs["transaction-error-modal"].hide();
    },
    showTipErrorModal(msg) {
      this.tip_error_message = msg;
      this.$refs["tip-not-allowed-modal"].show();
    },
    hideTipErrorModal() {
      this.tip_error_message = "";
      this.$refs["tip-not-allowed-modal"].hide();
      this.$router.push("/pay");
    },
    showTipConfirmation(type, amount, percentage = null) {
      this.pendingTipAction = type;
      this.pendingTipAmount = amount;

      if (type === 'no-tip') {
        this.confirmationMessage = "Are you sure you want to proceed without leaving a tip?";
      } else if (type === 'custom') {
        this.confirmationMessage = `Are you sure you want to add a custom tip of $${parseFloat(amount).toFixed(2)}?`;
      } else if (type === 'percentage') {
        this.confirmationMessage = `Are you sure you want to add a ${percentage}% tip of $${parseFloat(amount).toFixed(2)}?`;
      }

      this.$refs["tip-confirmation-modal"].show();
    },
    confirmTipSelection() {
      this.$refs["tip-confirmation-modal"].hide();

      if (this.pendingTipAction === 'no-tip') {
        // Proceed with no tip
        this.clickOK();
      } else {
        // Process the tip transaction
        this.makeTransactionForTip(this.pendingTipAmount);
      }

      // Reset pending action
      this.pendingTipAction = null;
      this.pendingTipAmount = 0;
    },
    cancelTipConfirmation() {
      this.$refs["tip-confirmation-modal"].hide();

      // Reset selection state
      this.selected = -1;
      this.tipamount = "";
      this.customTipAmount = "";

      // Reset pending action
      this.pendingTipAction = null;
      this.pendingTipAmount = 0;
    },
  },

  mounted: function () {

    const consumer_login_response = JSON.parse(localStorage.getItem("consumer_login_response"));
    this.rwState = consumer_login_response.rw_state
    this.rwInvited = consumer_login_response.rw_invited

    document
      .getElementsByClassName("content-wrap")[0]
      .style.setProperty("background-color", "#ffffff");
    this.enableTip = this.transactiondetails.tip_allowed;
    this.tipPercentage = this.transactiondetails.tip_percentage;
    this.$root.$emit("changeWhiteBackground", [true, true, "common"]);
    if (Number(this.transactiondetails.tip_amount) > 0) {
      this.finalTipamount = this.transactiondetails.tip_amount;
      this.showTipTransaction = true;
      this.finaltransactionAmount = (
        parseFloat(Number(this.transactiondetails.amount)) +
        parseFloat(Number(this.transactiondetails.tip_amount))
      ).toFixed(2);
    } else {
      this.finaltransactionAmount = Number(
        this.transactiondetails.amount
      ).toFixed(2);
    }
    this.reward_point_used = Number(
        this.transactiondetails.reward_point_used
      ).toFixed(2);
    this.cashback_points_earned = Number(
        this.transactiondetails.cashback_points_earned
      ).toFixed(2);
    this.reward_amount_used = Number(
        this.transactiondetails.reward_amount_used
      ).toFixed(2);
    this.cashback_amount_earned = Number(
        this.transactiondetails.cashback_amount_earned
      ).toFixed(2);
    this.consumer_bank_posting_amount = Number(
        this.transactiondetails.consumer_bank_posting_amount
      ).toFixed(2);
    this.generic_amount_used = Number(
        this.transactiondetails.generic_amount_used
      ).toFixed(2);
    this.generic_point_used = Number(
        this.transactiondetails.generic_point_used
      ).toFixed(2);
    this.merchant_amount_used = Number(
        this.transactiondetails.merchant_amount_used
      ).toFixed(2);
    this.merchant_point_used = Number(
        this.transactiondetails.merchant_point_used
      ).toFixed(2);

    this.transactionAmount = Number(this.transactiondetails.amount).toFixed(2);
    this.transactionID = this.transactiondetails.transaction_id;
    this.tipamountFor15 = ((this.transactionAmount * 15) / 100).toFixed(2);
    this.tipamountFor20 = ((this.transactionAmount * 20) / 100).toFixed(2);
    this.tipamountFor25 = ((this.transactionAmount * 25) / 100).toFixed(2);
    if(this.cashback_amount_earned != 0){
      this.show_cashback = true;
      setTimeout(()=>{
        this.show_cashback = false;
      },process.env.VUE_APP_CASHBACK_UI_TIMEOUT);
    }
    //changing the text
    this.approve_label =
      this.showTipTransaction != true
        ? "Payment Approved"
        : "Purchase Approved";

    this.$intercom.update({
      hide_default_launcher: true
    });

    // Set Invitation Modal 
    if(consumer_login_response.rw_state  == 'not_allowed' 
    && consumer_login_response.rw_invited  == 0
    && consumer_login_response.rw_complete_shutdown == 0
    && consumer_login_response.rw_partial_shutdown == 0
    && consumer_login_response.rw_invitation_state == 1
    ){
      rewardwheelapi
      .inviteConusmerForRewardWheelAfterTransaction()
      .then((response) => {
        if(response == 1){
          var self = this;
          setTimeout(function(){
            if(self.invitationModalShown == false){
              consumer_login_response.rw_invited = 1
              localStorage.setItem(
                "consumer_login_response",
                JSON.stringify(consumer_login_response)
              );
              const event = new CustomEvent('rwStateCahnged', {detail:consumer_login_response});
              document.dispatchEvent(event);

              self.invitationModalShown == true
            }
          }, 2000)
        }
      })
      .catch(function (error) {
      });
    }

    document.addEventListener('ignoreInvitationCahnged', (event) => {
        this.ignoreInvitation = event.detail.ignoreInvitation
    });
  },
  beforeDestroy() {
    // Show the launcher again
    this.$intercom.update({
      hide_default_launcher: false,
    });
  },
};
</script>

<style lang="scss">
#tip-modal-center___BV_modal_content_ {
  border-radius: 10px;
  margin: 10px;
  background-color: #ffffff;
}
#tip-modal-center___BV_modal_body_ {
  background-color: #ffffff !important;
  border-radius: 12px;
  margin: 10px;
}

/* Specific styles for custom tip input placeholder and text */
.custom-tip-input {
  text-align: center !important;
}
.custom-tip-input::-webkit-input-placeholder {
  text-align: center !important;
}
.custom-tip-input::-moz-placeholder {
  text-align: center !important;
}
.custom-tip-input:-ms-input-placeholder {
  text-align: center !important;
}
.custom-tip-input:-moz-placeholder {
  text-align: center !important;
}

#transaction-error-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#tip-not-allowed-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#tip-confirmation-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#tip-confirmation-modal .modal-dialog {
  max-width: 92% !important;
  width: 90% !important;
  margin: 1.75rem auto !important;
}
#tip-confirmation-modal___BV_modal_content_ {
  border-radius: 12px !important;
}
.modal-dialog {
  max-width: 90% !important;
  width: 90% !important;
}
#tip-confirmation-modal {
  --bs-modal-width: 90% !important;
}

/* Responsive modal width for tip confirmation */
@media (min-width: 768px) {
  #tip-confirmation-modal .modal-dialog {
    max-width: 400px !important;
    width: 400px !important;
  }
}

@media (max-width: 767px) {
  #tip-confirmation-modal .modal-dialog {
    max-width: 90% !important;
    width: 90% !important;
  }
}
.purchasepower-def-label {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}
.tip-confirmation-modal-content {
  padding: 20px 10px;
}
.tip-confirmation-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}
.tip-confirmation-message {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  line-height: 1.5;
  font-family: 'Montserrat', sans-serif;
  margin: 0 10px;
}
.tn-detail-label.total{
  font-size: 22px;
  font-weight: 700;
}
.tn-detail-value.total{
  font-size: 22px;
  font-weight: 700;
}
.tn-detail-label.point{
  color: #007EE5;
  font-size: 17px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.tn-detail-value.point{
  color: #007EE5;
  font-size: 17px;
  font-weight: 700;
}
.tn-detail-label.bank{
  font-size: 17px;
  font-weight: 700;
}
.tn-detail-value.bank{
  font-size: 17px;
  font-weight: 700;
}
.tip-black-btn{
    width: 140px;
    height: 60px;
    background-color: #000000 !important;
    border-color: #000000 !important;
    display: inline-block;
    vertical-align: top;
    border-radius: 7px;
}
.tip-black-btn span, .tip-btn span{
  font-size: 17px;
  font-weight: 500;
}
.tip-btn{
  height: 60px;
  border-radius: 7px;
}
.spin-rward-card{
  background: #007ee5;
  color: #fff;
  border-radius: 50px;
  padding: 0 15px 0 0;
  margin-bottom: 0;
  width: auto;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.spin-rward-card span{
  font-weight: 500;
  font-size: 14px;
  margin-left: 10px;
  margin-right: 10px;
  line-height: 14px;
}
.width-fix{
  width:85vw;
}
.padding-zero{
  padding:0px!important;
}
.bg-color-black{
background-color: #000000;
}
.pymnt-appvd-style{
  margin-top:10px;
  font-size:0.9rem;
  color:white;
  font-family:'Open Sans'
}
.add-payment-border-radius{
  border-radius: 10px;
}
.trnsctn-style{
  font-size:3rem;
  color:white;
  font-family:'montserrat'
}
.bg-grey{
  background-color: #ececec;
}
.hr-style{
  border: 0.2px solid #00000086;
}
.margin-zero{
  margin:0px!important;
}
.bg-transparent-opac{
  opacity:1;
  background-color:#ffffffe2;
}
.merchant-cashback{
  background-image:url("../../assets/gif/Cashback.gif");
}
</style>
