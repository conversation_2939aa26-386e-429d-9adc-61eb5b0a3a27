<template>
<div style="position:absolute;width: 100%;">
  <div class="container">
    <div>
      <div class="row" id="row-header">
        <!--Code for ToggleDrawer-->
        <div
          class="col-2 content"
          slot="content"
          style="z-index: 999999;  background-color: #149240;    margin-top: 25px;"
        >
          <a href="javascript:void(0)" @click="showDrawer">
            <i
              class="icon menu-termsandcondition-style"
              aria-hidden="true"
              v-show="showmenuicon"
            ></i>
            <div style="position:absolute;">
            <svg v-show="showmenuicon"  xmlns="http://www.w3.org/2000/svg" height="20"
            width="30" viewBox="0 0 67 52" fill="none">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M4 52.0002C1.79053 52.0002 0 50.2085 0 48.0002C0 45.7905 1.79053 44.0002 4 44.0002H63C65.2085 44.0002 67.0005 45.7905 67.0005 48.0002C67.0005 50.2085 65.2085 52.0002 63 52.0002H4ZM4 29.9998C1.79053 29.9998 0 28.2095 0 25.9998C0 23.79 1.79053 21.9998 4 21.9998H63C65.2085 21.9998 67.0005 23.79 67.0005 25.9998C67.0005 28.2095 65.2085 29.9998 63 29.9998H4ZM4 7.99926C1.79053 7.99926 0 6.20898 0 3.99926C0 1.79101 1.79053 -0.000976562 4 -0.000976562H63C65.2085 -0.000976562 67.0005 1.79101 67.0005 3.99926C67.0005 6.20898 65.2085 7.99926 63 7.99926H4Z" fill="white"/>
            </svg>
            <svg v-show="showmenuicon" xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 34 34" fill="none" class="alert-icon-red-circle-crew">
            <circle cx="17" cy="17" r="17" fill="#FF0040"></circle>
            </svg>
            </div>
            <svg 
            v-show="!showmenuicon"
              xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 24 46" fill="none" >
            <line x1="22.7385" y1="0.674269" x2="1.73848" y2="23.6743" stroke="white" stroke-width="2"></line>
            <line x1="23.153" y1="44.8746" x2="1.28792" y2="22.7021" stroke="white" stroke-width="2"></line>
            </svg>
          </a>

          <div>
        </div>
      </div>
    </div>
    </div>
  </div>
    <div 
     class="canpay-crew-inside-logo"
    >
          <svg v-if="!isLoading" xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 251 170" fill="none">
          <path d="M162.337 1.81982H98.4336V167.763H162.337C172.192 167.763 180.179 160.067 180.179 150.533V19.0497C180.179 9.51653 172.192 1.81982 162.337 1.81982Z" fill="black"/>
          <path d="M160.607 169.58H96.7047C95.6623 169.58 94.8188 168.767 94.8188 167.762V1.82046C94.8188 0.815848 95.6623 0.00292969 96.7047 0.00292969H160.607C171.484 0.00292969 180.336 8.54745 180.336 19.0479V150.531C180.336 161.035 171.484 169.58 160.607 169.58ZM98.5906 165.945H160.607C169.407 165.945 176.564 159.03 176.564 150.531V19.0479C176.564 10.5496 169.407 3.63799 160.607 3.63799H98.5906V165.945Z" fill="black"/>
          <path d="M171.772 19.0458V150.529C171.772 160.062 163.786 167.759 153.931 167.759H26.6609C16.7696 167.759 8.78345 160.062 8.78345 150.529V19.0458C8.78345 9.51306 16.7696 1.81592 26.6609 1.81592H153.931C163.786 1.81592 171.772 9.51306 171.772 19.0458Z" fill="white"/>
          <path d="M153.932 169.577H26.6602C15.7648 169.577 6.89893 161.032 6.89893 150.528V19.045C6.89893 8.54452 15.7648 0 26.6602 0H153.932C164.809 0 173.66 8.54452 173.66 19.045V150.528C173.66 161.032 164.809 169.577 153.932 169.577ZM26.6602 3.63506C17.8422 3.63506 10.6707 10.5466 10.6707 19.045V150.528C10.6707 159.027 17.8422 165.942 26.6602 165.942H153.932C162.731 165.942 169.888 159.027 169.888 150.528V19.045C169.888 10.5466 162.731 3.63506 153.932 3.63506H26.6602Z" fill="black"/>
          <path d="M171.785 19.7661V32.4055H8.79565V19.7661C8.79565 9.87438 16.782 1.88818 26.6735 1.88818H153.943C163.798 1.88818 171.785 9.87438 171.785 19.7661Z" fill="#A8A8A8"/>
          <path d="M171.785 34.2915H8.79555C7.75316 34.2915 6.90967 33.448 6.90967 32.4056V19.7643C6.90967 8.86882 15.7755 0.00292969 26.6746 0.00292969H153.942C164.819 0.00292969 173.67 8.86882 173.67 19.7643V32.4056C173.67 33.448 172.827 34.2915 171.785 34.2915ZM10.6814 30.5197H169.899V19.7643C169.899 10.9462 162.742 3.77471 153.942 3.77471H26.6746C17.8566 3.77471 10.6814 10.9462 10.6814 19.7643V30.5197Z" fill="black"/>
          <path d="M27.396 21.396C29.7561 21.396 31.6694 19.4827 31.6694 17.1225C31.6694 14.7624 29.7561 12.8491 27.396 12.8491C25.0358 12.8491 23.1226 14.7624 23.1226 17.1225C23.1226 19.4827 25.0358 21.396 27.396 21.396Z" fill="white"/>
          <path d="M42.2041 21.396C44.5642 21.396 46.4775 19.4827 46.4775 17.1225C46.4775 14.7624 44.5642 12.8491 42.2041 12.8491C39.8439 12.8491 37.9307 14.7624 37.9307 17.1225C37.9307 19.4827 39.8439 21.396 42.2041 21.396Z" fill="white"/>
          <path d="M57.0119 21.396C59.3721 21.396 61.2853 19.4827 61.2853 17.1225C61.2853 14.7624 59.3721 12.8491 57.0119 12.8491C54.6518 12.8491 52.7385 14.7624 52.7385 17.1225C52.7385 19.4827 54.6518 21.396 57.0119 21.396Z" fill="white"/>
          <path d="M101.201 19.0096H92.195C91.1526 19.0096 90.3091 18.1661 90.3091 17.1237C90.3091 16.0813 91.1526 15.2378 92.195 15.2378H101.201C102.243 15.2378 103.087 16.0813 103.087 17.1237C103.087 18.1661 102.243 19.0096 101.201 19.0096Z" fill="black"/>
          <path d="M157.453 19.0096H111.429C110.387 19.0096 109.543 18.1661 109.543 17.1237C109.543 16.0813 110.387 15.2378 111.429 15.2378H157.453C158.495 15.2378 159.339 16.0813 159.339 17.1237C159.339 18.1661 158.495 19.0096 157.453 19.0096Z" fill="black"/>
          <path d="M32.0809 32.4243L26.6619 54.3317C25.9552 58.2976 22.4996 61.1643 18.4551 61.1643H10.248C4.78973 61.1643 0.784569 56.0202 2.15885 50.7582L8.79512 32.4243H32.0809Z" fill="#179346"/>
          <path d="M18.4536 63.0481H10.2471C7.04625 63.0481 4.0885 61.5968 2.13263 59.0627C0.187817 56.5506 -0.467822 53.3461 0.335152 50.2815L7.02415 31.7836C7.29303 31.0358 8.00024 30.5386 8.79584 30.5386H32.0821C32.6604 30.5386 33.2092 30.8038 33.5665 31.2605C33.9238 31.7173 34.0527 32.314 33.9127 32.8775L28.4945 54.7826C27.6547 59.5194 23.4188 63.0481 18.4536 63.0481ZM10.1182 34.3104L3.93012 51.3975C3.48075 53.1619 3.89328 55.1767 5.11616 56.7569C6.35377 58.3591 8.22124 59.2763 10.2471 59.2763H18.4536C21.5882 59.2763 24.2623 57.0552 24.8074 53.998L29.6732 34.3104H10.1182Z" fill="black"/>
          <path d="M55.3664 32.4243L52.1857 53.7426C51.7145 57.9836 48.1412 61.1643 43.8609 61.1643H35.4575C30.2351 61.1643 26.269 56.4521 27.2114 51.308L32.0806 32.4243H55.3664Z" fill="#DDDDDD"/>
          <path d="M43.8609 63.0481H35.4592C32.3946 63.0481 29.5142 61.7 27.5583 59.35C25.6098 57.0147 24.8105 53.9612 25.3557 50.9703L30.2546 31.953C30.4719 31.1205 31.2196 30.5386 32.0815 30.5386H55.3678C55.9166 30.5386 56.4396 30.778 56.7969 31.1942C57.1542 31.6104 57.3126 32.1593 57.2315 32.7044L54.0491 54.0238C53.4819 59.1363 49.0987 63.0481 43.8609 63.0481ZM33.5438 34.3104L29.039 51.7806C28.7223 53.5339 29.2269 55.464 30.4535 56.9374C31.6948 58.4218 33.518 59.2763 35.4592 59.2763H43.8609C47.1759 59.2763 49.9458 56.8084 50.3105 53.5339L53.1798 34.3104H33.5438Z" fill="black"/>
          <path d="M78.6529 32.4243L77.5928 53.1144C77.4356 57.6301 73.7444 61.1643 69.2681 61.1643H60.7077C55.7206 61.1643 51.8331 56.8447 52.3827 51.8971L55.3671 32.4243H78.6529Z" fill="#179346"/>
          <path d="M69.2696 63.0481H60.7095C57.7885 63.0481 54.9966 61.8031 53.0517 59.6299C51.1143 57.4641 50.1861 54.5689 50.5065 51.6885L53.5048 32.1372C53.6448 31.2163 54.4367 30.5386 55.3686 30.5386H78.6511C79.1668 30.5386 79.6604 30.7522 80.0177 31.1242C80.375 31.4999 80.5591 32.0046 80.5333 32.5202L79.4762 53.2098C79.2847 58.7127 74.802 63.0481 69.2696 63.0481ZM56.9856 34.3104L54.2452 52.1821C54.0499 53.9464 54.6209 55.7292 55.8621 57.1142C57.0924 58.4881 58.8604 59.2763 60.7095 59.2763H69.2696C72.7578 59.2763 75.5866 56.5395 75.7081 53.0477L76.6658 34.3104H56.9856Z" fill="black"/>
          <path d="M102.92 52.5253C103.116 57.2375 99.3074 61.1643 94.5952 61.1643H85.9955C81.2443 61.1643 77.4745 57.2375 77.6317 52.5253L78.6525 32.4243H101.939L102.92 52.5253Z" fill="#DDDDDD"/>
          <path d="M94.5959 63.0481H85.9953C83.1886 63.0481 80.5697 61.9394 78.6249 59.9283C76.6764 57.9134 75.6524 55.2651 75.7445 52.462L76.7685 32.3287C76.8163 31.3268 77.6451 30.5386 78.6507 30.5386H101.937C102.942 30.5386 103.771 31.3268 103.819 32.3324L104.803 52.4326C104.803 52.4363 104.806 52.4436 104.806 52.4473C104.92 55.2319 103.915 57.8766 101.977 59.8951C100.029 61.9283 97.4064 63.0481 94.5959 63.0481ZM80.4445 34.3104L79.5126 52.6204C79.4573 54.3553 80.1019 56.0312 81.3358 57.3057C82.5661 58.5765 84.2199 59.2763 85.9953 59.2763H94.5959C96.3676 59.2763 98.0252 58.5691 99.2554 57.2836C100.475 56.0128 101.108 54.3553 101.034 52.6094L100.139 34.3104H80.4445Z" fill="black"/>
          <path d="M119.884 61.1643H111.323C106.808 61.1643 103.116 57.6301 102.959 53.1144L101.938 32.4243H125.224L128.169 51.8971C128.719 56.8447 124.871 61.1643 119.884 61.1643Z" fill="#179346"/>
          <path d="M119.882 63.0481H111.322C105.768 63.0481 101.267 58.7127 101.075 53.1803L100.055 32.5165C100.029 32.0009 100.213 31.4999 100.571 31.1242C100.928 30.7485 101.421 30.5386 101.937 30.5386H125.223C126.155 30.5386 126.947 31.22 127.087 32.1408L130.034 51.6112C130.365 54.5837 129.437 57.4862 127.503 59.6483C125.57 61.8105 122.792 63.0481 119.882 63.0481ZM103.919 34.3104L104.843 53.0219C104.965 56.5395 107.812 59.2763 111.322 59.2763H119.882C121.746 59.2763 123.455 58.5138 124.693 57.1326C125.916 55.766 126.498 53.9354 126.295 52.1011L123.603 34.3104H103.919Z" fill="black"/>
          <path d="M145.133 61.1643H136.691C132.41 61.1643 128.837 57.9836 128.366 53.7426L125.225 32.4243H148.471L153.34 51.308C154.283 56.4521 150.356 61.1643 145.133 61.1643Z" fill="#DDDDDD"/>
          <path d="M145.132 63.0481H136.689C131.452 63.0481 127.068 59.1363 126.49 53.9538L123.356 32.7007C123.278 32.1556 123.437 31.6068 123.794 31.1905C124.155 30.778 124.674 30.5386 125.223 30.5386H148.473C149.334 30.5386 150.086 31.1205 150.299 31.953L155.165 50.8377C155.743 53.9722 154.944 57.0331 152.996 59.3721C151.047 61.7073 148.182 63.0481 145.132 63.0481ZM127.407 34.3104L130.232 53.4676C130.604 56.8084 133.374 59.2763 136.689 59.2763H145.132C147.058 59.2763 148.87 58.4328 150.097 56.9558C151.327 55.4824 151.832 53.5486 151.482 51.648L147.01 34.3104H127.407Z" fill="black"/>
          <path d="M170.304 61.1643H162.097C158.092 61.1643 154.636 58.2976 153.89 54.3317L148.471 32.4243H171.757L178.432 50.7582C179.767 56.0202 175.762 61.1643 170.304 61.1643Z" fill="#179346"/>
          <path d="M170.304 63.0481H162.098C157.177 63.0481 152.948 59.5268 152.038 54.6758L146.642 32.8775C146.502 32.314 146.631 31.7173 146.989 31.2605C147.346 30.8038 147.895 30.5386 148.473 30.5386H171.755C172.547 30.5386 173.255 31.0358 173.527 31.7799L180.205 50.112C181.041 53.3682 180.367 56.5727 178.411 59.0811C176.448 61.6042 173.494 63.0481 170.304 63.0481ZM150.882 34.3104L155.722 53.8765C156.319 57.0479 158.993 59.2763 162.098 59.2763H170.304C172.323 59.2763 174.194 58.3628 175.435 56.7679C176.669 55.1804 177.096 53.1582 176.606 51.2207L170.437 34.3104H150.882Z" fill="black"/>
          <path d="M93.4353 145.55C111.987 145.55 127.026 130.511 127.026 111.96C127.026 93.4081 111.987 78.3691 93.4353 78.3691C74.8839 78.3691 59.845 93.4081 59.845 111.96C59.845 130.511 74.8839 145.55 93.4353 145.55Z" fill="#179346"/>
          <path d="M93.4373 147.304C73.9492 147.304 58.0938 131.449 58.0938 111.961C58.0938 92.4727 73.9492 76.6172 93.4373 76.6172C112.925 76.6172 128.779 92.4727 128.779 111.961C128.779 131.449 112.925 147.304 93.4373 147.304ZM93.4373 80.1231C75.8819 80.1231 61.5997 94.4071 61.5997 111.961C61.5997 129.515 75.8819 143.799 93.4373 143.799C110.991 143.799 125.273 129.515 125.273 111.961C125.273 94.4071 110.991 80.1231 93.4373 80.1231Z" fill="black"/>
          <path d="M87.2382 126.289C86.0105 126.289 84.7828 125.82 83.8456 124.883L77.1819 118.219C75.3075 116.345 75.3075 113.308 77.1819 111.434C79.0564 109.56 82.093 109.56 83.9674 111.434L87.2382 114.705L102.905 99.0377C104.78 97.1632 107.816 97.1632 109.691 99.0377C111.565 100.912 111.565 103.949 109.691 105.823L90.631 124.883C89.6938 125.82 88.466 126.289 87.2382 126.289Z" fill="white"/>
          <path d="M211.204 81.5397C232.682 81.5397 250.093 64.1283 250.093 42.6502C250.093 21.1722 232.682 3.76074 211.204 3.76074C189.726 3.76074 172.314 21.1722 172.314 42.6502C172.314 64.1283 189.726 81.5397 211.204 81.5397Z" fill="black"/>
          <path d="M204.442 81.5397C225.92 81.5397 243.331 64.1283 243.331 42.6502C243.331 21.1722 225.92 3.76074 204.442 3.76074C182.964 3.76074 165.552 21.1722 165.552 42.6502C165.552 64.1283 182.964 81.5397 204.442 81.5397Z" fill="white"/>
          <path d="M204.441 83.566C181.878 83.566 163.523 65.209 163.523 42.648C163.523 20.087 181.878 1.72998 204.441 1.72998C227.004 1.72998 245.359 20.087 245.359 42.648C245.359 65.209 227.004 83.566 204.441 83.566ZM204.441 5.78731C184.117 5.78731 167.581 22.3217 167.581 42.648C167.581 62.9742 184.117 79.5087 204.441 79.5087C224.765 79.5087 241.302 62.9742 241.302 42.648C241.302 22.3217 224.765 5.78731 204.441 5.78731Z" fill="black"/>
          <path d="M223.535 63.2805H185.354C182.879 63.2805 180.914 61.0239 181.46 58.6215C183.826 48.1025 193.216 40.2407 204.463 40.2407C215.564 40.2407 224.845 47.8841 227.393 58.2212C228.012 60.769 226.192 63.2805 223.535 63.2805Z" fill="#1B9142"/>
          <path d="M223.535 65.3082H185.355C183.499 65.3082 181.771 64.4761 180.614 63.0259C179.509 61.6391 179.095 59.872 179.481 58.1682C182.124 46.4202 192.398 38.2104 204.463 38.2104C216.288 38.2104 226.529 46.2379 229.364 57.7324C229.819 59.6105 229.405 61.5401 228.23 63.0338C227.093 64.4801 225.382 65.3082 223.535 65.3082ZM204.463 42.2678C194.308 42.2678 185.664 49.174 183.44 59.0637C183.285 59.7412 183.59 60.2524 183.784 60.4941C184.163 60.9695 184.749 61.2508 185.355 61.2508H223.535C224.349 61.2508 224.829 60.7952 225.043 60.5258C225.443 60.0146 225.582 59.349 225.423 58.6992C223.038 49.0273 214.418 42.2678 204.463 42.2678Z" fill="black"/>
          <path d="M204.847 45.7634H204.08C198.135 45.7634 193.316 40.9446 193.316 35.0003V29.8349C193.316 23.8906 198.135 19.0718 204.08 19.0718H204.847C210.791 19.0718 215.61 23.8906 215.61 29.8349V35.0003C215.61 40.9446 210.791 45.7634 204.847 45.7634Z" fill="#FFE7C5"/>
          <path d="M204.847 47.7899H204.078C197.026 47.7899 191.286 42.0526 191.286 34.9998V29.8331C191.286 22.7803 197.026 17.043 204.078 17.043H204.847C211.9 17.043 217.639 22.7803 217.639 29.8331V34.9998C217.639 42.0526 211.9 47.7899 204.847 47.7899ZM204.078 21.1003C199.262 21.1003 195.344 25.0189 195.344 29.8331V34.9998C195.344 39.8139 199.262 43.7326 204.078 43.7326H204.847C209.663 43.7326 213.582 39.8139 213.582 34.9998V29.8331C213.582 25.0189 209.663 21.1003 204.847 21.1003H204.078Z" fill="black"/>
          <path d="M215.594 30.0671C215.511 23.9827 210.567 19.0718 204.463 19.0718C198.359 19.0718 193.414 23.9827 193.331 30.0671H215.594Z" fill="#A8A8A8"/>
          <path d="M215.595 32.0955H193.331C192.789 32.0955 192.268 31.8776 191.887 31.4932C191.507 31.1049 191.295 30.5819 191.303 30.0391C191.402 22.8714 197.305 17.043 204.463 17.043C211.621 17.043 217.525 22.8714 217.624 30.0391C217.632 30.5819 217.42 31.1049 217.039 31.4932C216.659 31.8776 216.138 32.0955 215.595 32.0955ZM195.625 28.0382H213.301C212.316 24.068 208.709 21.1003 204.463 21.1003C200.218 21.1003 196.61 24.068 195.625 28.0382Z" fill="black"/>
          </svg>
    </div>
</div>
</template>

<script>
import DrawerLayout from "vue-drawer-layout";
export default {
  name: "CanPayCrewHeader",
  data: () => ({
    open: true,
    isLoading:false,
    showmenuicon: true,
    consumer_type: localStorage.getItem("consumer_login_response")
        ? JSON.parse(localStorage.getItem("consumer_login_response")).consumer_type
        : null,
  }),
  components: {
    DrawerLayout
  },
  mounted() {
    let self = this;
    self.$root.$on("Menu Drawer Close", function(data) {
      self.showmenuicon = true;
    });
    self.$root.$on("crewLaodingOn", function(data) {
      self.isLoading = data[0];
    });

    self.$root.$on("Menu Drawer Open", function(data) {
      setTimeout(function() {
        self.showmenuicon = false;
      }, 30);
    });
  },
  methods: {
    showDrawer() {
      this.showmenuicon = false;
      this.$root.$emit("Menu Drawer", [""]);
    }
  }
};
</script>