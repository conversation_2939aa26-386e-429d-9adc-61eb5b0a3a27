<template>
    <div>
        <b-modal
            ref="canpay-crew-error"
            hide-footer
            v-b-modal.modal-center
            no-close-on-backdrop
            modal-backdrop
            hide-header
            id="canpay-crew-error"
            centered
            title="BootstrapVue"
        >
            <div class="text-center" style="font-family:Open Sans">
						<svg
							version="1.1"
							id="Layer_1"
							xmlns="http://www.w3.org/2000/svg"
							xmlns:xlink="http://www.w3.org/1999/xlink"
							x="0px"
							y="0px"
							width="120"
							height="120"
							viewBox="0 0 100 125"
							style="enable-background: new 0 0 100 125"
							xml:space="preserve"
							fill="#e14343"
						>
							<path
							d="M96.2,47.5l-22-38c-0.4-0.6-1-1-1.7-1h-44c-0.7,0-1.4,0.4-1.7,1l-22,37.9c-0.4,0.6-0.4,1.4,0,2l22,38.1c0.4,0.6,1,1,1.7,1
					h44c0.7,0,1.4-0.4,1.7-1l22-38C96.6,48.9,96.6,48.1,96.2,47.5z M71.3,84.5H29.7L8.8,48.4l20.8-35.9h41.7l20.8,36L71.3,84.5z
					M50.5,60.5c1.1,0,2-0.9,2-2v-30c0-1.1-0.9-2-2-2c-1.1,0-2,0.9-2,2v30C48.5,59.6,49.4,60.5,50.5,60.5z M48.4,66.4
					c-0.6,0.6-0.9,1.3-0.9,2.1c0,0.8,0.3,1.6,0.9,2.1s1.3,0.9,2.1,0.9c0.8,0,1.6-0.3,2.1-0.9c0.6-0.6,0.9-1.3,0.9-2.1
					c0-0.8-0.3-1.6-0.9-2.1C51.5,65.3,49.5,65.3,48.4,66.4z"
							/>
						</svg>
                        <p class="mt-3 canpay-crew-text-font-14 canpay-crew-text-600"> {{textMessage}} </p>
            </div>
        </b-modal>
    </div>
</template>
<script>
export default {
    name:"CrewError",
    props:{
        textMessage:{
            type:String,
            default:""
        }
    },
    data(){
        return {
        }
    },
    methods:{
        showErrorModal(){
            this.$refs['canpay-crew-error'].show();
            setTimeout(()=>{
                this.$refs['canpay-crew-error'].hide();
            },3000);
        }
    },
    mounted(){

    }
}
</script>
<style scoped>

</style>