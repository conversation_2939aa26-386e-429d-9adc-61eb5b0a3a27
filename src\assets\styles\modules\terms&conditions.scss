.termsconditions{
    margin-top: 10px !important;
    margin: auto;
    background-color:white;
    border-radius:8px;
}

.title-margin{
    margin-top:70px;
}

.h6-font{
    text-align: justify;
}

.one-edge-shadow {
    box-shadow: -8px 2px 12px 0px #dad5d5;
    border-bottom-left-radius: 23px;
    border-bottom-right-radius: 23px;
}

.menuicon-termsandcondition-style {
    color: #000000; 
    margin-top: 30px;
    align-self: center;
    background-color: #ffffff !important;
    font-size: 25px;
}
.menuicon-participant-style {
    color: #000000; 
    margin-top: 30px;
    align-self: center;
    font-size: 25px;

}

  @media only screen and ( min-width:300px ) and ( max-width:600px ){
    .heading-termsandCondition {
        font-weight: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #000000;
        font-size: 15px;
        margin-top: 31px;
      }
  }
  @media only screen and ( min-width:360px ) and ( max-width:900px ){
    .heading-termsandCondition {
        font-weight: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #000000;
        font-size: 15px;
        margin-top: 33px;
      }
  }
  @media only screen and ( min-width:800px ) and ( max-width:1500px ){
    .heading-termsandCondition {
        font-weight: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #000000;
        font-size: 15px;
        margin-top: 31px;
      }
  }
  @media only screen and ( min-width:700px ) and ( max-width:1200px ){
    .heading-termsandCondition {
        font-weight: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #000000;
        font-size: 15px;
        margin-top: 33px;
      }
  }