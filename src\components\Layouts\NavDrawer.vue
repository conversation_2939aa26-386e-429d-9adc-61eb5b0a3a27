<template>
  <section id="navdrawer">
  <div id="navdrawer-container" v-bind:class="open ? 'nav-open' : 'nav-closed'">
    <transition name="left-nav">
      <div id="vue-navdrawer" v-show="open" class="hidden-md-down">
        <slot name="drawer-content"></slot>
       </div>
    </transition>
    <div id="vue-navcontent">
      <slot name="main-content"></slot>
    </div>
  </div>
</section>
</template>


<script>
export default {
   name: "nav-drawer",
   props: {
    open: {
      type: Boolean,
      required: true
    }
  },
  
}
</script>