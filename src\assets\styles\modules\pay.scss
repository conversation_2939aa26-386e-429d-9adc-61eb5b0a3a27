.payment_code_style{
    margin-top: 20px;
    background-color:$cp-white;
    width: 100%;
    border-radius:8px;
    font-weight:bold;
    font-size:35px;
    display: table-cell;
    vertical-align: middle;
}
.border {
    border:1px solid black;
}

.five{
    width:500px;
    background-color: $cp-primary;
    height:100%;
}

.font{
    color:$cp-white;
    text-align:center;
    margin-left:10px;
 }
    
::placeholder{
    color: rgb(0, 0, 0);
   opacity: 1;
   text-align:center;
   font-size: 30px;
   font-weight: bold;
 }
.style5{
    font-size:220px;
    margin-top:20px;
}
.dot {
    height: 180px;
    width: 180px;
    border-radius: 50%;
    background-color: $cp-primary;
    display: inline-block;
    margin-top:50px;
  }

  i.fa1{
      font-size: 100px;
      margin-top:35px;
      color: $cp-white;
  }

  i.fa2{
    float:right;
    color: $cp-white;
}

.modal-backdrop {
    background: rgba(0, 0, 0, 0.3); 
}

.color{
    background-color: $cp-white;
}

.img-style{
    height:50px;
    width:100px;
}

.btn-close{
    height:40px;
    width:100%;
}

.popup{
    color: #666666;
}

.usernamelabel {
    font-family: $cp-font; 
    font-size: 24px; 
    font-style: normal; 
    font-variant: normal;  
    line-height: 34.4px;
    color: $cp-white;
    margin-top: 10px;
    margin-top: 10px;
    
}

.nopaymentpin-box-title {
    font-family: $cp-font; 
    font-size: 11px; 
    font-style: normal; 
    font-variant: normal;  
    line-height: 22.4px;
    color: black;
}

.payLabel {
    font-family: $cp-font; 
    font-size: 30px; 
    font-style: normal; 
    font-variant: normal;  
    line-height: 32.4px;
    color: $cp-white;
    margin-top: 10px;
}

.menuicon-header-style {
    color: $cp-black; 
    margin-top: 30px;
    align-self: center;
    margin-left: 10px;
    background-color: $cp-white !important;
    font-size: 25px;
}
.purchasepower-def-label {
    font-family: $cp-font;
    font-size: 20px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-black;
    src: local('Open Sans Semibold'), local('OpenSans-Semibold'), url(http://fonts.gstatic.com/s/opensans/v13/MTP_ySUJH_bn48VBG8sNShampu5_7CjHW5spxoeN3Vs.woff2) format('woff2');


}

.forgetpassword-ok-label {
    font-family: $cp-font;
    font-size: 13px;
    font-weight: 400;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-white;
    src: local('Open Sans Semibold'), local('OpenSans-Semibold'), url(http://fonts.gstatic.com/s/opensans/v13/MTP_ySUJH_bn48VBG8sNShampu5_7CjHW5spxoeN3Vs.woff2) format('woff2');


}

.purchasepower-modal-text {
    font-family: $cp-font;
    font-size: 15px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 19.4px;
    letter-spacing: normal;
    text-align: center;
    color: #666666;
    margin: 10px;
  }
  .purchaserpower-modal-text{
    background-color: $cp-white;
    width:100%;
  }
  .purchasepower-modal-ok-label {
    font-family: $cp-font-secondary;
    font-size: 15px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 19.4px;
    letter-spacing: normal;
    text-align: center;
    color: $cp-white;
}
.cancel-modal-label {
    font-family: $cp-font-secondary;
    font-size: 15px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 19.4px;
    letter-spacing: normal;
    text-align: center;
    color: #000000;
}
.leave-a-tip-label {
    font-family: $cp-font-secondary;
    font-size: 15px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: 19.4px;
    letter-spacing: normal;
    text-align: center;
    color: $cp-white;
}
.button-paynow-title {
    font-family: $cp-font-secondary;
    font-size: 18px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 20.4px;
    letter-spacing: normal;
    text-align: center;
    color: $cp-white;
    font-weight: 600;
}
.justify-text {
    text-align: justify;
}
.left-align {
    text-align: left;
}
.card-section {
    background-color: gainsboro;
    margin-top: 30px;
  }
  .card-digit {
    height: 150px;
  }
  .card-qr-code {
    height: 250px;
    align-items: center;
  }
  .text-upper {
    font-size: 20px;
    margin-top: 25px;
  }
  .digit {
    font-size: 40px;
  }


  .copy-label {
    left: 30rem !important;
    margin-top: 34px!important;
    cursor: -webkit-grab; cursor: grab;
  }
@media only screen and ( min-height:500px ) and ( max-height:599px ){
    .section-purchasepower {
        margin-top: unset !important;
    }  
}
@media only screen and ( min-width:270px) and ( max-width:700px) {
    .row-gap {
        margin-top: 12px;
    } 
    .user-block {
        height: 80px;
        width: 80px;
        margin-right: 15px;
        border-radius: 50%;
        background-color: $cp-secondary !important;
        border-color:$cp-secondary !important;    display: inline-block;
        border:2px solid #fff;
        margin-top:20px;
    }
    .imgframe {
        display: inline-block;
        margin-top: 20px;
    }
    .usernamelabel {
        font-family: $cp-font; 
        font-size: 23px; 
        font-style: normal; 
        font-variant: normal;  
        line-height: 34.4px;
        color: $cp-white;
        margin-top: 10px;
        margin-top: 10px;
        
    }
    .purchasepower-box-title {
        font-family: $cp-font; 
        font-size: 15px; 
        font-style: normal; 
        font-variant: normal;  
        line-height: 22.4px;
        color: $cp-white;
        margin-top: 20px;
    }
    .nopurchasepower-box-title {
        font-family: $cp-font; 
        font-size: 11px; 
        font-style: normal; 
        font-variant: normal;  
        line-height: 22.4px;
        color: $cp-white;
    }
    .svg-btn-div{
        float: right;
    }
    .svg-icon{
        height: 21px;
        margin-top: 40px;
    }
    .svg-icon-for_text{
        height: 18px;
        // margin-top: -61px;      
    }
    .btn-gap{
        margin-top: 30px;
    }
    .btn-pay{
        height: 57px;
    }
    .qrcode_style {
        width:50%;
        background-color:$cp-white;
        border-radius:8px; 
        height: 170px;
    }
    .payment-code-style {
        width:60%;
        height: 50px;
        background-color:$cp-white;
        border-radius:8px;
        text-align: center;
    }
    .padding-left-right{
        padding-left: 52px;
        padding-right: 52px;
        margin-top: -8px;
    }
    .payment-pin-label {
        background-color:$cp-white;
        border-radius:8px; 
        font-family: $cp-font-secondary;
        font-size: 20px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 26.4px;
        letter-spacing: normal;
        text-align: center;
        color: #000000;
        font-weight: 600;
        margin-top: 12px;
        font-weight: 700;
    }
    .qr-code-gap{
        margin-top: 20px;
    }
    .qr-code {
        height: 120px;
        width: 120px;
        margin: 10px;
    }
    .paymentcode-boxl-title {
        font-family: $cp-font;
        font-size: 15px;
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: center;
        line-height: 19.4px;
        color: $cp-white;
        src: local('Open Sans Semibold'), local('OpenSans-Semibold'), url(http://fonts.gstatic.com/s/opensans/v13/MTP_ySUJH_bn48VBG8sNShampu5_7CjHW5spxoeN3Vs.woff2) format('woff2');
        height: 30px;
}

    .copy-label {
        left: 8.35rem !important;
        margin-top: 34px !important;
      }
      .payment-pin-copy-label {
        background-color: $cp-primary;
        border-radius:8px; 
        font-family: $cp-font-secondary;
        font-size: 20px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 26.4px;
        letter-spacing: normal;
        text-align: center;
        color: $cp-white;
        font-weight: 600;
        margin-top: 12px;
    }
}
@media only screen and ( min-width:350px) and ( max-width:800px) {
    .row-gap {
        margin-top: 12px;
    } 
    .user-block {
        height: 80px;
        width: 80px;
        margin-right: 15px;
        border-radius: 50%;
        background-color: $cp-secondary !important;
        border-color:$cp-secondary !important;    display: inline-block;
        border:2px solid #fff;
        margin-top:20px;
    }
    .imgframe {
        display: inline-block;
        margin-top: 20px;
    }
    .usernamelabel {
        font-family: $cp-font; 
        font-size: 23px; 
        font-style: normal; 
        font-variant: normal;  
        line-height: 34.4px;
        color: $cp-white;
        margin-top: 10px;
        margin-top: 10px;
        
    }
    .purchasepower-box-title {
        font-family: $cp-font; 
        font-size: 15px; 
        font-style: normal; 
        font-variant: normal;  
        line-height: 22.4px;
        color: $cp-white;
        margin-top: 20px;
    }
    .nopurchasepower-box-title {
        font-family: $cp-font; 
        font-size: 11px; 
        font-style: normal; 
        font-variant: normal;  
        line-height: 22.4px;
        color: $cp-white;
    }
    .svg-btn-div{
        float: right;
    }
    .svg-icon{
        height: 21px;
        margin-top: 40px;
    }
    .svg-icon-for_text{
        height: 18px;
        // margin-top: -61px;      
    }
    .btn-gap{
        margin-top: 30px;
    }
    .btn-pay{
        height: 57px;
    }
    .qrcode_style {
        width:50%;
        background-color:$cp-white;
        border-radius:8px; 
        height: 170px;
    }
    .payment-code-style {
        width:60%;
        height: 50px;
        background-color:$cp-white;
        border-radius:8px;
        text-align: center;
    }
    .padding-left-right{
        padding-left: 52px;
        padding-right: 52px;
        margin-top: -8px;
    }
    .payment-pin-label {
        background-color:$cp-white;
        border-radius:8px; 
        font-family: $cp-font-secondary;
        font-size: 20px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 26.4px;
        letter-spacing: normal;
        text-align: center;
        color: #000000;
        font-weight: 600;
        margin-top: 12px;
        font-weight: 700;
    }
    .qr-code-gap{
        margin-top: 20px;
    }
    .qr-code {
        height: 150px;
        width: 150px;
        margin: 10px;
    }
    .paymentcode-boxl-title {
        font-family: $cp-font;
        font-size: 15px;
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: center;
        line-height: 19.4px;
        color: $cp-white;
        src: local('Open Sans Semibold'), local('OpenSans-Semibold'), url(http://fonts.gstatic.com/s/opensans/v13/MTP_ySUJH_bn48VBG8sNShampu5_7CjHW5spxoeN3Vs.woff2) format('woff2');
        height: 30px;
}

    .copy-label {
        left: 8.35rem !important;
        margin-top: 34px !important;
      }
      .payment-pin-copy-label {
        background-color: $cp-primary;
        border-radius:8px; 
        font-family: $cp-font-secondary;
        font-size: 20px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 26.4px;
        letter-spacing: normal;
        text-align: center;
        color: $cp-white;
        font-weight: 600;
        margin-top: 12px;
    }
}
@media only screen and ( min-width:360px ) and ( max-width:900px ){
    .row-gap {
        margin-top: 40px;
    } 
    .user-block {
        height: 90px;
        width: 90px;
        margin-right: 15px;
        border-radius: 50%;
        background-color: $cp-secondary !important;
        border-color:$cp-secondary !important;    display: inline-block;
        border:2px solid #fff;
        margin-top:20px;
    }
    .imgframe {
        display: inline-block;
        margin-top: 40px;
    }
    .usernamelabel {
        font-family: $cp-font; 
        font-size: 24px; 
        font-style: normal; 
        font-variant: normal;  
        line-height: 34.4px;
        color: $cp-white;
        margin-top: 10px;
        
    }
    .purchasepower-box-title {
        font-family: $cp-font; 
        font-size: 17px; 
        font-style: normal; 
        font-variant: normal;  
        line-height: 22.4px;
        color: $cp-white;
        margin-top: 30px;
    }
    .nopurchasepower-box-title {
        font-family: $cp-font; 
        font-size: 11px; 
        font-style: normal; 
        font-variant: normal;  
        line-height: 22.4px;
        color: $cp-white;
    }
    .svg-btn-div{
        float: right;
    }
    .svg-icon{
        height: 24px;
        margin-top: 52px;
    }
    .svg-icon-for_text{
        height: 23px;
        margin-top: -38px;    
    }
    .btn-gap{
        margin-top: 30px;
    }
    .btn-pay{
        height: 57px;
    }
    .qrcode_style {
        width:50%;
        background-color:$cp-white;
        border-radius:8px; 
        height: 202px;
    }
    .payment-code-style {
        width:60%;
        height: 50px;
        background-color:$cp-white;
        border-radius:8px;
        text-align: center;
    }
    .padding-left-right{
        padding-left: 52px;
        padding-right: 52px;
        margin-top: -8px;
    }
    .payment-pin-label {
        background-color:$cp-white;
        border-radius:8px; 
        font-family: $cp-font-secondary;
        font-size: 20px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 26.4px;
        letter-spacing: normal;
        text-align: center;
        color: #000000;
        font-weight: 600;
        margin-top: 12px;
        font-weight: 700;
    }
    .qr-code-gap{
        margin-top: 20px;
    }
    .qr-code {
        height: 155px;
        width: 155px;
        margin: 10px;
    }
    .paymentcode-boxl-title {
        font-family: $cp-font;
        font-size: 17px;
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: center;
        line-height: 19.4px;
        color: $cp-white;
        src: local('Open Sans Semibold'), local('OpenSans-Semibold'), url(http://fonts.gstatic.com/s/opensans/v13/MTP_ySUJH_bn48VBG8sNShampu5_7CjHW5spxoeN3Vs.woff2) format('woff2');
        height: 30px;
}

    .copy-label {
        left: 8.35rem !important;
        margin-top: 27px !important;
      }

      .payment-pin-copy-label {
        background-color: $cp-primary;
        border-radius:8px; 
        font-family: $cp-font-secondary;
        font-size: 20px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 26.4px;
        letter-spacing: normal;
        text-align: center;
        color: $cp-white;
        font-weight: 600;
        margin-top: 12px;
    }
}
@media only screen and ( min-width:411px ) and (max-width:414px ){
    .svg-icon-for_text {
        height: 23px;
        margin-top: 8px;
    }
}
@media only screen and ( min-width:768px ) and (max-width:780px ){
    .svg-icon-for_text {
        height: 23px;
        margin-top: 5px;
    }
}
@media only screen and ( min-width:800px ) and ( max-width:1300px ){
    .row-gap {
        margin-top: 50px;
    } 
    .user-block {
        height: 180px;
        width: 180px;
        margin-right: 15px;
        border-radius: 50%;
        background-color: $cp-secondary !important;
        border-color:$cp-secondary !important;    display: inline-block;
        border:2px solid #fff;
        margin-top:20px;
    }
    .imgframe {
        display: inline-block;
        margin-top: 55px;
        height: 60px;
    }
    .usernamelabel {
        font-family: $cp-font; 
        font-size: 37px; 
        font-style: normal; 
        font-variant: normal;  
        line-height: 34.4px;
        color: $cp-white;
        margin-top: 10px;
        margin-top: 10px;
        
    }
    .purchasepower-box-title {
        font-family: $cp-font; 
        font-size: 25px; 
        font-style: normal; 
        font-variant: normal;  
        line-height: 22.4px;
        color: $cp-white;
        margin-top: 20px;
    }
    .nopurchasepower-box-title {
        font-family: $cp-font; 
        font-size: 20px; 
        font-style: normal; 
        font-variant: normal;  
        line-height: 22.4px;
        color: $cp-white;
    }
    .svg-btn-div{
        float: right;
    }
    .svg-icon{
        height: 50px;
        margin-right: 15px;
        margin-top: 90px;
    }
    .svg-icon-for_text{
        height: 50px;
        margin-right: 25px;
        margin-top: 45px;
    }
    .btn-gap{
        margin-top: 70px;
    }
    .btn-pay{
        height: 57px;
    }
    .qrcode_style {
        width:50%;
        background-color:$cp-white;
        border-radius:10px; 
        height: 350px;
    }
    .payment-code-style {
        width:80%;
        height: 80px;
        background-color:$cp-white;
        border-radius:10px;
        text-align: center;
    }
    .padding-left-right{
        padding-left: 100px;
        padding-right: 100px;
        margin-top: 10px;
    }
    .payment-pin-label {
        background-color:$cp-white;
        border-radius:8px; 
        font-family: $cp-font-secondary;
        font-size: 35px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 26.4px;
        letter-spacing: normal;
        text-align: center;
        color: #000000;
        font-weight: 600;
        margin-top: 30px;
        font-weight: 700;
    }
    .qr-code-gap{
        margin-top: 40px;
    }
    .qr-code {
        height: 250px;
        width: 250px;
        margin: 40px;
    }
    .paymentcode-boxl-title {
        font-family: $cp-font;
        font-size: 25px;
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: center;
        line-height: 19.4px;
        color: $cp-white;
        src: local('Open Sans Semibold'), local('OpenSans-Semibold'), url(http://fonts.gstatic.com/s/opensans/v13/MTP_ySUJH_bn48VBG8sNShampu5_7CjHW5spxoeN3Vs.woff2) format('woff2');
        height: 30px;
        margin-top: 40px;
}

    .copy-label {
        left: 28rem!important;
        margin-top: 45px!important;
      }

      .payment-pin-copy-label  {
        background-color: $cp-primary;
        border-radius:8px; 
        font-family: $cp-font-secondary;
        font-size: 35px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 26.4px;
        letter-spacing: normal;
        text-align: center;
        color: $cp-white;
        font-weight: 600;
        margin-top: 30px;
    }
}
@media screen and (min-width: 1200px) {
    .row-gap {
        margin-top: 50px;
    } 
    .user-block {
        height: 100px;
        width: 100px;
        margin-right: 15px;
        border-radius: 50%;
        background-color: $cp-secondary !important;
        border-color:$cp-secondary !important;    display: inline-block;
        border:2px solid #fff;
        margin-top:20px;
    }
    .imgframe {
        display: inline-block;
        margin-top: 40px;
    }
    .usernamelabel {
        font-family: $cp-font; 
        font-size: 27px; 
        font-style: normal; 
        font-variant: normal;  
        line-height: 34.4px;
        color: $cp-white;
        margin-top: 10px;
        
    }
    .purchasepower-box-title {
        font-family: $cp-font; 
        font-size: 17px; 
        font-style: normal; 
        font-variant: normal;  
        line-height: 22.4px;
        color: $cp-white;
        margin-top: 30px;
    }
    .nopurchasepower-box-title {
        font-family: $cp-font; 
        font-size: 11px; 
        font-style: normal; 
        font-variant: normal;  
        line-height: 22.4px;
        color: $cp-white;
    }
    .svg-btn-div{
        float: right;
    }
    .svg-icon{
        height: 30px;
        margin-top: 50px;
    }
    .svg-icon-for_text{
        height: 30px;
        margin-top: 51px;
    }
    .btn-gap{
        margin-top: 30px;
    }
    .btn-pay{
        height: 57px;
    }
    .qrcode_style {
        width:50%;
        background-color:$cp-white;
        border-radius:10px; 
        height: 200px;
    }
    .payment-code-style {
        width:60%;
        height: 60px;
        background-color:$cp-white;
        border-radius:10px;
        text-align: center;
    }
    .padding-left-right{
        padding-left: 80px;
        padding-right: 80px;
        margin-top: 10px;
    }
    .payment-pin-label {
        background-color:$cp-white;
        border-radius:8px; 
        font-family: $cp-font-secondary;
        font-size: 30px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 26.4px;
        letter-spacing: normal;
        text-align: center;
        color: #000000;
        font-weight: 600;
        margin-top: 20px;
        font-weight: 700;
    }
    .qr-code-gap{
        margin-top: 20px;
    }
    .qr-code {
        height: 150px;
        width: 150px;
        margin: 20px;
    }
    .paymentcode-boxl-title {
        font-family: $cp-font;
        font-size: 20px;
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: center;
        line-height: 19.4px;
        color: $cp-white;
        src: local('Open Sans Semibold'), local('OpenSans-Semibold'), url(http://fonts.gstatic.com/s/opensans/v13/MTP_ySUJH_bn48VBG8sNShampu5_7CjHW5spxoeN3Vs.woff2) format('woff2');
        height: 30px;
}

    .copy-label {
        left: 30rem !important;
        margin-top: 34px!important;
      }

      .payment-pin-copy-label {
        background-color: $cp-primary;
        border-radius:8px; 
        font-family: $cp-font-secondary;
        font-size: 30px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 26.4px;
        letter-spacing: normal;
        text-align: center;
        color: $cp-white;
        font-weight: 600;
        margin-top: 20px;
    }
  }

  .btn-align {
    text-align: right;
  }
  .payment-title {
    float: left;
    margin-left: -15px;
  }
  #payment-code-expired___BV_modal_content_ {
    border-radius: 10px;
    margin: 10px;
    background-color: $cp-white;
  }
  
  #payment-code-expired___BV_modal_body_ {
    border-radius: 10px;
    margin: 10px;
    background-color: $cp-white;
  }
  #purchase-modal___BV_modal_content_ {
    border-radius: 10px;
    margin: 10px;
    background-color: $cp-white;
  }
  
  #purchase-modal___BV_modal_body_ {
    border-radius: 10px;
    margin: 10px;
    background-color: $cp-white;
  }

  #transaction-modal___BV_modal_body_ {
    border-radius: 10px;
    margin: 10px;
    background-color: $cp-white;
  }
  #show-more-modal___BV_modal_body_ {
    border-radius: 10px;
    margin: 10px;
    background-color: $cp-white;
  }
  #transaction-modal___BV_modal_body_ {
    border-radius: 10px;
    margin: 10px;
    background-color: $cp-white;
  }
      .purchase_power_style {
        margin: auto;
        background-color: $cp-secondary !important;
        border-color:$cp-secondary !important;
        border-radius:8px;
    }

    // .purchasepower-box-amount {
    //     font-family: 'Montserrat';
    //     font-style: Regular; 
    //     font-variant: Regular;  
    //     // line-height: 38.4px;
    //     color: $cp-white;
    //     font-size: 40px;
    // }    