<template>
<div>
    <div v-if="isLoading">
        <CanPayLoader/>
    </div>
    <div v-else :style="petitions.length != 0 ? 'position:absolute;width:100%;z-index:7' : ''">
        <div v-if="petitions.length == 0" class="mt-4 pb-3" style="display:flex;align-items:center;flex-direction:column;background-color:#ECECEC">
            <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-700 canpay-crew-text-font-21 pt-3">No petition found</p>
        </div>
        <div v-else class=" pb-3 canpay-crew-successful-petition-display">
            <div class="mt-4 container">
            <div class="row">
                <div class="col-8 text-left canpay-crew-padding-right-none canpay-crew-text-font-18">
                    {{ totalUnclaimedRewards()<=1?"Lifetime Crew Spin:":"Lifetime Crew Spins"}} <b>{{ totalUnclaimedRewards() }}</b>
                </div>
                <div class="col-4 canpay-crew-padding-right-none" style="position:relative;">
                    <div class="canpay-crew-free-spin-style">
                    <span>
                    <p style="margin-bottom:0px!important;" class="canpay-crew-text-font-12"><b style="position:relative;top:4px">CREW</b></p>
                    <p style="margin-bottom:0px!important;" class="canpay-crew-text-font-12"><b style="position:relative;bottom:2px;">SPIN</b></p>
                    </span>
                    <div
                        style="
                            position: absolute;
                            top: -19px;
                            left: 0px;
                        "
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="38" height="77" viewBox="0 0 77 77" fill="none">
                        <path d="M38.2102 38.6169C36.2901 40.5221 34.3848 42.4274 32.4647 44.3326C25.6625 51.1348 18.8454 57.9371 12.0431 64.7542C11.8198 64.9774 11.5817 65.1858 11.2542 65.4835C10.2272 64.2928 9.18528 63.1913 8.26244 62.0005C3.51427 55.9425 0.835048 49.0808 0.224781 41.4003C0.195012 41.1622 0.150358 40.924 0.0908199 40.6859V38.4532C12.8022 38.4532 25.4988 38.4532 38.2102 38.4383V38.6169Z" fill="black"/>
                        <path d="M76.3142 38.4539C63.6624 38.4539 50.9956 38.4539 38.3437 38.439C38.2991 38.439 38.2395 38.4539 38.1949 38.4539C38.3884 38.2306 38.567 37.9925 38.7754 37.7841C47.334 29.2255 55.8926 20.6669 64.4512 12.1082C64.6447 11.9147 64.8382 11.7361 65.1359 11.4682C65.9992 12.4506 66.8774 13.3437 67.6663 14.326C72.3847 20.131 75.1979 26.7398 76.0314 34.182C76.1207 34.9709 76.2249 35.7449 76.3142 36.5189V38.4539Z" fill="black"/>
                        <path d="M65.1056 11.4224C56.16 20.3681 47.1995 29.3137 38.2539 38.2593C38.2241 38.2891 38.1943 38.3337 38.1646 38.3784C38.1348 38.0807 38.0902 37.783 38.0902 37.4853C38.0902 25.3841 38.0902 13.283 38.0902 1.18187C38.0902 0.913945 38.105 0.646024 38.1199 0.244141C39.4298 0.318563 40.6801 0.348332 41.9304 0.467409C49.3726 1.22652 56.026 3.92063 61.8757 8.59437C62.4859 9.08557 63.1111 9.56187 63.7362 10.0531L65.1056 11.4224Z" fill="#0DD668"/>
                        <path d="M11.1783 11.4233C20.1239 20.3689 29.0695 29.3294 38.0152 38.275C38.0449 38.3048 38.0896 38.3346 38.1342 38.3643C37.8365 38.3941 37.5388 38.4387 37.2412 38.4387C25.14 38.4387 13.0389 38.4387 0.937727 38.4387C0.669805 38.4387 0.401883 38.4239 0 38.409C0.0744228 37.0991 0.104192 35.8488 0.223268 34.5985C0.982381 27.1563 3.67649 20.5029 8.35023 14.6532C8.84143 14.043 9.31773 13.4178 9.80892 12.7927L11.1783 11.4233Z" fill="#D3FCC8"/>
                        <path d="M11.2543 65.5558C20.2 56.6102 29.1605 47.6646 38.1061 38.719C38.1358 38.6892 38.1656 38.6446 38.1954 38.5999C38.2252 38.8976 38.2698 39.1953 38.2698 39.493C38.2698 51.5941 38.2698 63.6953 38.2698 75.7964C38.2698 76.0643 38.2549 76.3323 38.24 76.7341C36.9302 76.6597 35.6799 76.6299 34.4296 76.5109C26.9873 75.7518 20.3339 73.0577 14.4843 68.3839C13.874 67.8927 13.2489 67.4164 12.6237 66.9252L11.2543 65.5558Z" fill="#0DD668"/>
                        <path d="M65.1647 65.452C56.2191 56.5064 47.2735 47.5459 38.3428 38.5854C38.313 38.5556 38.2683 38.5259 38.2237 38.4961C38.5214 38.4663 38.8191 38.4217 39.1168 38.4217C51.2179 38.4217 63.3191 38.4217 75.4202 38.4366C75.6881 38.4366 75.956 38.4515 76.3579 38.4663C76.2835 39.7762 76.2537 41.0265 76.1347 42.2768C75.3755 49.7191 72.6814 56.3725 68.0077 62.2221C67.5165 62.8324 67.0402 63.4575 66.549 64.0827L65.1647 65.452Z" fill="#D3FCC8"/>
                        <path d="M38.0752 0.257812C38.0752 12.9097 38.0752 25.5764 38.0604 38.2283C38.0604 38.2729 38.0752 38.3325 38.0752 38.3771C37.852 38.1836 37.6138 38.005 37.4054 37.7966C28.8468 29.238 20.2882 20.6794 11.7296 12.1208C11.5361 11.9273 11.3575 11.7338 11.0895 11.4361C12.0719 10.5728 12.965 9.69462 13.9474 8.90574C19.7524 4.18733 26.3611 1.37415 33.8034 0.540619C34.5923 0.451312 35.3663 0.34712 36.1402 0.257812H38.0752Z" fill="#007EE5"/>
                        <path d="M38.2095 76.5563C38.1946 76.3032 38.1798 76.0651 38.1798 75.812C38.1798 63.6216 38.1798 51.4311 38.1798 39.2556C38.1798 39.0323 38.1946 38.809 38.2095 38.5858C38.3732 38.7197 38.5667 38.8537 38.7156 39.0025C47.3486 47.6207 55.9668 56.2537 64.585 64.8719C64.7189 65.0058 64.8529 65.1547 65.0315 65.3631C64.8826 65.5268 64.7487 65.7054 64.5998 65.8543C58.9586 71.2425 52.3201 74.6361 44.6248 75.9311C42.9875 76.199 41.3353 76.3479 39.698 76.5563H38.2095Z" fill="#29576C"/>
                        </svg>
                    </div>
                    <div class="canpay-crew-green-spin">
                    x{{ totalUnclaimedRewards() }}
                    </div>
                    </div>
                    <div></div>
                </div>
            </div>
            </div>
                <div class="container">
                    <div :class="index==0?'canpay-crew-petition-box-1 canpay-crew-petition-padding mt-4':'canpay-crew-petition-box-1 canpay-crew-petition-padding mt-3' " v-for="(item, index) in petitions" :key="index" :style="item.type=='mayor'?'border-left:3px solid #FFD700':item.type=='crew leader'?'border-left:3px solid #C0C0C0;':''">
                    <div v-on:click="gotoDetailsPage(item)" class="canpay-crew-margin-for-successfull-petition">
                    <div class="row ">
                    <div class="col-9">
                        <div style="display:flex;">
                            <div>
                            <svg xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 104 104" fill="none">
                            <circle cx="52" cy="52" r="52" fill="#179346"/>
                            <path d="M59.1975 55.2093C58.1131 55.2205 57.0398 54.9913 56.0546 54.5381C55.0695 54.085 54.197 53.4191 53.5 52.5884C52.7978 53.413 51.9243 54.0747 50.9403 54.5274C49.9563 54.98 48.8855 55.2127 47.8025 55.2093C46.7181 55.2205 45.6448 54.9913 44.6596 54.5381C43.6745 54.085 42.802 53.4191 42.105 52.5884C41.8948 52.8405 41.6663 53.0766 41.4213 53.2949C40.654 53.993 39.7475 54.5205 38.7615 54.8426C37.7755 55.1648 36.7324 55.2742 35.701 55.1637C33.8342 54.9337 32.1181 54.0226 30.8816 52.6053C29.6451 51.1879 28.9753 49.364 29.0007 47.4833V45.8651C28.9988 44.7036 29.1835 43.5494 29.5477 42.4465L33.1257 31.7349C33.389 30.9394 33.8963 30.247 34.5753 29.7561C35.2544 29.2652 36.0709 29.0006 36.9088 29H70.0911C70.929 29.0006 71.7455 29.2652 72.4246 29.7561C73.1037 30.247 73.6109 30.9394 73.8742 31.7349L77.4523 42.4465C77.8164 43.5494 78.0011 44.7036 77.9992 45.8651V47.4833C78.0259 49.3571 77.3625 51.1753 76.1353 52.5916C74.9082 54.0079 73.203 54.9234 71.3445 55.1637C70.313 55.2772 69.2691 55.1691 68.2826 54.8469C67.2962 54.5246 66.3899 53.9955 65.6243 53.2949C65.3736 53.067 65.1229 52.7935 64.895 52.5428C64.1961 53.3746 63.3243 54.0441 62.3404 54.5046C61.3564 54.9651 60.2839 55.2056 59.1975 55.2093ZM36.9088 32.4186C36.7892 32.4204 36.6729 32.4584 36.5753 32.5276C36.4777 32.5967 36.4033 32.6938 36.3619 32.806L32.8066 43.5177C32.5597 44.2526 32.429 45.0215 32.4192 45.7967V47.4149C32.3858 48.4437 32.7319 49.449 33.3917 50.2391C34.0515 51.0292 34.9789 51.549 35.9972 51.6995C36.5468 51.7564 37.1022 51.6986 37.6282 51.5295C38.1542 51.3605 38.6394 51.084 39.0529 50.7176C39.4664 50.3512 39.7993 49.9028 40.0305 49.401C40.2616 48.8992 40.386 48.3548 40.3957 47.8023C40.3957 47.349 40.5758 46.9142 40.8963 46.5937C41.2169 46.2731 41.6516 46.093 42.105 46.093C42.5583 46.093 42.993 46.2731 43.3136 46.5937C43.6341 46.9142 43.8142 47.349 43.8142 47.8023C43.7881 48.333 43.8734 48.8632 44.0646 49.3589C44.2558 49.8546 44.5487 50.3048 44.9243 50.6805C45.3 51.0562 45.7502 51.3491 46.2459 51.5403C46.7416 51.7315 47.2718 51.8168 47.8025 51.7907C48.8584 51.7847 49.8693 51.3626 50.616 50.6159C51.3626 49.8692 51.7847 48.8583 51.7907 47.8023C51.7907 47.349 51.9708 46.9142 52.2913 46.5937C52.6119 46.2731 53.0466 46.093 53.5 46.093C53.9533 46.093 54.388 46.2731 54.7086 46.5937C55.0291 46.9142 55.2092 47.349 55.2092 47.8023C55.1831 48.333 55.2684 48.8632 55.4596 49.3589C55.6508 49.8546 55.9437 50.3048 56.3194 50.6805C56.695 51.0562 57.1452 51.3491 57.6409 51.5403C58.1366 51.7315 58.6668 51.8168 59.1975 51.7907C60.2534 51.7847 61.2643 51.3626 62.011 50.6159C62.7576 49.8692 63.1797 48.8583 63.1857 47.8023C63.1857 47.349 63.3658 46.9142 63.6863 46.5937C64.0069 46.2731 64.4416 46.093 64.895 46.093C65.3483 46.093 65.7831 46.2731 66.1036 46.5937C66.4241 46.9142 66.6042 47.349 66.6042 47.8023C66.6045 48.3605 66.7219 48.9125 66.9488 49.4225C67.1758 49.9325 67.5073 50.3891 67.9219 50.7629C68.3365 51.1367 68.825 51.4193 69.3556 51.5924C69.8863 51.7655 70.4474 51.8253 71.0027 51.7679C72.021 51.6174 72.9484 51.0976 73.6082 50.3075C74.268 49.5173 74.6142 48.5121 74.5807 47.4833V45.8651C74.5709 45.0899 74.4402 44.321 74.1933 43.586L70.6381 32.8744C70.5967 32.7622 70.5222 32.6651 70.4246 32.5959C70.327 32.5268 70.2107 32.4888 70.0911 32.487L36.9088 32.4186Z" fill="white"/>
                            <path d="M71.732 78H35.2679C34.212 77.994 33.2011 77.5719 32.4544 76.8252C31.7078 76.0786 31.2857 75.0676 31.2797 74.0116V52.3149C31.2797 51.8615 31.4598 51.4268 31.7803 51.1062C32.1009 50.7857 32.5356 50.6056 32.9889 50.6056C33.4423 50.6056 33.877 50.7857 34.1976 51.1062C34.5181 51.4268 34.6982 51.8615 34.6982 52.3149V74.0116C34.6982 74.1627 34.7582 74.3077 34.8651 74.4145C34.9719 74.5214 35.1168 74.5814 35.2679 74.5814H71.732C71.8831 74.5814 72.028 74.5214 72.1348 74.4145C72.2417 74.3077 72.3017 74.1627 72.3017 74.0116V52.2921C72.3017 51.8388 72.4818 51.404 72.8023 51.0834C73.1229 50.7629 73.5577 50.5828 74.011 50.5828C74.4643 50.5828 74.8991 50.7629 75.2196 51.0834C75.5401 51.404 75.7202 51.8388 75.7202 52.2921V74.0116C75.7142 75.0676 75.2921 76.0786 74.5455 76.8252C73.7988 77.5719 72.7879 77.994 71.732 78Z" fill="white"/>
                            <path d="M61.4765 78H45.5235C45.072 77.9941 44.6406 77.8121 44.3214 77.4928C44.0021 77.1735 43.8201 76.7422 43.8142 76.2907V64.8953C43.8202 63.8394 44.2423 62.8284 44.9889 62.0818C45.7356 61.3351 46.7466 60.913 47.8025 60.907H59.1975C60.2534 60.913 61.2643 61.3351 62.011 62.0818C62.7576 62.8284 63.1797 63.8394 63.1857 64.8953V76.2907C63.1798 76.7422 62.9978 77.1735 62.6786 77.4928C62.3593 77.8121 61.928 77.9941 61.4765 78ZM47.2327 74.5814H59.7672V64.8953C59.7672 64.7442 59.7072 64.5993 59.6003 64.4925C59.4935 64.3856 59.3486 64.3256 59.1975 64.3256H47.8025C47.6513 64.3256 47.5064 64.3856 47.3996 64.4925C47.2927 64.5993 47.2327 64.7442 47.2327 64.8953V74.5814Z" fill="white"/>
                            </svg>
                            </div>
                            <div class=" canpay-crew-padding-right-none" style="word-break:break-all;margin-left:10px;">
                                <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-bold text-left">
                                    <span style="cursor: pointer;" >{{item.store_name}} - {{ item.city }}</span>
                                </p>
                                <p class="canpay-crew-p-margin-bottom-1 text-left canpay-crew-text-font-13" style="margin-top:10px;">Lifetime extra spin: <b>{{item.total_reward_count}}</b></p>
                                <p class="canpay-crew-p-margin-bottom-1 text-left canpay-crew-text-font-13">Total Points Won: <b>{{pointNumberFormatter(item.total_reward_points)}}</b></p>
                            </div>
                        </div>  
                    </div>
                        <div class="col-3 text-right canpay-crew-padding-left-none canpay-crew-padding-right-none">
                    <div class="canpay-crew-small-free-spin">
                    <span style="">
                    <p style="margin-bottom:0px!important;text-align:center" class="canpay-crew-text-font-12;"><b style="position: relative;top: 4px;">CREW</b></p>
                    <p style="margin-bottom:0px!important;text-align:center" class="canpay-crew-text-font-12;"><b style="position:relative;bottom:4px;">SPIN</b></p>
                    </span>
                    <div
                        style="
                            position: absolute;
                            top: -14px;
                            left: 0px;
                        "
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="50" height="77" viewBox="0 0 77 77" fill="none">
                        <path d="M38.2102 38.6169C36.2901 40.5221 34.3848 42.4274 32.4647 44.3326C25.6625 51.1348 18.8454 57.9371 12.0431 64.7542C11.8198 64.9774 11.5817 65.1858 11.2542 65.4835C10.2272 64.2928 9.18528 63.1913 8.26244 62.0005C3.51427 55.9425 0.835048 49.0808 0.224781 41.4003C0.195012 41.1622 0.150358 40.924 0.0908199 40.6859V38.4532C12.8022 38.4532 25.4988 38.4532 38.2102 38.4383V38.6169Z" fill="black"/>
                        <path d="M76.3142 38.4539C63.6624 38.4539 50.9956 38.4539 38.3437 38.439C38.2991 38.439 38.2395 38.4539 38.1949 38.4539C38.3884 38.2306 38.567 37.9925 38.7754 37.7841C47.334 29.2255 55.8926 20.6669 64.4512 12.1082C64.6447 11.9147 64.8382 11.7361 65.1359 11.4682C65.9992 12.4506 66.8774 13.3437 67.6663 14.326C72.3847 20.131 75.1979 26.7398 76.0314 34.182C76.1207 34.9709 76.2249 35.7449 76.3142 36.5189V38.4539Z" fill="black"/>
                        <path d="M65.1056 11.4224C56.16 20.3681 47.1995 29.3137 38.2539 38.2593C38.2241 38.2891 38.1943 38.3337 38.1646 38.3784C38.1348 38.0807 38.0902 37.783 38.0902 37.4853C38.0902 25.3841 38.0902 13.283 38.0902 1.18187C38.0902 0.913945 38.105 0.646024 38.1199 0.244141C39.4298 0.318563 40.6801 0.348332 41.9304 0.467409C49.3726 1.22652 56.026 3.92063 61.8757 8.59437C62.4859 9.08557 63.1111 9.56187 63.7362 10.0531L65.1056 11.4224Z" fill="#0DD668"/>
                        <path d="M11.1783 11.4233C20.1239 20.3689 29.0695 29.3294 38.0152 38.275C38.0449 38.3048 38.0896 38.3346 38.1342 38.3643C37.8365 38.3941 37.5388 38.4387 37.2412 38.4387C25.14 38.4387 13.0389 38.4387 0.937727 38.4387C0.669805 38.4387 0.401883 38.4239 0 38.409C0.0744228 37.0991 0.104192 35.8488 0.223268 34.5985C0.982381 27.1563 3.67649 20.5029 8.35023 14.6532C8.84143 14.043 9.31773 13.4178 9.80892 12.7927L11.1783 11.4233Z" fill="#D3FCC8"/>
                        <path d="M11.2543 65.5558C20.2 56.6102 29.1605 47.6646 38.1061 38.719C38.1358 38.6892 38.1656 38.6446 38.1954 38.5999C38.2252 38.8976 38.2698 39.1953 38.2698 39.493C38.2698 51.5941 38.2698 63.6953 38.2698 75.7964C38.2698 76.0643 38.2549 76.3323 38.24 76.7341C36.9302 76.6597 35.6799 76.6299 34.4296 76.5109C26.9873 75.7518 20.3339 73.0577 14.4843 68.3839C13.874 67.8927 13.2489 67.4164 12.6237 66.9252L11.2543 65.5558Z" fill="#0DD668"/>
                        <path d="M65.1647 65.452C56.2191 56.5064 47.2735 47.5459 38.3428 38.5854C38.313 38.5556 38.2683 38.5259 38.2237 38.4961C38.5214 38.4663 38.8191 38.4217 39.1168 38.4217C51.2179 38.4217 63.3191 38.4217 75.4202 38.4366C75.6881 38.4366 75.956 38.4515 76.3579 38.4663C76.2835 39.7762 76.2537 41.0265 76.1347 42.2768C75.3755 49.7191 72.6814 56.3725 68.0077 62.2221C67.5165 62.8324 67.0402 63.4575 66.549 64.0827L65.1647 65.452Z" fill="#D3FCC8"/>
                        <path d="M38.0752 0.257812C38.0752 12.9097 38.0752 25.5764 38.0604 38.2283C38.0604 38.2729 38.0752 38.3325 38.0752 38.3771C37.852 38.1836 37.6138 38.005 37.4054 37.7966C28.8468 29.238 20.2882 20.6794 11.7296 12.1208C11.5361 11.9273 11.3575 11.7338 11.0895 11.4361C12.0719 10.5728 12.965 9.69462 13.9474 8.90574C19.7524 4.18733 26.3611 1.37415 33.8034 0.540619C34.5923 0.451312 35.3663 0.34712 36.1402 0.257812H38.0752Z" fill="#007EE5"/>
                        <path d="M38.2095 76.5563C38.1946 76.3032 38.1798 76.0651 38.1798 75.812C38.1798 63.6216 38.1798 51.4311 38.1798 39.2556C38.1798 39.0323 38.1946 38.809 38.2095 38.5858C38.3732 38.7197 38.5667 38.8537 38.7156 39.0025C47.3486 47.6207 55.9668 56.2537 64.585 64.8719C64.7189 65.0058 64.8529 65.1547 65.0315 65.3631C64.8826 65.5268 64.7487 65.7054 64.5998 65.8543C58.9586 71.2425 52.3201 74.6361 44.6248 75.9311C42.9875 76.199 41.3353 76.3479 39.698 76.5563H38.2095Z" fill="#29576C"/>
                        </svg>
                    </div>
                    <div class="canpay-crew-small-green-spin">
                    x{{item.unclaimed_rewards_count}}
                    </div>
                    </div>
                        </div>
                        <div class="col-12 canpay-crew-padding-left-none canpay-crew-padding-right-none">
                            <hr style="border:0.5px solid #ECECEC;margin-bottom:8px;margin-top:12px;">
                            <div style="margin:0px 7px;" class="text-right canpay-crew-text-font-12">
                                    <svg style="position:relative;top:-1px;" xmlns="http://www.w3.org/2000/svg" width="14" height="20" viewBox="0 0 41 42" fill="none">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.3 0C13.4322 0 14.35 0.940212 14.35 2.1H30.75C36.4109 2.1 41 6.80102 41 12.6V31.5C41 37.2989 36.4109 42 30.75 42H10.25C4.58909 42 0 37.2989 0 31.5V12.6C0 6.80102 4.58909 2.1 10.25 2.1C10.25 0.940212 11.1678 0 12.3 0ZM10.25 6.3C6.85346 6.3 4.1 9.12062 4.1 12.6V31.5C4.1 34.9795 6.85346 37.8 10.25 37.8H30.75C34.1466 37.8 36.9 34.9795 36.9 31.5V12.6C36.9 9.12062 34.1466 6.3 30.75 6.3H14.35V8.4C14.35 9.55979 13.4322 10.5 12.3 10.5C11.1678 10.5 10.25 9.55979 10.25 8.4V6.3Z" fill="black"/>
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M28.7 0C29.8322 0 30.75 0.940212 30.75 2.1V8.4C30.75 9.55979 29.8322 10.5 28.7 10.5C27.5678 10.5 26.65 9.55979 26.65 8.4V2.1C26.65 0.940212 27.5678 0 28.7 0Z" fill="black"/>
                                    </svg> Started accepting CanPay on <b>{{formatedDate(item.onboarded_date)}}</b>
                            </div>
                        </div>
                    </div>
                    </div>

                    </div>
                </div>
                
        </div>
    </div>
<div>
    <crew-error
        ref="CrewError"
        :textMessage="textMessage"
    >
    </crew-error>
</div>
</div>
</template>
<script>
import CrewError from "./Modal/CrewError.vue";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue";
import petitionApi from "../../api/petition.js";
import moment from "moment";
export default {
    name:"CanpayCrewSuccessfulPetition",
    data(){
        return{
            petitions:[],
            isLoading:false,
            textMessage:''
        }
    },
    
    components: {
        CanPayLoader,
        CrewError
    },
    methods: {
        totalUnclaimedRewards() {
            return this.petitions.reduce((sum, item) => {
                return sum + (item.unclaimed_rewards_count || 0);
            }, 0);
        },
        gotoDetailsPage(item) {
            this.$router.push({ name: 'PetitionDetails', params: { encodedData: btoa(item.id) } });
        },
        fetchPetitions() {
            let self = this;
            const payload = {
                status: "active",
            }
            self.isLoading = true;
            petitionApi
            .petitionList(payload)
            .then((res)=>{
                self.petitions = res.data;
            })
            .catch((err) => {
                this.textMessage = err.response.data.message;
                this.$refs.CrewError.showErrorModal();
                console.log(err);
            })
            .finally(()=>{
                self.isLoading = false;
            })
        },
        formatedDate(date) {
            return moment
            .utc(date)
            .local()
            .format("MMM D, YYYY");
        },
    },
    mounted(){
        var element = document.getElementsByClassName("content-wrap");
        if (element[0]) {
        element[0].style.setProperty("background-color", "#ECECEC");
        element[0].style.height = "114vh";
        if(window.innerWidth>1200){
            element[0].style.height = "121vh";
        }
        }
        this.$root.$emit("changeWhiteBackground", [false, false, "CanPayCrewPetitionHeader", true]);
        
        this.fetchPetitions();
    },
}
</script>