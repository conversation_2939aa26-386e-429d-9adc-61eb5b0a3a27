<template>
<div>
    <div class="container" style="position: relative;bottom: -30px;">
        <div class="row" >
            <div class="col-1">
                <svg xmlns="http://www.w3.org/2000/svg" v-on:click="routePush('canpaycrew')" width="20" height="20" viewBox="0 0 67 56" fill="none">
                <path d="M2.38113 25.5833L26.6029 1C27.9167 -0.333333 30.0515 -0.333333 31.3652 1C32.6789 2.33333 32.6789 4.5 31.3652 5.83333L12.8909 24.5833H63.6336C65.5221 24.5833 67 26.0833 67 28C67 29.9167 65.5221 31.4167 63.6336 31.4167H12.8909L31.3652 50.1667C32.6789 51.5 32.6789 53.6667 31.3652 55C30.7083 55.6667 29.8873 56 28.9841 56C28.0809 56 27.2598 55.6667 26.6029 55L2.38113 30.4167L0 28L2.38113 25.5833Z" fill="black"/>
                </svg>
            </div>
            <div class="col-10 text-left">
                <span class="ml-2" 
                 style="
                    font-weight: 700;
                    font-size: 18px;
                 "
                >My Petitions</span>
            </div>

        </div>

    </div>
    <div class="petition-header">
        <div class="container super-display-header-link">
            <div class="col-6 canpay-crew-padding-left-none canpay-crew-padding-right-none display-header-link" v-on:click="routePush('canpaycrewpetition')">
                <div :class="classPoint"><span class="show-link">Active Petitions</span></div>
            </div>
            <div class="col-6 canpay-crew-padding-left-none canpay-crew-padding-right-none display-header-link" v-on:click="routePush('canpaycrewsuccessfulpetition')">
                <div :class="classHistory"><span class="show-link">Successful Petitions</span></div>
            </div>
        </div>
    </div>
</div>
</template>
<script>
export default {
  name:"CanPayCrewPetitionHeader",
  computed:{
    classPoint(){
      if(this.$route.name == 'canpaycrewpetition'){
        return 'selected-my-petition'
      }else{
        return 'not-selected-my-petition'
      }
    },
    classHistory(){
      if(this.$route.name == 'canpaycrewsuccessfulpetition'){
        return 'selected-my-successful-petition'
      }else{
        return 'not-selected-my-successful-petition'
      }
    }
  },
  methods:{
    routePush(url){
            this.$router.push("/"+url).catch((err) => {console.log(err)});
    }
  }
}
</script>
<style scoped>
.super-display-header-link{
    width: 100%;
    display: flex;
    padding:0px!important;
    justify-content: space-between;
}
.display-header-link{
  display:flex;
  justify-content:center
}
.show-link{
position:relative;
top:-4px;
}
.header-link-card{
    position: absolute;
    bottom: 6px;
    width: 100%;
    padding: 0!important;
    margin: 0!important; 
}
.selected-my-petition{
  color:#000000!important;
  font-size:12px;
  font-weight:700;
  border-bottom: 5px solid black;
  width: 85%;
}
.not-selected-my-petition{
  color:#787878;
  font-weight:700;
  font-size:12px;
  border:none;
}
.selected-my-successful-petition{
  color:#000000!important;
  font-size:12px;
  font-weight:700;
  border-bottom: 5px solid black;
  width: 85%;
}
.not-selected-my-successful-petition{
  color:#787878;
  font-size:12px;
  font-weight:700;
  border:none;
}
.petition-header{
position: relative;
bottom: -50px; 
background-color:#ffffff;
border-radius:0px 0px 15px 15px;
z-index:8;width:100%;
}
@media only screen and (min-width: 700px) and (max-width:990px){
.petition-header{
position: relative;
bottom: -64px; 
background-color:#ffffff;
border-radius:0px 0px 15px 15px;
z-index:8;width:100%;
}
}
@media only screen and (max-width:320px){
  .petition-header[data-v-365c5223] {
    position: relative;
    bottom: -41px;
    background-color: #ffffff;
    border-radius: 0px 0px 15px 15px;
    z-index: 8;
    width: 100%;
}
}
</style>