<template>
<div>
<div>
  <div v-if="isLoading && !fromParentComponent">
    <CanPayLoader/>
  </div>
  <div class="container">
    <mx-connect-widget ref="mxRef" :on-event="handleWidgetEvent" :widget-url="mxConnectUrl" />
    <div
      id="connect-container"
      v-if="showFloatDiv && !isLoading"
      class="d-flex justify-content-start iframe-header"
    >
      <label
        >Having trouble?<button
          @click="havingtroubleShowManual()"
          type="button"
          class="ml-2 align-self-center btn-manual-link"
        >
          Enter Banking Manually
        </button>
      </label>
    </div>
    <div style="height:100vh;">
      <cp-onboardingheader v-if="isOnbordingPage"></cp-onboardingheader>
      <div class="row" v-if="!isLoading">
        
        <div v-if="find_bank" class="container">
          <!-- bank icon row -->
          <div class="row icon-space" v-if="isOnbordingPage">
            <div class="col-12">
              <img
                class="register-phone-image"
                src="../../assets/images/bank-acount.png"
                style="margin-top: 2.5rem; height: 4rem; width: 4rem"
              />
            </div>
          </div>
          <div class="row icon-space" v-else>
            <div class="col-12">
              <span class="text-center">
                <svg
                  version="1.1"
                  id="Layer_1"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  x="0px"
                  y="0px"
                  viewBox="0 0 1024 1280"
                  style="enable-background: new 0 0 1024 1280"
                  xml:space="preserve"
                  height="50"
                  width="50"
                  fill="#ffffff"
                >
                  <g>
                    <g>
                      <g>
                        <path
                          d="M904.2,390.1c-7,0-14,0-21,0c-19,0-37.9,0-56.9,0c-28.2,0-56.3,0-84.5,0c-34.2,0-68.4,0-102.7,0c-37.6,0-75.3,0-112.9,0
          c-38,0-76,0-114,0c-35.3,0-70.6,0-105.8,0c-29.9,0-59.9,0-89.8,0c-21.4,0-42.9,0-64.3,0c-10.2,0-20.4-0.3-30.6,0
          c-0.4,0-0.9,0-1.3,0c6.7,6.7,13.3,13.3,20,20c0-28.2,0-56.4,0-84.6c-3.3,5.8-6.6,11.5-9.9,17.3c12.9-6.7,25.8-13.5,38.7-20.2
          c31-16.1,62-32.3,92.9-48.4c37.3-19.5,74.7-38.9,112-58.4c32.5-17,65-33.9,97.6-50.9c15.6-8.2,31.7-15.8,47.1-24.5
          c0.2-0.1,0.5-0.2,0.7-0.4c-6.7,0-13.5,0-20.2,0c13.1,6.7,26.1,13.4,39.2,20.1c31.5,16.2,63,32.3,94.5,48.5
          c38,19.5,76,39,114,58.5c33,16.9,65.9,33.8,98.9,50.7c15.9,8.1,31.6,17.1,47.8,24.5c0.2,0.1,0.4,0.2,0.7,0.3
          c-3.3-5.8-6.6-11.5-9.9-17.3c0,28.2,0,56.4,0,84.6c0,10.5,9.2,20.5,20,20s20-8.8,20-20c0-28.2,0-56.4,0-84.6
          c0-6.7-3.7-14.1-9.9-17.3c-13.1-6.7-26.1-13.4-39.2-20.1c-31.2-16-62.5-32.1-93.7-48.1c-37.8-19.4-75.7-38.8-113.5-58.3
          c-33-16.9-66-33.9-99-50.8c-16.3-8.4-32.4-17-48.9-25.1c-7.1-3.5-14-3.9-21.3-0.3c-1.5,0.7-2.9,1.5-4.4,2.3
          c-7.7,4-15.4,8-23.1,12c-29.1,15.2-58.2,30.3-87.3,45.5c-37.7,19.6-75.3,39.3-113,58.9c-34,17.7-68.1,35.5-102.1,53.2
          c-18.7,9.8-37.5,19.5-56.2,29.3c-0.9,0.4-1.7,0.9-2.6,1.3c-6.2,3.2-9.9,10.5-9.9,17.3c0,28.2,0,56.4,0,84.6c0,10.8,9.2,20,20,20
          c7,0,14,0,21,0c19,0,37.9,0,56.9,0c28.2,0,56.3,0,84.5,0c34.2,0,68.4,0,102.7,0c37.6,0,75.3,0,112.9,0c38,0,76,0,114,0
          c35.3,0,70.6,0,105.8,0c29.9,0,59.9,0,89.8,0c21.4,0,42.9,0,64.3,0c10.2,0,20.4,0.2,30.6,0c0.4,0,0.9,0,1.3,0
          c10.5,0,20.5-9.2,20-20C923.7,399.3,915.4,390.1,904.2,390.1z"
                        />
                      </g>
                    </g>
                    <g>
                      <g>
                        <path
                          d="M924,881.1c-7.4,0-14.8,0-22.1,0c-20,0-40,0-59.9,0c-29.5,0-59,0-88.6,0c-36,0-72,0-108,0c-39.6,0-79.2,0-118.8,0
          c-39.8,0-79.6,0-119.5,0c-37.1,0-74.3,0-111.4,0c-31.4,0-62.8,0-94.2,0c-22.7,0-45.3,0-68,0c-10.7,0-21.4-0.2-32.1,0
          c-0.5,0-0.9,0-1.4,0c-10.5,0-20.5,9.2-20,20s8.8,20,20,20c7.4,0,14.8,0,22.1,0c20,0,40,0,59.9,0c29.5,0,59,0,88.6,0
          c36,0,72,0,108,0c39.6,0,79.2,0,118.8,0c39.8,0,79.6,0,119.5,0c37.1,0,74.3,0,111.4,0c31.4,0,62.8,0,94.2,0c22.7,0,45.3,0,68,0
          c10.7,0,21.4,0.2,32.1,0c0.5,0,0.9,0,1.4,0c10.5,0,20.5-9.2,20-20C943.5,890.3,935.2,881.1,924,881.1L924,881.1z"
                        />
                      </g>
                    </g>
                    <g>
                      <g>
                        <path
                          d="M391.3,510.1c0,12.9,0,25.9,0,38.8c0,31.1,0,62.2,0,93.3c0,37.6,0,75.3,0,112.9c0,32.6,0,65.1,0,97.7
          c0,15.9-0.4,31.8,0,47.7c0,0.2,0,0.4,0,0.7c0,10.5,9.2,20.5,20,20s20-8.8,20-20c0-12.9,0-25.9,0-38.8c0-31.1,0-62.2,0-93.3
          c0-37.6,0-75.3,0-112.9c0-32.6,0-65.1,0-97.7c0-15.9,0.4-31.8,0-47.7c0-0.2,0-0.4,0-0.7c0-10.5-9.2-20.5-20-20
          C400.5,490.6,391.3,498.9,391.3,510.1L391.3,510.1z"
                        />
                      </g>
                    </g>
                    <g>
                      <g>
                        <path
                          d="M230,901.1c0-12.9,0-25.9,0-38.8c0-31.1,0-62.2,0-93.3c0-37.6,0-75.3,0-112.9c0-32.6,0-65.1,0-97.7
          c0-15.9,0.4-31.8,0-47.7c0-0.2,0-0.4,0-0.7c0-10.5-9.2-20.5-20-20s-20,8.8-20,20c0,12.9,0,25.9,0,38.8c0,31.1,0,62.2,0,93.3
          c0,37.6,0,75.3,0,112.9c0,32.6,0,65.1,0,97.7c0,15.9-0.4,31.8,0,47.7c0,0.2,0,0.4,0,0.7c0,10.5,9.2,20.5,20,20
          C220.8,920.7,230,912.4,230,901.1L230,901.1z"
                        />
                      </g>
                    </g>
                    <g>
                      <g>
                        <path
                          d="M794,510.1c0,12.9,0,25.9,0,38.8c0,31.1,0,62.2,0,93.3c0,37.6,0,75.3,0,112.9c0,32.6,0,65.1,0,97.7
          c0,15.9-0.4,31.8,0,47.7c0,0.2,0,0.4,0,0.7c0,10.5,9.2,20.5,20,20s20-8.8,20-20c0-12.9,0-25.9,0-38.8c0-31.1,0-62.2,0-93.3
          c0-37.6,0-75.3,0-112.9c0-32.6,0-65.1,0-97.7c0-15.9,0.4-31.8,0-47.7c0-0.2,0-0.4,0-0.7c0-10.5-9.2-20.5-20-20
          C803.2,490.6,794,498.9,794,510.1L794,510.1z"
                        />
                      </g>
                    </g>
                    <g>
                      <g>
                        <path
                          d="M592.7,510.1c0,12.9,0,25.9,0,38.8c0,31.1,0,62.2,0,93.3c0,37.6,0,75.3,0,112.9c0,32.6,0,65.1,0,97.7
          c0,15.9-0.4,31.8,0,47.7c0,0.2,0,0.4,0,0.7c0,10.5,9.2,20.5,20,20s20-8.8,20-20c0-12.9,0-25.9,0-38.8c0-31.1,0-62.2,0-93.3
          c0-37.6,0-75.3,0-112.9c0-32.6,0-65.1,0-97.7c0-15.9,0.4-31.8,0-47.7c0-0.2,0-0.4,0-0.7c0-10.5-9.2-20.5-20-20
          C601.8,490.6,592.7,498.9,592.7,510.1L592.7,510.1z"
                        />
                      </g>
                    </g>
                    <g>
                      <g>
                        <path
                          d="M537.9,286.4c0,1.1-0.1,2.3-0.1,3.5c0,2.9,1.1-5,0.1-0.6c-0.4,1.7-0.7,3.5-1.3,5.2c-0.1,0.4-0.3,0.8-0.4,1.3
          c-0.6,1.6-0.6,1.7,0.1,0.1c0.2-0.4,0.3-0.8,0.5-1.2c-0.4,0.8-0.7,1.6-1.1,2.4c-0.2,0.4-3,5.7-3.4,5.6c-0.2-0.1,2.8-3.2,0.7-1
          c-0.7,0.8-1.4,1.6-2.2,2.4c-0.4,0.4-3.7,4.1-4.3,3.9c-0.2-0.1,3.6-2.4,0.9-0.8c-0.8,0.4-1.5,0.9-2.2,1.4c-0.9,0.6-1.9,1-2.9,1.5
          c-0.6,0.3-1.2,0.6-1.8,0.8c2.6-1.1,3.1-1.3,1.7-0.8c-1.9,0.2-3.9,1.2-5.8,1.6c0.1,0-2.6,0.6-2.7,0.5c0.3,0.4,4.7-0.4,0.5-0.3
          c-1.7,0.1-4.6-0.6-6.2-0.1c2.4-0.7,3.5,0.7,1.3,0.2c-1.3-0.3-2.6-0.5-3.9-0.9c-1-0.3-2.2-0.9-3.2-1c-2.6-0.3,4.2,2.2,0.5,0.3
          c-1.5-0.8-3.1-1.6-4.6-2.5c-0.4-0.2-0.7-0.5-1.1-0.7c-1.4-1-1.4-1-0.1,0c0.3,0.3,0.7,0.5,1,0.8c-0.7-0.5-1.3-1.1-2-1.7
          c-1.6-1.4-3.1-3-4.5-4.6c-2.6-2.8,0.9,0.9,0.7,1c-0.1,0.1-1.6-2.4-1.8-2.7c-0.2-0.4-3.1-4.7-2.7-5.2c0.2-0.3,1.4,4.2,0.6,1.1
          c-0.3-1.1-0.7-2.1-0.9-3.2c-0.3-1.1-0.5-2.2-0.7-3.3c-0.9-3.9,0,4.2,0.1-0.1c0-1.8,0-3.7,0-5.6c0.1-4.1-0.1,1.4-0.3,1.2
          c-0.2-0.2,0.5-2.9,0.6-3.3c0.1-0.3,1.3-5.8,1.7-5.7c0.2,0-1.9,3.9-0.5,1.1c0.6-1.2,1.2-2.3,1.8-3.5c0.5-0.9,1.4-1.8,1.7-2.7
          c0.9-2.4-3.2,3.5-0.4,0.4c1.2-1.3,2.3-2.6,3.6-3.8c0.8-0.7,1.7-1.4,2.4-2.1c1.8-1.9-4.1,2.5-0.4,0.4c1.9-1.1,3.7-2.2,5.7-3.2
          c3.4-1.8-1.1,0.6-1.1,0.5c0-0.2,2.8-0.9,3.1-1c1.9-0.6,4-0.7,5.9-1.3c-4.2,1.2-2.8,0.3-1.2,0.3c1.2,0,2.3-0.1,3.5-0.1
          c0.4,0,3.4,0,3.5,0.2c-0.1-0.3-4.9-1,0,0.1c0.5,0.1,6.4,1.4,6.4,1.9c0,0.2-3.9-1.9-1.1-0.5c1.2,0.6,2.3,1.2,3.5,1.8
          c0.7,0.4,1.5,1,2.2,1.4c3.6,2-2.7-2.6,0.1,0c1.8,1.6,3.4,3.3,5.1,5c2.8,3-0.4-0.5-0.3-0.5c0.2-0.1,2,3.1,2.1,3.3
          c0.4,0.8,0.9,1.5,1.3,2.3c0.3,0.6,0.6,1.2,0.9,1.8c0.6,1.4,0.4,0.8-0.8-1.7c0.5,0,1.7,5.9,1.9,6.4c0.1,0.4,0.2,0.9,0.3,1.3
          c0.3,1.8,0.3,1.8,0,0c-0.2-1.8-0.3-1.7-0.1,0.1C537.9,284.6,537.9,285.5,537.9,286.4c0.2,10.5,9.1,20.5,20,20
          c10.7-0.5,20.2-8.8,20-20c-0.4-28.1-18-52.4-44.3-62c-24.6-8.9-54.5-0.7-71,19.5c-17.6,21.5-21.1,52-6.5,76.1
          c14.6,24.2,42.4,35.4,69.8,30.5c30.3-5.5,51.6-34.1,52.1-64.1c0.2-10.5-9.3-20.5-20-20C546.9,266.9,538,275.2,537.9,286.4z"
                        />
                      </g>
                    </g>
                  </g>
                </svg>
              </span>
            </div>
          </div>
          <p :class="isOnbordingPage ? 'register-phone-number-tex' : 'rounting-heading-text'">Connect Your Bank Account</p>
          <!-- connect to your bank text -->
          <div class="row mt-3">
            <div class="col-12">
              <span :class="isOnbordingPage ? 'register-phone-number-tex' : 'rounting-heading-text'">Search to add your bank account</span>
            </div>
          </div>
          <div class="row mt-3">

            <div class="form-group col-12">
              <div>
              <multiselect
                placeholder="Search here by Bank Name or Routing Number"
                id="id"
                label="bank_name"
                :options="bankList"
                :loading="isSelectLoading"
                :internal-search="false"
                @search-change="searchBank"
                @select="afterBankSelect"
              >
              <template #noResult>
                <span>No matching institution found. Please check your search or try a different institution.</span>
              </template>
              </multiselect>
              </div>

            </div>
          </div>

              
              <div class="row" v-if="frontendVisibilityBanks.length > 0">
                <div class="col-md-4 col-sm-4 col-6 mt-3" v-for="(bank, index) in frontendVisibilityBanks" :key="index"v-if="bank.logo_url != null && bank.logo_url != ''">
                  <img @click="afterBankSelect(bank)" v-if="bank.logo_url != null && bank.logo_url != ''" v-bind:src="bank.logo_url" class="preview_bank img-fluid curser-pointer">
                </div>
              </div>
        <div class="row">
          <div class="col-12 text-center mt-2">
            <a href="#" v-if="event!=''" class="report-consumer-text" @click="showBankReportModal('bank-report-modal')">Unable to Connect Bank? Report Here</a> 
          </div>
        </div>
        </div>
        <div v-else-if="show_option" class="container">
          <!-- bank icon row -->
          <div class="row icon-space" v-if="isOnbordingPage">
            <div class="col-12 text-center">
              <img
                class="bank-phone-image"
                src="../../assets/images/bank-acount.png"
              />
            </div>
          </div>
          <div class="row icon-space" v-else>
            <div class="col-12">
              <span class="text-center">
                <svg
                  version="1.1"
                  id="Layer_1"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  x="0px"
                  y="0px"
                  viewBox="0 0 1024 1280"
                  style="enable-background: new 0 0 1024 1280"
                  xml:space="preserve"
                  height="50"
                  width="50"
                  fill="#ffffff"
                >
                  <g>
                    <g>
                      <g>
                        <path
                          d="M904.2,390.1c-7,0-14,0-21,0c-19,0-37.9,0-56.9,0c-28.2,0-56.3,0-84.5,0c-34.2,0-68.4,0-102.7,0c-37.6,0-75.3,0-112.9,0
          c-38,0-76,0-114,0c-35.3,0-70.6,0-105.8,0c-29.9,0-59.9,0-89.8,0c-21.4,0-42.9,0-64.3,0c-10.2,0-20.4-0.3-30.6,0
          c-0.4,0-0.9,0-1.3,0c6.7,6.7,13.3,13.3,20,20c0-28.2,0-56.4,0-84.6c-3.3,5.8-6.6,11.5-9.9,17.3c12.9-6.7,25.8-13.5,38.7-20.2
          c31-16.1,62-32.3,92.9-48.4c37.3-19.5,74.7-38.9,112-58.4c32.5-17,65-33.9,97.6-50.9c15.6-8.2,31.7-15.8,47.1-24.5
          c0.2-0.1,0.5-0.2,0.7-0.4c-6.7,0-13.5,0-20.2,0c13.1,6.7,26.1,13.4,39.2,20.1c31.5,16.2,63,32.3,94.5,48.5
          c38,19.5,76,39,114,58.5c33,16.9,65.9,33.8,98.9,50.7c15.9,8.1,31.6,17.1,47.8,24.5c0.2,0.1,0.4,0.2,0.7,0.3
          c-3.3-5.8-6.6-11.5-9.9-17.3c0,28.2,0,56.4,0,84.6c0,10.5,9.2,20.5,20,20s20-8.8,20-20c0-28.2,0-56.4,0-84.6
          c0-6.7-3.7-14.1-9.9-17.3c-13.1-6.7-26.1-13.4-39.2-20.1c-31.2-16-62.5-32.1-93.7-48.1c-37.8-19.4-75.7-38.8-113.5-58.3
          c-33-16.9-66-33.9-99-50.8c-16.3-8.4-32.4-17-48.9-25.1c-7.1-3.5-14-3.9-21.3-0.3c-1.5,0.7-2.9,1.5-4.4,2.3
          c-7.7,4-15.4,8-23.1,12c-29.1,15.2-58.2,30.3-87.3,45.5c-37.7,19.6-75.3,39.3-113,58.9c-34,17.7-68.1,35.5-102.1,53.2
          c-18.7,9.8-37.5,19.5-56.2,29.3c-0.9,0.4-1.7,0.9-2.6,1.3c-6.2,3.2-9.9,10.5-9.9,17.3c0,28.2,0,56.4,0,84.6c0,10.8,9.2,20,20,20
          c7,0,14,0,21,0c19,0,37.9,0,56.9,0c28.2,0,56.3,0,84.5,0c34.2,0,68.4,0,102.7,0c37.6,0,75.3,0,112.9,0c38,0,76,0,114,0
          c35.3,0,70.6,0,105.8,0c29.9,0,59.9,0,89.8,0c21.4,0,42.9,0,64.3,0c10.2,0,20.4,0.2,30.6,0c0.4,0,0.9,0,1.3,0
          c10.5,0,20.5-9.2,20-20C923.7,399.3,915.4,390.1,904.2,390.1z"
                        />
                      </g>
                    </g>
                    <g>
                      <g>
                        <path
                          d="M924,881.1c-7.4,0-14.8,0-22.1,0c-20,0-40,0-59.9,0c-29.5,0-59,0-88.6,0c-36,0-72,0-108,0c-39.6,0-79.2,0-118.8,0
          c-39.8,0-79.6,0-119.5,0c-37.1,0-74.3,0-111.4,0c-31.4,0-62.8,0-94.2,0c-22.7,0-45.3,0-68,0c-10.7,0-21.4-0.2-32.1,0
          c-0.5,0-0.9,0-1.4,0c-10.5,0-20.5,9.2-20,20s8.8,20,20,20c7.4,0,14.8,0,22.1,0c20,0,40,0,59.9,0c29.5,0,59,0,88.6,0
          c36,0,72,0,108,0c39.6,0,79.2,0,118.8,0c39.8,0,79.6,0,119.5,0c37.1,0,74.3,0,111.4,0c31.4,0,62.8,0,94.2,0c22.7,0,45.3,0,68,0
          c10.7,0,21.4,0.2,32.1,0c0.5,0,0.9,0,1.4,0c10.5,0,20.5-9.2,20-20C943.5,890.3,935.2,881.1,924,881.1L924,881.1z"
                        />
                      </g>
                    </g>
                    <g>
                      <g>
                        <path
                          d="M391.3,510.1c0,12.9,0,25.9,0,38.8c0,31.1,0,62.2,0,93.3c0,37.6,0,75.3,0,112.9c0,32.6,0,65.1,0,97.7
          c0,15.9-0.4,31.8,0,47.7c0,0.2,0,0.4,0,0.7c0,10.5,9.2,20.5,20,20s20-8.8,20-20c0-12.9,0-25.9,0-38.8c0-31.1,0-62.2,0-93.3
          c0-37.6,0-75.3,0-112.9c0-32.6,0-65.1,0-97.7c0-15.9,0.4-31.8,0-47.7c0-0.2,0-0.4,0-0.7c0-10.5-9.2-20.5-20-20
          C400.5,490.6,391.3,498.9,391.3,510.1L391.3,510.1z"
                        />
                      </g>
                    </g>
                    <g>
                      <g>
                        <path
                          d="M230,901.1c0-12.9,0-25.9,0-38.8c0-31.1,0-62.2,0-93.3c0-37.6,0-75.3,0-112.9c0-32.6,0-65.1,0-97.7
          c0-15.9,0.4-31.8,0-47.7c0-0.2,0-0.4,0-0.7c0-10.5-9.2-20.5-20-20s-20,8.8-20,20c0,12.9,0,25.9,0,38.8c0,31.1,0,62.2,0,93.3
          c0,37.6,0,75.3,0,112.9c0,32.6,0,65.1,0,97.7c0,15.9-0.4,31.8,0,47.7c0,0.2,0,0.4,0,0.7c0,10.5,9.2,20.5,20,20
          C220.8,920.7,230,912.4,230,901.1L230,901.1z"
                        />
                      </g>
                    </g>
                    <g>
                      <g>
                        <path
                          d="M794,510.1c0,12.9,0,25.9,0,38.8c0,31.1,0,62.2,0,93.3c0,37.6,0,75.3,0,112.9c0,32.6,0,65.1,0,97.7
          c0,15.9-0.4,31.8,0,47.7c0,0.2,0,0.4,0,0.7c0,10.5,9.2,20.5,20,20s20-8.8,20-20c0-12.9,0-25.9,0-38.8c0-31.1,0-62.2,0-93.3
          c0-37.6,0-75.3,0-112.9c0-32.6,0-65.1,0-97.7c0-15.9,0.4-31.8,0-47.7c0-0.2,0-0.4,0-0.7c0-10.5-9.2-20.5-20-20
          C803.2,490.6,794,498.9,794,510.1L794,510.1z"
                        />
                      </g>
                    </g>
                    <g>
                      <g>
                        <path
                          d="M592.7,510.1c0,12.9,0,25.9,0,38.8c0,31.1,0,62.2,0,93.3c0,37.6,0,75.3,0,112.9c0,32.6,0,65.1,0,97.7
          c0,15.9-0.4,31.8,0,47.7c0,0.2,0,0.4,0,0.7c0,10.5,9.2,20.5,20,20s20-8.8,20-20c0-12.9,0-25.9,0-38.8c0-31.1,0-62.2,0-93.3
          c0-37.6,0-75.3,0-112.9c0-32.6,0-65.1,0-97.7c0-15.9,0.4-31.8,0-47.7c0-0.2,0-0.4,0-0.7c0-10.5-9.2-20.5-20-20
          C601.8,490.6,592.7,498.9,592.7,510.1L592.7,510.1z"
                        />
                      </g>
                    </g>
                    <g>
                      <g>
                        <path
                          d="M537.9,286.4c0,1.1-0.1,2.3-0.1,3.5c0,2.9,1.1-5,0.1-0.6c-0.4,1.7-0.7,3.5-1.3,5.2c-0.1,0.4-0.3,0.8-0.4,1.3
          c-0.6,1.6-0.6,1.7,0.1,0.1c0.2-0.4,0.3-0.8,0.5-1.2c-0.4,0.8-0.7,1.6-1.1,2.4c-0.2,0.4-3,5.7-3.4,5.6c-0.2-0.1,2.8-3.2,0.7-1
          c-0.7,0.8-1.4,1.6-2.2,2.4c-0.4,0.4-3.7,4.1-4.3,3.9c-0.2-0.1,3.6-2.4,0.9-0.8c-0.8,0.4-1.5,0.9-2.2,1.4c-0.9,0.6-1.9,1-2.9,1.5
          c-0.6,0.3-1.2,0.6-1.8,0.8c2.6-1.1,3.1-1.3,1.7-0.8c-1.9,0.2-3.9,1.2-5.8,1.6c0.1,0-2.6,0.6-2.7,0.5c0.3,0.4,4.7-0.4,0.5-0.3
          c-1.7,0.1-4.6-0.6-6.2-0.1c2.4-0.7,3.5,0.7,1.3,0.2c-1.3-0.3-2.6-0.5-3.9-0.9c-1-0.3-2.2-0.9-3.2-1c-2.6-0.3,4.2,2.2,0.5,0.3
          c-1.5-0.8-3.1-1.6-4.6-2.5c-0.4-0.2-0.7-0.5-1.1-0.7c-1.4-1-1.4-1-0.1,0c0.3,0.3,0.7,0.5,1,0.8c-0.7-0.5-1.3-1.1-2-1.7
          c-1.6-1.4-3.1-3-4.5-4.6c-2.6-2.8,0.9,0.9,0.7,1c-0.1,0.1-1.6-2.4-1.8-2.7c-0.2-0.4-3.1-4.7-2.7-5.2c0.2-0.3,1.4,4.2,0.6,1.1
          c-0.3-1.1-0.7-2.1-0.9-3.2c-0.3-1.1-0.5-2.2-0.7-3.3c-0.9-3.9,0,4.2,0.1-0.1c0-1.8,0-3.7,0-5.6c0.1-4.1-0.1,1.4-0.3,1.2
          c-0.2-0.2,0.5-2.9,0.6-3.3c0.1-0.3,1.3-5.8,1.7-5.7c0.2,0-1.9,3.9-0.5,1.1c0.6-1.2,1.2-2.3,1.8-3.5c0.5-0.9,1.4-1.8,1.7-2.7
          c0.9-2.4-3.2,3.5-0.4,0.4c1.2-1.3,2.3-2.6,3.6-3.8c0.8-0.7,1.7-1.4,2.4-2.1c1.8-1.9-4.1,2.5-0.4,0.4c1.9-1.1,3.7-2.2,5.7-3.2
          c3.4-1.8-1.1,0.6-1.1,0.5c0-0.2,2.8-0.9,3.1-1c1.9-0.6,4-0.7,5.9-1.3c-4.2,1.2-2.8,0.3-1.2,0.3c1.2,0,2.3-0.1,3.5-0.1
          c0.4,0,3.4,0,3.5,0.2c-0.1-0.3-4.9-1,0,0.1c0.5,0.1,6.4,1.4,6.4,1.9c0,0.2-3.9-1.9-1.1-0.5c1.2,0.6,2.3,1.2,3.5,1.8
          c0.7,0.4,1.5,1,2.2,1.4c3.6,2-2.7-2.6,0.1,0c1.8,1.6,3.4,3.3,5.1,5c2.8,3-0.4-0.5-0.3-0.5c0.2-0.1,2,3.1,2.1,3.3
          c0.4,0.8,0.9,1.5,1.3,2.3c0.3,0.6,0.6,1.2,0.9,1.8c0.6,1.4,0.4,0.8-0.8-1.7c0.5,0,1.7,5.9,1.9,6.4c0.1,0.4,0.2,0.9,0.3,1.3
          c0.3,1.8,0.3,1.8,0,0c-0.2-1.8-0.3-1.7-0.1,0.1C537.9,284.6,537.9,285.5,537.9,286.4c0.2,10.5,9.1,20.5,20,20
          c10.7-0.5,20.2-8.8,20-20c-0.4-28.1-18-52.4-44.3-62c-24.6-8.9-54.5-0.7-71,19.5c-17.6,21.5-21.1,52-6.5,76.1
          c14.6,24.2,42.4,35.4,69.8,30.5c30.3-5.5,51.6-34.1,52.1-64.1c0.2-10.5-9.3-20.5-20-20C546.9,266.9,538,275.2,537.9,286.4z"
                        />
                      </g>
                    </g>
                  </g>
                </svg>
              </span>
            </div>
          </div>
          <!-- connect to your bank text -->
          <div class="row">
            <div class="col-12">
              <span :class="isOnbordingPage ? 'register-phone-number-tex' : 'rounting-heading-text'">Connect Your Bank Account</span>
            </div>
          </div>
          <!-- link checking account (finicity bank linking) button row -->
          <div class="row btn-row-space padding">
            <div class="col-12">
              <button
                type="button"
                class="btn-login btn-width"
                @click="bankLinkDirect"
              >
                Direct Link Checking Account
                <br />(recommended)
              </button>
            </div>
          </div>
          <!-- text section 1 -->
          <div class="row row-space">
            <div class="col-12">
              <span :class="isOnbordingPage ? 'success-description-label' : 'bank-linking-text'"
                >If your bank is supported, linking your checking account through
                your bank sign-in allows you to:</span
              >
            </div>
          </div>
          <!-- text section 2 -->
          <div class="row row-space">
            <div class="col-12">
              <span  :class="isOnbordingPage ? 'success-description-label' : 'bank-linking-text'">
                * Better manage your transactions
                <br />* Gives you customized spending limits
              </span>
            </div>
          </div>
          <!-- manual bank linking button row -->
          <div class="row padding margin-button" v-if="show_manual_link_option">
            <div class="col-12">
              <button
                type="button"
                class="manual-btn btn-width"
                @click="bankLinkManual"
              >
                I'd rather type my account number
              </button>
            </div>
          </div>
          <!-- text section 3 -->
          <div class="row row-space" v-if="show_manual_link_option">
            <div class="col-12">
              <span  :class="isOnbordingPage ? 'success-description-label' : 'bank-linking-text'"
                >Manually adding your checking account gives you:</span
              >
            </div>
          </div>
          <!-- text section 4 -->
          <div class="row row-space" v-if="show_manual_link_option">
            <div class="col-12">
              <span  :class="isOnbordingPage ? 'success-description-label' : 'bank-linking-text'">
                * Lower default spending limits
                <br />* Fewer available daily transactions
              </span>
            </div>
          </div>
        </div>
        <div v-else class="container">
          <div class="row icon-space" v-if="isOnbordingPage">
            <div class="col-12">
              <img
                class="register-phone-image"
                src="../../assets/images/bank-acount.png"
                style="margin-top: 2.5rem; height: 4rem; width: 4rem"
              />
            </div>
          </div>
          <div class="row routing-top-space" v-else>
            <div class="col-12">
              <span class="text-center">
                <svg
                  version="1.1"
                  id="Layer_1"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  x="0px"
                  y="0px"
                  viewBox="0 0 1024 1280"
                  style="enable-background: new 0 0 1024 1280"
                  xml:space="preserve"
                  height="80"
                  width="80"
                  fill="#ffffff"
                >
                  <g>
                    <g>
                      <g>
                        <path
                          d="M904.2,390.1c-7,0-14,0-21,0c-19,0-37.9,0-56.9,0c-28.2,0-56.3,0-84.5,0c-34.2,0-68.4,0-102.7,0c-37.6,0-75.3,0-112.9,0
          c-38,0-76,0-114,0c-35.3,0-70.6,0-105.8,0c-29.9,0-59.9,0-89.8,0c-21.4,0-42.9,0-64.3,0c-10.2,0-20.4-0.3-30.6,0
          c-0.4,0-0.9,0-1.3,0c6.7,6.7,13.3,13.3,20,20c0-28.2,0-56.4,0-84.6c-3.3,5.8-6.6,11.5-9.9,17.3c12.9-6.7,25.8-13.5,38.7-20.2
          c31-16.1,62-32.3,92.9-48.4c37.3-19.5,74.7-38.9,112-58.4c32.5-17,65-33.9,97.6-50.9c15.6-8.2,31.7-15.8,47.1-24.5
          c0.2-0.1,0.5-0.2,0.7-0.4c-6.7,0-13.5,0-20.2,0c13.1,6.7,26.1,13.4,39.2,20.1c31.5,16.2,63,32.3,94.5,48.5
          c38,19.5,76,39,114,58.5c33,16.9,65.9,33.8,98.9,50.7c15.9,8.1,31.6,17.1,47.8,24.5c0.2,0.1,0.4,0.2,0.7,0.3
          c-3.3-5.8-6.6-11.5-9.9-17.3c0,28.2,0,56.4,0,84.6c0,10.5,9.2,20.5,20,20s20-8.8,20-20c0-28.2,0-56.4,0-84.6
          c0-6.7-3.7-14.1-9.9-17.3c-13.1-6.7-26.1-13.4-39.2-20.1c-31.2-16-62.5-32.1-93.7-48.1c-37.8-19.4-75.7-38.8-113.5-58.3
          c-33-16.9-66-33.9-99-50.8c-16.3-8.4-32.4-17-48.9-25.1c-7.1-3.5-14-3.9-21.3-0.3c-1.5,0.7-2.9,1.5-4.4,2.3
          c-7.7,4-15.4,8-23.1,12c-29.1,15.2-58.2,30.3-87.3,45.5c-37.7,19.6-75.3,39.3-113,58.9c-34,17.7-68.1,35.5-102.1,53.2
          c-18.7,9.8-37.5,19.5-56.2,29.3c-0.9,0.4-1.7,0.9-2.6,1.3c-6.2,3.2-9.9,10.5-9.9,17.3c0,28.2,0,56.4,0,84.6c0,10.8,9.2,20,20,20
          c7,0,14,0,21,0c19,0,37.9,0,56.9,0c28.2,0,56.3,0,84.5,0c34.2,0,68.4,0,102.7,0c37.6,0,75.3,0,112.9,0c38,0,76,0,114,0
          c35.3,0,70.6,0,105.8,0c29.9,0,59.9,0,89.8,0c21.4,0,42.9,0,64.3,0c10.2,0,20.4,0.2,30.6,0c0.4,0,0.9,0,1.3,0
          c10.5,0,20.5-9.2,20-20C923.7,399.3,915.4,390.1,904.2,390.1z"
                        />
                      </g>
                    </g>
                    <g>
                      <g>
                        <path
                          d="M924,881.1c-7.4,0-14.8,0-22.1,0c-20,0-40,0-59.9,0c-29.5,0-59,0-88.6,0c-36,0-72,0-108,0c-39.6,0-79.2,0-118.8,0
          c-39.8,0-79.6,0-119.5,0c-37.1,0-74.3,0-111.4,0c-31.4,0-62.8,0-94.2,0c-22.7,0-45.3,0-68,0c-10.7,0-21.4-0.2-32.1,0
          c-0.5,0-0.9,0-1.4,0c-10.5,0-20.5,9.2-20,20s8.8,20,20,20c7.4,0,14.8,0,22.1,0c20,0,40,0,59.9,0c29.5,0,59,0,88.6,0
          c36,0,72,0,108,0c39.6,0,79.2,0,118.8,0c39.8,0,79.6,0,119.5,0c37.1,0,74.3,0,111.4,0c31.4,0,62.8,0,94.2,0c22.7,0,45.3,0,68,0
          c10.7,0,21.4,0.2,32.1,0c0.5,0,0.9,0,1.4,0c10.5,0,20.5-9.2,20-20C943.5,890.3,935.2,881.1,924,881.1L924,881.1z"
                        />
                      </g>
                    </g>
                    <g>
                      <g>
                        <path
                          d="M391.3,510.1c0,12.9,0,25.9,0,38.8c0,31.1,0,62.2,0,93.3c0,37.6,0,75.3,0,112.9c0,32.6,0,65.1,0,97.7
          c0,15.9-0.4,31.8,0,47.7c0,0.2,0,0.4,0,0.7c0,10.5,9.2,20.5,20,20s20-8.8,20-20c0-12.9,0-25.9,0-38.8c0-31.1,0-62.2,0-93.3
          c0-37.6,0-75.3,0-112.9c0-32.6,0-65.1,0-97.7c0-15.9,0.4-31.8,0-47.7c0-0.2,0-0.4,0-0.7c0-10.5-9.2-20.5-20-20
          C400.5,490.6,391.3,498.9,391.3,510.1L391.3,510.1z"
                        />
                      </g>
                    </g>
                    <g>
                      <g>
                        <path
                          d="M230,901.1c0-12.9,0-25.9,0-38.8c0-31.1,0-62.2,0-93.3c0-37.6,0-75.3,0-112.9c0-32.6,0-65.1,0-97.7
          c0-15.9,0.4-31.8,0-47.7c0-0.2,0-0.4,0-0.7c0-10.5-9.2-20.5-20-20s-20,8.8-20,20c0,12.9,0,25.9,0,38.8c0,31.1,0,62.2,0,93.3
          c0,37.6,0,75.3,0,112.9c0,32.6,0,65.1,0,97.7c0,15.9-0.4,31.8,0,47.7c0,0.2,0,0.4,0,0.7c0,10.5,9.2,20.5,20,20
          C220.8,920.7,230,912.4,230,901.1L230,901.1z"
                        />
                      </g>
                    </g>
                    <g>
                      <g>
                        <path
                          d="M794,510.1c0,12.9,0,25.9,0,38.8c0,31.1,0,62.2,0,93.3c0,37.6,0,75.3,0,112.9c0,32.6,0,65.1,0,97.7
          c0,15.9-0.4,31.8,0,47.7c0,0.2,0,0.4,0,0.7c0,10.5,9.2,20.5,20,20s20-8.8,20-20c0-12.9,0-25.9,0-38.8c0-31.1,0-62.2,0-93.3
          c0-37.6,0-75.3,0-112.9c0-32.6,0-65.1,0-97.7c0-15.9,0.4-31.8,0-47.7c0-0.2,0-0.4,0-0.7c0-10.5-9.2-20.5-20-20
          C803.2,490.6,794,498.9,794,510.1L794,510.1z"
                        />
                      </g>
                    </g>
                    <g>
                      <g>
                        <path
                          d="M592.7,510.1c0,12.9,0,25.9,0,38.8c0,31.1,0,62.2,0,93.3c0,37.6,0,75.3,0,112.9c0,32.6,0,65.1,0,97.7
          c0,15.9-0.4,31.8,0,47.7c0,0.2,0,0.4,0,0.7c0,10.5,9.2,20.5,20,20s20-8.8,20-20c0-12.9,0-25.9,0-38.8c0-31.1,0-62.2,0-93.3
          c0-37.6,0-75.3,0-112.9c0-32.6,0-65.1,0-97.7c0-15.9,0.4-31.8,0-47.7c0-0.2,0-0.4,0-0.7c0-10.5-9.2-20.5-20-20
          C601.8,490.6,592.7,498.9,592.7,510.1L592.7,510.1z"
                        />
                      </g>
                    </g>
                    <g>
                      <g>
                        <path
                          d="M537.9,286.4c0,1.1-0.1,2.3-0.1,3.5c0,2.9,1.1-5,0.1-0.6c-0.4,1.7-0.7,3.5-1.3,5.2c-0.1,0.4-0.3,0.8-0.4,1.3
          c-0.6,1.6-0.6,1.7,0.1,0.1c0.2-0.4,0.3-0.8,0.5-1.2c-0.4,0.8-0.7,1.6-1.1,2.4c-0.2,0.4-3,5.7-3.4,5.6c-0.2-0.1,2.8-3.2,0.7-1
          c-0.7,0.8-1.4,1.6-2.2,2.4c-0.4,0.4-3.7,4.1-4.3,3.9c-0.2-0.1,3.6-2.4,0.9-0.8c-0.8,0.4-1.5,0.9-2.2,1.4c-0.9,0.6-1.9,1-2.9,1.5
          c-0.6,0.3-1.2,0.6-1.8,0.8c2.6-1.1,3.1-1.3,1.7-0.8c-1.9,0.2-3.9,1.2-5.8,1.6c0.1,0-2.6,0.6-2.7,0.5c0.3,0.4,4.7-0.4,0.5-0.3
          c-1.7,0.1-4.6-0.6-6.2-0.1c2.4-0.7,3.5,0.7,1.3,0.2c-1.3-0.3-2.6-0.5-3.9-0.9c-1-0.3-2.2-0.9-3.2-1c-2.6-0.3,4.2,2.2,0.5,0.3
          c-1.5-0.8-3.1-1.6-4.6-2.5c-0.4-0.2-0.7-0.5-1.1-0.7c-1.4-1-1.4-1-0.1,0c0.3,0.3,0.7,0.5,1,0.8c-0.7-0.5-1.3-1.1-2-1.7
          c-1.6-1.4-3.1-3-4.5-4.6c-2.6-2.8,0.9,0.9,0.7,1c-0.1,0.1-1.6-2.4-1.8-2.7c-0.2-0.4-3.1-4.7-2.7-5.2c0.2-0.3,1.4,4.2,0.6,1.1
          c-0.3-1.1-0.7-2.1-0.9-3.2c-0.3-1.1-0.5-2.2-0.7-3.3c-0.9-3.9,0,4.2,0.1-0.1c0-1.8,0-3.7,0-5.6c0.1-4.1-0.1,1.4-0.3,1.2
          c-0.2-0.2,0.5-2.9,0.6-3.3c0.1-0.3,1.3-5.8,1.7-5.7c0.2,0-1.9,3.9-0.5,1.1c0.6-1.2,1.2-2.3,1.8-3.5c0.5-0.9,1.4-1.8,1.7-2.7
          c0.9-2.4-3.2,3.5-0.4,0.4c1.2-1.3,2.3-2.6,3.6-3.8c0.8-0.7,1.7-1.4,2.4-2.1c1.8-1.9-4.1,2.5-0.4,0.4c1.9-1.1,3.7-2.2,5.7-3.2
          c3.4-1.8-1.1,0.6-1.1,0.5c0-0.2,2.8-0.9,3.1-1c1.9-0.6,4-0.7,5.9-1.3c-4.2,1.2-2.8,0.3-1.2,0.3c1.2,0,2.3-0.1,3.5-0.1
          c0.4,0,3.4,0,3.5,0.2c-0.1-0.3-4.9-1,0,0.1c0.5,0.1,6.4,1.4,6.4,1.9c0,0.2-3.9-1.9-1.1-0.5c1.2,0.6,2.3,1.2,3.5,1.8
          c0.7,0.4,1.5,1,2.2,1.4c3.6,2-2.7-2.6,0.1,0c1.8,1.6,3.4,3.3,5.1,5c2.8,3-0.4-0.5-0.3-0.5c0.2-0.1,2,3.1,2.1,3.3
          c0.4,0.8,0.9,1.5,1.3,2.3c0.3,0.6,0.6,1.2,0.9,1.8c0.6,1.4,0.4,0.8-0.8-1.7c0.5,0,1.7,5.9,1.9,6.4c0.1,0.4,0.2,0.9,0.3,1.3
          c0.3,1.8,0.3,1.8,0,0c-0.2-1.8-0.3-1.7-0.1,0.1C537.9,284.6,537.9,285.5,537.9,286.4c0.2,10.5,9.1,20.5,20,20
          c10.7-0.5,20.2-8.8,20-20c-0.4-28.1-18-52.4-44.3-62c-24.6-8.9-54.5-0.7-71,19.5c-17.6,21.5-21.1,52-6.5,76.1
          c14.6,24.2,42.4,35.4,69.8,30.5c30.3-5.5,51.6-34.1,52.1-64.1c0.2-10.5-9.3-20.5-20-20C546.9,266.9,538,275.2,537.9,286.4z"
                        />
                      </g>
                    </g>
                  </g>
                </svg>
              </span>
            </div>
          </div>
          <div class="row icon-space">
            <div class="col-12">
              <span class="text-center">
                <label :class="isOnbordingPage ? 'register-phone-number-tex' : 'rounting-heading-text'" >Manually Add Your Bank Account</label
                >
              </span>
            </div>
          </div>
          <div class="row routing-bottom-space"></div>
          <div class="row input-box-row">
            <div class="form-group">
              <select class="form-control left-padding" v-model="manualBankType" v-bind:class="[ isOnbordingPage ? 'email-address' : '']" >
                <option value="">Select Bank Type</option>
                <option value="checking">Checking</option>
                <option value="savings">Savings</option>
              </select>
            </div>
          </div>
          <div class="row input-box-row">
            <div class="form-group">
              <input
                v-model="routingNumber"
                autocomplete="off"
                class="form-control left-padding"
                v-bind:class="[ isOnbordingPage ? 'email-address' : '']"
                placeholder="Routing Number"
                @keypress="isNumber($event)"
                type="text"
                inputmode="numeric"
                maxlength="9"
              />
            </div>
          </div>
          <div class="row input-box-row">
            <div class="form-group">
              <input
                class="form-control left-padding"
                v-bind:class="[ isOnbordingPage ? 'email-address' : '']"
                autocomplete="off"
                v-model="accountNumber"
                placeholder="Account Number (0-9 , A-Z only)"
                type="text"
                @keyup.enter="enterClicked()"
                @keypress="isAlphanumeric($event)"
                maxlength="17"
                minlength="5"
              />
            </div>
          </div>
          <div class="row icon-space button-space col-padding">
            <button type="button" class="btn-login" v-bind:class="[ isOnbordingPage ? 'btn-next-onboarding' : '']"  @click="updateBankDetails">
              {{isRegistrationPage ? 'Continue' : isOnbordingPage ? 'Next' : 'Update'}}
            </button>
          </div>
        </div>
      </div>
      <!-- MODAL FOR VALIDATION ERROR MESSAGES -->
      <div>
        <b-modal
          ref="validation-modal"
          hide-footer
          v-b-modal.modal-center
          modal-backdrop
          hide-header
          id="validation-modal"
          centered
        >
          <div class="color">
            <div class="purchaserpower-modal-text">
              <div class="d-block text-center">
                <label class="purchasepower-def-label">
                  {{ error_message }}
                </label>
              </div>
              <br />
              <br />
              <div v-if="check" class="text-center">
                <div class="row mt-2 mb-3">
                  <div class="col-12">
                    <button
                      type="button"
                      class="btn-modal-black p-1"
                      @click="hidevalidationModal"
                    >
                      Enter Again
                    </button>
                  </div>
                </div>
                <div class="row">
                  <div class="col-12">
                    <button
                      type="button"
                      class="btn-modal-green p-1"
                      @click="bankLinkDirect"
                    >
                      Direct Link
                    </button>
                  </div>
                </div>
              </div>
              <div class="text-center" v-else>
                <button
                  type="button"
                  class="mx-auto col-10 offset-1 btn-black"
                  style="height: 60px"
                  @click="hidevalidationModal"
                >
                  <label class="purchasepower-modal-ok-label">OK</label>
                </button>
              </div>
            </div>
          </div>
        </b-modal>

        <!-- show all banking solution -->
        <b-modal
          ref="banking-solution-modal"
          hide-footer
          v-b-modal.modal-center
          modal-backdrop
          hide-header
          id="banking-solution-modal"
          centered
        >
          <div class="color">
            <div class="purchaserpower-modal-text">
              <div class="d-block text-center">
                <label class="purchasepower-def-label">
                  Select Banking Solution
                </label>
              </div>
              <div class="row btn-row-space padding">
                <div class="col-6">
                  <button
                    type="button"
                    class="btn-login btn-width"
                    @click="directLinkGenerate('finicity',0)"
                  >Finicity
                  </button>
                </div>
                <div class="col-6">
                  <button
                    type="button"
                    class="btn-login btn-width"
                    @click="directLinkGenerate('akoya',0)"
                  >Akoya
                  </button>
                </div>
              </div>
            </div>
          </div>
        </b-modal>

        <!-- show finicity info modal -->
        <b-modal
          ref="finicity-info-modal"
          hide-footer
          v-b-modal.modal-center
          modal-backdrop
          hide-header
          id="finicity-info-modal"
          centered
        >
        <div class="color">
            <div class="purchaserpower-modal-text">
              <h3 class="purchasepower-modal-text text-justify">
                You have selected <strong>{{ selectedBank.bank_name }}</strong>. Other banks won't be allowed to direct links. If you want to change then please cancel and select the appropriate one.
              </h3>
              <div class="text-center mt-5">
                <div class="row">
                  <div class="col-6">
                    <button
                      type="button"
                      class="btn-modal-black p-1"
                      @click="hideModal('finicity-info-modal')"
                    >
                    Cancel
                    </button>
                  </div>
                  <div class="col-6">
                    <button
                      type="button"
                      class="btn-modal-green p-1"
                      @click="directLinkGenerate('finicity',0)"
                    >
                    Confirm
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </b-modal>

        <!-- terms-privacy modal -->
        <b-modal
          ref="terms-privacy-modal"
          hide-footer
          v-b-modal.modal-center
            no-close-on-backdrop
          modal-backdrop
          hide-header
          id="terms-privacy-modal"
          fullscreen 
        >
          <div class="color">
            <div class="purchaserpower-modal-text">
              <!-- first row for logo and cross sign-->
              <div class="row">
                  <div class="col-12 text-center">
                      <a class="pull-right" @click="hidetermsModal()">
                          <svg 
                          xmlns="http://www.w3.org/2000/svg" 
                          width="15" 
                          height="15" 
                          viewBox="0 0 50 50" 
                          fill="none">
                          <path 
                          d="M48.8004 1.14962C47.2676 -0.383206 44.7351 -0.383206 43.1356 1.14962L25.0083 19.2769L6.81439 1.14962C5.28157 -0.383206 2.74908 -0.383206 1.14962 1.14962C-0.383206 2.68244 -0.383206 5.21493 1.14962 6.81439L19.3436 24.9417L1.21626 43.1356C-0.316561 44.6684 -0.316561 47.2009 1.21626 48.8004C2.01599 49.6001 3.01566 50 4.01533 50C5.01499 50 6.08131 49.6001 6.81439 48.8004L25.0083 30.6065L43.2023 48.8004C44.002 49.6001 45.0017 50 46.0013 50C47.001 50 48.0673 49.6001 48.8004 48.8004C50.3332 47.2676 50.3332 44.7351 48.8004 43.1356L30.6731 24.9417L48.867 6.74775C50.3999 5.21493 50.3999 2.68244 48.8004 1.14962Z" 
                          fill="black"/>
                          </svg>
                        </a>

                      <div>
                          <img v-if="consumer_type == 'lite'" class="header-logo ml-2" src="../../assets/images/canpay-logo-lite.png" />
                          <img v-else class="header-logo ml-2" src="../../assets/images/canpay-logo-new.png" />
                      </div>

                  </div>
              </div>   

              <div class="row mt-5">
                <div class="col-12 text-center">
                  <svg width="145" height="145" viewBox="0 0 463 485" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g filter="url(#filter0_d_12399_134)">
                    <path d="M81.3078 19.1787L65.9766 10.2878C67.2088 10.8899 90.6099 22.3485 125.432 2.27918C125.821 2.05556 126.208 1.8279 126.6 1.59619L141.931 10.4871C141.538 10.7188 141.151 10.9452 140.762 11.1701C105.934 31.2408 82.5387 19.7822 81.3078 19.1787Z" fill="#E0E0E0"/>
                    <path d="M20.7982 80.2885L5.46826 71.3976C5.85698 71.1794 6.24974 70.9571 6.6371 70.7335C41.4688 50.6615 64.7525 12.3051 65.9766 10.2871L81.3079 19.1781C80.0824 21.1987 56.7987 59.5538 21.967 79.6258C21.5796 79.848 21.1882 80.0717 20.7982 80.2885Z" fill="#E0E0E0"/>
                    <path d="M109.74 67.2939C109.791 85.4799 97.1482 107.026 81.5618 116.008C65.7083 125.143 53.2505 118.056 53.1979 99.8752C53.148 82.2994 65.5234 60.297 81.3769 51.1608C92.5444 44.7257 102.243 46.7706 106.863 54.9125" fill="#1B9142"/>
                    <path d="M68.6293 121.232C65.7625 121.232 63.1198 120.565 60.7902 119.227C55.1215 115.977 51.9861 109.104 51.9591 99.8799C51.9078 81.6036 64.8312 59.2712 80.7577 50.0906C87.0635 46.4534 93.1533 45.2747 98.3942 46.6999C102.443 47.8005 105.743 50.4287 107.941 54.3017C108.022 54.4432 108.075 54.5994 108.095 54.7613C108.116 54.9231 108.104 55.0875 108.061 55.2448C108.017 55.4021 107.943 55.5493 107.842 55.6778C107.741 55.8064 107.616 55.9137 107.474 55.9936C107.187 56.1504 106.85 56.1892 106.536 56.1019C106.221 56.0146 105.952 55.808 105.788 55.5262C103.92 52.2419 101.139 50.0151 97.7423 49.0924C93.1655 47.8557 87.7262 48.9428 81.9967 52.2419C66.7518 61.0157 54.3886 82.3863 54.4385 99.8705C54.4615 108.177 57.1568 114.285 62.0279 117.088C66.9368 119.903 73.6529 119.141 80.9426 114.933C96.1942 106.152 108.552 84.7801 108.501 67.2973C108.5 67.1346 108.531 66.9734 108.593 66.8228C108.655 66.6722 108.746 66.5353 108.86 66.4199C108.975 66.3045 109.112 66.2129 109.262 66.1503C109.413 66.0877 109.574 66.0554 109.737 66.0552C110.065 66.0552 110.38 66.185 110.612 66.416C110.844 66.6471 110.975 66.9606 110.976 67.2878C111.029 85.878 98.3807 107.746 82.1789 117.077C77.3956 119.836 72.7783 121.232 68.6293 121.232Z" fill="black"/>
                    <path d="M95.6519 67.5859L77.0409 99.7591L67.2866 94.2103" fill="#E0E0E0"/>
                    <path d="M77.0415 100.996C76.8256 100.995 76.6135 100.938 76.4261 100.831L66.6718 95.2826C66.5278 95.2036 66.4009 95.0968 66.2986 94.9685C66.1964 94.8401 66.1208 94.6927 66.0762 94.5348C66.0316 94.377 66.019 94.2118 66.039 94.049C66.0591 93.8862 66.1114 93.7291 66.1929 93.5867C66.2745 93.4443 66.3836 93.3195 66.514 93.2197C66.6444 93.1199 66.7933 93.0469 66.9522 93.0052C67.1111 92.9634 67.2768 92.9536 67.4395 92.9764C67.6022 92.9992 67.7588 93.0541 67.9 93.1379L76.584 98.0778L94.5795 66.9662C94.6608 66.8257 94.769 66.7025 94.898 66.6036C95.027 66.5048 95.1742 66.4323 95.3312 66.3903C95.4882 66.3483 95.652 66.3375 95.8132 66.3587C95.9744 66.3798 96.1299 66.4325 96.2707 66.5136C96.4115 66.5947 96.535 66.7027 96.634 66.8314C96.733 66.9601 96.8056 67.1071 96.8477 67.2638C96.8898 67.4205 96.9006 67.584 96.8794 67.7449C96.8582 67.9058 96.8055 68.061 96.7242 68.2015L78.1132 100.377C78.0049 100.565 77.8488 100.722 77.6606 100.83C77.4724 100.939 77.2589 100.996 77.0415 100.996Z" fill="black"/>
                    <path d="M81.3078 19.1787L65.9766 10.2878C67.2088 10.8899 90.6099 22.3485 125.432 2.27918C125.821 2.05556 126.208 1.8279 126.6 1.59619L141.931 10.4871C141.538 10.7188 141.151 10.9452 140.762 11.1701C105.934 31.2408 82.5387 19.7822 81.3078 19.1787Z" fill="#1B9142"/>
                    <path d="M20.7982 80.2885L5.46826 71.3976C5.85698 71.1794 6.24974 70.9571 6.6371 70.7335C41.4688 50.6615 64.7525 12.3051 65.9766 10.2871L81.3079 19.1781C80.0824 21.1987 56.7987 59.5538 21.967 79.6258C21.5796 79.848 21.1882 80.0717 20.7982 80.2885Z" fill="#1B9142"/>
                    <path d="M54.2895 146.924L55.2343 146.803L55.6014 147.611C55.6014 147.731 55.399 149.643 54.9387 153.257C57.2764 149.755 58.4709 147.993 58.5748 147.869L59.1997 147.269L60.2957 147.416L60.5049 148.013L59.884 149.686L55.565 154.088L59.8921 153.568L60.5211 154.462L60.3146 155.298L59.1714 156.735L58.5951 156.893C58.4911 156.893 57.2899 156.508 54.9401 155.648C55.4192 158.783 55.6325 160.455 55.6325 160.575L55.2707 161.799L54.3354 162.757L53.4487 162.85L53.0289 162.074C53.0289 161.956 53.284 160.013 53.7429 156.339C51.4066 159.9 50.2121 161.665 50.1068 161.784L49.5345 162.294L48.3346 162.207L48.1767 161.52L48.7989 159.966L53.118 155.504L48.7868 156.085L48.1592 155.19L48.313 154.324L49.4562 152.887L50.0812 152.766C50.1864 152.766 51.3863 153.151 53.7361 153.951C53.2583 150.876 52.9925 149.236 52.9925 149.115L53.4055 147.859L54.2895 146.924Z" fill="black"/>
                    <path d="M72.411 136.487L73.3558 136.364L73.7229 137.173C73.7229 137.291 73.5204 139.204 73.0615 142.818C75.3466 139.348 76.5411 137.582 76.6963 137.43L77.2686 136.861L78.4171 136.977L78.5751 137.604L78.0001 139.246L73.6311 143.676L78.0109 143.127L78.5872 144.051L78.4333 144.859L77.2901 146.295L76.7165 146.446C76.56 146.477 75.3668 146.092 73.0629 145.203C73.5407 148.336 73.7539 150.008 73.7539 150.128L73.3922 151.354L72.4569 152.313L71.5188 152.434L71.1504 151.626C71.1504 151.506 71.3542 149.594 71.8131 145.921C69.528 149.45 68.3335 151.215 68.177 151.364L67.6573 151.844L66.4575 151.758L66.2982 151.072L66.8678 149.547L71.2367 145.056L66.8556 145.669L66.2793 144.745L66.4332 143.878L67.5764 142.442L68.15 142.351C68.3065 142.321 69.5078 142.705 71.805 143.535C71.3258 140.461 71.1126 138.789 71.1126 138.671L71.4743 137.445L72.411 136.487Z" fill="black"/>
                    <path d="M90.475 126.074L91.4198 125.953L91.7883 126.761C91.7883 126.879 91.5845 128.791 91.1256 132.405C93.4633 128.903 94.6578 127.141 94.7617 127.017L95.3852 126.417L96.4812 126.564L96.6918 127.161L96.0709 128.834L91.7519 133.236L96.079 132.716L96.7079 133.611L96.5014 134.448L95.3582 135.884L94.7846 136.034C94.6807 136.034 93.4795 135.651 91.1296 134.791C91.6088 137.924 91.822 139.596 91.822 139.716L91.4603 140.941L90.5236 141.898L89.6382 141.991L89.2185 141.215C89.2185 141.097 89.4736 139.154 89.9325 135.481C87.5948 139.041 86.4003 140.806 86.2964 140.926L85.7241 141.435L84.5242 141.349L84.3663 140.662L84.9872 139.107L89.3062 134.647L84.9791 135.226L84.3501 134.332L84.5053 133.465L85.6485 132.029L86.2734 131.908C86.3787 131.908 87.5786 132.292 89.9284 133.092C89.4493 130.018 89.1847 128.377 89.1847 128.256L89.5977 127.001L90.475 126.074Z" fill="black"/>
                    <path d="M108.603 115.632L109.489 115.541L109.909 116.315C109.909 116.435 109.654 118.376 109.246 121.961C111.531 118.491 112.726 116.725 112.882 116.572L113.455 116.004L114.602 116.12L114.761 116.746L114.139 118.421L109.82 122.822L114.148 122.303L114.776 123.196L114.622 124.004L113.426 125.471L112.905 125.591C112.749 125.622 111.548 125.237 109.25 124.348C109.677 127.512 109.943 129.153 109.943 129.273L109.528 130.529L108.646 131.457L107.701 131.579L107.334 130.771C107.334 130.651 107.536 128.74 107.995 125.066C105.71 128.595 104.464 130.391 104.36 130.51L103.796 131.028L102.648 130.911L102.488 130.22L103.058 128.695L107.426 124.204L103.047 124.813L102.472 123.889L102.626 123.021L103.769 121.585L104.343 121.493C104.447 121.493 105.693 121.848 107.996 122.678C107.519 119.603 107.305 117.932 107.305 117.813L107.667 116.587L108.603 115.632Z" fill="black"/>
                    <path d="M109.74 67.2939C109.791 85.4799 97.1482 107.026 81.5618 116.008C65.7083 125.143 53.2505 118.056 53.1979 99.8752C53.148 82.2994 65.5234 60.297 81.3769 51.1608C92.5444 44.7257 102.243 46.7706 106.863 54.9125" fill="#1B9142"/>
                    <path d="M140.318 307.743C140.458 307.661 140.582 307.553 140.68 307.424C140.779 307.295 140.851 307.148 140.893 306.991C140.935 306.834 140.945 306.67 140.924 306.509C140.902 306.348 140.849 306.193 140.767 306.052C140.685 305.912 140.577 305.789 140.447 305.69C140.318 305.592 140.17 305.52 140.013 305.478C139.856 305.436 139.692 305.426 139.531 305.448C139.369 305.469 139.214 305.522 139.073 305.604L126.066 313.148C123.465 314.662 122.542 316.179 122.542 317.019C122.542 317.86 123.487 319.426 126.151 320.965C135.978 326.561 135.623 326.593 136.248 326.593C137.506 326.593 137.964 324.917 136.868 324.287C129.753 319.995 126.366 318.663 125.15 317.022C125.409 316.678 129.138 314.442 140.318 307.743Z" fill="black"/>
                    <path d="M145.5 332.111C145.687 332.221 145.901 332.278 146.118 332.278C146.391 332.278 146.656 332.189 146.873 332.023C147.09 331.858 147.246 331.626 147.316 331.362C147.387 331.099 147.369 330.82 147.264 330.569C147.159 330.317 146.974 330.107 146.738 329.972L142.033 327.263C141.892 327.181 141.737 327.129 141.575 327.107C141.414 327.086 141.25 327.097 141.093 327.139C140.936 327.181 140.789 327.253 140.66 327.352C140.531 327.45 140.423 327.573 140.341 327.714C140.26 327.854 140.207 328.009 140.186 328.17C140.165 328.331 140.175 328.495 140.217 328.651C140.259 328.808 140.332 328.955 140.431 329.084C140.53 329.212 140.653 329.321 140.794 329.402L145.5 332.111Z" fill="black"/>
                    <path d="M450.447 305.268L440.948 299.793C440.663 299.629 440.325 299.585 440.008 299.669C439.691 299.754 439.421 299.961 439.256 300.244C439.092 300.528 439.047 300.865 439.132 301.182C439.217 301.498 439.424 301.768 439.709 301.932L449.208 307.407C453.786 310.046 456.311 313.483 456.317 317.086C456.317 317.108 456.292 317.121 456.292 317.144C455.68 323.946 449.92 326.296 443.628 329.993L295.261 416.048C286.18 421.314 270.958 421.831 260.671 416.626C260.274 416.424 259.83 416.27 259.456 416.051L104.377 326.69C103.642 326.243 102.935 325.751 102.262 325.216C94.6524 319.441 96.0992 312.149 104.26 307.41L137.345 288.219C137.488 288.139 137.614 288.031 137.716 287.902C137.817 287.773 137.892 287.625 137.936 287.467C137.979 287.308 137.991 287.143 137.97 286.98C137.948 286.817 137.895 286.661 137.813 286.519C137.73 286.377 137.62 286.252 137.489 286.153C137.358 286.054 137.209 285.982 137.049 285.941C136.89 285.9 136.724 285.892 136.562 285.915C136.399 285.939 136.242 285.995 136.102 286.08L103.016 305.269C97.7256 308.341 94.8143 312.49 94.8008 316.962C94.8008 316.962 94.8008 316.962 94.8008 316.97C94.8021 316.984 94.8021 316.997 94.8008 317.011C94.7833 326.789 94.7455 346.95 94.7482 345.665C94.7482 350.183 97.7013 354.394 103.073 357.52L258.154 446.875C268.733 452.953 285.92 452.97 296.478 446.875L394.139 390.228C394.423 390.062 394.63 389.791 394.714 389.474C394.798 389.156 394.752 388.818 394.587 388.534C394.421 388.25 394.149 388.044 393.831 387.96C393.513 387.876 393.175 387.922 392.89 388.087L295.225 444.739C290.173 447.655 283.482 449.049 276.833 448.979V422.736C284.841 422.829 292.053 420.768 296.524 418.186L409.544 352.628C456.835 325.158 452.158 328.188 454.843 325.512C455.335 325.021 455.881 324.545 456.281 324.03L456.238 345.788C456.238 349.328 453.76 352.74 449.264 355.391L411.042 377.55C410.764 377.718 410.562 377.989 410.482 378.303C410.401 378.618 410.448 378.952 410.612 379.233C410.776 379.513 411.044 379.719 411.358 379.804C411.672 379.889 412.007 379.848 412.291 379.689L450.514 357.516C455.804 354.396 458.711 350.228 458.711 345.78L458.769 317.221C458.832 311.954 455.061 307.927 450.447 305.268Z" fill="black"/>
                    <path d="M81.5146 200.332C81.6778 200.334 81.8397 200.303 81.9907 200.241C82.1418 200.18 82.279 200.089 82.3944 199.973C82.5098 199.858 82.601 199.721 82.6627 199.571C82.7243 199.42 82.7553 199.258 82.7537 199.095V197.02C82.7603 197.02 82.7669 197.019 82.773 197.016C82.7791 197.014 82.7847 197.01 82.7894 197.005C82.7941 197.001 82.7977 196.995 82.8002 196.989C82.8026 196.983 82.8038 196.976 82.8036 196.97L124.327 138.067C136.318 121.104 143.404 100.827 143.358 83.7151L143.161 10.5385C143.161 10.5183 143.174 10.5021 143.174 10.4833C143.173 10.2675 143.116 10.0557 143.007 9.86887C142.899 9.68202 142.744 9.52652 142.558 9.41769L127.225 0.526741C127.034 0.415371 126.817 0.357231 126.596 0.358416C126.376 0.359601 126.159 0.420068 125.97 0.533476L124.818 1.20703C119.024 4.54518 113.181 7.20439 107.442 9.11459C107.288 9.16598 107.145 9.24722 107.022 9.35367C106.899 9.46011 106.799 9.58968 106.726 9.73497C106.579 10.0284 106.555 10.3681 106.659 10.6793C106.763 10.9905 106.986 11.2477 107.28 11.3943C107.574 11.541 107.915 11.5651 108.227 11.4613C114.122 9.49852 120.12 6.76927 126.055 3.35029L126.603 3.02833L139.469 10.486L139.295 10.5816C124.917 18.6926 108.164 23.6634 91.9289 21.0271C83.3529 19.6032 81.3419 17.7402 75.688 14.4896C80.7899 15.3827 87.5991 15.7936 95.8093 14.5084C95.97 14.4835 96.1242 14.4272 96.2631 14.3427C96.402 14.2582 96.5229 14.1473 96.6187 14.0161C96.7146 13.885 96.7837 13.7363 96.822 13.5786C96.8603 13.4208 96.867 13.257 96.8418 13.0967C96.7877 12.7744 96.6094 12.4861 96.3451 12.2932C96.0808 12.1004 95.7514 12.0184 95.4273 12.0648C78.8436 14.6634 68.4428 10.1155 66.5235 9.17926C66.2293 9.03242 65.1603 9.24796 64.9174 9.64805C63.65 11.7415 56.7571 22.8403 45.5397 35.7875C45.3387 36.0371 45.2426 36.3549 45.2716 36.6739C45.3006 36.9928 45.4525 37.2881 45.6952 37.4976C45.938 37.7071 46.2527 37.8144 46.5731 37.7969C46.8935 37.7795 47.1947 37.6386 47.4131 37.404C57.5021 25.7555 64.1494 15.5673 66.4007 11.957L79.5873 19.6046C50.4716 64.2452 21.4058 78.3724 20.8375 78.8466L7.94657 71.4052C17.1475 66.0167 26.3646 58.8864 35.3374 50.2016C35.4544 50.0886 35.5479 49.9538 35.6127 49.8047C35.6774 49.6557 35.7122 49.4954 35.7149 49.333C35.7175 49.1705 35.6882 49.0092 35.6284 48.8581C35.5686 48.707 35.4796 48.5691 35.3664 48.4524C35.2532 48.3356 35.1181 48.2422 34.9688 48.1776C34.8195 48.1129 34.6588 48.0783 34.4961 48.0756C34.3334 48.0729 34.1717 48.1022 34.0203 48.1619C33.8689 48.2216 33.7308 48.3104 33.6138 48.4234C18.7482 62.8025 5.37134 69.9799 4.86385 70.318C4.67339 70.4251 4.5145 70.5803 4.40315 70.768C4.29181 70.9558 4.23191 71.1695 4.22949 71.3876C4.22949 71.9049 4.32127 126.745 4.37526 127.244C4.38916 127.522 4.49938 127.786 4.68704 127.992C4.79392 128.138 4.93316 128.258 5.09396 128.343C5.25476 128.427 5.4328 128.473 5.61428 128.477C5.77693 128.477 5.93795 128.445 6.08813 128.382C6.23831 128.32 6.37471 128.229 6.48954 128.114C6.60436 127.999 6.69535 127.862 6.7573 127.712C6.81926 127.562 6.85096 127.401 6.85061 127.239L6.77503 93.6958L6.73049 73.57L19.5742 81.0141L19.6296 100.922L19.747 153.545C19.747 153.99 19.8172 154.391 19.828 154.831C19.9494 160.936 21.5164 170.172 26.0015 176.718C23.1982 173.974 8.57283 171.911 7.04632 147.877C6.97748 146.807 6.8938 145.748 6.88975 144.634C6.88832 144.307 6.75715 143.994 6.52495 143.763C6.29274 143.533 5.9784 143.403 5.65073 143.403C5.536 143.403 5.46312 143.496 5.35919 143.526C5.15973 143.576 4.97776 143.68 4.83274 143.825C4.68772 143.971 4.58511 144.153 4.53587 144.353C4.50753 144.458 4.41575 144.531 4.41575 144.644C4.41575 145.832 4.51293 146.963 4.58716 148.106C5.12704 156.395 7.47822 163.436 11.3586 168.651C13.367 171.345 15.733 173.611 18.512 175.233L33.8392 184.124C33.8797 184.148 33.9256 184.14 33.9742 184.159C34.0228 184.177 34.0848 184.223 34.1415 184.252C38.7197 186.862 39.8238 186.295 80.2729 197.11V199.087C80.2761 199.416 80.4077 199.73 80.6398 199.962C80.8718 200.195 81.1858 200.327 81.5146 200.332ZM40.3879 184.167C27.389 180.842 22.3627 167.151 22.252 153.505C21.9929 54.1459 22.082 88.4138 22.0631 81.0006C23.1131 80.1924 44.7448 69.4909 69.1946 38.479C71.18 35.9491 80.335 23.6769 81.8116 20.7644C96.4653 26.9854 114.515 22.8484 114.019 22.8888C124.864 20.5987 132.415 17.1932 140.679 12.6508L140.877 83.7191C140.927 100.337 133.994 120.119 122.344 136.636L81.3135 194.847L40.3879 184.167Z" fill="black"/>
                    <path d="M81.5671 204.537C81.2401 204.541 80.9277 204.673 80.6964 204.904C80.4651 205.135 80.333 205.447 80.3281 205.773V212.944C80.3281 213.272 80.4587 213.586 80.691 213.818C80.9234 214.05 81.2385 214.181 81.5671 214.181C81.8958 214.181 82.2109 214.05 82.4433 213.818C82.6756 213.586 82.8062 213.272 82.8062 212.944V205.773C82.7926 205.45 82.6577 205.143 82.4282 204.914C82.1987 204.685 81.8914 204.55 81.5671 204.537Z" fill="black"/>
                    <path d="M81.6632 269.571C81.8263 269.572 81.9882 269.541 82.1392 269.48C82.2903 269.418 82.4275 269.327 82.5429 269.212C82.6583 269.097 82.7495 268.96 82.8112 268.809C82.8729 268.658 82.9038 268.497 82.9022 268.334V261.159C82.9077 260.994 82.8797 260.828 82.8199 260.674C82.7602 260.519 82.6698 260.378 82.5543 260.259C82.4388 260.139 82.3004 260.045 82.1474 259.98C81.9944 259.915 81.83 259.882 81.6638 259.882C81.4977 259.882 81.3332 259.915 81.1802 259.98C81.0273 260.045 80.8889 260.139 80.7734 260.259C80.6578 260.378 80.5675 260.519 80.5077 260.674C80.448 260.828 80.42 260.994 80.4255 261.159V268.33C80.4231 268.493 80.4535 268.655 80.5148 268.806C80.5761 268.958 80.6671 269.095 80.7824 269.211C80.8977 269.326 81.0351 269.418 81.1863 269.479C81.3376 269.541 81.4997 269.572 81.6632 269.571Z" fill="black"/>
                    <path d="M81.6633 255.722C81.9904 255.718 82.3027 255.586 82.534 255.355C82.7653 255.124 82.8974 254.812 82.9023 254.486L82.8524 247.315C82.8579 247.149 82.8299 246.984 82.7701 246.829C82.7104 246.675 82.62 246.534 82.5045 246.414C82.389 246.295 82.2506 246.2 82.0976 246.136C81.9446 246.071 81.7802 246.038 81.614 246.038C81.4479 246.038 81.2834 246.071 81.1304 246.136C80.9775 246.2 80.8391 246.295 80.7236 246.414C80.608 246.534 80.5177 246.675 80.4579 246.829C80.3982 246.984 80.3702 247.149 80.3757 247.315L80.4256 254.486C80.4238 254.649 80.4546 254.81 80.5161 254.961C80.5777 255.112 80.6687 255.249 80.784 255.364C80.8993 255.479 81.0364 255.57 81.1874 255.632C81.3384 255.693 81.5002 255.724 81.6633 255.722Z" fill="black"/>
                    <path d="M81.5671 228.027H81.6171C81.7775 228.028 81.9364 227.996 82.0841 227.933C82.2318 227.871 82.3653 227.779 82.4764 227.664C82.5876 227.548 82.6741 227.411 82.7308 227.261C82.7875 227.112 82.8131 226.952 82.8062 226.792V219.62C82.8062 219.292 82.6756 218.977 82.4433 218.746C82.2109 218.514 81.8958 218.383 81.5671 218.383C81.2385 218.383 80.9234 218.514 80.691 218.746C80.4587 218.977 80.3281 219.292 80.3281 219.62V226.792C80.3754 227.482 80.9206 228.027 81.5671 228.027Z" fill="black"/>
                    <path d="M81.6147 232.231C81.2876 232.236 80.9753 232.368 80.744 232.599C80.5127 232.83 80.3806 233.142 80.3757 233.468V240.639C80.3702 240.805 80.3982 240.97 80.4579 241.124C80.5177 241.279 80.608 241.42 80.7236 241.539C80.8391 241.659 80.9775 241.753 81.1304 241.818C81.2834 241.883 81.4479 241.916 81.614 241.916C81.7802 241.916 81.9446 241.883 82.0976 241.818C82.2506 241.753 82.389 241.659 82.5045 241.539C82.62 241.42 82.7104 241.279 82.7701 241.124C82.8299 240.97 82.8579 240.805 82.8524 240.639V233.468C82.8475 233.142 82.7156 232.83 82.4846 232.6C82.2535 232.369 81.9416 232.237 81.6147 232.231Z" fill="black"/>
                    <path d="M84.2891 275.807L82.9246 276.592V275.02C82.9231 274.693 82.7921 274.379 82.5599 274.148C82.3278 273.917 82.0135 273.787 81.6856 273.786C81.5229 273.786 81.3619 273.818 81.2117 273.881C81.0615 273.943 80.9251 274.034 80.8103 274.149C80.6955 274.264 80.6045 274.401 80.5425 274.551C80.4806 274.701 80.4489 274.862 80.4492 275.024V278.734C80.4499 278.951 80.5077 279.164 80.6167 279.351C80.7258 279.538 80.8823 279.694 81.0705 279.802C81.2587 279.91 81.4721 279.967 81.6893 279.967C81.9064 279.966 82.1197 279.909 82.3078 279.801L85.5254 277.946C85.8097 277.782 86.017 277.512 86.1018 277.196C86.1866 276.879 86.1419 276.542 85.9776 276.258C85.8133 275.974 85.5428 275.767 85.2257 275.683C84.9085 275.598 84.5706 275.643 84.2864 275.807H84.2891Z" fill="black"/>
                    <path d="M164.958 232.182L171.302 228.521C171.443 228.441 171.567 228.334 171.666 228.206C171.765 228.078 171.837 227.931 171.879 227.774C171.92 227.618 171.93 227.455 171.908 227.294C171.886 227.134 171.831 226.98 171.749 226.84C171.673 226.694 171.568 226.566 171.44 226.463C171.312 226.361 171.164 226.286 171.005 226.244C170.846 226.202 170.68 226.194 170.518 226.22C170.356 226.246 170.201 226.306 170.063 226.396L163.719 230.006C163.437 230.173 163.232 230.445 163.147 230.761C163.063 231.078 163.107 231.416 163.269 231.701C163.432 231.986 163.699 232.196 164.015 232.286C164.331 232.376 164.67 232.338 164.958 232.182Z" fill="black"/>
                    <path d="M121.057 254.636L114.714 258.294C114.118 258.592 113.92 259.383 114.267 259.977C114.439 260.25 114.708 260.448 115.021 260.531C115.334 260.613 115.666 260.574 115.951 260.421L122.295 256.761C122.57 256.594 122.768 256.325 122.848 256.013C122.927 255.701 122.88 255.371 122.718 255.093C122.556 254.815 122.291 254.611 121.98 254.526C121.67 254.44 121.338 254.48 121.056 254.636H121.057Z" fill="black"/>
                    <path d="M133.296 247.563L126.952 251.222C126.811 251.302 126.688 251.41 126.589 251.538C126.49 251.667 126.417 251.813 126.376 251.97C126.334 252.127 126.324 252.29 126.347 252.45C126.369 252.611 126.423 252.765 126.506 252.905C126.678 253.178 126.947 253.376 127.26 253.459C127.573 253.541 127.905 253.502 128.19 253.349L134.534 249.689C134.674 249.609 134.798 249.501 134.897 249.373C134.995 249.245 135.068 249.098 135.109 248.942C135.15 248.785 135.16 248.622 135.138 248.462C135.116 248.302 135.062 248.147 134.979 248.008C134.684 247.419 133.89 247.217 133.296 247.563Z" fill="black"/>
                    <path d="M108.817 261.658L102.474 265.318C102.187 265.491 101.98 265.769 101.896 266.092C101.813 266.416 101.86 266.759 102.027 267.048C102.309 267.612 103.093 267.802 103.711 267.494L110.055 263.834C110.196 263.753 110.319 263.646 110.418 263.518C110.517 263.389 110.589 263.243 110.63 263.086C110.672 262.93 110.682 262.767 110.659 262.606C110.637 262.446 110.583 262.292 110.5 262.153C110.335 261.872 110.069 261.665 109.757 261.574C109.444 261.482 109.108 261.512 108.817 261.658Z" fill="black"/>
                    <path d="M98.2134 269.175C97.9165 268.581 97.1242 268.384 96.529 268.731L90.1854 272.389C90.0443 272.47 89.9207 272.577 89.8217 272.706C89.7227 272.834 89.6504 272.981 89.6089 273.137C89.5674 273.294 89.5576 273.457 89.5802 273.618C89.6027 273.778 89.657 273.933 89.74 274.072C90.1597 274.746 90.9358 274.88 91.4244 274.517L97.768 270.906C98.0541 270.733 98.2609 270.455 98.3441 270.131C98.4274 269.808 98.3804 269.465 98.2134 269.175Z" fill="black"/>
                    <path d="M138.794 245.832C139.099 246.44 139.943 246.678 140.479 246.277L146.822 242.617C147.417 242.32 147.616 241.53 147.269 240.936C147.188 240.795 147.081 240.672 146.952 240.573C146.823 240.475 146.676 240.403 146.519 240.361C146.362 240.32 146.199 240.31 146.038 240.332C145.877 240.355 145.723 240.409 145.583 240.491L139.24 244.15C139.099 244.23 138.975 244.338 138.877 244.466C138.778 244.595 138.706 244.742 138.664 244.898C138.623 245.055 138.613 245.218 138.635 245.378C138.657 245.539 138.712 245.693 138.794 245.832Z" fill="black"/>
                    <path d="M152.72 239.202L159.063 235.592C159.35 235.419 159.557 235.141 159.641 234.817C159.724 234.494 159.677 234.15 159.51 233.861C159.429 233.72 159.322 233.597 159.193 233.499C159.064 233.4 158.917 233.328 158.76 233.287C158.603 233.245 158.44 233.235 158.279 233.258C158.119 233.28 157.964 233.334 157.824 233.416L151.481 237.076C151.34 237.157 151.217 237.264 151.118 237.393C151.019 237.521 150.947 237.668 150.905 237.824C150.864 237.98 150.854 238.144 150.876 238.304C150.899 238.464 150.953 238.618 151.035 238.758C151.455 239.431 152.231 239.571 152.72 239.202Z" fill="black"/>
                    <path d="M177.233 225.102L180.452 223.247C180.596 223.168 180.722 223.06 180.823 222.931C180.925 222.803 181 222.655 181.044 222.497C181.088 222.339 181.099 222.174 181.079 222.011C181.058 221.849 181.005 221.692 180.923 221.55C180.841 221.408 180.731 221.283 180.6 221.184C180.469 221.085 180.32 221.013 180.161 220.972C180.002 220.93 179.837 220.921 179.674 220.945C179.511 220.968 179.355 221.024 179.214 221.108L175.994 222.962C175.72 223.131 175.523 223.4 175.445 223.712C175.367 224.023 175.413 224.353 175.574 224.631C175.735 224.91 175.998 225.114 176.308 225.202C176.618 225.29 176.949 225.254 177.233 225.102Z" fill="black"/>
                    <path d="M110.977 67.2899C110.976 66.9628 110.845 66.6497 110.613 66.4189C110.38 66.1882 110.066 66.0586 109.738 66.0586C109.41 66.0597 109.095 66.1908 108.863 66.4231C108.632 66.6555 108.502 66.9701 108.502 67.298C108.553 84.7835 96.1915 106.153 80.9439 114.937C73.6556 119.139 66.9341 119.903 62.0292 117.093C57.1582 114.297 54.4628 108.183 54.4399 99.8806C54.3913 82.3951 66.7545 61.0231 81.9994 52.2372C90.9533 47.0818 100.834 46.803 105.791 55.5269C105.868 55.6724 105.974 55.8009 106.102 55.9049C106.23 56.0089 106.377 56.0863 106.536 56.1326C106.694 56.1788 106.86 56.1929 107.024 56.1741C107.188 56.1552 107.346 56.1038 107.49 56.0229C107.634 55.9419 107.76 55.8331 107.861 55.7028C107.962 55.5725 108.035 55.4233 108.078 55.2641C108.12 55.1049 108.13 54.9388 108.107 54.7757C108.084 54.6126 108.028 54.4558 107.943 54.3145C103.408 46.3113 93.4273 42.7926 80.7577 50.0913C64.8312 59.2705 51.9078 81.6057 51.9591 99.8779C51.9861 109.103 55.1215 115.973 60.7902 119.228C67.4469 123.051 75.5694 120.89 82.1803 117.073C98.3821 107.744 111.03 85.8747 110.977 67.2899Z" fill="black"/>
                    <path d="M94.5798 66.9693L76.5842 98.0769L67.9002 93.1357C67.6139 92.9731 67.2745 92.9308 66.9569 93.0179C66.6392 93.1051 66.3692 93.3146 66.2064 93.6004C66.0435 93.8863 66.0011 94.225 66.0884 94.542C66.1757 94.8591 66.3857 95.1285 66.672 95.2911C76.7691 100.966 76.4398 101.001 77.0418 101.001C78.4104 101.001 77.2699 101.342 96.7244 68.2114C96.8767 67.9283 96.9127 67.5971 96.8246 67.2881C96.7366 66.979 96.5314 66.7162 96.2527 66.5555C95.9739 66.3948 95.6434 66.3486 95.3311 66.4269C95.0189 66.5051 94.7494 66.7016 94.5798 66.9747V66.9693Z" fill="white"/>
                    <path d="M449.924 382.578L295.871 471.944C285.613 477.83 269.063 477.83 258.754 471.944L103.712 382.578C93.454 376.643 93.4054 367.048 103.563 361.159L107.676 358.785L258.757 445.828C269.063 451.713 285.616 451.713 295.821 445.828L445.762 358.839L449.776 361.163C460.082 367.043 460.082 376.643 449.924 382.578Z" fill="#E0E0E0"/>
                    <path d="M381.348 299.047L240.127 380.402C238.622 381.276 236.913 381.737 235.172 381.737C233.431 381.737 231.721 381.276 230.216 380.402L192.512 358.543C189.242 356.614 189.242 351.865 192.562 349.986L199.698 345.881L202.175 344.448L228.536 329.264L229.775 328.522L229.873 358.097C229.873 363.24 233.541 365.371 238.05 362.746L372.427 285.297L373.666 286.04L381.348 290.485C384.667 292.37 384.618 297.118 381.348 299.047Z" fill="#E0E0E0"/>
                    <path d="M207.186 190.227V162.342L193.004 170.507C188.446 173.127 184.829 179.359 184.829 184.552L185.274 332.226C185.274 334.799 186.219 336.579 187.703 337.469L232.25 363.334C230.766 362.443 229.871 360.614 229.871 358.091L229.425 210.366C229.425 205.223 233.043 198.991 237.552 196.37L248.215 190.222L207.186 190.227Z" fill="#E0E0E0"/>
                    <path d="M184.928 216.752C185.255 216.747 185.567 216.615 185.798 216.384C186.03 216.153 186.162 215.842 186.167 215.516L186.068 185.298L228.236 209.681C228.194 209.926 228.178 210.174 228.186 210.422C228.31 253.025 228.413 288.016 228.533 329.266L228.633 358.094C228.628 358.674 228.678 359.254 228.781 359.825V359.875L202.172 344.445L188.347 336.383C187.158 335.74 186.514 334.257 186.514 332.228L186.365 299.934C186.36 299.607 186.228 299.296 185.997 299.065C185.765 298.834 185.453 298.702 185.126 298.697C184.794 298.714 184.481 298.856 184.251 299.095C184.021 299.334 183.891 299.652 183.887 299.984L184.036 332.228C184.036 335.147 185.126 337.421 187.059 338.51L199.695 345.878L231.605 364.376H231.655C231.752 364.473 233.304 365.117 234.38 365.117C235.717 365.117 237.155 364.672 238.642 363.831L375.353 285.048C380.212 282.229 384.126 275.454 384.073 269.915V269.865L429.214 295.879C429.396 295.98 429.601 296.031 429.809 296.027C430.027 296.032 430.243 295.98 430.434 295.876C430.625 295.771 430.785 295.619 430.899 295.433C431.057 295.15 431.099 294.816 431.016 294.503C430.933 294.189 430.731 293.92 430.453 293.752L384.073 266.997L383.825 177.235C383.831 177.069 383.803 176.904 383.743 176.749C383.683 176.594 383.593 176.453 383.477 176.334C383.362 176.215 383.223 176.12 383.07 176.055C382.917 175.99 382.753 175.957 382.587 175.957C382.421 175.957 382.256 175.99 382.103 176.055C381.95 176.12 381.812 176.215 381.696 176.334C381.581 176.453 381.49 176.594 381.431 176.749C381.371 176.904 381.343 177.069 381.348 177.235C381.348 177.765 381.595 270.498 381.595 269.916C381.645 274.615 378.177 280.549 374.114 282.873L237.403 361.655C235.619 362.694 234.029 362.891 232.894 362.249H232.844C231.786 361.404 231.109 360.536 231.109 358.094L231.011 327.827C230.976 323.815 229.942 209.735 231.551 205.869C231.501 205.819 231.501 205.819 231.551 205.77C231.752 205.151 232.017 204.554 232.343 203.99C232.586 203.02 235.098 199.283 238.147 197.462L374.858 118.681C375.106 118.533 375.398 118.385 375.651 118.236C376.031 118.06 376.43 117.927 376.84 117.84C377.179 117.742 377.529 117.692 377.881 117.691C378.079 117.691 378.277 117.741 378.475 117.741C378.525 117.741 378.525 117.741 378.575 117.79C378.871 117.837 379.157 117.938 379.417 118.087C380.556 118.731 381.151 120.214 381.151 122.241L381.3 157.207C381.305 157.533 381.437 157.845 381.669 158.076C381.9 158.307 382.212 158.438 382.539 158.444C383.183 158.444 383.778 157.85 383.778 157.157L383.631 122.241C383.631 119.278 382.589 117.048 380.661 115.961L342.203 93.6556L342.153 50.2785C342.053 12.4246 312.565 -5.43411 280.116 10.808C279.822 10.9609 279.601 11.2232 279.5 11.5379C279.398 11.8527 279.426 12.1945 279.576 12.4892C279.723 12.775 279.977 12.9916 280.283 13.0924C280.589 13.1931 280.922 13.1701 281.212 13.0281C311.816 -2.34922 339.581 14.1058 339.682 50.2691C339.682 50.7998 339.73 128.745 339.73 128.162V128.606C339.411 128.752 339.08 128.867 338.74 128.953C333.197 130.888 323.606 131.062 316.838 128.36V50.3364C316.838 31.1293 306.421 24.814 289.238 34.6587C280.666 39.5555 272.49 47.5183 265.602 57.6566C265.509 57.7859 265.443 57.9324 265.408 58.0875C265.372 58.2427 265.368 58.4033 265.396 58.56C265.424 58.7167 265.483 58.8662 265.57 58.9998C265.656 59.1334 265.769 59.2483 265.901 59.3378C266.171 59.5257 266.504 59.5998 266.828 59.5441C267.153 59.4884 267.442 59.3073 267.634 59.0401C274.224 49.3476 282.345 41.4347 290.427 36.7858C305.7 28.0605 314.36 32.8145 314.36 50.3364V99.1451L252.814 134.655V101.527C252.814 93.0696 255.093 83.5239 259.354 73.8314C259.653 73.2374 259.354 72.4951 258.711 72.1987C258.117 71.9509 257.373 72.2486 257.076 72.8413C252.665 82.8315 250.337 92.7786 250.337 101.527V180.064C243.25 182.54 234.378 182.437 228.039 180.113C227.84 180.064 227.692 179.965 227.499 179.915V101.569C227.45 73.1323 245.045 39.0624 269.321 20.5598C269.458 20.4655 269.576 20.3443 269.665 20.2036C269.755 20.0629 269.815 19.9055 269.842 19.741C269.869 19.5765 269.863 19.4082 269.823 19.2462C269.784 19.0842 269.711 18.932 269.611 18.7985C269.511 18.6651 269.385 18.5532 269.24 18.4697C269.096 18.3862 268.936 18.3327 268.77 18.3125C268.604 18.2923 268.436 18.3058 268.275 18.3522C268.115 18.3985 267.965 18.4768 267.836 18.5822C242.962 37.524 224.975 72.4399 225.025 101.564V150.675L192.37 169.468C187.465 172.238 183.597 178.914 183.597 184.602L183.696 215.511C183.694 215.674 183.724 215.836 183.785 215.986C183.846 216.137 183.936 216.274 184.051 216.39C184.166 216.505 184.302 216.597 184.453 216.659C184.604 216.721 184.765 216.753 184.928 216.752ZM225.014 178.918C224.002 178.486 221.775 177.148 220.356 175.654C220.306 175.654 220.306 175.654 220.306 175.605C218.997 174.238 218.163 172.486 217.928 170.609C217.887 170.516 217.87 170.414 217.878 170.313V170.065C217.978 167.147 220.058 164.378 223.725 162.202C224.122 161.954 224.567 161.756 225.014 161.51V178.918ZM252.818 161.163C256.945 163.222 260.815 166.168 260.201 170.758C259.645 174.934 256.615 177.123 252.818 179.017V161.163ZM342.209 109.581C345.908 111.12 349.921 114.409 349.988 118.533C349.988 122.035 347.205 124.872 344.141 126.644V126.692C343.566 126.98 343.095 127.313 342.209 127.682V109.581ZM314.355 127.286C314.108 127.187 313.859 127.038 313.612 126.94C309.698 124.715 307.567 121.845 307.517 118.829C307.517 114.936 311.161 111.59 314.355 110.224V127.286Z" fill="black"/>
                    <path d="M352.559 279.264C352.329 279.403 352.15 279.613 352.048 279.862C351.946 280.11 351.927 280.385 351.994 280.646C352.06 280.906 352.209 281.139 352.418 281.308C352.626 281.478 352.884 281.576 353.153 281.589C353.377 281.593 353.598 281.542 353.797 281.441L359.794 277.979L370.744 271.647C370.94 271.541 371.103 271.383 371.217 271.192C371.33 271 371.389 270.781 371.388 270.559C371.388 270.051 371.282 246.523 371.289 248.007C371.284 247.68 371.152 247.369 370.921 247.138C370.689 246.907 370.377 246.775 370.05 246.77C369.406 246.82 368.813 247.364 368.813 248.057C368.813 248.564 368.918 271.297 368.911 269.866L357.312 276.544L352.559 279.264Z" fill="black"/>
                    <path d="M327.04 194.15C326.744 193.611 325.95 192.717 325.455 192.321C320.251 187.128 311.878 188.414 305.386 192.221C285.17 203.844 271.84 241.728 290.62 249.789H290.67C292.206 250.327 293.639 250.679 295.278 250.679C295.426 285.793 295.278 283.22 295.476 285.001C295.476 285.101 295.526 285.199 295.526 285.297C295.773 287.326 296.471 288.261 297.409 289.551H297.458C299.836 292.37 303.801 291.727 307.022 289.946C312.572 286.782 317.23 278.917 317.23 272.341L317.13 238.663C317.67 238.02 319.856 235.498 320.251 234.756C320.45 234.558 320.598 234.312 320.791 234.114C324.21 229.019 326.093 226.053 328.769 217.991C330.107 213.787 330.652 208.446 330.552 207.703C331.005 204.338 329.419 196.524 327.04 194.15ZM314.796 227.088C314.399 227.829 314.002 228.522 313.557 229.214C312.015 231.598 310.274 233.848 308.354 235.94C308.242 236.05 308.154 236.181 308.095 236.325C308.035 236.47 308.005 236.625 308.007 236.781L308.107 270.906C308.107 276.594 303.944 283.962 298.493 286.682C297.404 284.11 298.048 285.347 297.751 249.343C297.751 248.848 297.211 248.107 296.412 248.107C288.137 248.75 284.173 241.776 284.173 233.319C284.173 212.994 303.002 191.43 315.39 191.43C319.255 193.706 321.435 198.552 321.435 205.129C321.441 212.003 318.964 220.016 314.796 227.088Z" fill="black"/>
                    <path d="M155.644 276.198C155.724 276.338 155.831 276.462 155.96 276.561C156.088 276.659 156.235 276.732 156.392 276.773C156.549 276.815 156.712 276.825 156.873 276.802C157.034 276.78 157.188 276.726 157.328 276.643L183.782 261.312L183.832 282.974C183.827 283.139 183.855 283.304 183.914 283.459C183.974 283.614 184.064 283.755 184.18 283.874C184.295 283.993 184.434 284.088 184.587 284.153C184.74 284.218 184.904 284.251 185.07 284.251C185.237 284.251 185.401 284.218 185.554 284.153C185.707 284.088 185.845 283.993 185.961 283.874C186.076 283.755 186.167 283.614 186.226 283.459C186.286 283.304 186.314 283.139 186.309 282.974C186.12 233.574 186.222 253.632 186.16 232.528C186.162 232.365 186.131 232.204 186.069 232.053C186.008 231.902 185.916 231.765 185.801 231.65C185.686 231.535 185.548 231.444 185.397 231.382C185.246 231.321 185.084 231.29 184.921 231.292C184.597 231.305 184.29 231.44 184.06 231.669C183.831 231.898 183.696 232.205 183.682 232.528L183.782 258.443L156.084 274.516C155.943 274.597 155.82 274.705 155.722 274.833C155.624 274.962 155.552 275.109 155.511 275.265C155.47 275.421 155.461 275.585 155.483 275.745C155.506 275.905 155.561 276.059 155.644 276.198Z" fill="black"/>
                    <path d="M370.029 238.814C370.357 238.812 370.671 238.68 370.902 238.447C371.134 238.215 371.263 237.901 371.263 237.573L371.195 216.281C371.194 215.953 371.063 215.64 370.831 215.409C370.599 215.178 370.284 215.049 369.956 215.049C369.628 215.051 369.314 215.183 369.083 215.416C368.852 215.648 368.722 215.962 368.723 216.29L368.785 237.585C368.786 237.747 368.819 237.908 368.882 238.057C368.945 238.207 369.036 238.343 369.152 238.457C369.268 238.571 369.405 238.661 369.555 238.722C369.706 238.784 369.867 238.815 370.029 238.814Z" fill="black"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M260.67 416.629C265.379 419.011 271.122 420.195 276.836 420.273L276.833 420.273V451.44C269.955 451.411 263.396 449.89 258.153 446.878L103.073 357.523C97.7008 354.397 94.7477 350.186 94.7477 345.668C94.7465 346.215 94.7527 342.874 94.7616 338.101C94.7735 331.666 94.7903 322.628 94.8003 317.014C94.8016 317 94.8016 316.987 94.8003 316.973V316.965C94.8138 312.493 97.7251 308.344 103.016 305.272C103.016 305.272 135.96 286.167 136.101 286.083C136.242 285.998 136.398 285.942 136.561 285.918C136.724 285.895 136.89 285.903 137.049 285.944C137.208 285.985 137.358 286.057 137.489 286.156C137.62 286.255 137.73 286.38 137.812 286.521C137.895 286.663 137.948 286.82 137.969 286.983C137.99 287.146 137.979 287.311 137.935 287.469C137.892 287.628 137.817 287.776 137.715 287.905C137.614 288.034 137.488 288.142 137.344 288.222L104.259 307.413C96.0988 312.152 94.6519 319.444 102.261 325.219C102.935 325.754 103.641 326.246 104.376 326.693L259.456 416.053C259.674 416.181 259.915 416.287 260.158 416.393C260.331 416.469 260.505 416.544 260.67 416.629Z" fill="#1B9142"/>
                    </g>
                    <defs>
                    <filter id="filter0_d_12399_134" x="0.229492" y="0.358398" width="462.54" height="484" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                    <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                    <feOffset dy="4"/>
                    <feGaussianBlur stdDeviation="2"/>
                    <feComposite in2="hardAlpha" operator="out"/>
                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12399_134"/>
                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12399_134" result="shape"/>
                    </filter>
                    </defs>
                  </svg>
                </div>
              </div>


                <div class="row mt-4 px-2">
                  <div class="col-12">
                    <label class="label-permission">
                      With your permission, <strong>CanPay</strong> uses trusted <strong>Bank Linking Providers</strong> to securely access, process and validate your selected bank account(s) for CanPay purchases.
                    </label>
                  </div>
                </div>
                <div class="row mt-4">
                  <div class="col-12 cp-bg-greyshade">
                    <div class="div-encrypted px-2">
                      <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 68 58" fill="none">
                      <path d="M53.385 17.2424C52.898 12.9932 51.0034 9.04375 47.9189 5.93755C44.2952 2.33998 39.5057 0.358154 34.4318 0.358154C24.6545 0.358154 16.5703 7.75269 15.4797 17.2424C7.37917 17.8089 0.961914 24.5813 0.961914 32.8235C0.961914 40.4828 6.65245 47.1103 14.1984 48.2391C15.4143 48.4232 16.5518 47.5821 16.7348 46.3629C16.9179 45.1449 16.0779 44.0085 14.8587 43.8265C9.48083 43.0214 5.42457 38.2907 5.42457 32.8235C5.42457 26.6721 10.4298 21.6669 16.5812 21.6669H17.5858C18.818 21.6669 19.8171 20.6678 19.8171 19.4356C19.8171 11.3775 26.3738 4.82081 34.4318 4.82081C38.3192 4.82081 41.9931 6.34177 44.7637 9.0928C47.5256 11.8743 49.0466 15.5482 49.0466 19.4356C49.0466 20.6678 50.0457 21.6669 51.2779 21.6669H52.2825C58.4339 21.6669 63.4391 26.6721 63.4391 32.8235C63.4391 38.2907 59.3828 43.0214 54.005 43.8265C52.7858 44.0085 51.9458 45.1449 52.1288 46.3629C52.2944 47.4699 53.2456 48.2641 54.3318 48.2641C54.4419 48.2641 54.553 48.2565 54.6652 48.2391C62.2112 47.1103 67.9018 40.4828 67.9018 32.8235C67.9018 24.5813 61.4845 17.81 53.385 17.2424Z" fill="black"/>
                      <path d="M34.4315 23.9788C29.2029 23.9788 24.9483 28.2334 24.9483 33.462V35.6933H22.023C20.7907 35.6933 19.7916 36.6924 19.7916 37.9246V55.1368C19.7916 56.369 20.7907 57.3681 22.023 57.3681H46.84C48.0722 57.3681 49.0713 56.369 49.0713 55.1368V37.9246C49.0713 36.6924 48.0722 35.6933 46.84 35.6933H43.9146V33.462C43.9146 28.2334 39.6601 23.9788 34.4315 23.9788ZM29.411 33.462C29.411 30.6935 31.663 28.4415 34.4315 28.4415C37.1999 28.4415 39.452 30.6935 39.452 33.462V35.6933H29.411V33.462ZM44.6087 52.9054H24.2543V40.1559H44.6087V52.9054Z" fill="black"/>
                      </svg>
                    <span class="span-encrypted ml-1 store-font">Your provided data is encrypted.</span>
                    </div>
                    <div class="div-permission px-2">
                      <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 56 65" fill="none">
                      <path d="M54.0781 8.22853C49.4213 8.22853 45.1512 5.4938 43.1996 1.2616C42.8932 0.59702 42.2282 0.171387 41.4964 0.171387H14.8549C14.123 0.171387 13.458 0.59702 13.1516 1.2616C11.2 5.4938 6.92988 8.22853 2.27305 8.22853C1.23722 8.22853 0.397461 9.06829 0.397461 10.1041V25.7703C0.397461 32.5619 2.17789 39.2691 5.54607 45.1667C8.91437 51.0643 13.7866 56.0056 19.6365 59.4563L27.2226 63.9313C27.5166 64.1047 27.846 64.1914 28.1755 64.1914C28.505 64.1914 28.8345 64.1047 29.1284 63.9313L36.7146 59.4563C42.5643 56.0055 47.4367 51.0643 50.805 45.1667C54.1733 39.2691 55.9536 32.5619 55.9536 25.7703V10.1041C55.9537 9.06817 55.1139 8.22853 54.0781 8.22853ZM52.2025 25.7703C52.2025 38.2267 45.5377 49.8964 34.8088 56.2253L28.1756 60.1382L21.5424 56.2253C10.8136 49.8964 4.14876 38.2267 4.14876 25.7703V11.8665C9.11318 11.2655 13.5476 8.3083 16.0027 3.92256H40.3486C42.8038 8.3083 47.2381 11.2656 52.2025 11.8665V25.7703Z" fill="black"/>
                      <path d="M28.1755 10.0544C18.2962 10.0544 10.259 18.0979 10.259 27.9846C10.259 34.4406 13.6866 40.1099 18.8158 43.2676C18.8998 43.3272 18.9881 43.381 19.0813 43.4273C21.7488 45.0067 24.858 45.9149 28.1757 45.9149C31.4933 45.9149 34.6024 45.0067 37.27 43.4273C37.3632 43.381 37.4516 43.3272 37.5355 43.2676C42.6647 40.1099 46.0923 34.4406 46.0923 27.9846C46.0921 18.0979 38.0548 10.0544 28.1755 10.0544ZM22.1839 40.5733C22.9499 37.9716 25.377 36.0753 28.1755 36.0753C30.9741 36.0753 33.4012 37.9716 34.1671 40.5733C32.3511 41.4428 30.3192 41.9304 28.1755 41.9304C26.0319 41.9304 23.9999 41.4428 22.1839 40.5733ZM24.7344 28.6451C24.7344 26.7451 26.2781 25.1995 28.1755 25.1995C30.0729 25.1995 31.6167 26.7452 31.6167 28.6451C31.6167 30.5451 30.0729 32.0908 28.1755 32.0908C26.2781 32.0908 24.7344 30.5451 24.7344 28.6451ZM37.5703 38.2734C37.011 36.9755 36.1828 35.7969 35.1233 34.8149C34.6602 34.3857 34.1625 34.0043 33.6377 33.672C34.8558 32.3479 35.6012 30.5823 35.6012 28.645C35.6012 24.5479 32.27 21.2149 28.1755 21.2149C24.0811 21.2149 20.7499 24.548 20.7499 28.645C20.7499 30.5823 21.4953 32.3479 22.7133 33.672C22.1886 34.0043 21.6909 34.3857 21.2278 34.8149C20.1683 35.7968 19.34 36.9755 18.7808 38.2734C15.994 35.7217 14.2434 32.0541 14.2434 27.9846C14.2434 20.295 20.4933 14.0389 28.1754 14.0389C35.8575 14.0389 42.1074 20.295 42.1074 27.9846C42.1076 32.0541 40.3569 35.7217 37.5703 38.2734Z" fill="black"/>
                      </svg>
                    <span class="ml-1 store-font span-encrypted">Your data will only be used with your permission.</span>
                    </div>
                  </div>
                </div>
                <div class="row mt-4 px-2">
                  <div class="col-12 terms-privacy">
                    <span class="float-left"
                      >By continuing, you agree to the
                        <a href="https://www.canpaydebit.com/about-us/terms-conditions/" class="" target="_blank" >CanPay Terms and Conditions</a>
                        and
                        <a href="https://www.canpaydebit.com/about-us/privacy/" class="" target="_blank" >Privacy Policy</a>
                    </span>
                  </div>
                </div>
              <div class="row px-2">
                <div class="col-12">
                  <button
                    type="button"
                    class="btn-login terms-next"
                    v-on:click="termsNext(true)"
                  >
                  Continue
                  </button>
                </div>
              </div>
              <div class="row mt-2">
                <div class="col-12 terms-privacy text-center" style="font-weight: 600; font-size: 14px !important;">
                    <label>Secure Connection</label>
                </div>
            </div>
            </div>
          </div>
        </b-modal>
      
        <!-- Account already exists Modal-->
        <b-modal
          ref="sign-in-modal"
          hide-footer
          v-b-modal.modal-center
          modal-backdrop
          hide-header
          id="sign-in-modal"
          centered
        >
          <div class="color">
            <div class="purchaserpower-modal-text">
              <div class="d-block text-center">
                <label class="purchasepower-def-label">
                  {{ error_message }}
                </label>
              </div>
              <div class="row mt-3">
                <div class="col-12">
                  <button
                    type="button"
                    class="btn-login btn-get-started"
                    @click="clickSignIn"
                  >
                    Sign In
                  </button>
                </div>
              </div>
            </div>
          </div>
        </b-modal>
        <!-- Show modal for consumer to report problem due to unable to find their bank details. -->
        <b-modal
          ref="bank-report-modal"
          hide-footer
          v-b-modal.modal-center
          modal-backdrop
          id="bank-report-modal"
          centered
        >
        <div class="row" v-if="bankMissingReportDivNumber == 1">
          <div class="col-12">
            <!-- Option to select reason for report -->
              <b-form-select 
                v-model="selectedValue"
                :options="availableReportOptions"
                :state="stateSelectedValue"
                aria-describedby="option-error"
              ></b-form-select>
              <span v-if="selectedValue == '0' && (stateSelectedValue == true || stateSelectedValue == null)" class="required-class-font">
                *Required
              </span>
                <span v-if="stateSelectedValue == false && selectedValue =='0'" class="error-red">
                  &nbsp;Select an option
                </span>
             <!-- If consumer select other then show textarea to specify the reason -->
                <b-form-textarea
                  class="mt-3 custom-input"
                  id="textarea-state"
                  v-if="selectedValue =='Other'"
                  :state="stateOfSpecificReportReason"
                  v-model="specificReportReason"
                  placeholder="Specify your reason for report"
                  no-resize
                  rows="3"
                ></b-form-textarea>
                <span v-if="specificReportReason == '' && selectedValue =='Other' && (stateOfSpecificReportReason == true || stateOfSpecificReportReason == null)" class="required-class-font">
                  *Required
                </span>
                <span v-if="stateOfSpecificReportReason == false && selectedValue =='Other'" class="error-red">
                  &nbsp;Please specify your reason for report
                </span>
                <!-- Take routing number from consumer -->
                <div class="custom-dropdown mt-3">
                  <b-form-input  class="custom-input" v-model="reportedRoutingNumber" @keypress="OnlyNumber($event)" maxlength="9"  @input="updateOptions" :state="stateOfReportedRoutingNumber" placeholder="Routing Number" ></b-form-input>
                  <div class="drop-style">
                  <ul v-if="showDropdown" class="dropdown-options" >
                    <li class="dropdown-list" v-for="(option, index) in routingNumberOptions" :key="index" @click="selectOption(option)">
                      <span class="ml-2">{{ option.routing_no }}</span>
                    </li>
                  </ul>
                  </div>
                </div>
                <span v-if="reportedRoutingNumber == '' && (stateOfReportedRoutingNumber == true || stateOfReportedRoutingNumber == null)" class="required-class-font">
                  *Required
                </span>
                <span v-if="stateOfReportedRoutingNumber == false" class="error-red">
                  &nbsp;Enter valid routing number
                </span>
              <!-- Take Bank name from consumer -->
               <b-form-input   class="mt-3 custom-input" v-model="reportedBankName" :state="stateOfReportedBankName" placeholder="Bank Name" aria-describedby="bank-name-error" ></b-form-input>
                <span v-if="reportedBankName == '' && (stateOfReportedBankName == true || stateOfReportedBankName == null)" class="required-class-font">
                  *Required
                </span>
                <span v-if="stateOfReportedBankName == false" class="error-red">
                  &nbsp;Bank name is required
                </span>
                <!-- Take bank URL from consumer -->
                <b-form-input  class="mt-3 custom-input" @keypress="NoSpace($event)" v-model="reportedBankUrl" :state="reportedBankUrl!=''?true:null"  placeholder="Your bank's website URL" ></b-form-input>  
                <span class="required-class-font">
                  Optional but helpful
                </span>                             
          </div>
          <div class="col-12 mt-4 pull-right">
            <button class="button-report" @click="sendReportFromConsumer">Send Report</button>
          </div>
        </div>
        <div class="row" v-if="bankMissingReportDivNumber == 2">
          <div class="col-12 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="80" height="80" viewBox="0 0 197 197" fill="none">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H197V197H0V0Z" fill="url(#pattern0)"/>
              <defs>
                <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
                  <use xlink:href="#image0_0_557" transform="scale(0.********)"/>
                </pattern>
                <image id="image0_0_557" width="197" height="197" xlink:href="data:image/png;base64,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"/>
              </defs>
            </svg>
          </div>
          <div class="col-12 text-center mt-5">
            <p class="success-report-text-font">
              Thank you for reporting.
            </p>
            <p class="success-report-text-font">
              Our support teams are looking into it.
            </p>
          </div>
        </div>

        </b-modal>
			  <skip-banking-solution-modal ref="SkipBankingSolutionModal" :skipRedirection="true"></skip-banking-solution-modal>
      </div>
    </div>
  </div>
</div>
    <!--- IntermediateBankLinkingModal Start --->
    <IntermediateBankLinkingModal ref="IntermediateBankLinkingModal" :directLinkGenerateAPICall="directLinkGenerateAPICall"/>
    <!--- IntermediateBankLinkingModal End --->
</div>
</template>

<script>
/**
 * write a component's description
 * this component is use to login the user as a consumer
 */
import OnboardingHeader from "../OnBoardScreeen/OnboardingHeader.vue";
import constants from "../Common/constant.js";
import api from "../../api/account.js";
import transactionapi from "../../api/transactiondetails.js";
import login_api from "../../api/login.js";
import onboarding_api from "../../api/onboarding.js";
import registration_api from "../../api/registration.js";
import SkipBankingSolutionModal from '../Payment/SkipBankingSolutionModal.vue';
import Loading from "vue-loading-overlay";
import Multiselect from 'vue-multiselect';
import MxConnectWidget from '../MxConnectWidget.vue';
import CanPayLoader from "../CustomLoader/CanPayLoader.vue";
import IntermediateBankLinkingModal from "../Modal/IntermediateBankLinkingModal.vue"
export default {
  name: "BankLinkingOption",
  components: {
    "cp-onboardingheader": OnboardingHeader,
    Loading,
    Multiselect,
    MxConnectWidget,
	  SkipBankingSolutionModal,
    CanPayLoader,
    IntermediateBankLinkingModal
  },
  /**
   * @description-
   * routingNumber => this will take the consumer's phone no
   * routingNumber => this will take the consumer's accountnumber
   * pinColor => use to validate the accountnumber field
   * routingNumberColor => use to validate the phoneno field
   * token => use to store the user's token
   * @returns {any}
   */
  data() {
    return {
      constants: constants,
      fullPage: true,
      isLoading: false,
      isSelectLoading: false,
      show_option: false,
      find_bank: true,
      accountNumber: "",
      routingNumber: "",
      error_message: "",
      check: false,
      show_manual_link_option:false,
      options:['hello','how'],
      frontendVisibilityBanks: [],
      bankList: [],
      selectedBank: '',
      isOnbordingPage: false,
      isRegistrationPage: false,
      isBankUpdatePage: false,
      bank_search_on_registration: true,
      showFloatDiv: false,
      active_return: false,
      sessionId: null,
      currentUser: {},
      manualBankType: '',
      termsChecked: '',
      mxConnectUrl: null,
      event:"",
      searchbankpageparams:"",
      showTerms: true,
      stateOfSpecificReportReason:null,
      skip_current_banking_solution: false,
      availableReportOptions:[
        { value: '0', text: 'Select a reason for report' },
        { value: 'Unable to find bank', text: 'Unable to find bank' },
        { value: 'Incorrect Bank link page', text: 'Incorrect Bank link page' },
        { value: 'Bank Link failing', text: 'Bank Link failing' },
        { value: 'Other', text: 'Other'}
      ],
      selectedValue:'0',
      specificReportReason:'',
      reportedBankName:'',
      reportedRoutingNumber:'',
      reportedBankUrl:'',
      routingNumberTyped:0,
      stateOfReportedRoutingNumber:null,
      stateOfReportedBankName:null,
      stateSelectedValue:null,
      inputValue: '',
      routingNumberOptions: [],
      showDropdown: false,
      selected_id:'',
      bankMissingReportDivNumber:1,
      consumer_type: localStorage.getItem("consumer_login_response")
        ? JSON.parse(localStorage.getItem("consumer_login_response")).consumer_type
        : null,
    };
  },
  props: {
    showBankType: "",
    showSearchBanks: true,
    fromParentComponent:0,
    toogleParentLoader:{
      type:Function
    }
  },
  created() {
    this.currentUser = localStorage.getItem("consumer_login_response")
      ? JSON.parse(localStorage.getItem("consumer_login_response"))
      : null;
  },
  mounted() {
    let self = this;
    if (window.location.href.split("/").splice(-3, 1)[0] == 'onboringconnectbankaccount' ||  window.location.href.split("/").splice(-3, 1)[0] == 'onboardingmanualbankaccount') {
      self.isOnbordingPage = true;
      this.$root.$emit("show_header", false);
      this.$root.$emit("changeWhiteBackground", [true, false, ""]);
      document
        .getElementById("app")
        .style.setProperty("background-color", "#ffffff");

      var element = document.getElementsByClassName("wrapper");
      if (element[0]) {
        element[0].style.setProperty("background-color", "#ffffff");
      }
      var elementHtml = document.getElementsByTagName("html")[0];
      if (elementHtml) {
        elementHtml.style.setProperty("background-color", "#ffffff");
      }
      if (window.location.href.split("/").splice(-3, 1)[0] == 'onboardingmanualbankaccount') {
        self.showBankType = constants.manual;
      }
      self.sessionId = atob(window.location.href.split("/").splice(-2, 1)[0]);
    } else {
      if (localStorage.getItem('consumer_token') != null) {
        this.$root.$emit("changeWhiteBackground", [false, false, "BankLinkingHeader"]);
      } else {
        this.$root.$emit("show_header", 2);
      }
      var element = document.getElementsByClassName("content-wrap");
      if (element[0]) {
        element[0].style.setProperty("background-color", "#149240");
        element[0].style.height = "114vh";
      if(window.innerWidth>1200){
        element[0].style.height = "121vh";
      }
      }
    }
    // registration time no need to show terms and conditions
    if (localStorage.getItem('consumer_token') == null) {
      this.showTerms = false;
    }
    // migration from finicity to other bank
    if (localStorage.getItem('migration_from_finicity_to_other') == 'true') {
      localStorage.removeItem('migration_from_finicity_to_other');
      localStorage.setItem("delink_finicity_banking_solution",true);
      this.loadPageData();
      this.searchBank(localStorage.getItem('bank_search_txt'), true);
    } else if (localStorage.getItem('skip_current_banking_solution') == 'true') {
      this.skipCurrentBankingSolution();
    } else {
      if (this.showTerms == false) {
        this.loadPageData();
        if (this.showSearchBanks == false){
          this.selectedBank = localStorage.getItem("selected_bank") ? JSON.parse(localStorage.getItem("selected_bank")): '';
          this.searchBank(localStorage.getItem('bank_search_txt'));
        } else {
          localStorage.removeItem("bank_search_txt");
        }
      } else {
        this.showTermsModal();
      }
    }
    
  },
  watch: {
    showBankType: function (newval, oldval) {
      if (newval === constants.manual) {
        this.show_manual_link_option = true;
        this.find_bank = false;
        this.show_option = false;
      }
    },
    selectedValue: function (newval, oldval){
      if(newval == '0'){
        if(this.stateSelectedValue == true){
          this.stateSelectedValue = false;
        }
        else this.stateSelectedValue = null;
      }
      else{
        this.stateSelectedValue = true;
      }
    },
    specificReportReason: function (newval, oldval){
      if(newval == ''){
        if(this.stateOfSpecificReportReason == true){
          this.stateOfSpecificReportReason = false;
        }else this.stateOfSpecificReportReason = null;
      }else{
        this.stateOfSpecificReportReason = true;
      }
    },
    reportedRoutingNumber: function (newval, oldval){
      if(newval == ''){
        if(this.routingNumberTyped == 1){
          this.stateOfReportedRoutingNumber = false;
        }
        else this.stateOfReportedRoutingNumber = null;
      }else if(newval.length < 9 ){
        this.routingNumberTyped = 1;
        this.stateOfReportedRoutingNumber = false;
      }else{
        this.stateOfReportedRoutingNumber = true;
        this.showDropdown = false;
      }
    },
    reportedBankName: function (newval, oldval){
      if(newval == ''){
        if(this.stateOfReportedBankName == true){
          this.stateOfReportedBankName = false;
        }else this.stateOfReportedBankName = null;
      }else{
        this.stateOfReportedBankName = true;
      }  
    },
    isLoading: function (newval, oldval){
      console.log(newval);
      if(this.fromParentComponent == 1){
        this.toogleParentLoader(newval);
      }
    }

  },
  beforeRouteEnter(to, from, next) {
    // Store the previous route name when leaving the current route
    to.meta.previousRouteName = from.name;
    next();
  },
  methods: {
    OnlyNumber: function(evt) {
      evt = (evt) ? evt : window.event;
      var charCode = (evt.which) ? evt.which : evt.keyCode;
      if ((charCode > 31 && (charCode < 48 || charCode > 57)) && charCode !== 46) {
        evt.preventDefault();
      } else {
        return true;
      }
    },
    NoSpace: function(evt){
      evt = (evt) ? evt : window.event;
      var charCode = (evt.which) ? evt.which : evt.keyCode;
      if(charCode == 32){
        evt.preventDefault();
      } else {
        return true;
      }
    },
    updateOptions() {
      var self = this;
      if(self.reportedRoutingNumber.length <=3 && self.routingNumberOptions.length>0){
        self.routingNumberOptions = [];
        this.showDropdown = false;
        return;
      }
      if(self.reportedRoutingNumber.length == 9){
        this.showDropDown = false;
        return;
      }

      const payload = {
        'bank_routing_number':self.reportedRoutingNumber
      }
      if(self.reportedRoutingNumber.length>3){
        api
        .searchRoutingNumber(payload)
        .then((response)=>{
          self.routingNumberOptions = response.data;
          if(response.data.length != 0){
              this.showDropdown = true;
          }else{
            this.showDropdown = false;
          }
         
        })
        .catch((error)=>{
          
        })
      }

    },
    selectOption(option) {
      this.reportedRoutingNumber = option.routing_no;
      this.showDropdown = false;
    },
    sendReportFromConsumer(){
      var self = this;
      var failedValidation = false;
      // validation start
      if(self.reportedRoutingNumber.trim().length != 9){
        self.stateOfReportedRoutingNumber = false;
        this.showDropdown = false;
        failedValidation = true;
      }else{
        self.stateOfReportedRoutingNumber = true
      }
      if(self.reportedBankName.trim().length == 0){
        self.stateOfReportedBankName = false; 
        failedValidation = true;
      }else{
        self.stateOfReportedBankName = true;
      }
      if(self.selectedValue == 'Other' && self.specificReportReason.trim().length == 0){
        self.stateOfSpecificReportReason = false;
        failedValidation = true;
      }else if(self.selectedValue == 'Other' && self.specificReportReason.trim().length > 0){
        self.stateOfSpecificReportReason = true
      }
      if(self.selectedValue.length == 0  || self.selectedValue == '0'){
        self.stateSelectedValue = false;
        failedValidation = true;
      }else{
        self.stateSelectedValue = true;
      }
      if(failedValidation){
        return;
      }
      // validation end
      if(self.event == 'registration'){
        self.selected_id = JSON.parse(localStorage.getItem("session_data"));
      }
      else if(self.event == 'onboarding'){
        self.selected_id = JSON.parse(localStorage.getItem("sessionId"));
      }
      else if(self.event == 'bankchange'){
        self.selected_id = self.currentUser.user_id;
      }
      const payload = {
        'event':self.event,
        'current_consumer_id':self.selected_id,
        'bank_url':self.reportedBankUrl,
        'bank_name':self.reportedBankName,
        'routing_no':self.reportedRoutingNumber,
        'reason':self.selectedValue == 'Other'? self.specificReportReason:self.selectedValue
      }
      self.isLoading = true;
      api
      .consumerBankFailureReport(payload)
      .then((response) => {
        self.isLoading = false;
        self.bankMissingReportDivNumber = 2;
        setTimeout(() => {
          self.hideBankReportModal('bank-report-modal')
        },1000);
      })
      .catch((error) => {
        self.isLoading = false;
      })
    },
    skipCurrentBankingSolution() {
      localStorage.removeItem('skip_current_banking_solution');
      this.loadPageData();
      this.findRetryBank();
    },
    handleWidgetEvent(event) {
      let self = this;
      console.log('MX PostMessage: ', event);
      if (event.type === 'mx/connect/memberConnected') {
        self.$router.push({ name: 'mx-success', params: { 'user_guid': event.metadata.user_guid, 'member_guid':event.metadata.member_guid } })
      } else if (event.type === 'mx/connect/stepChange' && event.metadata.current == 'search' && event.metadata.previous == 'enterCreds') {
        self.$router.push({ name: 'BankLinkingOption'})
      } else if (event.type === 'mx/connect/loaded') {
        // Handle widget loading completion
        self.isLoading = false;
      }
    },
    loadPageData() {
      let self = this;
      if (localStorage.getItem('session_data') != null && !self.isOnbordingPage) {
        self.isRegistrationPage = true;
        self.event = 'registration';
        self.searchbankpageparams = JSON.parse(localStorage.getItem("session_data"));
      }else if(self.isOnbordingPage){
        self.event = 'onboarding';
        self.searchbankpageparams = JSON.parse(localStorage.getItem("sessionId"));
      }else{
        self.event = 'bankchange';
        self.searchbankpageparams = self.currentUser.phone;
      }
      if (self.showBankType === constants.manual) {
        self.show_manual_link_option = true;
        self.find_bank = false;
        self.show_option = false;
      } else {
        if (localStorage.getItem('consumer_token') != null && !self.isOnbordingPage) {
          self.bank_search_on_registration = false;
          self.isBankUpdatePage = true;
          self.getUserdetails();
          self.checkMicrobiltErrorExists();
          self.getReturnTransactions();
        } else if (localStorage.getItem('session_data') != null && !self.isOnbordingPage) {
          self.show_manual_link_option = true;
          self.checkStateAge();
        } else {
          self.show_manual_link_option = true;
        }
        self.getFrontendVisibilityBank();
      }
    },
    termsNext(termsChecked) {
      let self = this;
      self.termsChecked = termsChecked;
      if(self.termsChecked != '') {
        self.$refs["terms-privacy-modal"].hide();
        self.loadPageData();
      }
    },
    showTermsModal() {
      this.$refs["terms-privacy-modal"].show();
    },
    showBankReportModal(modal){
      let self = this;
      // 0 is the value assigned to show the option
      // "Please select an reason for reporting"
      self.bankMissingReportDivNumber = 1;
      self.selectedValue = '0';
      self.specificReportReason='';
      self.reportedBankName='';
      self.reportedRoutingNumber='';
      self.reportedBankUrl='';
      self.stateOfReportedRoutingNumber=null;
      self.stateOfReportedBankName=null;
      self.routingNumberTyped = 0;
      self.stateOfSpecificReportReason=null;
      self.stateSelectedValue=null;
      self.showDropdown = false;
      self.$refs[modal].show();
    },
    hideBankReportModal(modal){
      let self = this;
      self.$refs[modal].hide();
    },
    hidetermsModal() {
      this.clickBack();
    },
    clickBack() {
      const previousRouteName = this.$route.meta.previousRouteName;
      if(previousRouteName != '' && previousRouteName != 'success'){
        this.$router.go(-1);
      } else {
        if (localStorage.getItem('consumer_token') != null) {
          this.$router.push("/pay");
        } else {
          this.$router.push("/login");
        }
      }
    },
    hideModal(modal) {
      this.$refs[modal].hide();
    },
    showValidationModal(msg) {
      if (this.skip_current_banking_solution) {
				localStorage.setItem("skip_current_banking_solution", this.skip_current_banking_solution);
        this.skip_current_banking_solution = false;
				this.$bvModal.show("skip-banking-solution-modal");
			} else {
				this.error_message = msg;
				this.$refs["validation-modal"].show();
			}
    },
    hidevalidationModal() {
      if (this.check) {
        this.accountNumber = "";
        this.routingNumber = "";
        this.check = false;
      }
      this.error_message = "";
      this.$refs["validation-modal"].hide();
    },
    checkManualAvailableForThisConsumer(){
      let self = this;
      self.isLoading = true;
      api
        .checkManualAvailableForThisConsumer()
        .then((response) => {
          if (response.code == 200) {
            if(response.data == 0){
              self.show_manual_link_option = localStorage.getItem("can_choose_manual_direct_bank") != null ? true : localStorage.getItem("need_bank_link") != null ? false : localStorage.getItem("microbilt_error_need_bank_link") != null ? false : true;
              console.log('show_manual_link_option ', self.show_manual_link_option);
            }
          }
          self.isLoading = false;
        })
        .catch(function (error) {
          console.log(error);
          self.isLoading = false;
        });
    },
    checkStateAge(){
      var self = this;
      let request = {
        'sessionId': JSON.parse(localStorage.getItem("session_data"))
      }
      login_api
        .checkStateAge(request)
        .then((response) => {
          if (response.code == 200) {
            localStorage.removeItem("session_state");
            localStorage.setItem("session_state", response.data);
            if(response.data == 0){
              self.show_manual_link_option = true;
            } else {
              self.show_manual_link_option = false;
            }
            if(localStorage.getItem("microbilt_error_need_bank_link") != null){
              self.show_manual_link_option = false;
            }
          }
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    checkMicrobiltErrorExists(){
      let self = this;
      self.isLoading = true;
      api
        .checkMicrobiltErrorExists()
        .then((response) => {
          if (response.code == 200) {
            if(response.data){
              self.show_manual_link_option = false;
              localStorage.setItem("microbilt_error_need_bank_link", true);
            } else {
              self.show_manual_link_option = true;
              localStorage.removeItem("microbilt_error_need_bank_link");
            }
            
            self.checkManualAvailableForThisConsumer();
          }
          self.isLoading = false;
        })
        .catch(function (error) {
          console.log(error);
          self.isLoading = false;
        });
    },
    getFrontendVisibilityBank(){
      let self = this;
      self.isLoading = true;
      var request = {
        on_regitration: self.bank_search_on_registration,
      };
      api
        .getFrontendVisibilityBank(request)
        .then((response) => {
          if (response.code == 200) {
            self.frontendVisibilityBanks = response.data;
          }
          self.isLoading = false;
        })
        .catch(function (error) {
          console.log(error);
          self.isLoading = false;
        });
    },
    searchBank(searchtxt, finicity_bank_delink = false){
      if (finicity_bank_delink != true) {
        localStorage.removeItem('delink_finicity_banking_solution');
      }
      let self = this;
      if(searchtxt.length >= 3){
        localStorage.setItem("bank_search_txt", searchtxt);
        self.isSelectLoading = true;
        var request = {
          bank_name: searchtxt,
          on_regitration: self.bank_search_on_registration,
          event:self.event,
          searchbankpageparams:self.searchbankpageparams
        };
        api
          .searchBank(request)
          .then((response) => {
            if (response.code == 200) {
              self.bankList = response.data;
            } else {
              error(response.message);
            }
            self.isSelectLoading = false;
          })
          .catch(function (error) {
            console.log(error);
            self.isSelectLoading = false;
          });
      }
    },
    findRetryBank(){
      let self = this;
      self.isLoading = true;
      self.selectedBank = localStorage.getItem("selected_bank") ? JSON.parse(localStorage.getItem("selected_bank")): '';
      if(self.selectedBank){
        var request = {
          bank_id: self.selectedBank.id,
          batch_id: self.selectedBank.batch_id,
          event:self.event,
          searchbankpageparams:self.searchbankpageparams
        };
        api
          .findBank(request)
          .then((response) => {
            if (response.code == 200) {
              self.afterBankSelect(response.data, true);
            } else {
              error(response.message);
            }
            self.isLoading = false;
          })
          .catch(function (error) {
            self.isLoading = false;
            console.log(error);
          });
      }
    },
    afterBankSelect(selectedOption, show_manual_link_option = false){
      var self = this;
      if (!show_manual_link_option) {
        localStorage.removeItem('delink_finicity_banking_solution');
      }
      self.selectedBank = selectedOption;
      if (self.isBankUpdatePage) {
        self.selectedBank.bank_change_inactive.forEach(bank_solution => {
          self.selectedBank[bank_solution] = 0;
        });
      }
      localStorage.setItem("selected_bank",JSON.stringify(self.selectedBank));
      self.directLinkBankSequence(self.selectedBank, show_manual_link_option);
    },
    directLinkBankSequence(fed_bank, show_manual_link_option = false) {
      let self = this;
      self.show_manual_link_option = show_manual_link_option ? show_manual_link_option : self.show_manual_link_option;
      localStorage.setItem("selected_bank",JSON.stringify(fed_bank));
      let direct_linked = false;
      fed_bank.sequence.forEach(bank_solution => {
        if (fed_bank[bank_solution] == 1 && !direct_linked){
          
          let solution_name = bank_solution.split('_')[1];
          console.log(solution_name);
          self.directLinkGenerate(solution_name,0);
          direct_linked = true;
        }
      });
      if (!direct_linked) {
        if (self.show_manual_link_option) {
          self.find_bank = false;
          self.show_option = false;
        } else {
          alert("Please select another bank");
        }
      }
    },
    directLinkGenerate(banking_solution,intermediate_modal, skip_check = null, widget_mode = null) {
      let self = this;
      this.$refs["banking-solution-modal"].hide();
      this.$refs["finicity-info-modal"].hide();
      if (localStorage.getItem('consumer_token') != null) {
        self.directLinkGenerateAPICall(banking_solution, intermediate_modal, skip_check, widget_mode);
      } else if (self.isRegistrationPage) {
        self.bankUrlConnection(banking_solution);
      } else if (self.isOnbordingPage) {
        self.onboardingBankLinkFinicity(banking_solution);
      }
    },
    directLinkGenerateAPICall(banking_solution,intermediate_modal, skip_check, widget_mode) {
      let self = this;
      var selected_bank = JSON.parse(localStorage.getItem("selected_bank"));
      let request = {};
      if(intermediate_modal == 0){
        request = {
          params: {
            banking_solution: banking_solution,
            bank_id: selected_bank.id,
          }
        };
      }else if(intermediate_modal == 1){
        request = {
          params: {
            banking_solution: banking_solution,
            bank_id: selected_bank.id,
            skip_check: skip_check,
            widget_mode: widget_mode
          }
        };
      }

      self.isLoading = true;
      api
        .directLinkGenerate(request)
        .then((response) => {
          if (response.code == 200) {
            if(banking_solution == 'akoya'){
              self.isLoading = false;
              const akoyaConnectUrl = response.data;
              window.location.href = akoyaConnectUrl;
            }else if(banking_solution == 'mx'){
              self.mxConnectUrl = response.data;
            }else{
              const finicityConnectUrl = response.data.link;
              window.finicityConnect.launch(finicityConnectUrl, {
                selector: "#connect-container",
                overlay: "rgba(255,255,255, 0)",
                success: function (data) {
                  console.log("Yay! We got data", data);
                  if (data.code == 200) {
                    //now store the details at canpay end
                    if (self.currentUser.bank_link_type == 1) {
                      self.updateFinicityBankDetails();
                    } else {
                      self.storeBankDetails();
                    }
                  }
                },
                cancel: function () {
                  self.isLoading = false;
                  console.log("The user cancelled the iframe");
                },
                error: function (err) {
                  self.isLoading = false;
                  console.error(
                    "Some runtime error was generated during Finicity Connect",
                    err
                  );
                },
                loaded: function () {
                  self.isLoading = false;
                  console.log(
                    "This gets called only once after the iframe has finished loading"
                  );
                },
                route: function (event) {
                  self.isLoading = false;
                  console.log(
                    "This is called as the user progresses through Connect"
                  );
                },
              });
            }
          } else {
            self.isLoading = false;
          }
        })
        .catch((err) => {
          self.isLoading = false;
          if(err.response.data.code == 598){
            this.$refs.IntermediateBankLinkingModal.showModal();
          }
          console.log(err);
        });
    },
    bankLinkDirect() {
      let self = this;
      if (self.check) {
        self.check = false;
        this.$refs["validation-modal"].hide();
      }
      if (self.selectedBank.is_akoya == 1 && self.selectedBank.is_finicity == 1) {
        this.$refs["banking-solution-modal"].show();
      } else if (self.selectedBank.is_akoya == 1) {
        self.directLinkGenerate('akoya',0);
      } else if (self.selectedBank.is_finicity == 1) {
        self.directLinkGenerate('finicity',0);
      }
    },

    //stores the bank details into canpay end
    storeBankDetails() {
      var self = this;
      this.isLoading = true;
      var selected_bank = JSON.parse(localStorage.getItem("selected_bank"));
      let request = {
        bank_name: selected_bank.bank_name,
        bank_id: selected_bank.id,
        batch_id: selected_bank.batch_id,
				banking_solution: 'finicity',
      };
      api
        .directLinkBank(request)
        .then((response) => {
          localStorage.setItem(
            "consumer_login_response",
            JSON.stringify(response.data)
          );
          self.$router.push("/profile");
        })
        .catch(function (error) {
          if(error.response.data.code == 597){
            self.generateConnectFix(
              error.response.data.data.institutionId
            );
          }else{
            self.skip_current_banking_solution = error?.response?.data?.data?.skip_current_banking_solution === 1 
														? true 
														: false;
            self.showValidationModal(error.response.data.message);
          }
          self.isLoading = false;
        });
    },
    bankLinkManual() {
      this.show_option = false;
      if (self.isOnbordingPage) {
        this.$router.push("/onboardingmanualbankaccount/"+btoa(this.sessionId)+"/onboardingmanualbankaccount");
      }
    },
    // Manual Bank 
    updateBankDetails() {
      var self = this;
      var numberRegex = /^[0-9\s]+$/;
      var alphanumberRegex = /^[0-9a-zA-Z\s]+$/;

      if (self.manualBankType == '') {
        self.showValidationModal(
          "Please select bank type."
        );
        return false;
      }
      if (
        self.accountNumber != "" &&
        self.routingNumber != "" &&
        self.accountNumber == self.routingNumber
      ) {
        self.check = true;
        self.showValidationModal(
          "Account number and routing number can't be same. Please provide valid banking details or link directly."
        );
        return false;
      } else if (
        (self.accountNumber === "" && self.routingNumber === "") ||
        (!alphanumberRegex.test(self.accountNumber) &&
          !numberRegex.test(self.routingNumber)) ||
        ((self.accountNumber.length > 30 || self.accountNumber.length < 5) &&
          self.routingNumber.length != 9) ||
        (!alphanumberRegex.test(self.accountNumber) &&
          self.routingNumber.length != 9) ||
        (self.accountNumber.indexOf(" ") >= 0 &&
          self.routingNumber.indexOf(" ") >= 0)
      ) {
        self.showValidationModal(
          "Please provide valid routing and account number fields."
        );
        return false;
      } else if (
        self.accountNumber.length > 30 ||
        self.accountNumber.length < 5
      ) {
        self.showValidationModal(
          "Invalid account number (must be minimum 5 characters)"
        );
        return false;
      } else if (self.routingNumber.length != 9) {
        self.showValidationModal("Invalid routing number (must be 9 digits)");
        return false;
      } else if (
        !alphanumberRegex.test(self.accountNumber) ||
        self.accountNumber.indexOf(" ") >= 0
      ) {
        self.showValidationModal("Account number (Alphanumeric only)");
        return false;
      } else if (
        !numberRegex.test(self.routingNumber) ||
        self.routingNumber.indexOf(" ") >= 0
      ) {
        self.showValidationModal("Routing number (numbers only)");
        return false;
      } else if (self.checkAccountNumberContainNumber(self.accountNumber)) {
        self.showValidationModal("Account Number must have minimum 4 numbers");
        return false;
      }
      var selected_bank = JSON.parse(localStorage.getItem("selected_bank"));
      if (localStorage.getItem('consumer_token') != null) {  // From Bank Change
        self.isLoading = true;
        var request = {
          account_type: self.manualBankType,
          accountNumber: self.accountNumber,
          routingNumber: self.routingNumber,
          return: localStorage.getItem("redirect-return") != null ? 1 : 0,
          bank_name: selected_bank.bank_name,
          bank_id: selected_bank.id,
          batch_id: selected_bank.batch_id,
        };
        api
          .storeManualBankDetails(request)
          .then((response) => {
            if (localStorage.getItem("redirect-return") != null) {
              localStorage.removeItem("redirect-return");
              self.getUserdetails();
            } else {
              self.$router.push("/profile");
            }
          })
          .catch(function (error) {
            self.showValidationModal(error.response.data.message);
            self.isLoading = false;
            self.accountNumber = "";
            self.routingNumber = "";
            if(error.response.data.data == 'Error:3001'){
              self.show_manual_link_option = false;
              localStorage.setItem("microbilt_error_need_bank_link", true);
            }
          });
      } else if (self.isRegistrationPage) {  // From registration
        this.$root.$emit("bank_details", [
          self.routingNumber,
          self.accountNumber,
          self.manualBankType,
        ]);
      } else if (self.isOnbordingPage){
        this.isLoading = true;
        var request = {
          account_type: self.manualBankType,
          session_id: self.sessionId,
          routingNumber: self.routingNumber,
          accountNumber: self.accountNumber,
          bank_name: selected_bank.bank_name,
          bank_id: selected_bank.id,
        };
        onboarding_api
          .ValidateBankdetail(request)
          .then((response) => {
            self.isLoading = false;
            if (response.code == 200) {
              this.$router.push("/registrationsuccess");
            }
          })
          .catch((err) => {
            self.isLoading = false;
            self.showValidationModal(err.response.data.message);
            if(err.response.data.data == 'Error:3001'){
              localStorage.setItem("microbilt_error_need_bank_link", true);
            }
          });
      }
      
    },
    getUserdetails(){
      var self = this;
      api
      .getUserdetails()
      .then(function (response) {
        if (response.code == 200) {
          localStorage.setItem(
            "consumer_login_response",
            JSON.stringify(response.data)
          );
          self.currentUser = localStorage.getItem("consumer_login_response")
          ? JSON.parse(localStorage.getItem("consumer_login_response"))
          : null;
        }
      })
      .catch(function (error) {
      });
    },
    enterClicked() {
      this.updateBankDetails();
    },
    isNumber: function (evt) {
      evt = evt ? evt : window.event;
      var charCode = evt.which ? evt.which : evt.keyCode;
      if (charCode < 48 || charCode > 57) {
        evt.preventDefault();
      } else {
        return true;
      }
    },

    isAlphanumeric: function (evt) {
      const regex = /[0-9a-zA-Z]/g;
      evt = evt ? evt : window.event;
      var inputKey = String(evt.key);
      if (!inputKey.match(regex)) {
        evt.preventDefault();
      } else {
        return true;
      }
    },
    checkAccountNumberContainNumber(accountNumber) {
      let checkAccountNumber;
      checkAccountNumber = accountNumber.split("");
      let count;
      count = 0;
      checkAccountNumber.forEach(checkValue);

      function checkValue(value, index, array) {
        if (!isNaN(value)) {
          count++;
        }
      }
      var isFourDigit = count >= 4 ? false : true;
      return isFourDigit;
    },
    

    // Onbording
    onboardingBankLinkFinicity(banking_solution) {
      let self = this;
      this.isLoading = true;
      var selected_bank = JSON.parse(localStorage.getItem("selected_bank"));
      var request = {
        session_id:self.sessionId,
        banking_solution:banking_solution,
        bank_id: selected_bank.id,
      };
      onboarding_api
        .ConnectBank(request)
        .then((response) => {
          if (response.code == 200) {
            if(banking_solution == 'akoya'){
              self.isLoading = false;
              const akoyaConnectUrl = response.data;
              window.location.href = akoyaConnectUrl;
            }else if(banking_solution == 'mx'){
              self.mxConnectUrl = response.data;
            }else{
              const finicityConnectUrl = response.data.link;
              window.finicityConnect.launch(finicityConnectUrl, {
                selector: "#connect-container",
                overlay: "rgba(255,255,255, 0)",
                success: function (data) {
                  console.log("Yay! We got data", data);
                  if (data.code == 200) {
                    self.showFloatDiv = false;
                    self.redirectToSuccess();
                  }
                },
                cancel: function () {
                  self.isLoading = false;
                  self.showFloatDiv = false;
                  console.log("The user cancelled the iframe");
                },
                error: function (err) {
                  self.isLoading = false;
                  self.showFloatDiv = false;
                  console.error(
                    "Some runtime error was generated during Finicity Connect",
                    err
                  );
                },
                loaded: function () {
                  self.isLoading = false;
                  self.showFloatDiv = false;
                  console.log(
                    "This gets called only once after the iframe has finished loading"
                  );
                },
                route: function (event) {
                  
                    self.isLoading = false;
                    self.showFloatDiv = true;
                    document.getElementById("finicityConnectIframe").style.top =
                      "65px";
                    document.getElementById(
                      "finicityConnectIframe"
                    ).style.height = "calc(100vh - 65px)";
                  
                  console.log(
                    "This is called as the user progresses through Connect"
                  );
                },
              });
            }
          } else {
            self.isLoading = false;
            self.pp_alert_message = response.message;
            self.showValidationModal(self.pp_alert_message);
          }
        })
        .catch((err) => {
          self.isLoading = false;
          console.log(err);
        });
    },
    showManual() {
      this.showFloatDiv = false;
      window.finicityConnect.destroy();
      this.$router.push("/onboardingmanualbankaccount/"+btoa(this.sessionId)+"/onboardingmanualbankaccount");
    },
    havingtroubleShowManual() {
      this.showFloatDiv = false;
      window.finicityConnect.destroy();
      this.find_bank = false;
      this.show_option = false;
    },
    redirectToSuccess() {
      let self = this;
      this.isLoading = true;
			var selected_bank = JSON.parse(localStorage.getItem("selected_bank"));
      var request = {
        session_id: this.sessionId,
				bank_name: selected_bank.bank_name,
        bank_id: selected_bank.id,
        batch_id: selected_bank.batch_id,
				banking_solution: 'finicity',
      };
      onboarding_api
        .Finalonboarding(request)
        .then((response) => {
          self.isLoading = true;
          if (response.code == 200) {
            this.$router.push("/registrationsuccess");
          }
        })
        .catch((err) => {
          this.isLoading = false;
          this.pp_alert_message = err.response.data.message;
          if(err.response.data.code == 597){
            self.generateConnectFix(
              err.response.data.data.institutionId
            );
          }
          if(err.response.data.code == 599){
            self.skip_current_banking_solution = err?.response?.data?.data?.skip_current_banking_solution === 1 
														? true 
														: false;
            self.showValidationModal(this.pp_alert_message);
          }
        });
    },


    // Bank Update
    updateFinicityBankDetails() {
      var self = this;
      self.isLoading = true;
      var selected_bank = JSON.parse(localStorage.getItem("selected_bank"));
      var request = {
				bank_name: selected_bank.bank_name,
        bank_id: selected_bank.id,
        batch_id: selected_bank.batch_id,
				banking_solution: 'finicity',
      };
      api
        .updateBank(request)
        .then((response) => {
          const consumer_login_response = JSON.parse(localStorage.getItem("consumer_login_response"));
          consumer_login_response.account_no = response.data.account_no;
          localStorage.setItem("consumer_login_response", JSON.stringify(consumer_login_response));
          self.$router.push("/profile");
        })
        .catch(function (error) {
          if(error.response.data.code == 597){
            self.generateConnectFix(
              error.response.data.data.institutionId,
              error.response.data.data.institutionLoginId
            );
          }else{
            self.skip_current_banking_solution = error?.response?.data?.data?.skip_current_banking_solution === 1 
														? true 
														: false;
            self.showValidationModal(error.response.data.message);
          }
          if(error.response.data.code == 598){
            self.getUserDetails();
          }
          self.isLoading = false;
        });
    },
    getUserDetails() {
      var self = this;
      self.isLoading = true;
      api
        .getUserdetails()
        .then(function (response) {
          if (response.code == 200) {
            localStorage.setItem(
              "consumer_login_response",
              JSON.stringify(response.data)
            );
            self.currentUser = localStorage.getItem("consumer_login_response")
            ? JSON.parse(localStorage.getItem("consumer_login_response"))
            : null;
            self.isLoading = false;
          }
        })
        .catch(function (error) {
          self.isLoading = false;
        });
    },
    generateConnectFix(id, login_id = null) {
      let self = this;
      this.isLoading = true;
      var request = {
        institution_id: id,
        login_id: login_id,
      };
      api
        .generateConnectFix(request)
        .then((response) => {
          if (response.code == 200) {
            const finicityConnectUrl = response.data.link;
            window.finicityConnect.launch(finicityConnectUrl, {
              selector: "#connect-container",
              overlay: "rgba(255,255,255, 0)",
              success: function (data) {
                console.log("Yay! We got data", data);
                if (data.code == 200) {
                  if (localStorage.getItem('consumer_token') != null) {
                    if (self.currentUser.bank_link_type == 1) {
                      self.updateFinicityBankDetails();
                    } else {
                      //now store the details at canpay end
                      self.storeBankDetails();
                    }
                  } else if (self.isRegistrationPage) {
                    self.showFloatDiv = false;
                    self.registration();
                  } else if (self.isOnbordingPage) {
                    self.showFloatDiv = false;
                    self.redirectToSuccess();
                  }
                }
              },
              cancel: function () {
                self.isLoading = false;
                console.log("The user cancelled the iframe");
              },
              error: function (err) {
                console.log(err);
                self.isLoading = false;
                console.error(
                  "Some runtime error was generated during Finicity Connect",
                  err
                );
              },
              loaded: function () {
                self.isLoading = false;
                console.log(
                  "This gets called only once after the iframe has finished loading"
                );
              },
              route: function (event) {
                self.isLoading = false;
                console.log(
                  "This is called as the user progresses through Connect"
                );
              },
              user: function (event) {
                if (event.data.errorCode) {
                  console.log(event.data.data.institutionId);
                  setTimeout(() => {
                    window.finicityConnect.destroy();
                    //if error code is present then call the connect fix api
                    self.generateConnectFix(
                      event.data.data.institutionId,
                      null
                    );
                  }, 2000);
                }
              },
            });
          } else {
            self.isLoading = false;
          }
        })
        .catch((err) => {
          self.isLoading = false;
          console.log(err);
        });
    },
    
    getReturnTransactions() {
      var self = this;
      self.isLoading = true;
      transactionapi
        .getReturnTransactions()
        .then((response) => {
          self.isLoading = false;
          if (response.code == 200 && response.data.length != 0) {
            self.active_return = true;
          } else {
            self.active_return = false;
          }
        })
        .catch(function (error) {
          self.isLoading = false;
          self.active_return = false;
        });
    },


    // Registration
    //API call to verify all details to do the id validation
    bankUrlConnection(banking_solution) {
      let self = this;
      if (localStorage.getItem('session_data')) {
        this.isLoading = true;
        var selected_bank = JSON.parse(localStorage.getItem("selected_bank"));
        var request = {
          sessionId: localStorage.getItem('session_data'),
          banking_solution: banking_solution,
          bank_id: selected_bank.id,
          batch_id: selected_bank.batch_id,
        };
        registration_api
          .connectFinicity(request)
          .then((response) => {
            console.log(response);
            if (response.code == 200) {
              if(banking_solution == 'akoya'){
                self.isLoading = false;
                const akoyaConnectUrl = response.data;
                window.location.href = akoyaConnectUrl;
              }else if(banking_solution == 'mx'){
                self.mxConnectUrl = response.data;
              }else{
                  this.isLoading = false;
                  const finicityConnectUrl = response.data.link;
                  window.finicityConnect.launch(finicityConnectUrl, {
                    selector: "#connect-container",
                    overlay: "rgba(255,255,255, 0)",
                    success: function (data) {
                      console.log("Yay! We got data", data);
                      if (data.code == 200) {
                        self.showFloatDiv = false;
                        self.registration();
                      }
                    },
                    cancel: function () {
                      self.isLoading = false;
                      self.showFloatDiv = false;
                      console.log("The user cancelled the iframe");
                      if(self.show_manual_link){
                        // self.show = constants.manual;
                      }
                    },
                    error: function (err) {
                      self.isLoading = false;
                      self.showFloatDiv = false;
                      console.error(
                        "Some runtime error was generated during Finicity Connect",
                        err
                      );
                      // self.show = constants.manual;
                    },
                    loaded: function () {
                      self.isLoading = false;
                      self.showFloatDiv = false;
                      setTimeout(function() {
                        if(localStorage.getItem("session_state") == 0){
                            self.show_manual_link = true;
                          };
                        }, 1000);
                      console.log(
                        "This gets called only once after the iframe has finished loading"
                      );
                    },
                    route: function (event) {
                      self.isLoading = false;
                      self.showFloatDiv = true;
                      document.getElementById("finicityConnectIframe").style.top =
                        "65px";
                      document.getElementById("finicityConnectIframe").style.height =
                        "calc(100vh - 65px)";
                      console.log(
                        "This is called as the user progresses through Connect"
                      );
                    },
                  });
                }
            }else{
              this.isLoading = false;
            }
          })
          .catch((err) => {
            this.isLoading = false;
            console.log(err);
            self.showValidationModal(err);
          });
      }
    },
    
    registration: function () {
      this.isLoading = true;
      let self = this;
      var selected_bank = JSON.parse(localStorage.getItem("selected_bank"));
      var request = {
        sessionId: localStorage.getItem('session_data'),
        bank_name: selected_bank.bank_name,
        bank_id: selected_bank.id,
        batch_id: selected_bank.batch_id,
				banking_solution: 'finicity',
      };
      registration_api
        .registerUser(request)
        .then((response) => {
          if (response.code == 200) {
            this.isLoading = false;
            localStorage.setItem("user_id", response.data.user_id);
            localStorage.setItem("enrollment", true);
            this.show = "success";
            
            var currentSlug = "registrationsuccess";
            let stateObj = { id: currentSlug };
            window.history.pushState(stateObj,
            currentSlug, currentSlug);

            if (
              typeof response.data.global_radar != "undefined" &&
              response.data.global_radar == 1
            ) {
              self.showValidationModal(
                "Registration Complete. You may now login."
              );
            } else {
							self.$router.push("/successbanklisting");
						}
          }
        })
        .catch((err) => {
          self.isLoading = false;
          if(err.response.data.code == 595){
            self.showSignInModal(err.response.data.message);
          } else if(err.response.data.code!= 597){
            self.skip_current_banking_solution = err?.response?.data?.data?.skip_current_banking_solution === 1 
														? true 
														: false;
            self.showValidationModal(err.response.data.message);
          }
          if (err.response.data.code == 401) {
            self.$router.go();
          }
          if (err.response.data.code == 598) {
            self.$router.push("/login");
          }
          if(err.response.data.code == 597){
            self.generateConnectFix(
              err.response.data.data.institutionId
            );
          }
        });
    },

    validateManualBankAccount(continueWithCodeTwintynine) {
      let self = this;

      if (self.accountNumber === "" && self.routingNumber === "") {
        self.showValidationModal("Please fill in the required fields.");
        return false;
      }
      self.isLoading = true;
      var selected_bank = JSON.parse(localStorage.getItem("selected_bank"));
      var request = {
        sessionId: localStorage.getItem('session_data'),
        accountNumber: self.accountNumber,
        routingNumber: self.routingNumber,
        account_type: self.manualBankType,
        bank_name: selected_bank.bank_name,
        bank_id: selected_bank.id,
        batch_id: selected_bank.batch_id,
      };
      api
        .validateManualBankAccount(request)
        .then((response) => {
          if (response.code == 200) {
            self.isLoading = false;
            localStorage.setItem("user_id", response.data.user_id);
            self.show = "success";

            var currentSlug = "registrationsuccess";
            let stateObj = { id: currentSlug };
            window.history.pushState(stateObj,
            currentSlug, currentSlug);

            if (
              typeof response.data.global_radar != "undefined" &&
              response.data.global_radar == 1
            ) {
              self.showValidationModal(
                "Registration Complete. You may now login."
              );
            }
          }
        })
        .catch((err) => {
          this.isLoading = false;
          if(err.response.data.code == 595){
            self.showSignInModal(err.response.data.message);
          } else if(err.response.data.code != 503) {
            self.showValidationModal(err.response.data.message);
            self.show = constants.select_bank_linking;
          }
          
          if(err.response.data.data == 'Error:3001'){
            localStorage.setItem("microbilt_error_need_bank_link", true);
          }

          if (err.response.data.code == 401) {
            self.$router.go();
          }
          if (err.response.data.code == 598) {
            self.$router.push("/login");
          }
          if(err.response.data.code == 503)
          {
            self.show = constants.microbiltResponceCode29;
            self.continueWithCodeTwintynine = true;
            self.perchasePower = err.response.data.data;
          }
        });
    },

    showSignInModal(message) {
      this.error_message = message;
      this.$refs["sign-in-modal"].show();
    },
    
    clickSignIn() {
      this.error_message = "";
      this.$refs["sign-in-modal"].hide();
      this.$router.push("/login");
    },
  },
};
</script>
<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>
<style>
.label-permission{
  text-align: justify; 
  font-size: 0.9rem !important;
  font-weight:600;
}
.div-encrypted{
  padding: 1.2rem 0rem; 
  border-bottom: 1px solid #646464; 
  font-weight:600;
}
.span-encrypted{
  font-size: 0.8rem !important
}
.div-permission{
  padding: 1.2rem 0rem; 
  font-weight:600;
}
.terms-next{
  background-color: #149240 !important; 
  margin-top: 20px !important;
}
.cp-bg-greyshade{
    background-color:#ECECEC;
}
.terms-privacy{
  font-size: 0.8rem !important;
  font-weight:600;
}
.terms-privacy a{
  color: #1B9146 !important;
}
.preview_bank{
  margin: auto;
  width: 151px;
  height: 88px;
  border-radius: 7px;
}
.bank_last_number{
  border-bottom: 0.01rem solid rgb(255, 255, 255); 
  line-height: 18px; 
  cursor: pointer;
}
#banking-solution-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#finicity-info-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
.btn-transparent {
    background-color: #e6e8e9;
    color: #000;
    border: none;
    padding-top: 10px; /* Remove default padding */
    min-height: 45px; /* Set the height */
    line-height: 22px; /* Center the text vertically */
    margin-top:0;
    height:auto !important;
}

.btn-custom-shadow {
    box-shadow: 0px 2px 10px 4px rgba(0,0,0,.5); /* Customize the shadow effect as needed */
}

.btn-full-width {
    width: 100%; /* Make the button span the full width */
}

/* If you want to remove the default button styles */
.btn-transparent:focus,
.btn-transparent:hover {
    background-color: transparent;
    border: none;
}
#terms-privacy-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
}
#sign-in-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#bank-report-modal___BV_modal_body_{
  background-color: #ffffff;
  border-radius: 12px;
  margin: 2px 10px 10px 10px;
  padding: 2px 16px 16px 16px;
}
#bank-report-modal___BV_modal_content_{
    background-color: #ffffff;
}
.button-report{
  padding: 10px;
  border:0;
  width:100%;
  font-size:14px;
  font-weight:700;
  border-radius:5px;
  background-color:#000;
  color:#fff;
}
#bank-report-modal___BV_modal_header_{
  padding:8px 10px;
  border:none;
}
.report-consumer-text{
  font-family:'Open Sans';
  font-size:13px;
  color:rgb(0, 0, 176);
  font-weight:700;
}
.custom-input::placeholder {
  color: #000000bd;
  font-weight:500;
}
.custom-dropdown {
  width:100%;
  position: relative;
  display: inline-block;
}

.dropdown-options {
  top: 100%;
  left: 0;
  max-height: 150px;
  width: 100%;
  height:100px;
  color:#000!important;
  overflow-y:auto;
  background-color: #fff;
  border-top: none;
  list-style-type: none;
  padding: 0;
  margin: 0;
  border-radius:10px;
  box-shadow: rgba(0, 0, 0, 0.19) 0px 10px 20px, rgba(0, 0, 0, 0.23) 0px 6px 6px;
}

.dropdown-list {
  padding: 3px;
  font-size:14px;
  font-weight:600;
  cursor: pointer;
  font-family:'Montserrat';
  
}

.dropdown-list:hover {
  background-color: #ADE1F5;
}

#inputBox {
    width: 200px;
    padding: 1px;
}
.input-class-to-adjust{
  width:100%;
  border:2px solid #cccccc97;
  padding:5px;
  margin-top:8px;
  border-radius:5px;
}
.error-red{
  color:#DC3545; 
  font-size:14px;
}
.drop-style{
  position:absolute; 
  width:100%; 
  height:20px; 
  border-radius:10px;
}
.required-class-font{
  font-size: 11px;
  font-weight:600;
  font-family:'Open Sans';
  color:#DC3545;
  margin-left:5px;
}
.optional-class-font{
  font-size: 11px;
  font-weight:600;
  font-family:'Open Sans';
  color:#f4533a;
  margin-left:5px; 
}
.success-report-text-font{
  font-size:1rem;
  padding:0px;
  margin:0px;
  font-weight:700;
  font-family: 'Open Sans';
}
</style>
