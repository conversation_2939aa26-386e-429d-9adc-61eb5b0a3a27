.dot {
  height: 180px;
  width: 180px;
  border-radius: 50%;
  background-color: $cp-primary;
  display: inline-block;
  border: 2px solid #fff;
  margin-top: 50px;
}
.edit-apt-number{
  color: $cp-white !important;
  background-color: #149240 !important;
  border: transparent !important;
}
.edit-apt-number::placeholder{
  color: $cp-white !important;
}
.font {
  color: $cp-white;
  text-align: center;
  margin-left: 10px;
  margin-top: 20px;
}

.change-phone-text {
  font-size: 15px !important;
  text-align: left;
  color: blue;
  margin: 10px;
}
.btn-month-style {
  background-color: transparent !important;
  height: 50px !important;
  border-radius: 8px 0px 0px 8px !important;
  border: solid white;
  border-width: 1px 0 1px 1px;
}
.btn-day-style {
  background-color: transparent !important;
  height: 50px !important;
  border-radius: 8px !important;
  border-width: 1px;
}
.input-day-style {
  height: 50px !important;
  background-color: transparent !important;
  color: #ffffff !important;
  text-align: center !important;
  border-radius: 8px !important;
}
.btn-dropdown-style {
  width: 100% !important;
  background-color: transparent !important;
  height: 50px !important;
  border-radius: 8px 8px 8px 8px !important;
  border: solid white;
  border-width: 1px 1px 1px 1px;
}
.btn-group {
  width: 100% !important;
}
.dropdown-toggle {
  float: right !important;
  color: $cp-white;
}
.btn-canpay-green {
  background: $cp-primary;
  border: 1px solid $cp-primary;
}
.btn-align {
  text-align: right;
}
.right-align {
  text-align: left;
}
.curser-pointer {
  cursor: pointer;
}

.update-modal-title {
  font-family: $cp-font;
  font-size: 15px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: #000000;
  src: local("Open Sans Semibold"), local("OpenSans-Semibold"),
    url(http://fonts.gstatic.com/s/opensans/v13/MTP_ySUJH_bn48VBG8sNShampu5_7CjHW5spxoeN3Vs.woff2)
      format("woff2");
}

.dark-icon-box {
  margin-left: 15px;
  width: 40px;
  height: 40px;
  background-color: $cp-secondary !important;
  border-color: $cp-secondary !important;
  border-radius: 5px;
}
.svg-icon-position{
  margin-top: 8px;
}
.dark-icon-box-edit {
  margin-left: 15px;
  width: 30px;
  height: 30px;
  background-color: $cp-secondary !important;
  border-color: $cp-secondary !important;
  border-radius: 5px;
}
.svg-icon-position-edit{
  margin-top: 7px;
}
.style-display{
  display: table !important;
}
.align-middle{
  display: table-cell !important;
  vertical-align: middle !important;
}
.profile-text-style {
  font-family: $cp-font;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: $cp-white;
  float: left;
  margin-left: -11px;
  text-align: initial;
}
.top-row{
  margin-top: 30px;
}
.btn-update{
  height: 33px !important;
  border-radius: 4px !important;
  width: 72% !important;
  border-color: transparent !important;
  background-color: $cp-black !important;
  font-family: $cp-font;
  font-size: 11px;
  color: $cp-white;
  cursor: pointer;
}
.btn-save {
  height: 45px !important;
  width: 100%;
  border-radius: 8px !important;
  border-color: transparent !important;
  background-color: $cp-primary !important;
  font-family: $cp-font;
  font-size: 15px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: #ffffff;
  margin-top: 35px !important;
  cursor: pointer;
  float: right !important;
}
.btn-save-padding{
  padding-left: 160px;
}
.edit-profile-text-style {
  font-family: $cp-font;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left;
  color: #ffffff;
}
.profile-image-margin {
  margin-top: -70px;
  margin-left: 40px !important;
  margin-right: 40px !important;
}

.address-style {
  width: 100% !important;
  color: #ffffff !important;
  border: 0px !important;
  background-color: transparent !important;
  padding-left: 0px !important;
  font-family: $cp-font !important;
  font-size: 14px !important;
  font-weight: normal !important;
  font-stretch: normal !important;
  font-style: normal !important;
  line-height: normal !important;
  letter-spacing: normal !important;
  text-align: left !important;
  color: #ffffff !important;
}
.left-span {
  float: left !important;
}

.left-padding {
  padding-left: 5px !important;
}
.top-margin {
  margin-top: 10px !important;
}

.birth-input{
  color: $cp-white !important;
  background-color: $cp-primary !important;
  border-color: $cp-white;
}
.birth-input-month {
  width: 100% !important;
  height: 40px !important;
  border-radius: 4px 4px 4px 4px !important;
  border-width: 1px;
  margin-left: 5px;
}
#edit-btn-month.dropdown-toggle::after{
  display: inline-block;
  margin-left: .255em;
  vertical-align: .255em;
  content: "";
  border-top: .6em solid;
  border-right: .4em solid transparent;
  border-bottom: 0;
  border-left: .4em solid transparent;
  margin-top: 6px;
  margin-left: 24px;
}
.edit-month-text{
  font-size: 11px;
  margin-top: 25px;
  font-weight: 500;
}
.edit-dob-day{
  margin-left: -22px;
  height: 40px !important;
  width: 58px !important;
  font-size: 11px !important;
  text-align: center;
}
.edit-dob-day::placeholder{
  text-align: center !important;
  font-size: 11px !important;
  color: $cp-white !important; 
  font-family: $cp-font !important;
}
.edit-dob-year{
  margin-left: -11px;
  height: 40px !important;
  width: 58px !important;
  font-size: 11px !important;
  text-align: center !important;
}
.edit-dob-year::placeholder{
  text-align: center !important;
  font-size: 11px !important;
  color: $cp-white !important;
  font-family: $cp-font !important;
}
// --------------------------------------300px-600px----------------------

@media only screen and (min-width: 300px) and (max-width: 359px) {
  .all-text-margin {
    margin-top: 10%;
  }
  .name-text-margin {
    margin-top: 3%;
  }
  .btn-update {
    height: 28px !important;
    font-size: 9px;
    margin-left: 18px;
  }
  .btn-save-padding{
    padding-left: 36px;
  }
  .address-style {
    font-size: 11px !important;
  }
  .edit-profile-text-style {
    font-size: 11px !important;
  }
}

// --------------------------------------300px-600px----------------------

@media only screen and (min-width: 400px) and (max-width: 900px) {
  .all-text-margin {
    margin-top: 15%;
  }
  .name-text-margin {
    margin-top: 3%;
  }
}

// --------------------------------------360px-900px----------------------

@media only screen and (min-width: 360px) and (max-width: 900px) {
  .all-text-margin {
    margin-top: 40px !important;
  }
  .name-text-margin {
    margin-top: 3%;
  }
  .btn-save-padding{
    padding-left: 51px;
  }
}

// --------------------------------------800px-1500px----------------------

@media only screen and (min-width: 800px) and (max-width: 1500px) {
  .all-text-margin {
    margin-top: 5%;
  }
  .name-text-margin {
    margin-top: 3%;
  }
}

// --------------------------------------1024px-1400px----------------------
@media only screen and (min-width: 1024px) and (max-width: 1400px) {
  .all-text-margin {
    margin-top: 10%;
  }
  .name-text-margin {
    margin-top: 3%;
  }
}

@media only screen and (min-width: 360px) and (max-width: 900px) {
  .all-text-margin {
    margin-top: 10%;
  }
  .name-text-margin {
    margin-top: 3%;
  }
}
@media only screen and ( min-width:1024px ){
  .edit-dob-year{
    width: 192px !important;
  }
  .edit-dob-day{
    margin-left: -3px;
    width: 138px !important;
    font-size: 11px !important;
  }
  .edit-month-text{
    font-size: 11px;
    margin-top: 25px;
    font-weight: 100;
  }
  .dark-icon-box-edit {
    margin-left: 120px;
  }
  .top-row{
    margin-top: 40px;
  }
}
#edit-profile-verify___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#validation-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}