.forgot-pin-text-style {
  font-family: $cp-font;
  font-size: 18px;
  font-weight: 500;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: 0.5px;
  text-align: center;
  color: $cp-white;
}

.forgpt-pin-desc {
  font-family: $cp-font;
  font-size: 13px;
  font-weight: 100;
  font-stretch: unset;
  font-style: normal;
  line-height: 14.5px;
  letter-spacing: normal;
  text-align: center;
  color: $cp-white;
}
.form-group {
  width: 100% !important;
  margin-bottom: 0px !important;
}

.form-group .form-control {
  padding-left: 2.575rem !important;
  height: 50px !important;
  border-radius: 6px !important;
  margin-left: 0px !important;
}

.form-group .form-control-icon {
  position: absolute !important;
  z-index: 2;
  display: block;
  width: 2.375rem;
  height: 2.375rem;
  line-height: 2.375rem;
  text-align: center;
  pointer-events: none;
  color: #aaa;
  margin-top: 7px !important;
  padding-left: 3px;
}
.pin-text{
  font-weight: 900 !important;
  font-size: 18px !important;
  color: $cp-black !important;
  text-align: center !important;
}
.pin-text::placeholder{
  text-align: center !important;
}
.error-div-style {
  margin-top: -5px !important;
  margin-left: 50px !important;
  margin-right: 50px !important;
  background-color: $cp-red !important;
  color: $cp-white !important;
  height: 30px !important;
  border-radius: 0px 0px 8px 8px !important;
}
.red-border{
  border-top: 1px solid $cp-red

  ;
  border-right:  1px solid $cp-red

  ; 
  border-left: 1px solid $cp-red

  ;
  border-radius: 8px 8px 0px 0px !important;


}
.no-border{
  border: solid 0px 0px 0px 0px  !important;

}
.error-text-style {
  font-family: $cp-font;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: $cp-white;
  margin-top: 8px !important;
}

.input-box-padding {
  padding-top: 5px !important;
  padding-left: 8px !important;
}
.enter-code-text-style {
  font-family: $cp-font;
  font-size: 15px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: $cp-white;
}
.input-code-style {
  font-family: $cp-font;
  font-size: 13px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left;
  color: $cp-white;
  margin-left: -154px !important;
  margin-bottom: 15px !important;
}

.forgotpin-input-box-padding{
  display: table-cell !important;
  width: 40px !important;
  border-radius: 5px !important;
  height:40px !important; 
  padding-left: 15px !important;
    font-family: $cp-font-secondary;
    font-size: 14px;
    font-weight: 600 !important;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #000000;
  
}

.phone-input-code-style {
  font-family: $cp-font;
  font-size: 13px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left;
  color: $cp-white;
  margin-left: -50px !important;
}
.pin-input-style{
  margin-left: -86px !important;
  font-family: $cp-font;
  font-size: 13px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left;
  color: $cp-white;
}
.pin-svg{
  margin-left: 30px;
}
.quick-pin-style{
    font-family: $cp-font;
    font-size: 18px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-white;
  
}


.quick-input-code-style {
  font-family: $cp-font-secondary;
  font-size: 13px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left;
  color: $cp-white;
  margin-left: -40px !important;
  margin-bottom: 15px !important;
}
.re-eneter-quick-input-code-style {
  font-family: $cp-font;
  font-size: 13px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left;
  color: $cp-white;
  margin-left: -75px !important;
  margin-bottom: 10px !important;
  margin-top: 10px;
}
.quick-access-success{
    font-family: $cp-font;
    font-size: 17px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #000000;
    margin-left: 20px !important;
    margin-right: 20px !important;
  
}
.forgotpin-bottom-space{
  margin-top: 70px !important; 
}
.forgotpin-middle-space{
  margin-top: 30px !important;  
}  
.fortgot-passord-top-space{
  margin-top: 60px !important;  
}
.code-input-box-padding{
  display: table-cell !important;
  width: 50px !important;
  border-radius: 8px !important;
  height: 50px !important;
  margin-left: 5px !important; 
}
.forgot-pin-eye-icon-div{
  margin-left: -362px !important;
  margin-top: 13px !important;
  color: $cp-white;
}
.margin{
  margin-left: 42px;
}
.code-row{
  margin-top: 12px;
}
.phone-code-margin{
  margin-left: 42px;
}
.phone-code-input-box-padding{
  display: table-cell !important;
  width: 50px !important;
  border-radius: 8px !important;
  height: 50px !important;
  margin-left: 5px !important; 
}
.phone-code-eye-icon-div{
  color: $cp-white;
  margin-left: -415px;
  margin-top: 14px;
}
.next-btn-padding{
  padding-left: 33px !important;
  padding-right: 33px !important;
}

.margin-lock-icon{
  margin-top: 100px;
}

.label-margin{
  margin-top: 20px !important;
}
.input-email-row{
  margin-top: 40px !important;
}





// --------------------------------------300px-600px----------------------

@media only screen and (min-width: 300px) and (max-width: 359px){
  .pin-svg {
    margin-left: 30px;
    height: 50px;
}
.input-email-row{
  margin-top: 22px !important;
}
.quick-pin-style {
  font-size: 14px;
}
  .phone-code-margin{
    margin-left: 4px;
  }
  .error-div-style {
    margin-left: 15px !important;
    margin-right: 15px !important;
  }
  .phone-code-input-box-padding{
    display: table-cell !important;
    width: 45px !important;
    border-radius: 8px !important;
    height: 45px !important;
    margin-left: 5px !important;
  }
  .phone-input-code-style {
    font-size: 12px;
    margin-left: -61px !important;
  }
  .phone-code-eye-icon-div{
    margin-left: -55px;
  }
  .code-row{
    margin-top: 0px;
  }
  .margin {
    margin-left: 7px;
  } 
  .forgotpin-bottom-space{
    margin-top: 51px !important; 
  }
  .forgotpin-middle-space{
    margin-top: 30px !important;  
  }  
  .fortgot-passord-top-space{
    margin-top: 60px !important;  
  }
  .input-code-style {
    font-size: 12px;
    margin-left: -93px !important;
  }
  .code-input-box-padding{
    display: table-cell !important;
    width: 36px !important;
    border-radius: 8px !important;
    border-radius: 8px !important;
    height: 36px !important;
    margin-left: 5px !important; 
  }
  .forgot-pin-eye-icon-div{
    margin-left: -36px !important;
    margin-top: 9px !important;
  }
  .enter-code-text-style {
    font-family: $cp-font;
    font-size: 13px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: $cp-white;
  }
  .forgot-password-top-row {
    margin-top: 5px !important;
  }
  .forgot-password-error-top-row {
    margin-top: 5% !important;
  }
  .forgot-password-description-label-styel {
    margin-bottom: 0% !important;
  }

}

// --------------------------------------400px-900px----------------------

@media only screen and (min-width: 370px) and (max-width: 400px){
  .forgot-password-top-row {
    margin-top: 10px !important;
  }
  .error-div-style {
    margin-left: 15px !important;
    margin-right: 15px !important;
  }
  .forgot-password-description-label-styel {
    margin-bottom: 5% !important;
  }
  .forgotpin-bottom-space{
    height: 100px !important;  

}
.quickaccess-forgotpin-bottom-space{
  height: 20px !important;  

}
.quickaccess-forgotpin-middle-space{
  height: 20px !important;  

}
.forgotpin-bottom-space{
  height: 100px !important; 
}
.forgotpin-middle-space {
  margin-top: 20px !important;
}  
.fortgot-passord-top-space {
  margin-top: 57px !important;
}
.input-code-style {
  font-size: 13px;
  margin-left: -110px !important;
}
.code-input-box-padding {
  display: table-cell !important;
  width: 42px !important;
  border-radius: 8px !important;
  height: 42px !important;
  margin-left: 5px !important;
}
.margin {
  margin-left: 13px;
}
.code-row {
margin-top: 3px;
}
.forgot-pin-eye-icon-div{
margin-left: -45px !important;
margin-top: 9px !important;
}
.phone-code-margin{
  margin-left: 13px;
}
.phone-code-input-box-padding{
  display: table-cell !important;
  width: 45px !important;
  border-radius: 8px !important;
  height: 45px !important;
  margin-left: 5px !important;
}
.phone-input-code-style {
  font-size: 12px;
  margin-left: -50px !important;
}
.pin-input-style{
  margin-left: -82px !important;
  font-size: 12px;
}
.phone-code-eye-icon-div{
  margin-left: -75px;
}
.input-code-style {
  font-size: 13px;
  margin-left: -122px !important;
}
}
@media only screen and (width: 375px) and (height: 812px){
  .error-div-style {
    margin-left: 15px !important;
    margin-right: 15px !important;
  }
  .forgotpin-bottom-space{
    height: 100px !important; 
  }
  .forgotpin-middle-space{
    margin-top: 57px !important; 
  }  
  .fortgot-passord-top-space{
    margin-top: 90px !important;
  }
  }
// --------------------------------------360px-900px----------------------

@media only screen and (width: 360px){
  .error-div-style {
    margin-left: 15px !important;
    margin-right: 15px !important;
  }
  .pin-svg {
    margin-left: 30px;
    height: 50px;
}
.quick-pin-style {
  font-size: 14px;
}
  .phone-code-margin{
    margin-left: 13px;
  }
  .phone-code-input-box-padding{
    display: table-cell !important;
    width: 45px !important;
    border-radius: 8px !important;
    height: 45px !important;
    margin-left: 5px !important;
  }
  .phone-input-code-style {
    font-size: 12px;
    margin-left: -44px !important;
  }
  .pin-input-style{
    margin-left: -79px !important;
    font-size: 12px;
  }
  .phone-code-eye-icon-div{
    margin-left: -75px;
  }
  .input-code-style {
    font-size: 13px;
    margin-left: -122px !important;
  }
  .code-input-box-padding {
    display: table-cell !important;
    width: 42px !important;
    border-radius: 8px !important;
    height: 42px !important;
    margin-left: 5px !important;
}
.margin{
  margin-left: 6px;
}
.code-row {
  margin-top: 3px;
}
.forgot-pin-eye-icon-div{
  margin-left: -36px !important;
  margin-top: 9px !important;
}
  .forgot-password-top-row {
    margin-top: 20px !important;
  }
  .forgot-password-description-label-styel {
    margin-bottom: 5% !important;
  }
  .forgotpin-bottom-space{
    margin-top: 54px !important; 
  }
  .forgotpin-middle-space{
    margin-top: 50px !important; 
  }  
  .fortgot-passord-top-space{
    margin-top: 90px !important;  
  }
.quickaccess-forgotpin-bottom-space{
  height: 40px !important;  

}
.quickaccess-forgotpin-middle-space{
  height: 55px !important;  

}

}

@media only screen and (min-width: 411px) and (max-width: 414px){
  .error-div-style {
    margin-left: 15px !important;
    margin-right: 15px !important;
  }
  .input-code-style {
    font-size: 13px;
    margin-left: -161px !important;
  }
  .margin {
    margin-left: 11px;
  }
  .code-row {
  margin-top: 3px;
  }
  .forgot-pin-eye-icon-div{
    margin-left: -46px !important;
  margin-top: 9px !important;
  }
  .forgotpin-bottom-space{
    margin-top: 70px !important; 
  }
  .forgotpin-middle-space{
    margin-top: 50px !important; 
  }  
  .fortgot-passord-top-space{
    margin-top: 90px !important;  
  }
  .phone-code-margin{
    margin-left: 13px;
  }
  .phone-code-input-box-padding{
    display: table-cell !important;
    width: 45px !important;
    border-radius: 8px !important;
    height: 45px !important;
    margin-left: 5px !important;
  }
  .phone-input-code-style {
    font-size: 12px;
    margin-left: -52px !important;
  }
  .pin-input-style{
    margin-left: -85px !important;
    font-size: 12px;
  }
  .phone-code-eye-icon-div{
    margin-left: -104px;
    margin-top: 11px;
  }
  .input-code-style {
    font-size: 13px;
    margin-left: -122px !important;
  }
}
@media only screen and (width: 375px) and (height: 667px){
  .phone-input-code-style {
    font-size: 12px;
    margin-left: -49px !important;
  }
  .pin-input-style{
    margin-left: -82px !important;
    font-size: 12px;
  }
}
// --------------------------------------800px-1500px----------------------

@media only screen and (min-width: 800px) and (max-width: 900px) {
  .error-div-style {
    margin-left: 17px !important;
    margin-right: 17px !important;
  }
  .forgot-password-top-row {
    margin-top: 20% !important;
  }
  .forgot-password-description-label-styel {
    margin-bottom: 5% !important;
  }
  .phone-code-margin{
    margin-left: 27px;
  }
  .phone-code-input-box-padding{
    display: table-cell !important;
    width: 45px !important;
    border-radius: 8px !important;
    height: 45px !important;
    margin-left: 5px !important;
  }
  .phone-input-code-style {
    font-size: 12px;
    margin-left: -38px !important;
  }
  .phone-code-eye-icon-div{
    margin-left: -264px;
    margin-top: 11px;
  }
  .input-code-style {
    font-size: 13px;
    margin-left: -122px !important;
  }
  .forgotpin-bottom-space{
    margin-top: 70px !important; 
  }
  .forgotpin-middle-space{
    margin-top: 50px !important; 
  }  
  .fortgot-passord-top-space{
    margin-top: 90px !important;  
  }
  .forgot-pin-eye-icon-div {
    margin-left: -200px !important;
    margin-top: 13px !important;
    color: $cp-white;
}
  .code-row {
    margin-top: 3px;
    }
}

// --------------------------------------700px-1200px----------------------
@media only screen and (min-width: 700px) and (max-width: 799px){

  .error-div-style {
    margin-left: 16px !important;
    margin-right: 16px !important;
  }
  .forgot-password-top-row {
    margin-top: 20% !important;
  }
  .forgot-password-description-label-styel {
    margin-bottom: 5% !important;
  }
  .input-code-style {
    font-size: 13px;
    margin-left: -161px !important;
  }
  .margin {
    margin-left: 24px;
  }
  .code-row {
  margin-top: 3px;
  }
  .forgot-pin-eye-icon-div{
    margin-left: -204px !important;
    margin-top: 9px !important;
  }
  .forgotpin-bottom-space{
    margin-top: 70px !important; 
  }
  .forgotpin-middle-space{
    margin-top: 50px !important; 
  }  
  .fortgot-passord-top-space{
    margin-top: 90px !important;  
  }
  .phone-code-margin{
    margin-left: 27px;
  }
  .phone-code-input-box-padding{
    display: table-cell !important;
    width: 45px !important;
    border-radius: 8px !important;
    height: 45px !important;
    margin-left: 5px !important;
  }
  .phone-code-eye-icon-div{
    margin-left: -271px;
    margin-top: 11px;
  }
  .input-code-style {
    font-size: 13px;
    margin-left: -122px !important;
  }

}
@media only screen and (min-width: 992px) and (max-width: 999px){
  .forgot-password-top-row {
    margin-top: 10% !important;
  }
  .forgot-password-description-label-styel {
    margin-bottom: 5% !important;
  }
  .input-code-style {
    font-size: 13px;
    margin-left: -161px !important;
  }
  .margin {
    margin-left: 11px;
  }
  .code-row {
  margin-top: 3px;
  }
  .forgot-pin-eye-icon-div{
    margin-left: -46px !important;
  margin-top: 9px !important;
  }
  .forgotpin-bottom-space{
    margin-top: 70px !important; 
  }
  .forgotpin-middle-space{
    margin-top: 50px !important; 
  }  
  .fortgot-passord-top-space{
    margin-top: 90px !important;  
  }
}
.code-input{
  border-radius: 7px !important;
  height: 45px !important;
}
@-moz-document url-prefix() { 
  .login-input-box-padding {
    padding-top: 12px !important;
    padding-left: 8px !important;
  }
}