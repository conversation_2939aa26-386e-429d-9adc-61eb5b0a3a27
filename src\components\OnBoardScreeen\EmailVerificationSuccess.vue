<template>
  <div class="container splash">
    <div class="col-12">
      <img class="success-image" src="../../assets/images/success-image.png" />
    </div>
    <div class="col-12 success-text-onboarding">
      <label>Success! </label>
    </div>
    <div class="col-12 success-description-label">
      <label>Your email is verified. </label>
    </div>

    <div class="row input-box-row row-for-input">
      <button type="button" class="btn-login btn-next" v-on:click="next">
        Next
      </button>
    </div>
  </div>
</template>
<script>
export default {
  /**
   * write a component's description
   * this component is use to Upgrade to the new canpay the user's email as a consumer
   */
  name: "EmailVerificationSuccess",
  /**
   * @description-

   * @returns {any}
   */

  data() {
    let self = this;
    return {};
  },
  destroyed() {
    document
      .getElementById("app")
      .style.setProperty("background-color", "#149240");
  },
  created() {},
  mounted() {
    this.$root.$emit("show_header", false);
    this.$root.$emit("changeWhiteBackground", [true, false, ""]);
    document
      .getElementById("app")
      .style.setProperty("background-color", "#ffffff");

    var element = document.getElementsByClassName("wrapper");
    element[0].style.setProperty("background-color", "#ffffff");

    var elementHtml = document.getElementsByTagName("html")[0];
    elementHtml.style.setProperty("background-color", "#ffffff");
  },
  methods: {
    next() {
      this.$router.push("/existingphonenumberverification");
    },
  },
};
</script>
<style lang="scss"></style>
