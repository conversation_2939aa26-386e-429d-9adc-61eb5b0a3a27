<template>
  <div>
    <b-modal
      ref="canpay-crew-calendly-setup-modal"
      hide-footer
      modal-backdrop
      hide-header
      id="canpay-crew-calendly-setup-modal"
      centered
    >
      <div id="calendly-inline-widget" style="width:100%; height:700px"></div>
    </b-modal>
    <b-modal
        ref="canpay-crew-calendly-setup-success"
        hide-footer
        v-b-modal.modal-center
        modal-backdrop
        hide-header
        id="canpay-crew-calendly-setup-success"
        centered
        title="BootstrapVue"
    >
      <div class="text-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="55" height="55" viewBox="0 0 160 159" fill="none">
          <mask id="path-1-outside-1_16385_131" maskUnits="userSpaceOnUse" x="0" y="0" width="160" height="159" fill="black">
          <rect fill="white" width="160" height="159"/>
          <path d="M159 98.5756C159 91.7189 154.084 85.9171 147.589 84.6864C149.696 82.2251 150.924 79.0605 150.924 75.5442C150.924 67.8085 144.604 61.4793 136.88 61.4793H101.242L104.753 36.8656C106.509 24.5588 98.0822 13.8343 89.3044 9.9664C85.0911 8.03247 81.7556 8.03247 79.1222 9.61478C75.4356 11.9003 74.5578 17.3505 74.3822 24.5588C74.2067 34.9317 68.94 73.4345 48.5756 73.4345H43.1333V63.4132C43.1333 62.0067 41.9044 60.776 40.5 60.776H3.63333C2.22889 60.776 1 62.0067 1 63.4132V155.363C1 156.769 2.22889 158 3.63333 158H40.5C41.9044 158 43.1333 156.769 43.1333 155.363V146.396C45.5911 147.275 48.2244 148.506 50.8578 149.737C59.2844 153.605 68.94 158 81.7556 158H126.171C133.72 158 139.864 151.847 139.864 144.287C139.864 141.122 138.811 138.133 136.88 135.848C144.604 135.848 150.924 129.518 150.924 121.783C150.924 118.267 149.696 115.102 147.589 112.641C154.084 111.234 159 105.432 159 98.5756ZM37.8667 152.726H6.26667V66.0504H37.8667V152.726ZM144.956 107.366H121.08C119.676 107.366 118.447 108.597 118.447 110.003C118.447 111.41 119.676 112.641 121.08 112.641H136.704C141.62 112.641 145.482 116.684 145.482 121.431C145.482 126.354 141.444 130.222 136.704 130.222H118.447C117.042 130.222 115.813 131.452 115.813 132.859C115.813 134.265 117.042 135.496 118.447 135.496H126.171C130.911 135.496 134.598 139.364 134.598 143.935C134.598 148.682 130.736 152.374 126.171 152.374H81.58C69.8178 152.374 60.8644 148.155 52.9644 144.638C49.6289 143.056 46.2933 141.649 42.9578 140.419V78.7088H48.4C77.3667 78.7088 79.4733 25.262 79.4733 24.7346C79.4733 22.449 79.6489 15.4166 81.7556 14.0101C82.6333 13.4826 84.3889 13.6585 86.8467 14.8891C92.4644 17.3505 100.716 25.262 99.3111 36.1624L95.6244 63.7648C95.4489 64.4681 95.8 65.3471 96.3267 65.8746C96.8533 66.402 97.5556 66.7536 98.2578 66.7536H136.88C141.796 66.7536 145.658 70.7973 145.658 75.5442C145.658 80.2912 141.62 84.3348 136.88 84.3348H121.256C119.851 84.3348 118.622 85.5655 118.622 86.972C118.622 88.3785 119.851 89.6092 121.256 89.6092H144.956C149.871 89.6092 153.733 93.6528 153.733 98.3998C153.733 103.498 149.696 107.366 144.956 107.366Z"/>
          <path d="M21.1889 25.4379L33.3022 31.2396L39.0956 43.3707C39.4467 44.2497 40.5 44.953 41.5533 44.953C42.6067 44.953 43.4844 44.4255 44.0111 43.3707L49.8044 31.2396L61.9178 25.4379C62.7956 25.0862 63.4978 24.0314 63.4978 22.9765C63.4978 21.9216 62.9711 21.0426 61.9178 20.5151L49.8044 14.7133L44.0111 2.58231C43.66 1.70325 42.6067 1 41.5533 1C40.5 1 39.6222 1.52743 39.0956 2.58231L33.3022 14.7133L21.1889 20.5151C20.3111 20.8667 19.6089 21.9216 19.6089 22.9765C19.6089 24.0314 20.3111 25.0862 21.1889 25.4379ZM36.4622 19.2844C36.9889 19.1086 37.5156 18.5812 37.6911 18.0538L41.5533 9.9664L45.4156 18.0538C45.5911 18.5812 46.1178 19.1086 46.6444 19.2844L54.72 23.1523L46.6444 26.8443C46.1178 27.0202 45.5911 27.5476 45.4156 28.075L41.5533 36.1624L37.6911 28.075C37.5156 27.5476 36.9889 27.0202 36.4622 26.8443L28.3867 22.9765L36.4622 19.2844Z"/>
          <path d="M121.782 35.1075H127.576V40.9093C127.576 42.3158 128.804 43.5465 130.209 43.5465C131.613 43.5465 132.842 42.3158 132.842 40.9093V35.1075H138.636C140.04 35.1075 141.269 33.8768 141.269 32.4703C141.269 31.0638 140.04 29.8331 138.636 29.8331H132.842V24.0314C132.842 22.6249 131.613 21.3942 130.209 21.3942C128.804 21.3942 127.576 22.6249 127.576 24.0314V29.8331H121.782C120.378 29.8331 119.149 31.0638 119.149 32.4703C119.149 33.8768 120.378 35.1075 121.782 35.1075Z"/>
          </mask>
          <path d="M159 98.5756C159 91.7189 154.084 85.9171 147.589 84.6864C149.696 82.2251 150.924 79.0605 150.924 75.5442C150.924 67.8085 144.604 61.4793 136.88 61.4793H101.242L104.753 36.8656C106.509 24.5588 98.0822 13.8343 89.3044 9.9664C85.0911 8.03247 81.7556 8.03247 79.1222 9.61478C75.4356 11.9003 74.5578 17.3505 74.3822 24.5588C74.2067 34.9317 68.94 73.4345 48.5756 73.4345H43.1333V63.4132C43.1333 62.0067 41.9044 60.776 40.5 60.776H3.63333C2.22889 60.776 1 62.0067 1 63.4132V155.363C1 156.769 2.22889 158 3.63333 158H40.5C41.9044 158 43.1333 156.769 43.1333 155.363V146.396C45.5911 147.275 48.2244 148.506 50.8578 149.737C59.2844 153.605 68.94 158 81.7556 158H126.171C133.72 158 139.864 151.847 139.864 144.287C139.864 141.122 138.811 138.133 136.88 135.848C144.604 135.848 150.924 129.518 150.924 121.783C150.924 118.267 149.696 115.102 147.589 112.641C154.084 111.234 159 105.432 159 98.5756ZM37.8667 152.726H6.26667V66.0504H37.8667V152.726ZM144.956 107.366H121.08C119.676 107.366 118.447 108.597 118.447 110.003C118.447 111.41 119.676 112.641 121.08 112.641H136.704C141.62 112.641 145.482 116.684 145.482 121.431C145.482 126.354 141.444 130.222 136.704 130.222H118.447C117.042 130.222 115.813 131.452 115.813 132.859C115.813 134.265 117.042 135.496 118.447 135.496H126.171C130.911 135.496 134.598 139.364 134.598 143.935C134.598 148.682 130.736 152.374 126.171 152.374H81.58C69.8178 152.374 60.8644 148.155 52.9644 144.638C49.6289 143.056 46.2933 141.649 42.9578 140.419V78.7088H48.4C77.3667 78.7088 79.4733 25.262 79.4733 24.7346C79.4733 22.449 79.6489 15.4166 81.7556 14.0101C82.6333 13.4826 84.3889 13.6585 86.8467 14.8891C92.4644 17.3505 100.716 25.262 99.3111 36.1624L95.6244 63.7648C95.4489 64.4681 95.8 65.3471 96.3267 65.8746C96.8533 66.402 97.5556 66.7536 98.2578 66.7536H136.88C141.796 66.7536 145.658 70.7973 145.658 75.5442C145.658 80.2912 141.62 84.3348 136.88 84.3348H121.256C119.851 84.3348 118.622 85.5655 118.622 86.972C118.622 88.3785 119.851 89.6092 121.256 89.6092H144.956C149.871 89.6092 153.733 93.6528 153.733 98.3998C153.733 103.498 149.696 107.366 144.956 107.366Z" fill="#179346"/>
          <path d="M21.1889 25.4379L33.3022 31.2396L39.0956 43.3707C39.4467 44.2497 40.5 44.953 41.5533 44.953C42.6067 44.953 43.4844 44.4255 44.0111 43.3707L49.8044 31.2396L61.9178 25.4379C62.7956 25.0862 63.4978 24.0314 63.4978 22.9765C63.4978 21.9216 62.9711 21.0426 61.9178 20.5151L49.8044 14.7133L44.0111 2.58231C43.66 1.70325 42.6067 1 41.5533 1C40.5 1 39.6222 1.52743 39.0956 2.58231L33.3022 14.7133L21.1889 20.5151C20.3111 20.8667 19.6089 21.9216 19.6089 22.9765C19.6089 24.0314 20.3111 25.0862 21.1889 25.4379ZM36.4622 19.2844C36.9889 19.1086 37.5156 18.5812 37.6911 18.0538L41.5533 9.9664L45.4156 18.0538C45.5911 18.5812 46.1178 19.1086 46.6444 19.2844L54.72 23.1523L46.6444 26.8443C46.1178 27.0202 45.5911 27.5476 45.4156 28.075L41.5533 36.1624L37.6911 28.075C37.5156 27.5476 36.9889 27.0202 36.4622 26.8443L28.3867 22.9765L36.4622 19.2844Z" fill="#179346"/>
          <path d="M121.782 35.1075H127.576V40.9093C127.576 42.3158 128.804 43.5465 130.209 43.5465C131.613 43.5465 132.842 42.3158 132.842 40.9093V35.1075H138.636C140.04 35.1075 141.269 33.8768 141.269 32.4703C141.269 31.0638 140.04 29.8331 138.636 29.8331H132.842V24.0314C132.842 22.6249 131.613 21.3942 130.209 21.3942C128.804 21.3942 127.576 22.6249 127.576 24.0314V29.8331H121.782C120.378 29.8331 119.149 31.0638 119.149 32.4703C119.149 33.8768 120.378 35.1075 121.782 35.1075Z" fill="#179346"/>
          <path d="M159 98.5756C159 91.7189 154.084 85.9171 147.589 84.6864C149.696 82.2251 150.924 79.0605 150.924 75.5442C150.924 67.8085 144.604 61.4793 136.88 61.4793H101.242L104.753 36.8656C106.509 24.5588 98.0822 13.8343 89.3044 9.9664C85.0911 8.03247 81.7556 8.03247 79.1222 9.61478C75.4356 11.9003 74.5578 17.3505 74.3822 24.5588C74.2067 34.9317 68.94 73.4345 48.5756 73.4345H43.1333V63.4132C43.1333 62.0067 41.9044 60.776 40.5 60.776H3.63333C2.22889 60.776 1 62.0067 1 63.4132V155.363C1 156.769 2.22889 158 3.63333 158H40.5C41.9044 158 43.1333 156.769 43.1333 155.363V146.396C45.5911 147.275 48.2244 148.506 50.8578 149.737C59.2844 153.605 68.94 158 81.7556 158H126.171C133.72 158 139.864 151.847 139.864 144.287C139.864 141.122 138.811 138.133 136.88 135.848C144.604 135.848 150.924 129.518 150.924 121.783C150.924 118.267 149.696 115.102 147.589 112.641C154.084 111.234 159 105.432 159 98.5756ZM37.8667 152.726H6.26667V66.0504H37.8667V152.726ZM144.956 107.366H121.08C119.676 107.366 118.447 108.597 118.447 110.003C118.447 111.41 119.676 112.641 121.08 112.641H136.704C141.62 112.641 145.482 116.684 145.482 121.431C145.482 126.354 141.444 130.222 136.704 130.222H118.447C117.042 130.222 115.813 131.452 115.813 132.859C115.813 134.265 117.042 135.496 118.447 135.496H126.171C130.911 135.496 134.598 139.364 134.598 143.935C134.598 148.682 130.736 152.374 126.171 152.374H81.58C69.8178 152.374 60.8644 148.155 52.9644 144.638C49.6289 143.056 46.2933 141.649 42.9578 140.419V78.7088H48.4C77.3667 78.7088 79.4733 25.262 79.4733 24.7346C79.4733 22.449 79.6489 15.4166 81.7556 14.0101C82.6333 13.4826 84.3889 13.6585 86.8467 14.8891C92.4644 17.3505 100.716 25.262 99.3111 36.1624L95.6244 63.7648C95.4489 64.4681 95.8 65.3471 96.3267 65.8746C96.8533 66.402 97.5556 66.7536 98.2578 66.7536H136.88C141.796 66.7536 145.658 70.7973 145.658 75.5442C145.658 80.2912 141.62 84.3348 136.88 84.3348H121.256C119.851 84.3348 118.622 85.5655 118.622 86.972C118.622 88.3785 119.851 89.6092 121.256 89.6092H144.956C149.871 89.6092 153.733 93.6528 153.733 98.3998C153.733 103.498 149.696 107.366 144.956 107.366Z" stroke="#1B9146" stroke-width="2" mask="url(#path-1-outside-1_16385_131)"/>
          <path d="M21.1889 25.4379L33.3022 31.2396L39.0956 43.3707C39.4467 44.2497 40.5 44.953 41.5533 44.953C42.6067 44.953 43.4844 44.4255 44.0111 43.3707L49.8044 31.2396L61.9178 25.4379C62.7956 25.0862 63.4978 24.0314 63.4978 22.9765C63.4978 21.9216 62.9711 21.0426 61.9178 20.5151L49.8044 14.7133L44.0111 2.58231C43.66 1.70325 42.6067 1 41.5533 1C40.5 1 39.6222 1.52743 39.0956 2.58231L33.3022 14.7133L21.1889 20.5151C20.3111 20.8667 19.6089 21.9216 19.6089 22.9765C19.6089 24.0314 20.3111 25.0862 21.1889 25.4379ZM36.4622 19.2844C36.9889 19.1086 37.5156 18.5812 37.6911 18.0538L41.5533 9.9664L45.4156 18.0538C45.5911 18.5812 46.1178 19.1086 46.6444 19.2844L54.72 23.1523L46.6444 26.8443C46.1178 27.0202 45.5911 27.5476 45.4156 28.075L41.5533 36.1624L37.6911 28.075C37.5156 27.5476 36.9889 27.0202 36.4622 26.8443L28.3867 22.9765L36.4622 19.2844Z" stroke="#1B9146" stroke-width="2" mask="url(#path-1-outside-1_16385_131)"/>
          <path d="M121.782 35.1075H127.576V40.9093C127.576 42.3158 128.804 43.5465 130.209 43.5465C131.613 43.5465 132.842 42.3158 132.842 40.9093V35.1075H138.636C140.04 35.1075 141.269 33.8768 141.269 32.4703C141.269 31.0638 140.04 29.8331 138.636 29.8331H132.842V24.0314C132.842 22.6249 131.613 21.3942 130.209 21.3942C128.804 21.3942 127.576 22.6249 127.576 24.0314V29.8331H121.782C120.378 29.8331 119.149 31.0638 119.149 32.4703C119.149 33.8768 120.378 35.1075 121.782 35.1075Z" stroke="#1B9146" stroke-width="2" mask="url(#path-1-outside-1_16385_131)"/>
          </svg>
          <p class="canpay-crew-text-font-18 canpay-crew-text-700 canpay-crew-p-margin-bottom-1 mt-3">Your meeting has been scheduled successfully. Please check your email for the calendar invite.</p>
      </div>
    </b-modal>
  </div>
</template>

<script>
export default {
  name: 'CalendlyWidget',
  data() {
    return {
      calendlyBaseUrl: process.env.VUE_APP_CALENDLY_PUBLIC_MEETING_LINK
    };
  },
  methods: {
    openCanpayCalendlyModal() {
      this.$refs['canpay-crew-calendly-setup-modal'].show();

      // Load Calendly widget slightly after modal opens
      setTimeout(() => {
        this.loadCalendly();
      }, 500);

      // Set up event listener for Calendly scheduling
      window.addEventListener('message', this.handleCalendlyEvent);
    },

    loadCalendly() {
      const fullUrl = this.calendlyBaseUrl;
      const parent = document.getElementById('calendly-inline-widget');
      parent.innerHTML = ''; // Clear previous instance
      if (window.Calendly) {
        window.Calendly.initInlineWidget({
          url: fullUrl,
          parentElement: parent
        });
      } else {
        console.error('Calendly widget script not loaded');
      }
    },

    handleCalendlyEvent(e) {
      if (e.origin !== 'https://calendly.com') return;

      const eventName = e.data?.event;
      if (eventName === 'calendly.event_scheduled') {
        console.log('Meeting scheduled — closing modal...');
        this.$refs['canpay-crew-calendly-setup-modal'].hide();
        this.$refs['canpay-crew-calendly-setup-success'].show();
        setTimeout(() => {
          this.$refs['canpay-crew-calendly-setup-success'].hide();
        },3000)

        // Cleanup listener
        window.removeEventListener('message', this.handleCalendlyEvent);
      }
    }
  },

  beforeDestroy() {
    window.removeEventListener('message', this.handleCalendlyEvent);
  }
};
</script>
