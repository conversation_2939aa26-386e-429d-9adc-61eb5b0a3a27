const manifestJSON = require('./public/manifest.json')
module.exports = {
  'transpileDependencies': [
    'vuetify'
  ],
  lintOnSave: false,
  pwa: {
    themeColor: manifestJSON.theme_color,
    name: manifestJSON.short_name,
    assetsVersion: manifestJSON.version,
    msTileColor: manifestJSON.background_color,
    appleMobileWebAppCapable: 'yes',
    appleMobileWebAppStatusBarStyle: 'black-translucent',
    assetsVersion: manifestJSON.version,
    workboxPluginMode: 'InjectManifest',
    msTileColor:'white',
    workboxOptions: {
      swSrc: 'service-worker.js',
    },
    iconPaths: {
      favicon32: 'img/icons/favicon-32x32.png',
      favicon16: 'img/icons/favicon-16x16.png',
      appleTouchIcon: 'img/icons/apple-touch-icon-192x192.png',
      maskIcon: 'img/icons/safari-pinned-tab.svg',
      msTileImage: 'img/icons/msapplication-icon-144x144.png'
    }
  },
}
