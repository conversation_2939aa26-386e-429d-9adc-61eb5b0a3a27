<template>
<div>
<div v-if="isLoading">
    <CanPayLoader/>
</div>
<div v-if="!isLoading">
  <div>
    <div class="header canpay-crew-signer">
      <div class="back-button container"  style="
        position: relative;
        top: -12px;

      ">
        <span class="back-arrow" v-on:click="goBack()">
          <svg  xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 67 56" fill="none" style="
              position: relative;
              top: -3px;
          ">
          <path d="M2.38113 25.5833L26.6029 1C27.9167 -0.333333 30.0515 -0.333333 31.3652 1C32.6789 2.33333 32.6789 4.5 31.3652 5.83333L12.8909 24.5833H63.6336C65.5221 24.5833 67 26.0833 67 28C67 29.9167 65.5221 31.4167 63.6336 31.4167H12.8909L31.3652 50.1667C32.6789 51.5 32.6789 53.6667 31.3652 55C30.7083 55.6667 29.8873 56 28.9841 56C28.0809 56 27.2598 55.6667 26.6029 55L2.38113 30.4167L0 28L2.38113 25.5833Z" fill="black"/>
          </svg>
</span>
        <span class="title ml-2 canpay-crew-text-font-21"><b>All Signers ({{signers.length}} of {{max_consumer_allowed}})</b></span>
      </div>
    </div>
    <div class="mt-2" style="background-color:#ECECEC">
    <div v-if="anyUserLeftToTip" class="signers-list container">
    <div class="tip-all-padding">
    <div class="row" style="margin:0px;">
      <div class="col-10" style="padding:0px"></div>
      <div class="col-2" style="padding:0px">
        <button class="crew-tip-button" v-on:click='tipAll()'>Tip All</button>
      </div>
    </div>
    </div>
    </div>
    <div v-for="(signer,index) in signers" :key="signer.consumer_id + '-' + signer.allow_tipping">
    <div class="signers-list container">
      <div
        :style="{
              borderLeft: signer.type == 'mayor' ? '3px solid #FFD700' : signer.type == 'crew leader' ? '3px solid #C0C0C0' : '',
        }"  class="signer-item ">
        <!-- Signer information column -->
        <div class="row w-100" style='margin:0px;'>
          <div :class="checkColumn(signer.allow_tipping)" style='place-content:center;padding:0px;word-break:break-all;text-align: justify;;text-align: justify;'>
          <div class="d-flex align-items-center">
            <div class="user-icon">
              <svg v-if="signer.type == 'mayor'" xmlns="http://www.w3.org/2000/svg" width="29" height="29" viewBox="0 0 310 307" fill="none">
                <path d="M0 150C0 67.1573 67.1573 0 150 0V0C232.843 0 300 67.1573 300 150V150C300 232.843 232.843 300 150 300V300C67.1573 300 0 232.843 0 150V150Z" fill="#ECECEC"/>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M149.842 80.6109C135.19 80.6109 123.314 92.2292 123.314 106.561C123.314 120.893 135.19 132.511 149.842 132.511C164.492 132.511 176.37 120.893 176.37 106.561C176.37 92.2292 164.492 80.6109 149.842 80.6109ZM105.629 106.561C105.629 82.6746 125.423 63.3108 149.842 63.3108C174.26 63.3108 194.055 82.6746 194.055 106.561C194.055 130.447 174.26 149.811 149.842 149.811C125.423 149.811 105.629 130.447 105.629 106.561Z" fill="black"/>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M149.842 175.761C119.401 175.761 103.399 186.313 95.4165 199.096C92.6383 203.545 93.337 207.742 96.7288 211.597C100.429 215.803 107.119 219.012 114.471 219.012H185.212C192.564 219.012 199.255 215.803 202.954 211.597C206.346 207.742 207.045 203.545 204.268 199.096C196.284 186.313 180.282 175.761 149.842 175.761ZM80.3243 190.077C92.2558 170.971 114.797 158.461 149.842 158.461C184.887 158.461 207.428 170.971 219.359 190.077C226.765 201.936 223.93 214.277 216.36 222.882C209.096 231.137 197.395 236.312 185.212 236.312H114.471C102.288 236.312 90.5868 231.137 83.3242 222.882C75.7534 214.277 72.9191 201.936 80.3243 190.077Z" fill="black"/>
                <path d="M151.042 37C153.302 37 155.134 38.8322 155.134 41.0918C155.134 42.6915 154.214 44.0746 152.876 44.7471C153.257 45.0273 153.58 45.4133 153.8 45.9053L163.762 68.2266L183.98 49.9678C184.238 49.7352 184.517 49.5609 184.807 49.4385C184.537 48.8932 184.386 48.2792 184.386 47.6299C184.386 45.3702 186.218 43.5381 188.478 43.5381C190.737 43.5382 192.568 45.3703 192.568 47.6299C192.568 49.7854 190.901 51.5491 188.786 51.707C188.804 51.8393 188.815 51.9753 188.815 52.1152V90.1055C188.815 91.7041 187.52 93 185.921 93H114.822C113.224 93 111.928 91.7041 111.928 90.1055V51.8174C111.928 51.6739 111.938 51.5339 111.957 51.3984C109.76 51.3273 108 49.5251 108 47.3105C108 45.0509 109.832 43.2188 112.092 43.2188C114.351 43.2189 116.183 45.051 116.183 47.3105C116.183 47.9445 116.038 48.5444 115.78 49.0801C116.095 49.1921 116.402 49.3632 116.686 49.6016L138.628 68.0518L148.513 45.9053C148.715 45.4518 149.006 45.089 149.349 44.8154C147.935 44.1714 146.951 42.747 146.951 41.0918C146.951 38.8323 148.783 37.0002 151.042 37Z" fill="black"/>
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 300 300" fill="none">
                <path d="M0 150C0 67.1573 67.1573 0 150 0C232.843 0 300 67.1573 300 150C300 232.843 232.843 300 150 300C67.1573 300 0 232.843 0 150Z" fill="#ECECEC"/>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M149.842 80.6109C135.19 80.6109 123.314 92.2292 123.314 106.561C123.314 120.893 135.19 132.511 149.842 132.511C164.492 132.511 176.37 120.893 176.37 106.561C176.37 92.2292 164.492 80.6109 149.842 80.6109ZM105.629 106.561C105.629 82.6746 125.423 63.3108 149.842 63.3108C174.26 63.3108 194.055 82.6746 194.055 106.561C194.055 130.447 174.26 149.811 149.842 149.811C125.423 149.811 105.629 130.447 105.629 106.561Z" fill="black"/>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M149.842 175.761C119.401 175.761 103.399 186.313 95.4165 199.096C92.6383 203.545 93.337 207.742 96.7288 211.597C100.429 215.803 107.119 219.012 114.471 219.012H185.212C192.564 219.012 199.255 215.803 202.954 211.597C206.346 207.742 207.045 203.545 204.268 199.096C196.284 186.313 180.282 175.761 149.842 175.761ZM80.3243 190.077C92.2558 170.971 114.797 158.461 149.842 158.461C184.887 158.461 207.428 170.971 219.359 190.077C226.765 201.936 223.93 214.277 216.36 222.882C209.096 231.137 197.395 236.312 185.212 236.312H114.471C102.288 236.312 90.5868 231.137 83.3242 222.882C75.7534 214.277 72.9191 201.936 80.3243 190.077Z" fill="black"/>
              </svg>
            </div>
            <div class=" ml-2">
            <div class='signer-info'>
              <b>{{ signer.name }}</b> <span class="location" v-if="signer.state">({{ signer.state }})</span>
            </div>
            </div>
            </div>
          </div>

          <!-- Tip button column -->
          <div v-if="signer.allow_tipping == 1" class="col-2 d-flex crew-justify-content-right align-items-center" style="padding:0px;">
            <button :class="['crew-tip-button', { 'loading': signer.is_loading }]"
                    v-on:click="tipSigner(index,signer)"
                    :disabled="signer.is_loading">
              <span v-if="!signer.is_loading">Tip</span>
              <span v-else class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
            </button>
          </div>
          <div v-if="signer.allow_tipping == 0" class="tip-awarded col-4 d-flex crew-justify-content-right align-items-center" style="padding:0px;">
          <span>+{{pointNumberFormatter(signer.total_points)}}</span>
          <svg data-v-03db4a85="" class="ml-1 mr-2" width="25" height="25" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg"><path data-v-03db4a85="" d="M43.6484 32.2123C45.9758 27.2992 45.0233 22.6013 44.7537 21.3551C44.4482 19.9476 43.6754 16.6214 40.8627 13.6359C39.5231 12.233 37.9321 11.0927 36.1719 10.2738C33.7996 9.17106 31.7777 8.95589 30.6724 8.8483C26.1164 8.40003 22.3512 9.72692 20.2485 10.6773C21.2723 9.69865 22.4425 8.88501 23.7171 8.26555C24.8436 7.72841 26.0375 7.34542 27.2667 7.12693C29.2792 6.74323 31.3425 6.70382 33.3683 7.01038C34.3028 7.15383 38.2028 7.82624 42.0938 10.8386C47.7641 15.2228 50.0915 21.3103 48.7705 28.3123C46.7576 39.0261 36.4595 45.3019 26.0176 42.5406C22.9443 41.7247 19.7093 39.7971 18.2715 37.9144C18.3434 37.9144 18.4243 37.8964 18.4692 37.9233C18.8287 38.1564 19.1791 38.4075 19.5476 38.6406C25.3886 42.2985 31.3733 42.3254 37.3222 39.053C38.0754 38.6376 38.791 38.1577 39.4609 37.6185C42.0399 35.5385 43.2171 33.1178 43.6484 32.2123Z" fill="black"></path><path data-v-03db4a85="" d="M49.2647 32.8578C49.2018 33.2344 49.094 33.7723 48.9232 34.4088C47.6831 38.9633 44.8525 41.8412 43.6394 43.0426C39.2002 47.4446 34.0332 48.7177 31.6698 49.2736C26.8083 50.4122 22.8814 49.9281 21.4706 49.7039C16.3934 48.897 12.9067 46.8529 11.8374 46.1805C9.65575 44.8138 7.70037 43.117 6.04133 41.1509C4.1037 38.8639 2.58573 36.2538 1.55724 33.4406C1.29664 32.7233 0.308168 29.9082 0.0565558 26.053C-0.177084 22.3772 0.380057 19.6337 0.514849 18.9972C0.954323 16.9176 1.66425 14.9043 2.62659 13.0083C3.12982 12.04 5.69087 7.30621 11.3521 3.77381C12.6641 2.94899 16.0968 1.01244 20.9583 0.277275C22.2434 0.0800349 33.3233 -1.4441 41.2581 5.48622C44.9334 8.69586 47.099 12.9903 47.4405 13.6807C47.9662 14.747 48.4225 15.8461 48.8064 16.971C48.6806 16.8006 47.746 15.4917 47.7191 15.4558C44.2684 10.3545 40.2606 7.95172 40.2606 7.95172C38.7451 7.06349 37.1157 6.38487 35.4171 5.93449C30.0164 4.48208 25.4694 5.77311 24.3911 6.09587C18.0648 7.98758 14.3176 13.0172 14.1019 13.3131C10.2828 18.5668 10.3637 24.0537 10.4356 25.5778C10.6782 30.7061 12.9247 34.3281 13.9491 35.7895C14.03 35.8881 14.1468 36.0406 14.2906 36.2199C14.3985 36.3633 14.5063 36.5068 14.6141 36.6502C17.292 40.1647 20.8415 42.6123 24.7864 43.8316C27.7263 44.7285 30.8328 44.9427 33.8684 44.4579C36.9039 43.9731 39.7882 42.802 42.3004 41.0343C44.511 39.4743 45.85 37.8516 46.6767 36.8474C47.773 35.5206 48.5728 34.2116 48.8334 33.6916C48.8693 33.6288 49.2467 32.8578 49.2647 32.8578Z" fill="#ECB800"></path><path data-v-03db4a85="" d="M20.3742 32.9385C15.6295 32.9385 13.2571 29.8096 13.2571 25.0131C13.2571 20.0552 15.7553 17.0697 20.6438 17.0697C22.5309 17.0697 24.0495 17.5179 25.2177 18.4683C26.314 19.4455 26.9251 20.7724 27.0778 22.4579H25.1728C24.4898 22.4579 23.9956 22.1441 23.6991 21.5255C23.1779 20.4227 22.1534 19.8579 20.6527 19.8579C17.7233 19.8579 16.4203 21.92 16.4203 25.022C16.4203 28.0344 17.6693 30.1592 20.5539 30.1592C22.5309 30.1592 23.6631 29.0744 24.0046 27.371H27.0689C26.7903 30.9213 24.2832 32.9385 20.3742 32.9385Z" fill="black"></path><path data-v-03db4a85="" d="M36.756 17.1055H31.3733C30.4657 17.1055 29.7468 17.8944 29.7468 18.7999V32.8847H32.9818V27.2634H36.9896C40.1617 27.2634 41.8601 25.291 41.8601 22.1799C41.8601 18.8986 40.1168 17.1055 36.756 17.1055ZM36.3876 24.4123H33.2334V19.8399H36.5493C38.059 19.8399 38.8318 20.602 38.8318 22.1351C38.8408 23.6682 38.032 24.4392 36.3876 24.4123Z" fill="#ECB800"></path></svg>
          </div>
        </div>
      </div>
    </div>
    </div>
    </div>
  </div>
</div>
</div>
</template>

<script>
import petitionApi from "../../api/petition.js";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue";
export default {
  components:{
    CanPayLoader
  },
  name:'SignerPage',
  created(){
    this.petition_id = localStorage.getItem("petition_id");
    this.currentUser = localStorage.getItem("consumer_login_response")
    ? JSON.parse(localStorage.getItem("consumer_login_response"))
    : null;
  },
  watch:{
    anyUserLeftToTip(newValue,oldValue){
      if(newValue < 0){
        this.anyUserLeftToTip = 0;
      }
    }
  },
  mounted(){
    this.$root.$emit("changeWhiteBackground", [false, false, "signerspage",true]);
    var element = document.getElementsByClassName("content-wrap");
    if (element[0]) {
    element[0].style.setProperty("background-color", "#149240");
    element[0].style.height = "114vh";
    if(window.innerWidth>1200){
        element[0].style.height = "121vh";
    }
    }
    this.petition_id = localStorage.getItem("petition_id");
    this.getPititionDetails();
  },
  data() {
    return {
      petition_id: "",
      petitionsdetails:[],
      isLoading: false,
      currentUser:null,
      signers: [
      ],
      max_consumer_allowed:process.env.VUE_APP_MAX_CONSUMER_ALLOWED_TO_SIGN,
      tipRewardPoints: parseInt(process.env.VUE_APP_TIP_REWARD_POINTS || 200),
      anyUserLeftToTip:false, // this data will check if there is any user left to tip by current logged in user
      audioContext: null, // Audio context for smoother audio playback
    };
  },
  methods:{
    tipAll(){
      let self = this;
      if(self.currentUser == null){
        return;
      }
      const payload = {
        tip_from: self.currentUser.user_id,
        petition_id: self.petition_id,
        type: 'tip_all'
      }
      self.isLoading = true;
      petitionApi
      .tipConsumer(payload)
      .then((res)=>{
        self.getPititionDetails();
        self.playDingAudio();
        self.anyUserLeftToTip = 0;
      })
      .catch((err) => {
        console.log(err);
      })
    },
    checkColumn(allowTipping){
      if(allowTipping == 1){
        // now if we find the allow tipping for any user we will enable the tip all button
        this.anyUserLeftToTip = true;
        return 'col-10';
      }else if(allowTipping == 0){
        return 'col-8';
      }
      return 'col-12'
    },
    goBack(){
      this.$router.go(-1);
    },
    getPititionDetails() {
        let self = this;
        let payload = {
            petition_id : this.petition_id,
        }
        if(self.currentUser){
          payload.user_id = self.currentUser.user_id;
        }else {
          payload.user_id = null
        }
        self.isLoading = true;
        petitionApi
        .signedUsersForPetition(payload)
        .then((res)=>{
            self.signers = res.data;
            self.anyUserLeftToTip = res.data.filter(signer => signer.allow_tipping == 1).length;
        })
        .catch((err) => {
            console.log(err);
        })
        .finally(()=>{
            self.isLoading = false;
        })
    },
    playDingAudio(){
          // Play the ding sound effect with smooth audio settings
          if (this.currentUser) {
            try {
              // Create a new Audio instance for each play to allow multiple simultaneous sounds
              const audio = new Audio('/sounds/ding.mp3');
              audio.preload = 'auto';
              audio.playbackRate = 1.25; // Play at 2x speed
              audio.volume = 0.7; // Set to comfortable volume level

              // Add smooth audio context settings for better quality
              if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
                // Use Web Audio API for smoother playback if available
                const AudioContextClass = window.AudioContext || window.webkitAudioContext;
                if (!this.audioContext) {
                  this.audioContext = new AudioContextClass();
                }

                // Simple volume ramp for smoother start
                audio.addEventListener('loadeddata', () => {
                  audio.volume = 0.1;
                  setTimeout(() => { audio.volume = 0.7; }, 10);
                });
              }

              audio.play()
                .catch(error => {
                  // Handle any errors that might occur when playing the sound
                  console.error("Error playing sound:", error);
                });
            } catch (error) {
              console.error("Error creating audio:", error);
            }
          }
    },
    initiateTip(index,signer){
      if(this.currentUser == null){
        return;
      }

      // Set loading state for this specific signer
      this.signers[index].is_loading = true;

      const payload = {
        tip_from: this.currentUser.user_id,
        tip_to: signer.consumer_id,
        petition_id: this.petition_id,
        type: 'single_tip'
      }

      petitionApi
      .tipConsumer(payload)
      .then((res)=>{
        this.$set(this.signers[index], 'allow_tipping', 0);
        this.$set(this.signers[index], 'total_points', this.signers[index].total_points + this.tipRewardPoints);
        this.anyUserLeftToTip = this.anyUserLeftToTip - 1;
        this.playDingAudio()
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        // Clear loading state when API call completes (success or error)
        this.signers[index].is_loading = false;
      });
    },
    tipSigner(index,signer) {
      this.initiateTip(index,signer);
    },
  }
};
</script>

<style scoped>
.signer-item {
  margin: 0;
  padding: 12px 16px;
}
.tip-all-padding{
  margin: 0;
  padding: 4px 16px;
}
.tip-awarded{
  font-size:14px;
  font-family:'Montserrat';
  font-weight:600;
}
.crew-justify-content-right {
  justify-content: flex-end;
}
.crew-tip-button {
  background-color: #149240;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
}
.crew-tip-button:hover {
  background-color: #0e7532;
}
.crew-tip-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}
.crew-tip-button.loading {
  min-width: 60px;
}
.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner-border 0.75s linear infinite;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.2em;
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}
/* Responsive styles */
@media (max-width: 576px) {
  .signer-item {
    padding: 10px 12px;
  }
  .tip-all-padding{
    padding: 4px 12px;
  }
  .signer-info {
    font-size: 13px;
  }
  .crew-tip-button {
    font-size: 11px;
    padding: 4px 8px;
  }
}
</style>