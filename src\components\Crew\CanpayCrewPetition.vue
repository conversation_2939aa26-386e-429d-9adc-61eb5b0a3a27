<template>
<div>
    <div v-if="isLoading">
        <CanPayLoader/>
    </div>
    <div v-else :style="petitions.length != 0 ? 'position:absolute;width:100%;z-index:7' : ''">
        <div v-if="petitions.length == 0" class="mt-4 pb-3" style="display:flex;align-items:center;flex-direction:column;background-color:#ECECEC">
            <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-700 canpay-crew-text-font-21 pt-3">No petition found</p>
        </div>
        <div v-else class="pb-3 canpay-crew-petition-box-display-1">
            <div
                class="container"
                v-for="(item, index) in petitions"
                :key="index"
            >
            <div
                v-on:click="gotoDetailsPage(item)"
                class="canpay-crew-petition-box-1 canpay-crew-petition-padding mt-3 "

            :style="{
                borderLeft: item.type == 'mayor' ? '3px solid #FFD700' : item.type == 'crew leader' ? '3px solid #C0C0C0' : '',
            }"
            >
                <div style="margin:14px 7px 7px 0px;" class="row">
                <div class="col-1 canpay-crew-padding-left-none">
                    <svg xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 104 104" fill="none">
                    <circle cx="52" cy="52" r="52" fill="#179346"/>
                    <path d="M59.1975 55.2093C58.1131 55.2205 57.0398 54.9913 56.0546 54.5381C55.0695 54.085 54.197 53.4191 53.5 52.5884C52.7978 53.413 51.9243 54.0747 50.9403 54.5274C49.9563 54.98 48.8855 55.2127 47.8025 55.2093C46.7181 55.2205 45.6448 54.9913 44.6596 54.5381C43.6745 54.085 42.802 53.4191 42.105 52.5884C41.8948 52.8405 41.6663 53.0766 41.4213 53.2949C40.654 53.993 39.7475 54.5205 38.7615 54.8426C37.7755 55.1648 36.7324 55.2742 35.701 55.1637C33.8342 54.9337 32.1181 54.0226 30.8816 52.6053C29.6451 51.1879 28.9753 49.364 29.0007 47.4833V45.8651C28.9988 44.7036 29.1835 43.5494 29.5477 42.4465L33.1257 31.7349C33.389 30.9394 33.8963 30.247 34.5753 29.7561C35.2544 29.2652 36.0709 29.0006 36.9088 29H70.0911C70.929 29.0006 71.7455 29.2652 72.4246 29.7561C73.1037 30.247 73.6109 30.9394 73.8742 31.7349L77.4523 42.4465C77.8164 43.5494 78.0011 44.7036 77.9992 45.8651V47.4833C78.0259 49.3571 77.3625 51.1753 76.1353 52.5916C74.9082 54.0079 73.203 54.9234 71.3445 55.1637C70.313 55.2772 69.2691 55.1691 68.2826 54.8469C67.2962 54.5246 66.3899 53.9955 65.6243 53.2949C65.3736 53.067 65.1229 52.7935 64.895 52.5428C64.1961 53.3746 63.3243 54.0441 62.3404 54.5046C61.3564 54.9651 60.2839 55.2056 59.1975 55.2093ZM36.9088 32.4186C36.7892 32.4204 36.6729 32.4584 36.5753 32.5276C36.4777 32.5967 36.4033 32.6938 36.3619 32.806L32.8066 43.5177C32.5597 44.2526 32.429 45.0215 32.4192 45.7967V47.4149C32.3858 48.4437 32.7319 49.449 33.3917 50.2391C34.0515 51.0292 34.9789 51.549 35.9972 51.6995C36.5468 51.7564 37.1022 51.6986 37.6282 51.5295C38.1542 51.3605 38.6394 51.084 39.0529 50.7176C39.4664 50.3512 39.7993 49.9028 40.0305 49.401C40.2616 48.8992 40.386 48.3548 40.3957 47.8023C40.3957 47.349 40.5758 46.9142 40.8963 46.5937C41.2169 46.2731 41.6516 46.093 42.105 46.093C42.5583 46.093 42.993 46.2731 43.3136 46.5937C43.6341 46.9142 43.8142 47.349 43.8142 47.8023C43.7881 48.333 43.8734 48.8632 44.0646 49.3589C44.2558 49.8546 44.5487 50.3048 44.9243 50.6805C45.3 51.0562 45.7502 51.3491 46.2459 51.5403C46.7416 51.7315 47.2718 51.8168 47.8025 51.7907C48.8584 51.7847 49.8693 51.3626 50.616 50.6159C51.3626 49.8692 51.7847 48.8583 51.7907 47.8023C51.7907 47.349 51.9708 46.9142 52.2913 46.5937C52.6119 46.2731 53.0466 46.093 53.5 46.093C53.9533 46.093 54.388 46.2731 54.7086 46.5937C55.0291 46.9142 55.2092 47.349 55.2092 47.8023C55.1831 48.333 55.2684 48.8632 55.4596 49.3589C55.6508 49.8546 55.9437 50.3048 56.3194 50.6805C56.695 51.0562 57.1452 51.3491 57.6409 51.5403C58.1366 51.7315 58.6668 51.8168 59.1975 51.7907C60.2534 51.7847 61.2643 51.3626 62.011 50.6159C62.7576 49.8692 63.1797 48.8583 63.1857 47.8023C63.1857 47.349 63.3658 46.9142 63.6863 46.5937C64.0069 46.2731 64.4416 46.093 64.895 46.093C65.3483 46.093 65.7831 46.2731 66.1036 46.5937C66.4241 46.9142 66.6042 47.349 66.6042 47.8023C66.6045 48.3605 66.7219 48.9125 66.9488 49.4225C67.1758 49.9325 67.5073 50.3891 67.9219 50.7629C68.3365 51.1367 68.825 51.4193 69.3556 51.5924C69.8863 51.7655 70.4474 51.8253 71.0027 51.7679C72.021 51.6174 72.9484 51.0976 73.6082 50.3075C74.268 49.5173 74.6142 48.5121 74.5807 47.4833V45.8651C74.5709 45.0899 74.4402 44.321 74.1933 43.586L70.6381 32.8744C70.5967 32.7622 70.5222 32.6651 70.4246 32.5959C70.327 32.5268 70.2107 32.4888 70.0911 32.487L36.9088 32.4186Z" fill="white"/>
                    <path d="M71.732 78H35.2679C34.212 77.994 33.2011 77.5719 32.4544 76.8252C31.7078 76.0786 31.2857 75.0676 31.2797 74.0116V52.3149C31.2797 51.8615 31.4598 51.4268 31.7803 51.1062C32.1009 50.7857 32.5356 50.6056 32.9889 50.6056C33.4423 50.6056 33.877 50.7857 34.1976 51.1062C34.5181 51.4268 34.6982 51.8615 34.6982 52.3149V74.0116C34.6982 74.1627 34.7582 74.3077 34.8651 74.4145C34.9719 74.5214 35.1168 74.5814 35.2679 74.5814H71.732C71.8831 74.5814 72.028 74.5214 72.1348 74.4145C72.2417 74.3077 72.3017 74.1627 72.3017 74.0116V52.2921C72.3017 51.8388 72.4818 51.404 72.8023 51.0834C73.1229 50.7629 73.5577 50.5828 74.011 50.5828C74.4643 50.5828 74.8991 50.7629 75.2196 51.0834C75.5401 51.404 75.7202 51.8388 75.7202 52.2921V74.0116C75.7142 75.0676 75.2921 76.0786 74.5455 76.8252C73.7988 77.5719 72.7879 77.994 71.732 78Z" fill="white"/>
                    <path d="M61.4765 78H45.5235C45.072 77.9941 44.6406 77.8121 44.3214 77.4928C44.0021 77.1735 43.8201 76.7422 43.8142 76.2907V64.8953C43.8202 63.8394 44.2423 62.8284 44.9889 62.0818C45.7356 61.3351 46.7466 60.913 47.8025 60.907H59.1975C60.2534 60.913 61.2643 61.3351 62.011 62.0818C62.7576 62.8284 63.1797 63.8394 63.1857 64.8953V76.2907C63.1798 76.7422 62.9978 77.1735 62.6786 77.4928C62.3593 77.8121 61.928 77.9941 61.4765 78ZM47.2327 74.5814H59.7672V64.8953C59.7672 64.7442 59.7072 64.5993 59.6003 64.4925C59.4935 64.3856 59.3486 64.3256 59.1975 64.3256H47.8025C47.6513 64.3256 47.5064 64.3856 47.3996 64.4925C47.2927 64.5993 47.2327 64.7442 47.2327 64.8953V74.5814Z" fill="white"/>
                    </svg>
                </div>
                    <div class="col-11 canpay-crew-padding-left-none canpay-crew-padding-right-none">
                    <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-bold text-left ml-3">
                        <span style="cursor: pointer;" >{{item.store_name}} - {{ item.city }}</span>
                    </p>
                    <p class="canpay-crew-p-margin-bottom-1 text-left ml-3 mt-2 canpay-crew-text-font-13">Total Points Won (Pending): <b>{{pointNumberFormatter(item.total_reward_points)}}</b></p>
                    <div class="mt-2">
                    <div class="canpay-crew-accordian" v-on:click.stop="shouldDisplayPointer(index)">
                   <p class="canpay-crew-accordian-display canpay-crew-p-margin-bottom-1" >
                        <span>
                            <svg style="position:Relative;top:-2px;" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 75 99" fill="none">
                            <path d="M37.5 0C16.7902 0 0 16.8495 0 37.6325C0 65.9284 37.5 99 37.5 99C37.5 99 75 66.6654 75 37.6325C75 16.8495 58.2098 0 37.5 0Z" fill="#0B7F00"/>
                            <circle cx="37.5" cy="35.5" r="25.5" fill="white"/>
                            </svg> <b>Address</b>
                        </span>
                        <span >
                            <div v-if="index == 0">
                            <!--- top pointed arrow ---->
                            <svg style="position: relative;top: -1px;" v-if="item.show_accordian_address == 0" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 145 129" fill="none">
                            <path d="M62.1077 5.99999C66.7265 -2 78.2735 -2 82.8923 6L143.081 110.25C147.7 118.25 141.926 128.25 132.689 128.25H12.3112C3.07363 128.25 -2.69987 118.25 1.91893 110.25L62.1077 5.99999Z" fill="black"/>
                            </svg>
                            <!--- down arrow ---->
                            <svg style="position: relative;top: -1px;" v-if="item.show_accordian_address == 1" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 145 129" fill="none">
                            <path d="M82.8923 123C78.2735 131 66.7265 131 62.1077 123L1.91893 18.75C-2.69987 10.75 3.07362 0.75 12.3112 0.75H132.689C141.926 0.75 147.7 10.75 143.081 18.75L82.8923 123Z" fill="black"/>
                            </svg>
                            </div>
                            <div v-else>
                            <!--- top pointed arrow ---->
                            <svg style="position: relative;top: -1px;" v-if="item.show_accordian_address == 1" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 145 129" fill="none">
                            <path d="M62.1077 5.99999C66.7265 -2 78.2735 -2 82.8923 6L143.081 110.25C147.7 118.25 141.926 128.25 132.689 128.25H12.3112C3.07363 128.25 -2.69987 118.25 1.91893 110.25L62.1077 5.99999Z" fill="black"/>
                            </svg>
                            <!--- down arrow ---->
                            <svg style="position: relative;top: -1px;" v-if="item.show_accordian_address == 0" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 145 129" fill="none">
                            <path d="M82.8923 123C78.2735 131 66.7265 131 62.1077 123L1.91893 18.75C-2.69987 10.75 3.07362 0.75 12.3112 0.75H132.689C141.926 0.75 147.7 10.75 143.081 18.75L82.8923 123Z" fill="black"/>
                            </svg>
                            </div>

                        </span>
                    </p>
                    <transition
                    name="accordion"
                    @before-enter="beforeEnter"
                    @enter="enter"
                    @after-enter="afterEnter"
                    @before-leave="beforeLeave"
                    @leave="leave"
                    @after-leave="afterLeave"
                    >
                    <p
                        v-if="openAccordian(index,item.show_accordian_address)"
                        ref="accordionContent"
                        class="canpay-crew-p-margin-bottom-1 text-left ml-4 canpay-crew-text-font-13"
                        v-html="storeAddress(item)"
                    ></p>
                    </transition>


                    </div>
                    <div v-if="item.status_code != 1004"  v-on:click.stop="openSharePetition(item)" class="canpay-friend-text-button">
                        Add friends, get points
                    </div>
                    </div>
                    </div>
                    <div class="col-12 canpay-crew-padding-left-none canpay-crew-padding-right-none">
                        <hr style="border:0.5px solid #ECECEC;margin-bottom:8px;">
                        <div style="display:flex;justify-content:space-between;margin:0px 7px;" v-if="item.status_code != 1004">
                            <div >
                                <svg
                                 xmlns="http://www.w3.org/2000/svg" class="canpay-crew-petition-person-icon" viewBox="0 0 36 42" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M18 4.2C14.4285 4.2 11.5334 7.02062 11.5334 10.5C11.5334 13.9794 14.4285 16.8 18 16.8C21.5713 16.8 24.4666 13.9794 24.4666 10.5C24.4666 7.02062 21.5713 4.2 18 4.2ZM7.22236 10.5C7.22236 4.70102 12.0477 0 18 0C23.9522 0 28.7776 4.70102 28.7776 10.5C28.7776 16.299 23.9522 21 18 21C12.0477 21 7.22236 16.299 7.22236 10.5Z" fill="black"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M18 27.3C10.5797 27.3 6.679 29.8616 4.73304 32.965C4.05582 34.0452 4.22615 35.0639 5.05294 35.9999C5.95485 37.0209 7.58576 37.8 9.37789 37.8H26.6221C28.4142 37.8 30.045 37.0209 30.9469 35.9999C31.7738 35.0639 31.9441 34.0452 31.267 32.965C29.321 29.8616 25.4202 27.3 18 27.3ZM1.0541 30.7755C3.96257 26.137 9.4573 23.1 18 23.1C26.5428 23.1 32.0374 26.137 34.9458 30.7755C36.7511 33.6544 36.06 36.6505 34.2147 38.7395C32.4441 40.7438 29.5917 42 26.6221 42H9.37789C6.40818 42 3.55574 40.7438 1.78539 38.7395C-0.0601066 36.6505 -0.751016 33.6544 1.0541 30.7755Z" fill="black"/>
                                </svg><span class="canpay-crew-petition-text-count"> Signed by <b>{{item.signed_users_count}}</b> of <b>{{max_consumer_allowed}}</b></span>
                            </div>
                            <div >
                                <svg
                                 xmlns="http://www.w3.org/2000/svg" class="canpay-crew-calender-tick-icon" viewBox="0 0 41 42" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M12.3 0C13.4322 0 14.35 0.940212 14.35 2.1H30.75C36.4109 2.1 41 6.80102 41 12.6V31.5C41 37.2989 36.4109 42 30.75 42H10.25C4.58909 42 0 37.2989 0 31.5V12.6C0 6.80102 4.58909 2.1 10.25 2.1C10.25 0.940212 11.1678 0 12.3 0ZM10.25 6.3C6.85346 6.3 4.1 9.12061 4.1 12.6V31.5C4.1 34.9795 6.85346 37.8 10.25 37.8H30.75C34.1466 37.8 36.9 34.9795 36.9 31.5V12.6C36.9 9.12061 34.1466 6.3 30.75 6.3H14.35V8.4C14.35 9.55979 13.4322 10.5 12.3 10.5C11.1678 10.5 10.25 9.55979 10.25 8.4V6.3Z" fill="black"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M28.7 0C29.8322 0 30.75 0.940212 30.75 2.1V8.4C30.75 9.55979 29.8322 10.5 28.7 10.5C27.5678 10.5 26.65 9.55979 26.65 8.4V2.1C26.65 0.940212 27.5678 0 28.7 0Z" fill="black"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M28.052 17.3681C28.8779 18.1612 28.9198 19.4901 28.1455 20.3362L20.458 28.7362C19.7237 29.5388 18.5092 29.6247 17.6741 28.9334L13.0615 25.1152C12.1809 24.3863 12.0439 23.0641 12.7555 22.1619C13.4671 21.2598 14.7578 21.1195 15.6384 21.8484L18.7696 24.4404L25.1543 17.4638C25.9288 16.6176 27.226 16.5748 28.052 17.3681Z" fill="black"/>
                                </svg><span class="canpay-crew-petition-text-date"> Signed on <b>{{formatedDate(item.signed_on)}}</b></span>
                            </div>
                        </div>
                        <div style="display:flex;justify-content:space-between;margin:0px 7px;" v-else>
                            Pending Admin Approval
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </div>
    </div>
    <div>
        <canpay-crew-share-petition
            ref="CanpayCrewSharePetition"
            :passPetitionStatistics="passPetitionStatistics"
        ></canpay-crew-share-petition>
        <canpay-crew-create-petition
            ref="CanpayCrewCreatePetition"
            :storeAddress="storeAddress"
            :createThePetition="createThePetition"
            :getPetitionId="getPetitionId"
            :addAdditionalContact="addAdditionalContact"
        ></canpay-crew-create-petition>
        <crew-error
            ref="CrewError"
            :textMessage="textMessage"
        >
        </crew-error>
    <b-modal
      ref="canpay-crew-address-modal"
      hide-footer
      hide-header
      centered
      id="canpay-crew-address-modal"
      title="BootstrapVue"
    >
      <!------ initiating the canpay crew fill up end -------->
      <!------ Missing Details in Adress Start ---------------------->
      <div class="text-center mt-4" v-if="storeAddressModelStep == 1">
        <svg xmlns="http://www.w3.org/2000/svg" width="90" height="90" viewBox="0 0 104 104" fill="none">
        <circle cx="52" cy="52" r="52" fill="#179346"/>
        <path d="M58.1975 55.2093C57.1131 55.2205 56.0398 54.9913 55.0546 54.5381C54.0695 54.085 53.197 53.4191 52.5 52.5884C51.7978 53.413 50.9243 54.0747 49.9403 54.5274C48.9563 54.98 47.8855 55.2127 46.8025 55.2093C45.7181 55.2205 44.6448 54.9913 43.6596 54.5381C42.6745 54.085 41.802 53.4191 41.105 52.5884C40.8948 52.8405 40.6663 53.0766 40.4213 53.2949C39.654 53.993 38.7475 54.5205 37.7615 54.8426C36.7755 55.1648 35.7324 55.2742 34.701 55.1637C32.8342 54.9337 31.1181 54.0226 29.8816 52.6053C28.6451 51.1879 27.9753 49.364 28.0007 47.4833V45.8651C27.9988 44.7036 28.1835 43.5494 28.5477 42.4465L32.1257 31.7349C32.389 30.9394 32.8963 30.247 33.5753 29.7561C34.2544 29.2652 35.0709 29.0006 35.9088 29H69.0911C69.929 29.0006 70.7455 29.2652 71.4246 29.7561C72.1037 30.247 72.6109 30.9394 72.8742 31.7349L76.4523 42.4465C76.8164 43.5494 77.0011 44.7036 76.9992 45.8651V47.4833C77.0259 49.3571 76.3625 51.1753 75.1353 52.5916C73.9082 54.0079 72.203 54.9234 70.3445 55.1637C69.313 55.2772 68.2691 55.1691 67.2826 54.8469C66.2962 54.5246 65.3899 53.9955 64.6243 53.2949C64.3736 53.067 64.1229 52.7935 63.895 52.5428C63.1961 53.3746 62.3243 54.0441 61.3404 54.5046C60.3564 54.9651 59.2839 55.2056 58.1975 55.2093ZM35.9088 32.4186C35.7892 32.4204 35.6729 32.4584 35.5753 32.5276C35.4777 32.5967 35.4033 32.6938 35.3619 32.806L31.8066 43.5177C31.5597 44.2526 31.429 45.0215 31.4192 45.7967V47.4149C31.3858 48.4437 31.7319 49.449 32.3917 50.2391C33.0515 51.0292 33.9789 51.549 34.9972 51.6995C35.5468 51.7564 36.1022 51.6986 36.6282 51.5295C37.1542 51.3605 37.6394 51.084 38.0529 50.7176C38.4664 50.3512 38.7993 49.9028 39.0305 49.401C39.2616 48.8992 39.386 48.3548 39.3957 47.8023C39.3957 47.349 39.5758 46.9142 39.8963 46.5937C40.2169 46.2731 40.6516 46.093 41.105 46.093C41.5583 46.093 41.993 46.2731 42.3136 46.5937C42.6341 46.9142 42.8142 47.349 42.8142 47.8023C42.7881 48.333 42.8734 48.8632 43.0646 49.3589C43.2558 49.8546 43.5487 50.3048 43.9243 50.6805C44.3 51.0562 44.7502 51.3491 45.2459 51.5403C45.7416 51.7315 46.2718 51.8168 46.8025 51.7907C47.8584 51.7847 48.8693 51.3626 49.616 50.6159C50.3626 49.8692 50.7847 48.8583 50.7907 47.8023C50.7907 47.349 50.9708 46.9142 51.2913 46.5937C51.6119 46.2731 52.0466 46.093 52.5 46.093C52.9533 46.093 53.388 46.2731 53.7086 46.5937C54.0291 46.9142 54.2092 47.349 54.2092 47.8023C54.1831 48.333 54.2684 48.8632 54.4596 49.3589C54.6508 49.8546 54.9437 50.3048 55.3194 50.6805C55.695 51.0562 56.1452 51.3491 56.6409 51.5403C57.1366 51.7315 57.6668 51.8168 58.1975 51.7907C59.2534 51.7847 60.2643 51.3626 61.011 50.6159C61.7576 49.8692 62.1797 48.8583 62.1857 47.8023C62.1857 47.349 62.3658 46.9142 62.6863 46.5937C63.0069 46.2731 63.4416 46.093 63.895 46.093C64.3483 46.093 64.7831 46.2731 65.1036 46.5937C65.4241 46.9142 65.6042 47.349 65.6042 47.8023C65.6045 48.3605 65.7219 48.9125 65.9488 49.4225C66.1758 49.9325 66.5073 50.3891 66.9219 50.7629C67.3365 51.1367 67.825 51.4193 68.3556 51.5924C68.8863 51.7655 69.4474 51.8253 70.0027 51.7679C71.021 51.6174 71.9484 51.0976 72.6082 50.3075C73.268 49.5173 73.6142 48.5121 73.5807 47.4833V45.8651C73.5709 45.0899 73.4402 44.321 73.1933 43.586L69.6381 32.8744C69.5967 32.7622 69.5222 32.6651 69.4246 32.5959C69.327 32.5268 69.2107 32.4888 69.0911 32.487L35.9088 32.4186Z" fill="white"/>
        <path d="M70.732 78H34.2679C33.212 77.994 32.2011 77.5719 31.4544 76.8252C30.7078 76.0786 30.2857 75.0676 30.2797 74.0116V52.3149C30.2797 51.8615 30.4598 51.4268 30.7803 51.1062C31.1009 50.7857 31.5356 50.6056 31.9889 50.6056C32.4423 50.6056 32.877 50.7857 33.1976 51.1062C33.5181 51.4268 33.6982 51.8615 33.6982 52.3149V74.0116C33.6982 74.1627 33.7582 74.3077 33.8651 74.4145C33.9719 74.5214 34.1168 74.5814 34.2679 74.5814H70.732C70.8831 74.5814 71.028 74.5214 71.1348 74.4145C71.2417 74.3077 71.3017 74.1627 71.3017 74.0116V52.2921C71.3017 51.8388 71.4818 51.404 71.8023 51.0834C72.1229 50.7629 72.5577 50.5828 73.011 50.5828C73.4643 50.5828 73.8991 50.7629 74.2196 51.0834C74.5401 51.404 74.7202 51.8388 74.7202 52.2921V74.0116C74.7142 75.0676 74.2921 76.0786 73.5455 76.8252C72.7988 77.5719 71.7879 77.994 70.732 78Z" fill="white"/>
        <path d="M60.4765 78H44.5235C44.072 77.9941 43.6406 77.8121 43.3214 77.4928C43.0021 77.1735 42.8201 76.7422 42.8142 76.2907V64.8953C42.8202 63.8394 43.2423 62.8284 43.9889 62.0818C44.7356 61.3351 45.7466 60.913 46.8025 60.907H58.1975C59.2534 60.913 60.2643 61.3351 61.011 62.0818C61.7576 62.8284 62.1797 63.8394 62.1857 64.8953V76.2907C62.1798 76.7422 61.9978 77.1735 61.6786 77.4928C61.3593 77.8121 60.928 77.9941 60.4765 78ZM46.2327 74.5814H58.7672V64.8953C58.7672 64.7442 58.7072 64.5993 58.6003 64.4925C58.4935 64.3856 58.3486 64.3256 58.1975 64.3256H46.8025C46.6513 64.3256 46.5064 64.3856 46.3996 64.4925C46.2927 64.5993 46.2327 64.7442 46.2327 64.8953V74.5814Z" fill="white"/>
        </svg>

        <p class="canpay-crew-text-font-18 canpay-crew-p-margin-bottom-1 canpay-crew-text-700 mt-3">Enter Store Name</p>
        <p v-if="storeData.length == 0"  class="canpay-crew-p-margin-bottom-1 canpay-crew-text-font-16 mt-4">We couldn’t find your store. Want to start a new petition?</p>

        <div class="mt-3 row">
            <div class="col-12 text-center">
              <gmap-autocomplete
                ref="storeAddressRef"
                :value="storeAddressModel"
                placeholder="Store Name"
                @place_changed="setStorePlace"
                id="findStoreAddress"
                class="canpay-crew-general-input-box"
                :style="storeAddressError == true?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}"
                :options="autocompleteOptions">
              </gmap-autocomplete>
               <!-- Optional Error Message -->
                <div v-if="storeAddressError" class="text-red-crew">
                    Please provide a complete US store address.
                </div>

            </div>
        </div>

        <div class="row mt-3">
            <div class="col-12">
                <button class="canpay-crew-sign-petition-modal-button" v-on:click="continueStorePlace()">
                Continue
                </button>
            </div>
            <div class="col-12">
                <button class="canpay-crew-sign-petition-not-ok-modal-button mt-2" v-on:click="hideModal('canpay-crew-address-modal')">
                    Not Right Now
                </button>
            </div>

        </div>
      </div>

      <!------ Missing Details in Adress Start ---------------------->
      <div class="text-center mt-4" v-if="storeAddressModelStep == 2">
        <svg xmlns="http://www.w3.org/2000/svg" width="104" height="104" viewBox="0 0 104 104" fill="none">
        <circle cx="52" cy="52" r="52" fill="#179346"/>
        <path d="M58.1975 55.2093C57.1131 55.2205 56.0398 54.9913 55.0546 54.5381C54.0695 54.085 53.197 53.4191 52.5 52.5884C51.7978 53.413 50.9243 54.0747 49.9403 54.5274C48.9563 54.98 47.8855 55.2127 46.8025 55.2093C45.7181 55.2205 44.6448 54.9913 43.6596 54.5381C42.6745 54.085 41.802 53.4191 41.105 52.5884C40.8948 52.8405 40.6663 53.0766 40.4213 53.2949C39.654 53.993 38.7475 54.5205 37.7615 54.8426C36.7755 55.1648 35.7324 55.2742 34.701 55.1637C32.8342 54.9337 31.1181 54.0226 29.8816 52.6053C28.6451 51.1879 27.9753 49.364 28.0007 47.4833V45.8651C27.9988 44.7036 28.1835 43.5494 28.5477 42.4465L32.1257 31.7349C32.389 30.9394 32.8963 30.247 33.5753 29.7561C34.2544 29.2652 35.0709 29.0006 35.9088 29H69.0911C69.929 29.0006 70.7455 29.2652 71.4246 29.7561C72.1037 30.247 72.6109 30.9394 72.8742 31.7349L76.4523 42.4465C76.8164 43.5494 77.0011 44.7036 76.9992 45.8651V47.4833C77.0259 49.3571 76.3625 51.1753 75.1353 52.5916C73.9082 54.0079 72.203 54.9234 70.3445 55.1637C69.313 55.2772 68.2691 55.1691 67.2826 54.8469C66.2962 54.5246 65.3899 53.9955 64.6243 53.2949C64.3736 53.067 64.1229 52.7935 63.895 52.5428C63.1961 53.3746 62.3243 54.0441 61.3404 54.5046C60.3564 54.9651 59.2839 55.2056 58.1975 55.2093ZM35.9088 32.4186C35.7892 32.4204 35.6729 32.4584 35.5753 32.5276C35.4777 32.5967 35.4033 32.6938 35.3619 32.806L31.8066 43.5177C31.5597 44.2526 31.429 45.0215 31.4192 45.7967V47.4149C31.3858 48.4437 31.7319 49.449 32.3917 50.2391C33.0515 51.0292 33.9789 51.549 34.9972 51.6995C35.5468 51.7564 36.1022 51.6986 36.6282 51.5295C37.1542 51.3605 37.6394 51.084 38.0529 50.7176C38.4664 50.3512 38.7993 49.9028 39.0305 49.401C39.2616 48.8992 39.386 48.3548 39.3957 47.8023C39.3957 47.349 39.5758 46.9142 39.8963 46.5937C40.2169 46.2731 40.6516 46.093 41.105 46.093C41.5583 46.093 41.993 46.2731 42.3136 46.5937C42.6341 46.9142 42.8142 47.349 42.8142 47.8023C42.7881 48.333 42.8734 48.8632 43.0646 49.3589C43.2558 49.8546 43.5487 50.3048 43.9243 50.6805C44.3 51.0562 44.7502 51.3491 45.2459 51.5403C45.7416 51.7315 46.2718 51.8168 46.8025 51.7907C47.8584 51.7847 48.8693 51.3626 49.616 50.6159C50.3626 49.8692 50.7847 48.8583 50.7907 47.8023C50.7907 47.349 50.9708 46.9142 51.2913 46.5937C51.6119 46.2731 52.0466 46.093 52.5 46.093C52.9533 46.093 53.388 46.2731 53.7086 46.5937C54.0291 46.9142 54.2092 47.349 54.2092 47.8023C54.1831 48.333 54.2684 48.8632 54.4596 49.3589C54.6508 49.8546 54.9437 50.3048 55.3194 50.6805C55.695 51.0562 56.1452 51.3491 56.6409 51.5403C57.1366 51.7315 57.6668 51.8168 58.1975 51.7907C59.2534 51.7847 60.2643 51.3626 61.011 50.6159C61.7576 49.8692 62.1797 48.8583 62.1857 47.8023C62.1857 47.349 62.3658 46.9142 62.6863 46.5937C63.0069 46.2731 63.4416 46.093 63.895 46.093C64.3483 46.093 64.7831 46.2731 65.1036 46.5937C65.4241 46.9142 65.6042 47.349 65.6042 47.8023C65.6045 48.3605 65.7219 48.9125 65.9488 49.4225C66.1758 49.9325 66.5073 50.3891 66.9219 50.7629C67.3365 51.1367 67.825 51.4193 68.3556 51.5924C68.8863 51.7655 69.4474 51.8253 70.0027 51.7679C71.021 51.6174 71.9484 51.0976 72.6082 50.3075C73.268 49.5173 73.6142 48.5121 73.5807 47.4833V45.8651C73.5709 45.0899 73.4402 44.321 73.1933 43.586L69.6381 32.8744C69.5967 32.7622 69.5222 32.6651 69.4246 32.5959C69.327 32.5268 69.2107 32.4888 69.0911 32.487L35.9088 32.4186Z" fill="white"/>
        <path d="M70.732 78H34.2679C33.212 77.994 32.2011 77.5719 31.4544 76.8252C30.7078 76.0786 30.2857 75.0676 30.2797 74.0116V52.3149C30.2797 51.8615 30.4598 51.4268 30.7803 51.1062C31.1009 50.7857 31.5356 50.6056 31.9889 50.6056C32.4423 50.6056 32.877 50.7857 33.1976 51.1062C33.5181 51.4268 33.6982 51.8615 33.6982 52.3149V74.0116C33.6982 74.1627 33.7582 74.3077 33.8651 74.4145C33.9719 74.5214 34.1168 74.5814 34.2679 74.5814H70.732C70.8831 74.5814 71.028 74.5214 71.1348 74.4145C71.2417 74.3077 71.3017 74.1627 71.3017 74.0116V52.2921C71.3017 51.8388 71.4818 51.404 71.8023 51.0834C72.1229 50.7629 72.5577 50.5828 73.011 50.5828C73.4643 50.5828 73.8991 50.7629 74.2196 51.0834C74.5401 51.404 74.7202 51.8388 74.7202 52.2921V74.0116C74.7142 75.0676 74.2921 76.0786 73.5455 76.8252C72.7988 77.5719 71.7879 77.994 70.732 78Z" fill="white"/>
        <path d="M60.4765 78H44.5235C44.072 77.9941 43.6406 77.8121 43.3214 77.4928C43.0021 77.1735 42.8201 76.7422 42.8142 76.2907V64.8953C42.8202 63.8394 43.2423 62.8284 43.9889 62.0818C44.7356 61.3351 45.7466 60.913 46.8025 60.907H58.1975C59.2534 60.913 60.2643 61.3351 61.011 62.0818C61.7576 62.8284 62.1797 63.8394 62.1857 64.8953V76.2907C62.1798 76.7422 61.9978 77.1735 61.6786 77.4928C61.3593 77.8121 60.928 77.9941 60.4765 78ZM46.2327 74.5814H58.7672V64.8953C58.7672 64.7442 58.7072 64.5993 58.6003 64.4925C58.4935 64.3856 58.3486 64.3256 58.1975 64.3256H46.8025C46.6513 64.3256 46.5064 64.3856 46.3996 64.4925C46.2927 64.5993 46.2327 64.7442 46.2327 64.8953V74.5814Z" fill="white"/>
        </svg>

        <p class="canpay-crew-text-font-18 canpay-crew-p-margin-bottom-1 canpay-crew-text-700 mt-3">The store address is incomplete. Please add the missing details below</p>

        <div class="mt-3 row">
            <div class="col-12">
                <input type="text"
                    class="canpay-crew-general-input-box-1 canpay-crew-p-margin-bottom"
                    required placeholder="Store Name" v-model="googleStoreData.store_name"
                    :style="googleStoreDataError.error_store_name != ''?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}">
                <p class="text-red-crew" v-if="googleStoreDataError.error_store_name != ''">{{googleStoreDataError.error_store_name}}</p>
                <input type="text"
                    class="canpay-crew-general-input-box-1 canpay-crew-p-margin-bottom"
                    required placeholder="Street Address" v-model="googleStoreData.street_address"
                    :style="googleStoreDataError.error_street_address != ''?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}">
                <p class="text-red-crew" v-if="googleStoreDataError.error_street_address != ''">{{googleStoreDataError.error_street_address}}</p>
                <input type="text"
                    class="canpay-crew-general-input-box-1 canpay-crew-p-margin-bottom"
                    required placeholder="City" v-model="googleStoreData.city"
                    :style="googleStoreDataError.error_city != ''?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}">
                <p class="text-red-crew" v-if="googleStoreDataError.error_city != ''">{{googleStoreDataError.error_city}}</p>
                <input type="text"
                    class="canpay-crew-general-input-box-1 canpay-crew-p-margin-bottom"
                    required :maxlength="2" placeholder="State (e.g., CA, NY)" v-model="googleStoreData.state"
                    :style="googleStoreDataError.error_state != ''?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}">
                <p class="text-red-crew" v-if="googleStoreDataError.error_state != ''">{{googleStoreDataError.error_state}}</p>
                <input type="text"
                    class="canpay-crew-general-input-box-1 canpay-crew-p-margin-bottom"
                    required placeholder="Zipcode" v-on:keypress="zipisNumber($event)" v-model="googleStoreData.zipcode"
                    :style="googleStoreDataError.error_zipcode != ''?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}">
                <p class="text-red-crew" v-if="googleStoreDataError.error_zipcode != ''">{{googleStoreDataError.error_zipcode}}</p>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <button class="canpay-crew-sign-petition-modal-button" v-on:click="updateTheGoogleAddress()">
                    Update the Address
                </button>
            </div>

        </div>
      </div>
      <!------ Missing Details in Adress End ------------------------->
      <!------ Missing Details in Adress End ------------------------->
    </b-modal>
    </div>
</div>
</template>
<script>
import CrewError from "./Modal/CrewError.vue";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue";
import petitionApi from "../../api/petition.js";
import CanpayCrewActivePetition from "./Modal/CanpayCrewActivePetition.vue"
import CanpayCrewSharePetition from "./Modal/CanpayCrewSharePetition.vue"
import CanpayCrewCreatePetition from "./Modal/CanpayCrewCreatePetition.vue"
import moment from "moment";
export default {
    name:"CanpayCrewPetition",
    components:{
        CanPayLoader,
        CanpayCrewActivePetition,
        CanpayCrewSharePetition,
        CanpayCrewCreatePetition,
        CrewError
    },
    data(){
        return{
            googleStoreData:{},
            petitions:[],
            storeData: [], // Added missing storeData property
            storeAddressModelStep: 1,
            current_petition:[],
            withdrawSuccess:false,
            storeAddressError: false, // Flag for error handling
            isLoading:false,
            storeAddressModel: '',
            petitionStatistics:{
                consumer_active_petition_count :0,
                total_store_petition_count:0,
                total_signers: 0,
                total_accept_canpay_stores_via_crew : 0,
                lifetime_rewards_from_crew: 0,
            },
            textMessage:"",
            max_consumer_allowed:process.env.VUE_APP_MAX_CONSUMER_ALLOWED_TO_SIGN,
            storeObj: {
                store_name: "",
                page: 1,
                per_page: 10
            },
            autocompleteOptions: {
                types: ['establishment'], // Only search businesses like stores
                componentRestrictions: { country: 'us' }
            },
            usaState: [
                'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA',
                'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD',
                'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ',
                'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC',
                'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'
            ],
            googleStoreDataError: {
                error_store_name: '',
                error_street_address: '',
                error_city: '',
                error_state: '',
                error_zipcode: ''
            },
            petition_id: null,
        }
    },

    methods: {
        openAccordian(index,item){
            if(index == 0){
                if(item == false){
                    return true;
                }else{
                    false;
                }
            }else{
                return item;
            }
        },
        shouldDisplayPointer(index){
            let self = this;
            self.petitions[index].show_accordian_address = !self.petitions[index].show_accordian_address;
        },
        storeAddress(storeAdd) {
            var full_address = "";
            full_address =
                full_address +
                (storeAdd.street_address != null ? storeAdd.street_address + "<br>" : "");
            full_address =
                full_address + (storeAdd.city != null ? storeAdd.city + ", " : "");
            full_address =
                full_address + (storeAdd.state != null ? storeAdd.state + " " : "");
            full_address =
                full_address + (storeAdd.zipcode != null ? storeAdd.zipcode + " " : "");
            return full_address;
        },
        addAdditionalContact(payload){
            let self = this;
            self.isLoading = true;
            this.$refs.CanpayCrewCreatePetition.closeCanPayCrewCreatePetition(false);
            petitionApi
            .addAdditionalContact(payload)
            .then((res) => {
                this.$router.go()
            })
            .catch((err) => {
                this.textMessage = err.response.data.message;
                this.$refs.CrewError.showErrorModal();
            })
            .finally(() =>{
                self.isLoading = false;
            })
        },
        passPetitionStatistics(){
            let self = this;
            return self.petitionStatistics;
        },
        openSharePetition(data){
            this.$refs.CanpayCrewSharePetition.openSharePetition(data);
        },
        gotoDetailsPage(item) {
            this.$router.push({ name: 'PetitionDetails', params: { encodedData: btoa(item.id) } });
        },
        fetchPetitions() {
            let self = this;
            const payload = {
                status: "pending",
            }
            self.isLoading = true;
            petitionApi
            .petitionList(payload)
            .then((res)=>{
                self.petitions = res.data;
            })
            .catch((err) => {
                this.textMessage = err.response.data.message;
                this.$refs.CrewError.showErrorModal();
            })
            .finally(()=>{
               self.isLoading = false;
            })
        },
        formatedDate(date) {
            return moment
            .utc(date)
            .local()
            .format("MMM D, YYYY");
        },
        beforeEnter(el) {
            el.style.maxHeight = '0';
            el.style.opacity = '0';
            el.style.overflow = 'hidden';
        },
        enter(el) {
            el.style.transition = 'max-height 0.3s ease, opacity 0.3s ease';
            const scrollHeight = el.scrollHeight + 'px';
            void el.offsetHeight; // trigger reflow
            el.style.maxHeight = scrollHeight;
            el.style.opacity = '1';
        },
        afterEnter(el) {
            el.style.maxHeight = 'none'; // allow natural height
            el.style.overflow = 'visible';
        },
        beforeLeave(el) {
            el.style.maxHeight = el.scrollHeight + 'px';
            el.style.opacity = '1';
            el.style.overflow = 'hidden';
        },
        leave(el) {
            el.style.transition = 'max-height 0.3s ease, opacity 0.3s ease';
            void el.offsetHeight;
            el.style.maxHeight = '0';
            el.style.opacity = '0';
        },
        afterLeave(el) {
            // cleanup
            el.style.maxHeight = null;
            el.style.opacity = null;
            el.style.overflow = null;
        },
        setStorePlace(place) {
            var self = this;
            const getAddressComponent = (components, type, useShortName = false) => {
                const comp = components.find(c => c.types.includes(type));
                return comp ? (useShortName ? comp.short_name : comp.long_name) : null;
            };

            const formattedAddress = place.formatted_address || '';
            const streetNumber = getAddressComponent(place.address_components, 'street_number');
            const route = getAddressComponent(place.address_components, 'route');
            const streetAddress = [streetNumber, route].filter(Boolean).join(' ') || null;
            const aptNumber = null; // Autocomplete won't provide this — you can ask the user separately
            const city = getAddressComponent(place.address_components, 'locality') ||
                        getAddressComponent(place.address_components, 'sublocality') ||
                        getAddressComponent(place.address_components, 'administrative_area_level_2');
            let state = getAddressComponent(place.address_components, 'administrative_area_level_1', true); // short_name here
            // Make comparison case-insensitive by converting both to uppercase
            if (state && !self.usaState.map(s => s.toUpperCase()).includes(state.toUpperCase())) {
                state = null;
            }

            const zipcode = getAddressComponent(place.address_components, 'postal_code');
            const lat = place.geometry?.location?.lat?.();
            const lng = place.geometry?.location?.lng?.();

            self.googleStoreData = {
                store_name: place.name || null,
                formatted_address: formattedAddress,
                street_address: streetAddress,
                apt_number: aptNumber,
                city: city,
                state: state,
                zipcode: zipcode,
                place_id: place.place_id || null,
                latitude: lat ?? null,
                longitude: lng ?? null
            };
            self.storeAddressError = false;

            self.storeAddressModel = `${place.name || ''}, ${place.formatted_address || ''}`;
        },
        continueStorePlace() {
            var self = this;
            if (Object.keys(self.googleStoreData).length === 0) {
                // Show error if any required field is missing
                self.storeAddressError = true;
                return false;
            } else {
                // 🔍 Validate required fields
                const { store_name, street_address, city, state, zipcode } = this.googleStoreData;

                if (!store_name || !street_address || !city || !state || !zipcode) {
                    // Reset all errors first
                    self.clearErrors();
                    self.storeAddressModelStep = 2; // Switch to manual entry
                    self.storeAddressError = false;
                    return false;
                } else {
                    self.storeAddressError = false;
                    self.hideModal("canpay-crew-address-modal");
                    self.createPetition(self.googleStoreData);

                    return true;
                }
            }
        },
        hideModal(modal) {
            this.$refs[modal].hide();
        },
        clearErrors() {
            let self = this;
            self.googleStoreDataError = {
                error_store_name: '',
                error_street_address: '',
                error_city: '',
                error_state: '',
                error_zipcode: ''
            };
        },
        createThePetition(payload){
            let self = this;
            self.petition_id = null;
            self.isLoading = true;
            this.$refs.CanpayCrewCreatePetition.closeCanPayCrewCreatePetition(false);
            petitionApi
            .createPetition(payload)
            .then((res) => {
                self.isLoading = false;
                self.petition_id = res.data.petition_id;
                this.$refs.CanpayCrewCreatePetition.showFormSucessMessage();
            })
            .catch((err) => {
                this.textMessage = err.response.data.message;
                this.$refs.CrewError.showErrorModal();
                if(err && err.response.data.code == 598){
                    setTimeout(()=>{
                        this.$router.push({ name: 'PetitionDetails', params: { encodedData: btoa(err.response.data.data) } });
                    },3010);
                }
                self.isLoading = false;
            });
        },
        getPetitionId(){
            let self = this;
            return self.petition_id;
        },
        createPetition(item){
            let self = this;
            self.petition_id = null;
            self.isLoading = true;
            petitionApi
            .petitionExistsCheck(item)
            .then(() => {
                self.isLoading = false;
                this.$refs.CanpayCrewCreatePetition.showCanpayCrewCreatePetition(item);
            })
            .catch((err) => {
                this.textMessage = err.response.data.message;
                this.$refs.CrewError.showErrorModal();
                if(err && err.response.data.code == 598){
                    setTimeout(()=>{
                        this.$router.push({ name: 'PetitionDetails', params: { encodedData: btoa(err.response.data.data) } });
                    },3010);
                }
                self.isLoading = false;
            });
        },
        triggerGoogleAutocomplete() {
            const autocompleteInput = document.getElementById('findStoreAddress');
            if (autocompleteInput) {
                autocompleteInput.focus();

                // Programmatically set input value
                const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
                    window.HTMLInputElement.prototype,
                    'value'
                ).set;
                nativeInputValueSetter.call(autocompleteInput, this.storeAddressModel);

                // Dispatch input event to trigger suggestions
                autocompleteInput.dispatchEvent(new Event('input', { bubbles: true }));

                // Optional: simulate keypress to help trigger the dropdown
                autocompleteInput.dispatchEvent(new KeyboardEvent('keydown', { bubbles: true, keyCode: 40 }));
            }
        },
        findStoreToCreatePetition(){
            let self = this;
            self.storeAddressError = false;
            self.googleStoreData = {};
            self.storeAddressModelStep = 1; // 1 for google place serch, 2 for manual entry
            self.storeAddressModel = self.storeObj.store_name;
            this.$refs["canpay-crew-address-modal"].show();
            // Wait for DOM to update, then focus
            this.$nextTick(() => {
                setTimeout(() => {
                    this.triggerGoogleAutocomplete();
                }, 300); // Delay slightly to allow rendering
            });
        },
        zipisNumber(evt) {
            // Allow only numbers in zipcode field
            const keysAllowed = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
            const keyPressed = evt.key;
            if (!keysAllowed.includes(keyPressed)) {
                evt.preventDefault();
            }
        },
        updateTheGoogleAddress() {
            // Validate the manually entered address
            let self = this;
            let isValid = true;

            // Reset errors
            self.clearErrors();

            // Validate store name
            if (!self.googleStoreData.store_name || self.googleStoreData.store_name.trim() === '') {
                self.googleStoreDataError.error_store_name = 'Store name is required';
                isValid = false;
            }

            // Validate street address
            if (!self.googleStoreData.street_address || self.googleStoreData.street_address.trim() === '') {
                self.googleStoreDataError.error_street_address = 'Street address is required';
                isValid = false;
            }

            // Validate city
            if (!self.googleStoreData.city || self.googleStoreData.city.trim() === '') {
                self.googleStoreDataError.error_city = 'City is required';
                isValid = false;
            }

            // Validate state
            if (!self.googleStoreData.state || self.googleStoreData.state.trim() === '') {
                self.googleStoreDataError.error_state = 'State is required';
                isValid = false;
            } else if (!self.usaState.map(s => s.toUpperCase()).includes(self.googleStoreData.state.toUpperCase())) {
                self.googleStoreDataError.error_state = 'Please enter a valid US state code (e.g., CA, NY)';
                isValid = false;
            }

            // Validate zipcode
            if (!self.googleStoreData.zipcode || self.googleStoreData.zipcode.trim() === '') {
                self.googleStoreDataError.error_zipcode = 'Zipcode is required';
                isValid = false;
            } else if (!/^\d{5}(-\d{4})?$/.test(self.googleStoreData.zipcode)) {
                self.googleStoreDataError.error_zipcode = 'Please enter a valid US zipcode';
                isValid = false;
            }

            if (isValid) {
                self.hideModal("canpay-crew-address-modal");
                self.createPetition(self.googleStoreData);
            }
        }
    },
    mounted(){
        var element = document.getElementsByClassName("content-wrap");
        if (element[0]) {
        element[0].style.setProperty("background-color", "#149240");
        element[0].style.height = "114vh";
        if(window.innerWidth>1200){
            element[0].style.height = "121vh";
        }
        }
        this.$root.$emit("changeWhiteBackground", [false, false, "CanPayCrewPetitionHeader", true]);
        this.fetchPetitions();
        this.$root.$emit("customIntercomButton", {
            show: true,
            type: "launch_petition"
        });
    },
}
</script>


