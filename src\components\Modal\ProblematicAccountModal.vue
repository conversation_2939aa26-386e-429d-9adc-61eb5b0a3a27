<template>
    <div>
    <div>
        <b-modal
            ref="problematic-account-modal"
            hide-footer
            no-close-on-backdrop
            no-close-on-esc
            modal-backdrop
            hide-header
            id="problematic-account-modal"
            centered
        >
            <div class="text-center">
                <div>
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="85" height="85" viewBox="0 0 213 186" fill="none">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H213V186H0V0Z" fill="url(#pattern0_10067_408)"/>
                <defs>
                    <pattern id="pattern0_10067_408" patternContentUnits="objectBoundingBox" width="1" height="1">
                    <use xlink:href="#image0_10067_408" transform="scale(0.******** 0.********)"/>
                    </pattern>
                    <image id="image0_10067_408" width="213" height="186" xlink:href="data:image/png;base64,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"/>
                </defs>
                </svg>
                <p style="font-weight:bolder;font-size:17px;font-family:'montserrat';" class="mt-3">
                   There is an issue with the connection to your banking. Please click “Fix” below and follow any steps that your bank requires.
                </p>
                </div>
                <div class="row mt-3 mb-2">
                <div class="col-12" >
                <div class="position-bank-svg" style="display:flex;margin-left:39%;">
                <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 1024 1280" xml:space="preserve" height="75" width="75" fill="#149240"><g><g><g><path d="M904.2,390.1c-7,0-14,0-21,0c-19,0-37.9,0-56.9,0c-28.2,0-56.3,0-84.5,0c-34.2,0-68.4,0-102.7,0c-37.6,0-75.3,0-112.9,0
                        c-38,0-76,0-114,0c-35.3,0-70.6,0-105.8,0c-29.9,0-59.9,0-89.8,0c-21.4,0-42.9,0-64.3,0c-10.2,0-20.4-0.3-30.6,0
                        c-0.4,0-0.9,0-1.3,0c6.7,6.7,13.3,13.3,20,20c0-28.2,0-56.4,0-84.6c-3.3,5.8-6.6,11.5-9.9,17.3c12.9-6.7,25.8-13.5,38.7-20.2
                        c31-16.1,62-32.3,92.9-48.4c37.3-19.5,74.7-38.9,112-58.4c32.5-17,65-33.9,97.6-50.9c15.6-8.2,31.7-15.8,47.1-24.5
                        c0.2-0.1,0.5-0.2,0.7-0.4c-6.7,0-13.5,0-20.2,0c13.1,6.7,26.1,13.4,39.2,20.1c31.5,16.2,63,32.3,94.5,48.5
                        c38,19.5,76,39,114,58.5c33,16.9,65.9,33.8,98.9,50.7c15.9,8.1,31.6,17.1,47.8,24.5c0.2,0.1,0.4,0.2,0.7,0.3
                        c-3.3-5.8-6.6-11.5-9.9-17.3c0,28.2,0,56.4,0,84.6c0,10.5,9.2,20.5,20,20s20-8.8,20-20c0-28.2,0-56.4,0-84.6
                        c0-6.7-3.7-14.1-9.9-17.3c-13.1-6.7-26.1-13.4-39.2-20.1c-31.2-16-62.5-32.1-93.7-48.1c-37.8-19.4-75.7-38.8-113.5-58.3
                        c-33-16.9-66-33.9-99-50.8c-16.3-8.4-32.4-17-48.9-25.1c-7.1-3.5-14-3.9-21.3-0.3c-1.5,0.7-2.9,1.5-4.4,2.3
                        c-7.7,4-15.4,8-23.1,12c-29.1,15.2-58.2,30.3-87.3,45.5c-37.7,19.6-75.3,39.3-113,58.9c-34,17.7-68.1,35.5-102.1,53.2
                        c-18.7,9.8-37.5,19.5-56.2,29.3c-0.9,0.4-1.7,0.9-2.6,1.3c-6.2,3.2-9.9,10.5-9.9,17.3c0,28.2,0,56.4,0,84.6c0,10.8,9.2,20,20,20
                        c7,0,14,0,21,0c19,0,37.9,0,56.9,0c28.2,0,56.3,0,84.5,0c34.2,0,68.4,0,102.7,0c37.6,0,75.3,0,112.9,0c38,0,76,0,114,0
                        c35.3,0,70.6,0,105.8,0c29.9,0,59.9,0,89.8,0c21.4,0,42.9,0,64.3,0c10.2,0,20.4,0.2,30.6,0c0.4,0,0.9,0,1.3,0
                        c10.5,0,20.5-9.2,20-20C923.7,399.3,915.4,390.1,904.2,390.1z"></path></g></g><g><g><path d="M924,881.1c-7.4,0-14.8,0-22.1,0c-20,0-40,0-59.9,0c-29.5,0-59,0-88.6,0c-36,0-72,0-108,0c-39.6,0-79.2,0-118.8,0
                        c-39.8,0-79.6,0-119.5,0c-37.1,0-74.3,0-111.4,0c-31.4,0-62.8,0-94.2,0c-22.7,0-45.3,0-68,0c-10.7,0-21.4-0.2-32.1,0
                        c-0.5,0-0.9,0-1.4,0c-10.5,0-20.5,9.2-20,20s8.8,20,20,20c7.4,0,14.8,0,22.1,0c20,0,40,0,59.9,0c29.5,0,59,0,88.6,0
                        c36,0,72,0,108,0c39.6,0,79.2,0,118.8,0c39.8,0,79.6,0,119.5,0c37.1,0,74.3,0,111.4,0c31.4,0,62.8,0,94.2,0c22.7,0,45.3,0,68,0
                        c10.7,0,21.4,0.2,32.1,0c0.5,0,0.9,0,1.4,0c10.5,0,20.5-9.2,20-20C943.5,890.3,935.2,881.1,924,881.1L924,881.1z"></path></g></g><g><g><path d="M391.3,510.1c0,12.9,0,25.9,0,38.8c0,31.1,0,62.2,0,93.3c0,37.6,0,75.3,0,112.9c0,32.6,0,65.1,0,97.7
                        c0,15.9-0.4,31.8,0,47.7c0,0.2,0,0.4,0,0.7c0,10.5,9.2,20.5,20,20s20-8.8,20-20c0-12.9,0-25.9,0-38.8c0-31.1,0-62.2,0-93.3
                        c0-37.6,0-75.3,0-112.9c0-32.6,0-65.1,0-97.7c0-15.9,0.4-31.8,0-47.7c0-0.2,0-0.4,0-0.7c0-10.5-9.2-20.5-20-20
                        C400.5,490.6,391.3,498.9,391.3,510.1L391.3,510.1z"></path></g></g><g><g><path d="M230,901.1c0-12.9,0-25.9,0-38.8c0-31.1,0-62.2,0-93.3c0-37.6,0-75.3,0-112.9c0-32.6,0-65.1,0-97.7
                        c0-15.9,0.4-31.8,0-47.7c0-0.2,0-0.4,0-0.7c0-10.5-9.2-20.5-20-20s-20,8.8-20,20c0,12.9,0,25.9,0,38.8c0,31.1,0,62.2,0,93.3
                        c0,37.6,0,75.3,0,112.9c0,32.6,0,65.1,0,97.7c0,15.9-0.4,31.8,0,47.7c0,0.2,0,0.4,0,0.7c0,10.5,9.2,20.5,20,20
                        C220.8,920.7,230,912.4,230,901.1L230,901.1z"></path></g></g><g><g><path d="M794,510.1c0,12.9,0,25.9,0,38.8c0,31.1,0,62.2,0,93.3c0,37.6,0,75.3,0,112.9c0,32.6,0,65.1,0,97.7
                        c0,15.9-0.4,31.8,0,47.7c0,0.2,0,0.4,0,0.7c0,10.5,9.2,20.5,20,20s20-8.8,20-20c0-12.9,0-25.9,0-38.8c0-31.1,0-62.2,0-93.3
                        c0-37.6,0-75.3,0-112.9c0-32.6,0-65.1,0-97.7c0-15.9,0.4-31.8,0-47.7c0-0.2,0-0.4,0-0.7c0-10.5-9.2-20.5-20-20
                        C803.2,490.6,794,498.9,794,510.1L794,510.1z"></path></g></g><g><g><path d="M592.7,510.1c0,12.9,0,25.9,0,38.8c0,31.1,0,62.2,0,93.3c0,37.6,0,75.3,0,112.9c0,32.6,0,65.1,0,97.7
                        c0,15.9-0.4,31.8,0,47.7c0,0.2,0,0.4,0,0.7c0,10.5,9.2,20.5,20,20s20-8.8,20-20c0-12.9,0-25.9,0-38.8c0-31.1,0-62.2,0-93.3
                        c0-37.6,0-75.3,0-112.9c0-32.6,0-65.1,0-97.7c0-15.9,0.4-31.8,0-47.7c0-0.2,0-0.4,0-0.7c0-10.5-9.2-20.5-20-20
                        C601.8,490.6,592.7,498.9,592.7,510.1L592.7,510.1z"></path></g></g><g><g><path d="M537.9,286.4c0,1.1-0.1,2.3-0.1,3.5c0,2.9,1.1-5,0.1-0.6c-0.4,1.7-0.7,3.5-1.3,5.2c-0.1,0.4-0.3,0.8-0.4,1.3
                        c-0.6,1.6-0.6,1.7,0.1,0.1c0.2-0.4,0.3-0.8,0.5-1.2c-0.4,0.8-0.7,1.6-1.1,2.4c-0.2,0.4-3,5.7-3.4,5.6c-0.2-0.1,2.8-3.2,0.7-1
                        c-0.7,0.8-1.4,1.6-2.2,2.4c-0.4,0.4-3.7,4.1-4.3,3.9c-0.2-0.1,3.6-2.4,0.9-0.8c-0.8,0.4-1.5,0.9-2.2,1.4c-0.9,0.6-1.9,1-2.9,1.5
                        c-0.6,0.3-1.2,0.6-1.8,0.8c2.6-1.1,3.1-1.3,1.7-0.8c-1.9,0.2-3.9,1.2-5.8,1.6c0.1,0-2.6,0.6-2.7,0.5c0.3,0.4,4.7-0.4,0.5-0.3
                        c-1.7,0.1-4.6-0.6-6.2-0.1c2.4-0.7,3.5,0.7,1.3,0.2c-1.3-0.3-2.6-0.5-3.9-0.9c-1-0.3-2.2-0.9-3.2-1c-2.6-0.3,4.2,2.2,0.5,0.3
                        c-1.5-0.8-3.1-1.6-4.6-2.5c-0.4-0.2-0.7-0.5-1.1-0.7c-1.4-1-1.4-1-0.1,0c0.3,0.3,0.7,0.5,1,0.8c-0.7-0.5-1.3-1.1-2-1.7
                        c-1.6-1.4-3.1-3-4.5-4.6c-2.6-2.8,0.9,0.9,0.7,1c-0.1,0.1-1.6-2.4-1.8-2.7c-0.2-0.4-3.1-4.7-2.7-5.2c0.2-0.3,1.4,4.2,0.6,1.1
                        c-0.3-1.1-0.7-2.1-0.9-3.2c-0.3-1.1-0.5-2.2-0.7-3.3c-0.9-3.9,0,4.2,0.1-0.1c0-1.8,0-3.7,0-5.6c0.1-4.1-0.1,1.4-0.3,1.2
                        c-0.2-0.2,0.5-2.9,0.6-3.3c0.1-0.3,1.3-5.8,1.7-5.7c0.2,0-1.9,3.9-0.5,1.1c0.6-1.2,1.2-2.3,1.8-3.5c0.5-0.9,1.4-1.8,1.7-2.7
                        c0.9-2.4-3.2,3.5-0.4,0.4c1.2-1.3,2.3-2.6,3.6-3.8c0.8-0.7,1.7-1.4,2.4-2.1c1.8-1.9-4.1,2.5-0.4,0.4c1.9-1.1,3.7-2.2,5.7-3.2
                        c3.4-1.8-1.1,0.6-1.1,0.5c0-0.2,2.8-0.9,3.1-1c1.9-0.6,4-0.7,5.9-1.3c-4.2,1.2-2.8,0.3-1.2,0.3c1.2,0,2.3-0.1,3.5-0.1
                        c0.4,0,3.4,0,3.5,0.2c-0.1-0.3-4.9-1,0,0.1c0.5,0.1,6.4,1.4,6.4,1.9c0,0.2-3.9-1.9-1.1-0.5c1.2,0.6,2.3,1.2,3.5,1.8
                        c0.7,0.4,1.5,1,2.2,1.4c3.6,2-2.7-2.6,0.1,0c1.8,1.6,3.4,3.3,5.1,5c2.8,3-0.4-0.5-0.3-0.5c0.2-0.1,2,3.1,2.1,3.3
                        c0.4,0.8,0.9,1.5,1.3,2.3c0.3,0.6,0.6,1.2,0.9,1.8c0.6,1.4,0.4,0.8-0.8-1.7c0.5,0,1.7,5.9,1.9,6.4c0.1,0.4,0.2,0.9,0.3,1.3
                        c0.3,1.8,0.3,1.8,0,0c-0.2-1.8-0.3-1.7-0.1,0.1C537.9,284.6,537.9,285.5,537.9,286.4c0.2,10.5,9.1,20.5,20,20
                        c10.7-0.5,20.2-8.8,20-20c-0.4-28.1-18-52.4-44.3-62c-24.6-8.9-54.5-0.7-71,19.5c-17.6,21.5-21.1,52-6.5,76.1
                        c14.6,24.2,42.4,35.4,69.8,30.5c30.3-5.5,51.6-34.1,52.1-64.1c0.2-10.5-9.3-20.5-20-20C546.9,266.9,538,275.2,537.9,286.4z"></path></g></g></g></svg>
                </div>
                </div>
                </div>
                <div class="row text-center">
                    <p class="text-bold" style="width:100%;">Account Details</p>
                </div>
                 <hr style="border:1px solid #ccc;margin-top:0px!important;">
                <div style="margin-left:3px;font-family:'montserrat';">
                <div style="position:absolute;bottom:130px;display:flex;" class="text-center">
                    <p style="margin-left:8px;font-size:14px;margin-bottom:0px!important;" class="mr-1"><span class="text-bold">Bank Name:</span> <span style="font-weight:500">{{account_details.bank_name}}</span></p>
                </div>
                  <div style="position:absolute;bottom:80px;display:flex;" class="text-center">
                    <p style="margin-left:8px;font-size:14px;" class="mr-1"><span class="text-bold" >Bank Account(s):</span> <span style="font-weight:500">{{account_details.account_no.split(',').join(', ')}}</span></p>
                </div>
                </div>
                <div class="row" style="margin-top:95px;">
                    <div class="col-6">
                        <button class="repair-font" @click="repair()">
                            Fix Banking
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="relink-font" @click="relinkBank()">
                            Link New Bank
                        </button>
                    </div>
                </div>

            </div>
        </b-modal>
    </div>
    <real-account-number-modal page_name="pay" ref="RealAccountNumberModal"></real-account-number-modal>
    </div>
</template>
<script>
import MxConnectWidget from '../MxConnectWidget.vue';
import CanPayLoader from '../CustomLoader/CanPayLoader.vue'
import RealAccountNumberModal from '../Payment/RealAccountNumberModal.vue';
export default {
name: "ProblematicAccountModal",
  props: {
    account_details: {
      type: Object
    },
    handleCanPayLoader:{
        type:Function
    },
    connectMXBankAccount:{
        type:Function
    }
  },
    components:{
        MxConnectWidget,
        RealAccountNumberModal,
        CanPayLoader
    },
  data(){
    return {
        mxConnectUrl:null,
        isLoading:false
    }
  },
  watch:{
    isLoading(newVal,oldVal) {
        this.handleCanPayLoader(newVal)
    }
  },
methods:{
    showModal(){
        let self = this;
        self.$bvModal.show("problematic-account-modal");
    },
    hideModal(){
        let self = this;

        self.$bvModal.hide("problematic-account-modal");
    },
    relinkBank(){
        let self = this;
        self.hideModal();
        self.$router.push("/banklinking")
    },
    repair(){
        let self = this;
        self.hideModal();
        self.connectMXBankAccount();
    },
}
}
</script>
<style scoped>
#problematic-account-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#problematic-account-modal___BV_modal_content_{
    background-color: #ffffff;

}
.repair-font{
    border:none;
    width:100%;
    padding:15px;
    border-radius:5px;
    background-color:#149240;
    font-family:"Montserrat";
    font-size:12px;
    font-weight:bolder;
    color:#ffffff;
}
.relink-font{
    border:none;
    width:100%;
    padding:15px;
    border-radius:5px;
    background-color:#000000;
    font-family:"Montserrat";
    font-size:12px;
    font-weight:bolder;
    color:#ffffff;
}
.error-light-animation{
    width:22px;
    height:22px;
    border-radius:50%;
    background-color:rgb(255, 76, 76);
    color:#ffffff;
    font-weight:bold;
    animation: boxShadowAnimation 600ms linear alternate infinite;
}
.position-bank-svg{
    margin:0px auto;
}
@keyframes boxShadowAnimation {
  0% {
    box-shadow: 1px 0 1px rgba(255, 0, 0, 0.564);
  }
  50% {
    box-shadow: 1px 0 3px rgba(255, 0, 0, 0.777);
  }
  100% {
    box-shadow: 1px 0 7px rgb(255, 0, 0);
  }
}
</style>