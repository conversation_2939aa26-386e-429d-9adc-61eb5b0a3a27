file="public/manifest.json"
# get current commit tag 
tag=`git tag | sort -V | tail -1`
# get current commit hash short 
version=`git rev-parse --short HEAD`

#Check whether there is any tag
if [ ! -z "$tag" ] 
then 
    echo "Tag found"
	version=$tag
fi 
# get the existing version number
ver=$( sed -n 's/.*"version": "\(.*\)",/\1/p' $file )
echo "Existing version $ver"
# write output
echo "Bump manifest.json version number. New version $version"
sed -i "s/\"version\": \"$ver\"/\"version\": \"$version\"/g" $file




