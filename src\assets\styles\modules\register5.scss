.register5-label{
    font-weight: bold;
    color:white;
    text-align: left;
}

.preview {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}

.preview img {
  max-width: 100%;
  max-height: 500px;
}

.btn-register5{
    width:80px;
    height:40px;
}

.register5-div {
    margin: auto;
    width: 100%;
    height:800px;
}


.container {
    display: block;
  }

.store-account{
    margin-top:20px;
    margin-left:20px;
    float:left;
}

.store-form{
    float:left;
    margin-top:20px;
}
.filter-form{
  float:left;
  margin-left:10px;
  margin-top:20px;
}


.div-row{
    margin-top:20px;
    float:left;
}

.white-row1{
    background-color: white;
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;

}

.white-row2{
    background-color: white;
    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;
    

}
.white-row-table-cell{
    margin: 10px;
    background-color: white;
    border-radius: 10px;
    text-align: center;
}

.white-row{
    background-color: white;
}
.white-row-border-radius{
    margin: 20px;
    margin-bottom: 10px;
    background-color: white;
    border-radius: 5px;
}

.gray-row{
    background-color: $cp-gray;
}
.store-input{
    border:none;
    float:right;
    outline:none;
    margin-top:20px;
    background-color: #ffffff;
    margin-right: 25px;
}

::placeholder{
    text-align:justify;
  }

.gray-input{
    border:none;
    float:right;
    outline:none;
    background-color: $cp-gray;
    margin-top:20px;

}

.gray-input-password{
    border:none;
    float:right;
    outline:none;
    background-color:$cp-gray;
    margin-top:20px;
    font-family: $cp-font-secondary;
        font-size: 20px;
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #000000;
        margin-right: 20px !important;

}

.white-input{
    border:none;
    float:left;
    outline:none;
    margin-left:20px;
    width:80%;
}

.static-text{
    float:left;
    margin-left:20px;
    color:gray;
}

.column{
    margin-bottom:20px;
}
div.hr-line {
    position: relative;
    display: inline-block;
    margin-left: 15px;
    margin-right: 15px;
    margin-top: 10px;
    width: 100%;
    border-bottom: 1px solid rgb(201, 201, 201);
}

.modal-backdrop {
    background: rgba(0, 0, 0, 0.2); 
}

.disabled {
    cursor: not-allowed;
    color: gray
  }
  .enabled {
    cursor: pointer;
  }

  .card-title{
        font-family: $cp-font-secondary;
        font-size: 20px;
        font-weight: 600;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color:  $cp-primary;
  }

  .card-sub-title {
    font-family: $cp-font;
    font-size: 12px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #000000;
  }

  .card-sub-text {
    font-family: $cp-font;
    font-size: 12px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #7f7f7f;
  }

  .Store-title {
    font-family: $cp-font-secondary;
    font-size: 22px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #000000;
  }

  .Open-style {
    font-family: $cp-font;
    font-size: 12px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #000000;
    float: left !important;
    margin-left: 10px;
  }
  
  .Open-style .text-style-1 {
    color: #1b9142;
  }

  .store-address {
   
    font-family: $cp-font;
    font-size: 15px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #000000;
    src: local('Open Sans Semibold'), local('OpenSans-Semibold'), url(http://fonts.gstatic.com/s/opensans/v13/MTP_ySUJH_bn48VBG8sNShampu5_7CjHW5spxoeN3Vs.woff2) format('woff2');

  }

  .store-contact {
   
    font-family: $cp-font;
    font-size: 15px;
    font-weight: 800;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #000000;
  }


  .Open-time {
    margin-top: 20px;
    font-family: $cp-font;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 2.25;
    letter-spacing: normal;
    color: #666666;
    float: right;
  }
  .Open-time-text-style {
    font-family: $cp-font;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 2.25;
    letter-spacing: normal;
    color: #666666;
    float: left;
    padding-left: 5px;
    margin-top: -5px;
    color: #1b9142;
  
}

.merchant-edge-shadow {
    box-shadow: 0 2px 5px -6px black;
  }

  .default-tabs {
    height: 45px !important;
    background-color: white;
    position: relative;
    margin: 0 auto;
    &__item {
      height: 45px !important;
      font-family: $cp-font;
      font-size: 12px !important;
      font-weight: 600;
      font-stretch: normal;
      font-style: normal;
      line-height: normal;
      text-align: left;
      color: #000000;
      display: inline-block;
      margin: 0 5px;
      padding: 10px;
      padding-bottom: 8px;
      letter-spacing: 0.8px;
      text-decoration: none;
      border: none;
      background-color: white;
      border-bottom: 2px solid transparent;
      cursor: pointer;
      transition: all 0.25s;
      &_active {
        border-radius: 4px;
        border-bottom: 2px solid #000000;
      }
      &:hover {
        border-bottom: 2px solid gray;
      }
      &:focus {
        outline: none;
        border-radius: 4px;
        border-bottom: 2px solid #149240;
        color: #000000;
      }
      &:first-child {
        margin-left: 0;
      }
      &:last-child {
        margin-right: 0;
      }
    }
    &__active-line {
      position: absolute;
      bottom: 0;
      left: 0;
      height: 4px;
      border-radius: 4px;
      background-color: #149240;
      transition: transform 0.4s ease, width 0.4s ease;
    }
  }
  .content {
    background-color: #ececec;
    font-size: 20px;
    height: 100%;
  }
  .tab-content {
    background-color: #ececec;
    font-size: 20px;
    height: 100%;
    margin-top: -20px !important;
  }
  .no-store-style{
  margin-left: 10px; background-color: #ffffff; border-bottom: transparent !important;

.gmap-search-box{
  border: none !important;
  border-color: transparent !important;
  width: 90% !important;
  float: right;
}
  .g-map-style{
    width:100%;  height: 400px;
  }
  }
  .bottom-edge-shadow {
    box-shadow: 0 2px 5px -6px black;
  }
  .content-div {
    margin: 20px;
    background-color: white;
    border-radius: 8px;
    height: 100% !important;
    overflow: hidden !important;
    // margin-top: 40px !important;
  }

  .content-search-div {
    margin-top: 40px !important;
    margin: 20px;
    background-color: white;
    border-radius: 8px;
    height: 100% !important;
    overflow: hidden !important;
  }
 
  .card {
    padding-top: 10px;
    padding-left: 10px;
  }