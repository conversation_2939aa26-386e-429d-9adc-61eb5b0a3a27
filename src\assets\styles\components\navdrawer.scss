a {
  cursor: pointer;
}
.navbar {
 
  color: white;
}

#vue-navdrawer .nav-item {
  padding-top: 5px 0;
  color: lightgray;
}
#vue-navdrawer .nav-item:first-child {
  margin-top: 20px;
}

.nav-item.active {
  color: white;
  height: 60px !important;
}
#vue-navdrawer .nav-item i {
  font-size: 1.2em;
}

#app {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
#app .card .card-block {
  color: #EF6C00;
  text-align: center;
  font-size: 6rem;
}

#vue-navcontent {
  position: relative;
  width: 100%;
  height: 100%;
}
#navdrawer-container {
  position: absolute;
  width: 100%;
  height: 100%;
}
.nav-open #vue-navcontent {
  left: 10%;
  width: 90%;
  animation: content-open 0.5s ease;
}
.nav-closed #vue-navcontent {
  animation: content-close 0.5s ease;
}
.left-nav-enter,
.left-nav-leave-to {
  transform: translateX(-100%);
  -webkit-transform: translateX(-100%);
}
.left-nav-enter-active,
.left-nav-leave-active {
  transition: all 0.5s ease;
}
@keyframes content-close {
  from {
    width: 90%;
    left: 10%;
  }
  to {
    width: 100%;
    left: 0;
  }
}
@keyframes content-open {
  from {
    width: 100%;
    left: 0;
  }
  to {
    width: 90%;
    left: 10%;
  }
}
@media only screen and ( min-width:270px) and ( max-width:700px) {
    .nav-item.active {
        color: white;
        height: 53px !important;
    }
}

@media only screen and ( min-width:401px) {
    .nav-item.active {
        color: white;
        height: 60px !important;
    }
}