<template>
  <div>
    <div class="row" id="row-header">
      <!--Code for ToggleDrawer-->
      <div
        class="col-2 col-md-1 content"
        slot="content"
        style="height: 70px;z-index: 999999;background-color: rgb(255, 255, 255) !important;margin-left: 10px;"
      >
        <a href="javascript:void(0)" @click="showDrawer">
          <svg
            version="1.1"
            id="Layer_1"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            x="0px"
            y="0px"
            viewBox="0 0 2540 3175"
            style="enable-background:new 0 0 2540 3175;  margin-top:30px;"
            xml:space="preserve"
            height="30"
            width="30"
             v-show="!showmenuicon"
          >
            <g>
              <path
                d="M1878,2270c39,39,39,104,0,143c-40,40-104,40-144,0L662,1342c-39-40-39-104,0-144L1734,127c40-40,104-40,144,0
		c39,39,39,104,0,143L878,1270L1878,2270z"
              />
            </g>
          </svg>
          
        </a>
      </div>
      <div class="col-6 col-md-8 text-left" style="padding-left: 0px !important;">
        <label class="participant-merchant-style"
          ><b>Participating Merchants</b></label
        >
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "PreParticipantHeader",
  data: () => ({
    open: true,
    searchOpen: false,
    filterOpen: false,
    showmenuicon: true,
    showdrawer: true,
        favcolor:"#000000",
        favcolor2:"#000000"

  }),
 
  mounted() {
        this.$root.$emit("loginapp", [""]);
    let self = this;
       self.$root.$on("Menu Drawer Close", function(data) {
                       self.showmenuicon = true;
      
    });

   self.$root.$on("Menu Drawer Open", function(data) { 
       setTimeout(function() {
     self.showmenuicon = false;
        }, 30);
      
    });

     
    
  },
  methods: {
     clickONfav(data){
      if (this.searchOpen == false) {
        this.searchOpen = true;
        this.favcolor = "#1b9142"
      }else{
      this.searchOpen = false;
              this.favcolor = "#000000"

      }
     },
     clickOnFilter(){
       if (this.filterOpen == false) {
        this.filterOpen = true;
        this.favcolor2 = "#1b9142"
      }else{
      this.filterOpen = false;
              this.favcolor2 = "#000000"

      }
      this.$root.$emit("fliterOn", [""]);
     },
    showDrawer() {
      this.$router.push("/login");
                          this.$root.$emit("loginapp", [""]);

    },

  },
};
</script>
<style scoped>
#container {
  height: 100px !important;
}

#row-header {
  height: 100px !important;
}

#col-header {
  margin-top: 23px;
  margin-left: -20px;
}
</style>
