<template>
  <div>
  <div style="z-index:************" v-if="isLoading">
    <CanPayLoader/>
  </div>
  <div class="container splash mt-4">
    <div class="row m-5">
      <div class="col-12 remove-padding">
        <span class="bl-heading"
          >Change Funding Account<b /> For Future Purchases</span
        >
      </div>
    </div>

    <div class="bank-list" v-if="!isLoading">
      <div v-if="show_refresh_btn" class="row pl-1 pr-1 pb-1">
        <div class="col-12">
          <button
            type="button"
            class="btn-signup-splash"
            @click="showModal('refresh-balance-modal')"
          >
            One-Time Balance Refresh
          </button>
        </div>
      </div>
      <div v-else class="row pl-1 pr-1 pb-2">
        <div class="col-12">
          <button
            type="button"
            class="btn-signup-splash"
            style="
              color: rgb(110 99 99 / 55%);
              background-color: transparent !important;
              border-color: rgb(110 99 99 / 55%) !important;
              border: 0.5px solid !important;
            "
          >
            One-Time Balance Refresh
          </button>
        </div>
      </div>
      
      <div class="accordian-main-div-class">
    <!-- Active Bank Accordion -->
    <div class="accordion-item" v-if="bank_list.active_bank">
      <div class="accordion-header" :class="[{ 'active-accordion': activeAccordion === 'active_bank' && darkAccordion },{ 'dark-accordion': activeAccordion === 'active_bank' && !darkAccordion }]" @click="toggleAccordion('active_bank','')">
       {{currentBankName}} ({{ bank_list.active_bank_account_count }})
        <span :class="{ 'rotate-arrow': activeAccordion === 'active_bank', 'rotate': activeAccordion === 'active_bank' }">&#9660;</span>
      </div>
      <div v-if="activeAccordion === 'active_bank'" class="accordion-content">
        <div v-for="(bank, bankName) in bank_list.active_bank" :key="bankName">
          <div v-for="(accounts, accountType) in bank" :key="accountType">
            <div class="row m-2 pl-2">
              <div class="col-4 remove-padding">
        <span class="float-left">{{ ucfirst(accountType) }}</span>
      </div>
      <div class="col-8 pl-2 remove-padding">
        <span class="float-left font-weight-bold">PurchPower</span>

        <div
          @click="showModal('purchase-power-modal')"
          style="margin-right: 4.5rem"
        >
          <svg
            version="1.1"
            id="Layer_1"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            x="0px"
            y="0px"
            width="16"
            height="16"
            fill="#000000"
            viewBox="0 0 90 90"
            style="enable-background: new 0 0 90 90"
            xml:space="preserve"
          >
            <path
              d="M45,0C20.1,0,0,20.1,0,45s20.1,45,45,45s45-20.1,45-45C90,20.2,69.8,0,45,0z M45,82C24.6,82,8,65.4,8,45S24.6,8,45,8
s37,16.6,37,37S65.4,82,45,82z M40,20c0-2.8,2.2-5,5-5s5,2.2,5,5s-2.2,5-5,5S40,22.8,40,20z M56,65.4V68c0,1.1-0.9,2-2,2H36
c-1.1,0-2-0.9-2-2v-2.6c0-0.9,0.6-1.6,1.4-1.9l4.1-1.3c0.3-0.1,0.5-0.4,0.5-0.7V41h-3.1c-1.6,0-2.9-1.3-2.9-2.9c0-1.2,0.8-2.3,2-2.7
l11.4-3.6c1.1-0.3,2.2,0.3,2.5,1.3c0.1,0.2,0.1,0.4,0.1,0.6v27.8c0,0.3,0.2,0.6,0.5,0.7l4.1,1.3C55.4,63.7,56,64.5,56,65.4z"
            />
          </svg>
        </div>
      </div>
              </div>
              <div class="row" v-for="(account, index) in accounts" :key="index">
      <div class="col-12" v-if="account.banking_solution_name == 'mx' && account.user_action_required == 1">
        {{changeOneTimeRefreshModal(false)}}
        <div class="row bank-name-list m-2" style="padding-left:5px;">
          <div class="col-4 align-self-center bl-f-weight">
            <span >x{{ account.account_no }}
            </span>
            <div v-if="account.banking_solution_name == 'mx' && account.user_action_required == 1" style="position:absolute; top:-8px;left:-3px">
                <a  href="javascript:void(0);" @click="showModalMxError('error-message-mx',account.id)" > 
                <label
                  class="problematic-account-bank-label float-right"
                ></label>
              </a>
            </div>
          </div>
          <div class="col-4 align-self-center bl-f-weight">
            <span v-if="account.purchase_power!=blacklistedAccountNumber && account.purchase_power!=blockedRoutingNumber">$</span><span>{{ account.purchase_power }}</span>
          </div>
          <div class="col-4 align-self-center">
            <div class="text-center" v-if="account.user_action_required == 1">
                <button class="repair-font" style="font-size:10px;" @click="fixMxConnectCall(account.id)">Fix Banking</button>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12" v-else>
        {{changeOneTimeRefreshModal(true)}}
        <div class="row bank-name-list m-2">
          <div class="col-4 align-self-center bl-f-weight">
            <span >x{{ account.account_no }}
            </span>
          </div>
          <div class="col-4 align-self-center bl-f-weight">
            <span v-if="account.purchase_power!=blacklistedAccountNumber && account.purchase_power!=blockedRoutingNumber">$</span><span>{{ account.purchase_power }}</span>
          </div>
          <div class="col-2 align-self-center">
            <div class="text-center" v-if="account.status == 0 || account.blocked_account_number == 1">
              <a href="javascript:void(0);" @click="confirmDelete(account)" title="Disable Bank Account"> 
                <label
                  class="delete-bank-label float-right"
                ></label>
              </a>
            </div>
          </div>
          <div class="col-2 align-self-center">
            <div class="text-center">
              <input
                :id="account.id"
                class="radio-custom"
                name="radio-group"
                type="radio"
                :ischecked="account.status"
                :checked="account.status"
                @click="selectBank(account)"
                v-if="account.blocked_account_number == 0"
              />
              <label
                :for="account.id"
                class="radio-custom-label float-right"
              ></label>
            </div>
          </div>
        </div>
      </div>
      </div>
          </div>
        </div>
      </div>
    </div>



    <!-- Other Banks Accordions -->
<div v-for="(otherBank, index) in bank_list.other_banks" :key="index" class="accordion-item">
  <div class="accordion-header" :class="[{ 'active-accordion': activeAccordion === index && darkAccordion },{ 'dark-accordion': activeAccordion === index && !darkAccordion }]" @click="toggleAccordion(index,otherBank.institution_id)">
    {{ otherBank.name }} ({{ otherBank.count }})
    <span :class="{ 'rotate-arrow': activeAccordion === index, 'rotate': activeAccordion === index }">&#9660;</span>
  </div>
  <div v-if="activeAccordion === index" class="accordion-content">
    <div v-for="(acc, accType) in otherBank.accounts[0]" :key="accType">
      <div v-for="(accounts, accountType) in acc" :key="accountType">
        <div class="row m-2 pl-2">
        <div class="col-4 remove-padding">
    <span class="float-left">{{ ucfirst(accountType) }}</span>
    </div>
    <div class="col-8 pl-2 remove-padding">
    <span class="float-left font-weight-bold">PurchPower</span>

    <div
    @click="showModal('purchase-power-modal')"
    style="margin-right: 4.5rem"
    >
    <svg
      version="1.1"
      id="Layer_1"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      x="0px"
      y="0px"
      width="16"
      height="16"
      fill="#000000"
      viewBox="0 0 90 90"
      style="enable-background: new 0 0 90 90"
      xml:space="preserve"
    >
      <path
        d="M45,0C20.1,0,0,20.1,0,45s20.1,45,45,45s45-20.1,45-45C90,20.2,69.8,0,45,0z M45,82C24.6,82,8,65.4,8,45S24.6,8,45,8
    s37,16.6,37,37S65.4,82,45,82z M40,20c0-2.8,2.2-5,5-5s5,2.2,5,5s-2.2,5-5,5S40,22.8,40,20z M56,65.4V68c0,1.1-0.9,2-2,2H36
    c-1.1,0-2-0.9-2-2v-2.6c0-0.9,0.6-1.6,1.4-1.9l4.1-1.3c0.3-0.1,0.5-0.4,0.5-0.7V41h-3.1c-1.6,0-2.9-1.3-2.9-2.9c0-1.2,0.8-2.3,2-2.7
    l11.4-3.6c1.1-0.3,2.2,0.3,2.5,1.3c0.1,0.2,0.1,0.4,0.1,0.6v27.8c0,0.3,0.2,0.6,0.5,0.7l4.1,1.3C55.4,63.7,56,64.5,56,65.4z"
      />
    </svg>
    </div>
    </div>
        </div>
        <div class="row" v-for="(account, index) in accounts" :key="index">
      <div class="col-12" v-if="account.banking_solution_name == 'mx' && account.user_action_required == 1">
        {{changeOneTimeRefreshModal(false)}}
        <div class="row bank-name-list m-2" style="padding-left:5px;">
          <div class="col-4 align-self-center bl-f-weight">
            <span >x{{ account.account_no }}
            </span>
            <div v-if="account.banking_solution_name == 'mx' && account.user_action_required == 1" style="position:absolute; top:-8px;left:-3px;">
                <a  href="javascript:void(0);" @click="showModalMxError('error-message-mx',account.id)" > 
                <label
                  class="problematic-account-bank-label float-right"
                ></label>
              </a>
            </div>
          </div>
          <div class="col-4 align-self-center bl-f-weight">
            <span v-if="account.purchase_power!=blacklistedAccountNumber && account.purchase_power!=blockedRoutingNumber">$</span><span>{{ account.purchase_power }}</span>
          </div>
          <div class="col-4 align-self-center">
            <div class="text-center" v-if="account.user_action_required == 1">
                <button class="repair-font" style="font-size:10px;" @click="fixMxConnectCall(account.id)">Fix Banking</button>
            </div>
          </div>
        </div>
      </div>
    <div class="col-12" v-else>
      {{changeOneTimeRefreshModal(true)}}
    <div class="row bank-name-list m-2">
    <div class="col-4 align-self-center bl-f-weight">
      <span>x{{ account.account_no }}</span>
    </div>
    <div class="col-4 align-self-center bl-f-weight">
      <span v-if="account.purchase_power!=blacklistedAccountNumber && account.purchase_power!=blockedRoutingNumber">$</span><span>{{ account.purchase_power }}</span>
    </div>
    <div class="col-2 align-self-center">
      <div class="text-center" v-if="account.status == 0 || account.blocked_account_number == 1">
        <a href="javascript:void(0);" @click="confirmDelete(account)" title="Disable Bank Account"> 
          <label
            class="delete-bank-label float-right"
          ></label>
        </a>
      </div>
    </div>
    <div class="col-2 align-self-center">
      <div class="text-center">
        <input
          :id="account.id"
          class="radio-custom"
          name="radio-group"
          type="radio"
          :ischecked="account.status"
          :checked="account.status"
          @click="selectBank(account)"
          v-if="account.blocked_account_number == 0"
        />
        <label
          :for="account.id"
          class="radio-custom-label float-right"
        ></label>
      </div>
    </div>
    </div>
    </div>
    </div>
      </div>
      
    </div>
   
  </div>
</div>
  </div>
  






    </div>

    <div v-if="!isLoading">
      <div
        v-if="bank_changed"
        class="row mt-5"
      >
        <div class="col-6">
          <button type="button" class="btn-signup-splash" v-on:click="checkRealAccNo">
            Save
          </button>
        </div>
        <div class="col-6">
          <button type="button" class="btn-login-splash" v-on:click="clickClose">
            Close
          </button>
        </div>
        <div class="col-12 mt-2">
          <span class="bl-footer-text">
            Don’t see the account you want?<br />
            <span class="bl-f-color" @click="changeBankAccount()"
              ><u>Update banking.</u></span
            ></span
          >
        </div>
      </div>
      <div
        v-else
        class="row mt-5 justify-content-center"
      >
        <div class="col-12 col-md-6 col-lg-6 d-flex flex-column">
          <button
            type="button"
            class="btn-login-splash w-100"
            v-on:click="clickClose"
          >
            Close
          </button>
          <div>
            <span class="bl-footer-text">
            Don’t see the account you want?<br />
              <span class="bl-f-color" @click="changeBankAccount()"
                ><u>Update banking.</u>
              </span>
            </span>
          </div>
        </div>
      </div> 
    </div>


    <b-modal
      ref="delay-in-refresh-balance"
      hide-footer
      no-close-on-backdrop
      modal-backdrop
      hide-header
      id="delay-in-refresh-balance"
      centered
    >
    <div class="text-center" style="display: flex; justify-content: center">
    <div>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="48" height="48" viewBox="0 0 254 222" fill="none">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H254V222H0V0Z" fill="url(#pattern0_10044_8)"/>
  <defs>
    <pattern id="pattern0_10044_8" patternContentUnits="objectBoundingBox" width="1" height="1">
      <use xlink:href="#image0_10044_8" transform="scale(0.******** 0.0045045)"/>
    </pattern>
    <image id="image0_10044_8" width="254" height="222" xlink:href="data:image/png;base64,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"/>
  </defs>
</svg>
    </div>
    </div>
    <div style="font-weight:bolder;font-family:'montserrat';margin-top:30px;text-align:center;">
      We’re currently experiencing issues retrieving your balance. We appreciate your patience.
    </div>
    <button class="black-button-ok mt-4" @click="hideUpdateModal()">OK</button>
    </b-modal>

    <b-modal
      ref="update-bank-modal"
      hide-footer
      no-close-on-backdrop
      modal-backdrop
      hide-header
      id="update-bank-modal"
      centered
    >
      <div class="color">
        <div class="col-12 text-center">
          <svg
            version="1.1"
            id="Layer_1"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            x="0px"
            y="0px"
            width="120"
            height="120"
            viewBox="0 0 100 125"
            style="enable-background: new 0 0 100 125"
            xml:space="preserve"
            fill="#e14343"
          >
            <path
              d="M96.2,47.5l-22-38c-0.4-0.6-1-1-1.7-1h-44c-0.7,0-1.4,0.4-1.7,1l-22,37.9c-0.4,0.6-0.4,1.4,0,2l22,38.1c0.4,0.6,1,1,1.7,1
	h44c0.7,0,1.4-0.4,1.7-1l22-38C96.6,48.9,96.6,48.1,96.2,47.5z M71.3,84.5H29.7L8.8,48.4l20.8-35.9h41.7l20.8,36L71.3,84.5z
	 M50.5,60.5c1.1,0,2-0.9,2-2v-30c0-1.1-0.9-2-2-2c-1.1,0-2,0.9-2,2v30C48.5,59.6,49.4,60.5,50.5,60.5z M48.4,66.4
	c-0.6,0.6-0.9,1.3-0.9,2.1c0,0.8,0.3,1.6,0.9,2.1s1.3,0.9,2.1,0.9c0.8,0,1.6-0.3,2.1-0.9c0.6-0.6,0.9-1.3,0.9-2.1
	c0-0.8-0.3-1.6-0.9-2.1C51.5,65.3,49.5,65.3,48.4,66.4z"
            />
          </svg>
        </div>
        <div class="purchaserpower-modal-text">
          <div class="d-block text-center">
            <label class="purchasepower-def-label">
              There was some problem trying to link your bank account. Please
              Update your banking details.
            </label>
          </div>
          <br />
          <br />
          <div class="row">
            <div class="col-12 text-center">
              <button
                type="button"
                class="mx-auto col-10 offset-1 btn-black"
                style="height: 60px"
                @click="changeBankAccount()"
              >
                <span class="purchasepower-modal-ok-label">Update Banking</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </b-modal>
    <b-modal
      ref="purchase-power-modal"
      hide-footer
      v-b-modal.modal-center
      modal-backdrop
      hide-header
      id="pay-modal-center"
      centered
      title="BootstrapVue"
    >
      <div class="color">
        <div class="purchaserpower-modal-text">
          <div class="col-12 text-center mt-5 mb-5">
            <svg
              version="1.1"
              id="Layer_1"
              xmlns:cc="http://creativecommons.org/ns#"
              xmlns:dc="http://purl.org/dc/elements/1.1/"
              xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
              xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
              xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
              xmlns:svg="http://www.w3.org/2000/svg"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              x="0px"
              y="0px"
              viewBox="0 0 26.5 33.1"
              style="enable-background: new 0 0 26.5 33.1"
              xml:space="preserve"
              width="75"
              height="75"
              fill="#149240"
            >
              <g transform="translate(0,-270.54165)">
                <path
                  d="M17.1,272.1c-0.3,0-0.6,0.1-0.8,0.2l-5.6,3.2H3.4c-1,0-1.8,0.8-1.8,1.8v16.3c0,1,0.8,1.8,1.8,1.8h18.8c1,0,1.8-0.8,1.8-1.8
		v-4.8h0.4c0.3,0,0.5-0.2,0.5-0.5v-4.7c0-0.3-0.2-0.5-0.5-0.5h-0.4v-5.7c0-1-0.8-1.8-1.8-1.8h-2l-1.5-2.6
		C18.3,272.4,17.7,272.1,17.1,272.1L17.1,272.1z M17.2,273.2c0.2,0,0.4,0.1,0.6,0.4l2.5,4.4H8.7l8-4.6
		C16.9,273.2,17,273.2,17.2,273.2L17.2,273.2z M3.4,276.6h5.4l-2.2,1.2H4.1c-0.7,0-0.7,1,0,1h18.8v4.2h-3.3c-1.6,0-2.9,1.3-2.9,2.9
		s1.3,2.9,2.9,2.9h3.3v4.8c0,0.4-0.3,0.7-0.7,0.7H3.4c-0.4,0-0.7-0.3-0.7-0.7v-16.3C2.6,277,2.9,276.6,3.4,276.6L3.4,276.6z
		 M20.8,276.6h1.4c0.4,0,0.7,0.3,0.7,0.7v0.5h-1.4L20.8,276.6z M19.6,284.1h4.3v3.6h-4.3c-1,0-1.8-0.8-1.8-1.8
		C17.7,284.9,18.5,284.1,19.6,284.1z M19.8,284.9c-0.6,0-1.1,0.5-1.1,1c0,0.6,0.5,1,1.1,1s1.1-0.5,1.1-1
		C20.9,285.4,20.4,284.9,19.8,284.9z"
                />
              </g>
            </svg>
          </div>
          <div class="d-block text-center">
            <label class="purchasepower-def-label">
              <b>What is my Purchase Power?</b>
            </label>
          </div>
          <br />

          <h3 class="purchasepower-modal-text text-justify">
            Purchase Power is your available spending with CanPay. Your Purchase
            Power is impacted by many factors including recent purchases
            through CanPay, your total CanPay spending history, and how long
            you've had a CanPay account. Negative items, like NSF payment
            returns, can also impact your Purchase Power.
          </h3>

          <div class="text-center mt-5">
            <button
              type="button"
              class="mx-auto col-10 offset-1 btn-black"
              style="height: 60px"
              @click="hideModal('purchase-power-modal')"
            >
              <label class="purchasepower-modal-ok-label">OK</label>
            </button>
          </div>
        </div>
      </div>
    </b-modal>
    
    <no-primary-account-modal ref="NoPrimaryAccountModal"></no-primary-account-modal>

    <b-modal
      ref="refresh-balance-modal"
      hide-footer
      no-close-on-backdrop
      modal-backdrop
      hide-header
      id="refresh-balance-modal"
      centered
    >
      <div class="color">
        <div class="row mt-4">
          <div class="col-12 text-center">
            <svg
              version="1.1"
              id="Layer_1"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              x="0px"
              y="0px"
              width="90"
              height="90"
              viewBox="0 0 86.1 74.3"
              style="enable-background: new 0 0 86.1 74.3"
              xml:space="preserve"
              fill="#149240"
            >
              <path
                d="M22.9,66c-0.9-0.6-2.2-0.4-2.8,0.6c-1.3,2-3.4,3.3-5.7,3.6c-2.4,0.3-4.7-0.4-6.5-2c0.4-0.2,0.7-0.4,1-0.8
	c0.6-0.9,0.3-2.2-0.6-2.8l-3.9-2.4c-0.5-0.3-1.2-0.4-1.7-0.2c-0.6,0.2-1,0.7-1.2,1.3l-1.4,4.4c-0.3,1.1,0.3,2.2,1.3,2.5
	c0.2,0.1,0.4,0.1,0.6,0.1c0.7,0,1.3-0.4,1.7-0.9c2.2,3,5.8,4.9,9.7,4.9c0.5,0,1.1,0,1.6-0.1c3.5-0.5,6.6-2.4,8.5-5.4
	C24.1,67.9,23.9,66.6,22.9,66z"
              />
              <path
                d="M25.4,54.3c-0.9-0.3-1.8,0.1-2.3,0.8c-2.6-3.4-6.8-5.4-11.3-4.8c-3.5,0.5-6.6,2.4-8.5,5.4c-0.6,0.9-0.4,2.2,0.6,2.8
	C4.8,59.1,6,58.9,6.6,58c1.3-2,3.4-3.3,5.7-3.6c2.4-0.3,4.8,0.4,6.5,2c-0.4,0.2-0.7,0.4-1,0.8c-0.6,0.9-0.3,2.2,0.6,2.8l3.9,2.4
	c0.3,0.2,0.7,0.3,1.1,0.3c0.2,0,0.5,0,0.7-0.1c0.6-0.2,1-0.7,1.2-1.3l1.4-4.4C27,55.8,26.4,54.7,25.4,54.3z"
              />
              <path
                d="M61.8,9.7H16.3c-1.1,0-2,0.9-2,2s0.9,2,2,2h45.5c1.1,0,2-0.9,2-2S62.9,9.7,61.8,9.7z"
              />
              <path
                d="M81.7,33.2v-2c0-5.2-3.3-9.6-7.9-11.3v-8.5C73.8,5.1,68.7,0,62.4,0H15.7C9.5,0,4.5,4.8,4.3,11c0,0,0,0,0,0c0,0,0,0,0,0.1
	v33.5c0,1.1,0.9,2,2,2s2-0.9,2-2V20c2.1,1.9,4.9,3.1,8,3.1h53.4c0.4,0,0.9,0,1.3,0.1c0.3,0.1,0.5,0.2,0.8,0.2c0,0,0,0,0,0
	c3.4,0.9,5.9,4,5.9,7.7v1.9H62.9c-3,0-5.4,2.4-5.4,5.4v9.1c0,3,2.4,5.4,5.4,5.4h14.8c0,0.1-0.1,0.3-0.1,0.5v6.1c0,4.4-3.6,8-8,8
	H32.1c-1.1,0-2,0.9-2,2s0.9,2,2,2h37.6c6.6,0,12-5.4,12-12v-6.1c0-0.2,0-0.4-0.1-0.5c2.6-0.4,4.5-2.7,4.5-5.4v-9.1
	C86.1,35.9,84.2,33.7,81.7,33.2z M16.3,19.1c-4.4,0-8-3.6-8-7.9c0,0,0,0,0,0c0.1-4,3.4-7.2,7.4-7.2h46.7c4.1,0,7.4,3.3,7.4,7.4v7.7
	c0,0-0.1,0-0.1,0L16.3,19.1L16.3,19.1z M82.1,47.6c0,0.8-0.6,1.4-1.4,1.4H62.9c-0.8,0-1.4-0.6-1.4-1.4v-9.1c0-0.8,0.6-1.4,1.4-1.4
	h17.7c0.8,0,1.4,0.6,1.4,1.4V47.6z"
              />
              <circle cx="68.4" cy="43.1" r="3" />
            </svg>
          </div>
        </div>
        <div class="d-block text-center m-3">
          <span
            style="
              color: black;
              font-size: 1.5rem;
              font-weight: 600;
              line-height: 1px;
            "
            >Caution, balance refresh can only be used once per day.
          </span>
        </div>
        <div class="d-block text-center">
          <span style="color: #5b5a5a; font-weight: 300">
            Bank balance is one of the many data points used to calculate your
            PurchPower(TM).
            <br /><br />
            If you’ve had a recent deposit, and you would like PurchPower(TM) to
            take this deposit into account, refresh now.
          </span>
        </div>
        <div class="row mt-5">
          <div class="col-6 text-center">
            <button
              type="button"
              class="btn-black"
              style="
                height: 60px;
                width: 100%;
                background: #000000 !important;
                border: none !important;
              "
              @click="hideModal('refresh-balance-modal')"
            >
              <span class="purchasepower-modal-ok-label">Cancel</span>
            </button>
          </div>
          <div class="col-6 text-center">
            <button
              type="button"
              class="btn-black"
              style="
                height: 60px;
                width: 100%;
                background: #149240 !important;
                border: none !important;
              "
              @click="refreshBalance()"
            >
              <span class="purchasepower-modal-ok-label">Refresh Now</span>
            </button>
          </div>
        </div>
      </div>
    </b-modal>
    <b-modal
      ref="disable-bank-success-modal"
      hide-footer
      no-close-on-backdrop
      modal-backdrop
      hide-header
      id="disable-bank-success-modal"
      centered
    >
      <div class="color">
        <div class="col-12 text-center">
          
        </div>
        <div class="purchaserpower-modal-text">
          <div class="d-block text-center">
            <label class="purchasepower-def-label">
              Bank Account Disabled successfully
            </label>
          </div>
          <br />
          <br />
          <div class="row">
            <div class="col-12 text-center">
              <button
                type="button"
                class="mx-auto col-10 offset-1 btn-black"
                style="height: 60px"
                @click="closeRemoveBankModal()"
              >
                <span class="purchasepower-modal-ok-label"
                  >OK</span
                >
              </button>
            </div>
          </div>
        </div>
      </div>
    </b-modal>
    <b-modal
        ref="confirm-modal"
        hide-footer
        v-b-modal.modal-center
        modal-backdrop
        hide-header
        id="confirm-modal"
        centered
      >
        <div class="color">
          <div class="col-12 text-center">
            <svg
              version="1.1"
              id="Layer_1"
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              x="0px"
              y="0px"
              width="120"
              height="120"
              viewBox="0 0 100 125"
              style="enable-background: new 0 0 100 125"
              xml:space="preserve"
              fill="#e14343"
            >
              <path
                d="M96.2,47.5l-22-38c-0.4-0.6-1-1-1.7-1h-44c-0.7,0-1.4,0.4-1.7,1l-22,37.9c-0.4,0.6-0.4,1.4,0,2l22,38.1c0.4,0.6,1,1,1.7,1
	h44c0.7,0,1.4-0.4,1.7-1l22-38C96.6,48.9,96.6,48.1,96.2,47.5z M71.3,84.5H29.7L8.8,48.4l20.8-35.9h41.7l20.8,36L71.3,84.5z
	 M50.5,60.5c1.1,0,2-0.9,2-2v-30c0-1.1-0.9-2-2-2c-1.1,0-2,0.9-2,2v30C48.5,59.6,49.4,60.5,50.5,60.5z M48.4,66.4
	c-0.6,0.6-0.9,1.3-0.9,2.1c0,0.8,0.3,1.6,0.9,2.1s1.3,0.9,2.1,0.9c0.8,0,1.6-0.3,2.1-0.9c0.6-0.6,0.9-1.3,0.9-2.1
	c0-0.8-0.3-1.6-0.9-2.1C51.5,65.3,49.5,65.3,48.4,66.4z"
              />
            </svg>
          </div>
          <div class="purchaserpower-modal-text">
            <div class="d-block text-center">
              <label class="purchasepower-def-label">
                Do you wish to disable this account from CanPay? 
                Once you disable this account, it can no longer be 
                used for future purchases unless you enable it. 
              </label>
            </div>
            <br />
            <br />
            <div class="row">
              <div class="col-12 text-center">
                <button
                  @click="hideModal('confirm-modal')"
                  class="btn btn-danger btn-md center-block cancel-btn"
                >
                  <label class="forgetpassword-ok-label">Cancel</label>
                </button>
                <button
                  @click="disableBank()"
                  class="btn btn-danger btn-md center-block tip-ok-btn"
                >
                  <label class="forgetpassword-ok-label">Confirm</label>
                </button>
              </div>
            </div>
          </div>
        </div>
    </b-modal>
    <!-- Validation modal -->
    <b-modal
      ref="validation-modal"
      hide-footer
      v-b-modal.modal-center
      modal-backdrop
      hide-header
      id="validation-modal"
      centered
    >
      <div class="color">
        <div class="purchaserpower-modal-text">
          <div class="d-block text-center">
            <label class="purchasepower-def-label">
              {{ error_message }}
            </label>
          </div>
          <br />
          <br />
          <div class="text-center">
            <button
              type="button"
              class="mx-auto col-10 offset-1 btn-black"
              style="height: 60px"
              @click="hidevalidationModal"
            >
              <label class="purchasepower-modal-ok-label">OK</label>
            </button>
          </div>
        </div>
      </div>
    </b-modal>
    <real-account-number-modal page_name="banklist" ref="RealAccountNumberModal"></real-account-number-modal>
  </div>
      <!-----------------------  MODAL FOR ERROR MESSAGE DUE TO MX   !---------------->
      <div>
        <b-modal
          ref="error-message-mx"
          hide-footer
          v-b-modal.modal-center
          modal-backdrop
          hide-header
          no-close-on-backdrop
          id="error-message-mx"
          centered
          title="BootstrapVue"
        >
        <div style="text-align:center;">
<svg data-v-877fe06c="" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 1024 1280" xml:space="preserve" height="75" width="75" fill="#149240"><g data-v-877fe06c=""><g data-v-877fe06c=""><g data-v-877fe06c=""><path data-v-877fe06c="" d="M904.2,390.1c-7,0-14,0-21,0c-19,0-37.9,0-56.9,0c-28.2,0-56.3,0-84.5,0c-34.2,0-68.4,0-102.7,0c-37.6,0-75.3,0-112.9,0
                    c-38,0-76,0-114,0c-35.3,0-70.6,0-105.8,0c-29.9,0-59.9,0-89.8,0c-21.4,0-42.9,0-64.3,0c-10.2,0-20.4-0.3-30.6,0
                    c-0.4,0-0.9,0-1.3,0c6.7,6.7,13.3,13.3,20,20c0-28.2,0-56.4,0-84.6c-3.3,5.8-6.6,11.5-9.9,17.3c12.9-6.7,25.8-13.5,38.7-20.2
                    c31-16.1,62-32.3,92.9-48.4c37.3-19.5,74.7-38.9,112-58.4c32.5-17,65-33.9,97.6-50.9c15.6-8.2,31.7-15.8,47.1-24.5
                    c0.2-0.1,0.5-0.2,0.7-0.4c-6.7,0-13.5,0-20.2,0c13.1,6.7,26.1,13.4,39.2,20.1c31.5,16.2,63,32.3,94.5,48.5
                    c38,19.5,76,39,114,58.5c33,16.9,65.9,33.8,98.9,50.7c15.9,8.1,31.6,17.1,47.8,24.5c0.2,0.1,0.4,0.2,0.7,0.3
                    c-3.3-5.8-6.6-11.5-9.9-17.3c0,28.2,0,56.4,0,84.6c0,10.5,9.2,20.5,20,20s20-8.8,20-20c0-28.2,0-56.4,0-84.6
                    c0-6.7-3.7-14.1-9.9-17.3c-13.1-6.7-26.1-13.4-39.2-20.1c-31.2-16-62.5-32.1-93.7-48.1c-37.8-19.4-75.7-38.8-113.5-58.3
                    c-33-16.9-66-33.9-99-50.8c-16.3-8.4-32.4-17-48.9-25.1c-7.1-3.5-14-3.9-21.3-0.3c-1.5,0.7-2.9,1.5-4.4,2.3
                    c-7.7,4-15.4,8-23.1,12c-29.1,15.2-58.2,30.3-87.3,45.5c-37.7,19.6-75.3,39.3-113,58.9c-34,17.7-68.1,35.5-102.1,53.2
                    c-18.7,9.8-37.5,19.5-56.2,29.3c-0.9,0.4-1.7,0.9-2.6,1.3c-6.2,3.2-9.9,10.5-9.9,17.3c0,28.2,0,56.4,0,84.6c0,10.8,9.2,20,20,20
                    c7,0,14,0,21,0c19,0,37.9,0,56.9,0c28.2,0,56.3,0,84.5,0c34.2,0,68.4,0,102.7,0c37.6,0,75.3,0,112.9,0c38,0,76,0,114,0
                    c35.3,0,70.6,0,105.8,0c29.9,0,59.9,0,89.8,0c21.4,0,42.9,0,64.3,0c10.2,0,20.4,0.2,30.6,0c0.4,0,0.9,0,1.3,0
                    c10.5,0,20.5-9.2,20-20C923.7,399.3,915.4,390.1,904.2,390.1z"></path></g></g><g data-v-877fe06c=""><g data-v-877fe06c=""><path data-v-877fe06c="" d="M924,881.1c-7.4,0-14.8,0-22.1,0c-20,0-40,0-59.9,0c-29.5,0-59,0-88.6,0c-36,0-72,0-108,0c-39.6,0-79.2,0-118.8,0
                    c-39.8,0-79.6,0-119.5,0c-37.1,0-74.3,0-111.4,0c-31.4,0-62.8,0-94.2,0c-22.7,0-45.3,0-68,0c-10.7,0-21.4-0.2-32.1,0
                    c-0.5,0-0.9,0-1.4,0c-10.5,0-20.5,9.2-20,20s8.8,20,20,20c7.4,0,14.8,0,22.1,0c20,0,40,0,59.9,0c29.5,0,59,0,88.6,0
                    c36,0,72,0,108,0c39.6,0,79.2,0,118.8,0c39.8,0,79.6,0,119.5,0c37.1,0,74.3,0,111.4,0c31.4,0,62.8,0,94.2,0c22.7,0,45.3,0,68,0
                    c10.7,0,21.4,0.2,32.1,0c0.5,0,0.9,0,1.4,0c10.5,0,20.5-9.2,20-20C943.5,890.3,935.2,881.1,924,881.1L924,881.1z"></path></g></g><g data-v-877fe06c=""><g data-v-877fe06c=""><path data-v-877fe06c="" d="M391.3,510.1c0,12.9,0,25.9,0,38.8c0,31.1,0,62.2,0,93.3c0,37.6,0,75.3,0,112.9c0,32.6,0,65.1,0,97.7
                    c0,15.9-0.4,31.8,0,47.7c0,0.2,0,0.4,0,0.7c0,10.5,9.2,20.5,20,20s20-8.8,20-20c0-12.9,0-25.9,0-38.8c0-31.1,0-62.2,0-93.3
                    c0-37.6,0-75.3,0-112.9c0-32.6,0-65.1,0-97.7c0-15.9,0.4-31.8,0-47.7c0-0.2,0-0.4,0-0.7c0-10.5-9.2-20.5-20-20
                    C400.5,490.6,391.3,498.9,391.3,510.1L391.3,510.1z"></path></g></g><g data-v-877fe06c=""><g data-v-877fe06c=""><path data-v-877fe06c="" d="M230,901.1c0-12.9,0-25.9,0-38.8c0-31.1,0-62.2,0-93.3c0-37.6,0-75.3,0-112.9c0-32.6,0-65.1,0-97.7
                    c0-15.9,0.4-31.8,0-47.7c0-0.2,0-0.4,0-0.7c0-10.5-9.2-20.5-20-20s-20,8.8-20,20c0,12.9,0,25.9,0,38.8c0,31.1,0,62.2,0,93.3
                    c0,37.6,0,75.3,0,112.9c0,32.6,0,65.1,0,97.7c0,15.9-0.4,31.8,0,47.7c0,0.2,0,0.4,0,0.7c0,10.5,9.2,20.5,20,20
                    C220.8,920.7,230,912.4,230,901.1L230,901.1z"></path></g></g><g data-v-877fe06c=""><g data-v-877fe06c=""><path data-v-877fe06c="" d="M794,510.1c0,12.9,0,25.9,0,38.8c0,31.1,0,62.2,0,93.3c0,37.6,0,75.3,0,112.9c0,32.6,0,65.1,0,97.7
                    c0,15.9-0.4,31.8,0,47.7c0,0.2,0,0.4,0,0.7c0,10.5,9.2,20.5,20,20s20-8.8,20-20c0-12.9,0-25.9,0-38.8c0-31.1,0-62.2,0-93.3
                    c0-37.6,0-75.3,0-112.9c0-32.6,0-65.1,0-97.7c0-15.9,0.4-31.8,0-47.7c0-0.2,0-0.4,0-0.7c0-10.5-9.2-20.5-20-20
                    C803.2,490.6,794,498.9,794,510.1L794,510.1z"></path></g></g><g data-v-877fe06c=""><g data-v-877fe06c=""><path data-v-877fe06c="" d="M592.7,510.1c0,12.9,0,25.9,0,38.8c0,31.1,0,62.2,0,93.3c0,37.6,0,75.3,0,112.9c0,32.6,0,65.1,0,97.7
                    c0,15.9-0.4,31.8,0,47.7c0,0.2,0,0.4,0,0.7c0,10.5,9.2,20.5,20,20s20-8.8,20-20c0-12.9,0-25.9,0-38.8c0-31.1,0-62.2,0-93.3
                    c0-37.6,0-75.3,0-112.9c0-32.6,0-65.1,0-97.7c0-15.9,0.4-31.8,0-47.7c0-0.2,0-0.4,0-0.7c0-10.5-9.2-20.5-20-20
                    C601.8,490.6,592.7,498.9,592.7,510.1L592.7,510.1z"></path></g></g><g data-v-877fe06c=""><g data-v-877fe06c=""><path data-v-877fe06c="" d="M537.9,286.4c0,1.1-0.1,2.3-0.1,3.5c0,2.9,1.1-5,0.1-0.6c-0.4,1.7-0.7,3.5-1.3,5.2c-0.1,0.4-0.3,0.8-0.4,1.3
                    c-0.6,1.6-0.6,1.7,0.1,0.1c0.2-0.4,0.3-0.8,0.5-1.2c-0.4,0.8-0.7,1.6-1.1,2.4c-0.2,0.4-3,5.7-3.4,5.6c-0.2-0.1,2.8-3.2,0.7-1
                    c-0.7,0.8-1.4,1.6-2.2,2.4c-0.4,0.4-3.7,4.1-4.3,3.9c-0.2-0.1,3.6-2.4,0.9-0.8c-0.8,0.4-1.5,0.9-2.2,1.4c-0.9,0.6-1.9,1-2.9,1.5
                    c-0.6,0.3-1.2,0.6-1.8,0.8c2.6-1.1,3.1-1.3,1.7-0.8c-1.9,0.2-3.9,1.2-5.8,1.6c0.1,0-2.6,0.6-2.7,0.5c0.3,0.4,4.7-0.4,0.5-0.3
                    c-1.7,0.1-4.6-0.6-6.2-0.1c2.4-0.7,3.5,0.7,1.3,0.2c-1.3-0.3-2.6-0.5-3.9-0.9c-1-0.3-2.2-0.9-3.2-1c-2.6-0.3,4.2,2.2,0.5,0.3
                    c-1.5-0.8-3.1-1.6-4.6-2.5c-0.4-0.2-0.7-0.5-1.1-0.7c-1.4-1-1.4-1-0.1,0c0.3,0.3,0.7,0.5,1,0.8c-0.7-0.5-1.3-1.1-2-1.7
                    c-1.6-1.4-3.1-3-4.5-4.6c-2.6-2.8,0.9,0.9,0.7,1c-0.1,0.1-1.6-2.4-1.8-2.7c-0.2-0.4-3.1-4.7-2.7-5.2c0.2-0.3,1.4,4.2,0.6,1.1
                    c-0.3-1.1-0.7-2.1-0.9-3.2c-0.3-1.1-0.5-2.2-0.7-3.3c-0.9-3.9,0,4.2,0.1-0.1c0-1.8,0-3.7,0-5.6c0.1-4.1-0.1,1.4-0.3,1.2
                    c-0.2-0.2,0.5-2.9,0.6-3.3c0.1-0.3,1.3-5.8,1.7-5.7c0.2,0-1.9,3.9-0.5,1.1c0.6-1.2,1.2-2.3,1.8-3.5c0.5-0.9,1.4-1.8,1.7-2.7
                    c0.9-2.4-3.2,3.5-0.4,0.4c1.2-1.3,2.3-2.6,3.6-3.8c0.8-0.7,1.7-1.4,2.4-2.1c1.8-1.9-4.1,2.5-0.4,0.4c1.9-1.1,3.7-2.2,5.7-3.2
                    c3.4-1.8-1.1,0.6-1.1,0.5c0-0.2,2.8-0.9,3.1-1c1.9-0.6,4-0.7,5.9-1.3c-4.2,1.2-2.8,0.3-1.2,0.3c1.2,0,2.3-0.1,3.5-0.1
                    c0.4,0,3.4,0,3.5,0.2c-0.1-0.3-4.9-1,0,0.1c0.5,0.1,6.4,1.4,6.4,1.9c0,0.2-3.9-1.9-1.1-0.5c1.2,0.6,2.3,1.2,3.5,1.8
                    c0.7,0.4,1.5,1,2.2,1.4c3.6,2-2.7-2.6,0.1,0c1.8,1.6,3.4,3.3,5.1,5c2.8,3-0.4-0.5-0.3-0.5c0.2-0.1,2,3.1,2.1,3.3
                    c0.4,0.8,0.9,1.5,1.3,2.3c0.3,0.6,0.6,1.2,0.9,1.8c0.6,1.4,0.4,0.8-0.8-1.7c0.5,0,1.7,5.9,1.9,6.4c0.1,0.4,0.2,0.9,0.3,1.3
                    c0.3,1.8,0.3,1.8,0,0c-0.2-1.8-0.3-1.7-0.1,0.1C537.9,284.6,537.9,285.5,537.9,286.4c0.2,10.5,9.1,20.5,20,20
                    c10.7-0.5,20.2-8.8,20-20c-0.4-28.1-18-52.4-44.3-62c-24.6-8.9-54.5-0.7-71,19.5c-17.6,21.5-21.1,52-6.5,76.1
                    c14.6,24.2,42.4,35.4,69.8,30.5c30.3-5.5,51.6-34.1,52.1-64.1c0.2-10.5-9.3-20.5-20-20C546.9,266.9,538,275.2,537.9,286.4z"></path></g></g></g></svg>

         <p style="font-family:'montserrat';font-weight:bolder;font-size:15px;margin-top:15px;">The connection to your bank experienced an issue. Please select the Fix option below and follow the steps from your bank to restore the connection.</p>
         <div class="row">
          <div class="col-6">
        <button class="green-button-fix" @click="fixMxConnectCall(cur_account_id)">Fix Banking</button>
          </div>
          <div class="col-6">
            <button class="black-button-ok" @click="hideModal('error-message-mx')">OK</button>
          </div>
         </div>
        </div>
        </b-modal>
      </div>
      <!-----------------------  MODAL FOR ERROR MESSAGE DUE TO MX !----------------->
    <!--- Problematic Account Modal Start --->
    <problematic-account-modal ref="ProblematicAccountModal" :handleCanPayLoader="handleCanPayLoader" :account_details="problematic_account_details" :connectMXBankAccount="connectMXBankAccount"></problematic-account-modal>
    <!--- Problematic Account Modal End --->
    <!---- Mx Widget --->
    <mx-connect-widget ref="mxRef" :on-event="handleWidgetEvent" :widget-url="mxConnectUrl" />
    <!---- Mx Widget --->
    <!-----------------------  show backend error message start !----------------->
    <div>
    <b-modal
        ref="refresh-error-message"
        hide-footer
        v-b-modal.modal-center
        modal-backdrop
        hide-header
        no-close-on-backdrop
        id="refresh-error-message"
        centered
        title="BootstrapVue"
    >
     <div style="text-align:center;">
          <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="75" height="75" viewBox="0 0 213 186" fill="none">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H213V186H0V0Z" fill="url(#pattern0_10067_408)"/>
        <defs>
        <pattern id="pattern0_10067_408" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use xlink:href="#image0_10067_408" transform="scale(0.00469484 0.00537634)"/>
        </pattern>
        <image id="image0_10067_408" width="213" height="186" xlink:href="data:image/png;base64,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"/>
        </defs>
        </svg>
                 <p style="font-family:'montserrat';font-weight:bolder;font-size:15px;margin-top:15px;">{{error_text}}</p>
         <div class="row">
          <div class="col-12">
            <button class="black-button-ok" @click="hideModal_refresh_pp('refresh-error-message')">OK</button>
          </div>
         </div>
         </div>
    </b-modal>
    </div>
    <!-----------------------  show backend error message end !----------------->
  <!-----------------------  Non Actional Action Status Start  !----------------->
    <non-actional-action-status-modal ref="NonActionalActionStatusModal"/>
  <!-----------------------  Non Actional Action Status End  !----------------->
  <!-----------------------  Non Actional Action Resolve Status Start  !----------------->
    <non-actional-resolve-modal ref="NonActionalResolveModal"/>
  <!-----------------------  Non Actional Action Resolve Status End  !----------------->
      <!-----------------------  MODAL FOR UPDATED PURCHASE POWER  !---------------->
      <div>
        <b-modal
          ref="updated-purchase-power"
          hide-footer
          v-b-modal.modal-center
          modal-backdrop
          hide-header
          no-close-on-backdrop
          id="updated-purchase-power"
          centered
          title="BootstrapVue"
        >
        <div style="text-align:center;">
<svg width="50" height="50" viewBox="0 0 216 182" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H216V182H0V0Z" fill="url(#pattern0_10067_872)"/>
<defs>
<pattern id="pattern0_10067_872" patternContentUnits="objectBoundingBox" width="1" height="1">
<use xlink:href="#image0_10067_872" transform="scale(0.00462963 0.00549451)"/>
</pattern>
<image id="image0_10067_872" width="216" height="182" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANgAAAC2CAYAAAC73kPHAAAABHNCSVQICAgIfAhkiAAAGyZJREFUeF7tnQvYdtWYx78Yw1CDHMZUxkdqFA1TgzFIckxJMlKpvKHUVCRjHDK8opFKhFApIRRqRjFpakYINRozUYqiT04jQjON0xzM/5f9fNfzPT3vsw77vPf/vq7/1df7rL33Wvde/73Wutd932u9jY/fflXDclc9bxNhM2EL4b7CHwh3E9YXfqvh+vhxzWng13rUL4SbhH8XrhOuFb4qfEv4jvC/zVWn/iet1xDB7lAQakv999HCQwtiQarb1N9MP6HDGvi56vY94XLhkgKQ7vsdrnN01eom2AYFmXbQf58kbBVdMxccqwZ+rIZ/Svh74dPCN/qsiLoIxjRvW+HZwk7CPfusJNe9NQ1AsI8KZxejXGsVyX1wHQTbXJXZX3iGsDq3Yr7OGpjSwDn698nCeUKv1mhVE+zpUsAhwmPdPayBijXAmux04XjhuxXfu7bbVUUw1lovEg4UNqqttr6xNbBqFaPZkcI/90EZVRCM9dWysK+AtdBiDdStgX/RAw4Xzq/7QWXvX5Zg91IFjhYwZtjcXvZt+PoUDXxdhf9K+FjKRU2XLUMw9rAg13OE2zZdcT/PGpAG2Kg+WMCk30nJJdhvqzXMg18o8O9c+aYuZBefRSs7+/8p/F+B3Hv6um5rgP6yoYA3D148DxHuVKLKV+ja5wmdXJPlEoyvxlGZisFV5izhM8JVwpqCWL/Uf/+nhKJ9aT80sJ6qeTuB9Trrd1zmthF2LP6b04oLdNF+Au5WnZIcguG8+G6Br0+qnKoLPiR8RfiR0Ks9jdTGuny0Bn5HJTcVHie8QMBHNUXoR28VXi3cnHJh3WVTCYYJ/p3CzokVg1DLwkUCrjAWa2CeBhjZ7i8cILD8SBF8GrFkn5lyUd1lUwl2qCqEYQNFxApuLi8X8CljfWWxBkIauLMK4An0RuHuocJTv39e/14Srkm4ptaiKQRjMXqKsHVkjQhNOFF4hfDTyGtczBqYaICPOFNGXKQwiMQIH/BXCscJ/x1zQd1lUgj2KlXmCIFFaoyw3mKY/6+Ywi5jDczRAH0NkuEi9XuRGmJ/7JnClyPL11oslmCMXhDmjyNr80mV21vAkGGxBspogD3WZwnvFWKDcV+msm8RflXmwVVcG0MwviLsMzBUxwhRqU8V/i2msMtYAxEaYJ+MpQZr+RinhstUbneh9ViyGIJhOXy7gKd8SDCXMi08SfCeVkhb/j1FA5jx3yMQER8SRq7dBByDsQW0JjEE+zPV7sPCxhG1JEBuT4EQcIs1UKUG8HVl2YHh7PYRN6YcU0Xyf7QmIYLRqCWB6WHImZcR6y8EvjIevVp7pYN+MMmRIM6TI1r5ryqzq7AmomxtRUIEIwPU6wvihCrBmmsP4epQQf9uDWRqANM9cYdvEEIGDxwadhEuFlqbJoYItlqVwy0KU2lI3qYCLERtlg9pyr+X0cAjdPH7BDw+Fgn7YDhG0H9bsyaGCPZgVY71F3k2Qo0hmhlTatnpIdOAewh8rVr78pTpAb52rgZwZcIZt+yaCKPbuwQs1SF5kwosC635J4YIRm4NCBZyV7leZYgLuyjU4gW/k3aAOTPTzAcKOIBCMJOshFI7cOnEMeFG1QUjGPupl5aoF/0E0hwWcY8zVAa7wE8iytZSJEQwdsRxj6JRi4QQbqKav5ZZy0lOD8LAnXYgU4k9uYz4rYME1kY5vqnsgzFbYnQKxSJeqDJ7CT9oSzeLCMaXZ0k4QWA0WSQ0ZB8hNxsrO/VYKkNEbktPfm61Gvinor/kZoeivxDVgRFukZApmFlRbr8s3epFBMNK83wBl5PQvgMbeuRCzPlSkHqAVFyMgJZxaICRC295+k3OKMb6C+NFKKHtl1TmaQLeRa1IiGCQ5s1CaCgmJwLuVIT9p8qDdAHzcvLVW8ajAZzH8XrH+JEqRD/TZ0IEY+sIgmEjaEWqIthnVXtGoG9ntAKDBpvTJliG8np8CVs6fLxJFZEqoyMYEcssJnNCBAiuY4qIFdIyDg3gs8ra6Fwhx0o8OoLdIEUtCYSp5CiMYZw9NMhmGb4Gyiwp0M7oCMYXiX0JLDs5kaRYKdmveK1QJoXX8Ltm/1v4BTUBd6cvlmjK6AiGrlhHvVjI3am/o659isBoRlahyX5YzohY4t350oo1ML3R/Dnd+wPClSWfMUqCEapNqAqbzrnCy8BNipTc3nDO1WL3ruMjyYeXfa8qfFVHSTD2M14iEJxZ1h+xe13ENeqSBkZJMF4AU4DnCoxmFmugLg2MlmAYOzjxgiyrHsXq6l6+72gJxqvHLYX8HazFbKAwGerQwKgJhkI5s4n0xzmuU3W8EN9zWBoYPcF4nccIy8LPhvVu3ZoOaMAEK15CGX+zDrxHV6GjGjDBpl7M64rRjMP1LNZAFRowwWa0SCJSpoycaJkT/1PFS/E9hqMBE2zOu+RESzJPEc3qM8KG09nbaIkJtoLWOcYICyNptzibmQhom/Lb6KL9fuYOqj6+r6ETVwYTcJn6un6oC/D6gGwogRGNzD9YHH2sbKo2x1eebGcfFPBZXSSjJdhEKZCJPPZkGmJEY5Oa9F44hE7CXmLPIxtfNxtfi+kvvxC2FQh5CcUPQjCOO86JtK9Eu1WlDKikMlM3wc0KY4jJVbVm+32/yXKCfhFzjDHHGD1JaG3NXxXBIEQoV3i/X61r30cNkOwG39jzhVaOMa6KYFgIrxNIxbV+H9+E6zxYDZATkYzCnxBIadHoqatVEYwcdYT7kxCSL0YondZg36Yb1lkNkJ/+H4UPCeRjzEkXl9y4qgh2lp68r0AjCPkn8PJRybXxBdZA/RrAAZ3RjIzVnCFWq1RFsLNVS/KFk10KIacGeRI5kZDTUizWQNc0QDgVWavJEVLbXmxVBGMEg2Dsb02E7FAkEyVfIpacTbqmYddn9BpgfcbyBje+Wnxl6yTY5O1x9NEfCeQTZ9q4lRDKdT/6N28FNKYB1mKnC68RKj8kogmCTTT1u/oHh6c9UtimINr9ir81pk0/yBqYowG2mZiFYTvIPfFlrmKbJNikAuyXMX0kPRvHgN5bYPqIXxmjHWZ+NhG9yWwuzNPAZL1EH6EfscYPHRAZo0m8RDhsktyeOacEdYZgsxWBcCiLE1zIhQg4ZO02MVpxmVFqAJLRb+grGwoY1fhYbybgp5ibHRr3PE5tYSSrIn/jqjZGsFH2CDe6Vg3wgYZs+CbeR3iCQPpAZkepArGOFI4SSlsXTbBU9bt8HzRAGnaWHZy6enBBvJR642LFVJHtp1JigpVSny/uuAawVj+8GJEwrsWu6xm5cP/jhNdry7TRBCujPV/bBw1Mzjtg2seebOyZB0RzcND6K4XsJLomWB+6iOtYhQYwihwhHCpwXFaMMHpxrNYFMYXnlTHBcjXn6/qqAbw2CNaMiSejjVgVWcdlOQebYH3tJq53rgYgFu5R+MnGrMmIhn6BcF7OA02wHK35mr5rAAsjx9jithcjHGbC3ljyWswEi1GvywxRA/jGfkSI8Yu9XOWWBHJ8JIkJlqQuFx6QBpgqkvqNsKoY2V+FTo4pOF3GBEvVmMsPSQPskZEIl43pkJyiAhhHklyoTLCQWv37kDVAhAdp3Ul1ERICNPEMIb1gtJhg0apywQFqACsiiZpYi4WEkYuyZKiKFhMsWlUuOFANbKl2YYKPSW1xkMq9I0UPJliKtlx2iBogDvF4IWaaeKzK/bVAduEoMcGi1ORCA9YAvomkGiTtYEhILYCXfXRuRRMspFL/PnQNENj7HAGXqJCQuBSvDsJZosQEi1KTCw1cA7uofeTkCEXRf74gY3QIiwk28J7j5kVpgLSCfyuEvOwx1RPycnXUXVXIBIvVlMsNWQNPLEaw0LkKuErtKVwVqwwTLFZTLjdkDZhgQ367blvrGmCK+FHBI1jrr8IVGKIGHqNGnStsEGicp4hDfPsDahNOteS/7IqQ3IbcG48T3h8xgl2qMrsJNtN35Q2OvB6cE7eDgNc6rkjkLYwN1W9KdZDsLsLmQshMzymZnC/2cYGzxn4ZqqSNHCEN+fccDWyqi8hjsaNAinTWNmRrjgnRz3lek9fgJsVJLKQSYNQ7TVjxeFoTrMlXM/xn4XZErovDhY2FoZ/bzQjGIX6vEMijyHRzHTHBht/pm2oh00GIRZqzoRNrVqc/1h/In8hots600QRrqvsN+zmcbkJiT8LqxyocHIEj8InC2uQ4JthYu0N17cYy+FJhubpb9vZOBGXiSvUx4ZaDI0yw3r7LTlQcowWbtDjKxuS16ESla64EblQ7Cd80wWrW9Ahuz9lchNtvP4K2pjTxaBV+OaOYR7AUtbnstAYYvTDD4wFhWVcDN+h/HyZcb4K5a+RqgISdpKBmzREr/6GCk3D7vuyJTQ7hYy8vdhr8K5UlSvoEEyy2a7jcrAbIZXGZQBrqkGC6vrgY7ThkHI+JvhCMs5up6wOEXYUHR9QdUhL9vLsJFuoa/n0lDfypfiDCN0QUTNZ4qrMZu6bn6nyo6k9WqT+JaMc1ENIEi9CUi9xKA5CKLEz45YWERJ3kvGC0G4LQluMEDDyLhHXY3ibYEF55821gikfyl5gcgYTi0ynx3xuC4GfJPtcDA425Ub/vb4IN4ZU33wYIdqDw9ohHs0e2JNwcUbYPRYgKwJs+dPQRBDvQBOvDK+1eHSHYAcIJEVU7uyDYUEaw+6o95wgPihjBDjDBInqIi9xKAyaYCWZa1KiBpgiGMQVjwt0E4sl+IvxQwHTelngEa0vzI3puEwTDFE5CULYDiITmmazjrhTwHrlQwIO9aTHBmtb4CJ9XJ8GIJWN9x0kmdOZ5R7x+S39nbfeGYkRr8hWYYE1qe6TPqpNgxFS9SgjtM+GOxBGwBDoS8NiUmGBNaXrEz6mLYE+QTslzgRtWjOCCxXFCx8QUrqiMCVaRIn2blTVQB8HINvUB4ZmJir9E5fcTrki8Lre4CZarOV8XrYE6CEZ4xwcFPCVSBEMH67WTUy4qUdYEK6E8XxqngToIhjvVWwUOJk8VpoiEhzQhJlgTWh75M+ogGJZDnGhDRwjNU/079ccXCmuTzdT4fkywGpXrW/9GA3UQbB/d920eweZ3MRw6cf5kl90yfA3UQTDSa3MG8v0T1Ycl8RBh0GswNv0gGDEwluFroA6Ccc8zhGcUI2SsFgn6JHSml1ZEzJ9vEUInYjCCkdHVBIvtFv0uVwfB0MjjBTaPY9IQUJ7NZrIJH9ugOitbg1HnpwnsTdwp0ADCFmjoTQ021I9qTwN1EYwWMd07QuDEk0VC8pxTBTw5mux3lRKMm50mbLugpcT5kFkIB8xJBp72Xr2f3IQG6iQY9WfmxOksmwkcKDGd94M1F2v99wrMrn7URIOnnlEpwbgvoxih4RvNaQipgt8lLAtDiVht+H318nF1EwylbC3sLJBo5t4FySATp5mcJ3A+161OM2lAm5UTjK8HDX2JgIWHfQr2G3CwZPqIaZU4Hct4NNAEwSba5NQWpouTcJXvtKzmygk2aQ/nPeHOwn+ZFnJe7eUtN9aPb0cDTRKsnRau/NTaCNa1hro+7WnABKsgZUB7r89P7roGUgg25qxSC5PedP0lu37taQCCYenDwBWSv1MBHHnJSz8EwQ5Bm2LyIu63KKvUEJThNtSjAQhG3BaeFyG5WgWWhEtDBXvy+76q55uEuwbqy1bCXiZYT95qB6uJ7yBuSpBtkbA3Sm56zsu65VC6HgtGPrastolow9dVZmFu+oh7uMiINUBYP6PSfSJ0wH7VZ4Xzhe9HlO9SEepOyjimhuwJh6aG1J1rPiEszE3fpUa6Lt3TANmeiMNiyhQr7J/+NLZwR8oxAjNKk5sxVmjnocJJniLGqszlZjUwOZ8ZrwrLuhrgDDTyOX7XBHPXKKMBPCzOFJ5Y5iYDvPZItYnUc6tMsAG+3YabRKo1jigKRVw0XK3WHkfm4ScLt7h0mWCtvYfBPBhv98MEvtpjF9aXrEnZJ7tFxkww9jG2EIgr+tLYe0bJ9pOF9/UCUe1jlZ+p4TjEnyKszZk/RoLdQwoggxHD+N0FLD4sSokv4kjUNkIghtAp0etLCwyhPSltYFOZfT7yOvLBXitjI9hqtZwcejsJTG2mBVee1wqc2kgouiVdAxvokj8XOPCcYMkxyD+okUcJn5vXb8ZEMDZEiYB9qsDG4Tz5uf6ICxCmZ49kefRAt6uF3YTnCqlZovKe2uxVnE8GoU4SLhAYweZG84+FYJyri//YrkLItedElXmZ0GSeh2a7RzNPg2iscznn6zHClgJm/XnHETVTo/lP4UO6vvCHQii507dVhjUWXhq4QhHFv/BDPAaCEW5+tPAsYTq3w0ovFf+6PQXOoLJUowH0Dvi4cf5XzHuo5smL7wI5MEiw1UB0fshb4zMqw6j8jdjKDZ1gkOuNwh6xClG5LxTlTbAEpfW8KBvlHxFCefGJ4Ofj+9XY9g6ZYKQ24ATEvWOVUZRjXs0UsW8+c4nNdPEpDUAwPP4x0iwS0mRAsKtitTdUgkEu9mWWYhVRlCPXCFNJvL5t5EhUXo+Lm2AJL+/3VRZzOxG3KcJGIaQ8XuDflvFowASLfNfEKL1GSPUogFCcTfU6kytS08MqZoJFvE8sQK8WOCcqRdh5J/U3xCSRqmV8GjDBAu+c/RXCA/AFSxHSMLPvxUHaQ0nKktJ+l/2NBkywBT2BTUIIknqEKPsfWAwhpi2G46aaCbbC+8efEL83poYpgoUQcnEyh9N+p2humGVNsDnv9Xb6Gx7MrJ1W8i1cqTtwGiLXkl/fYg2YYDN9AFcbRi7IFfIfm+0+p+kPTCd93K2JNdGACTbTF4g7Yq8r9UT69+sarv2B+5Y1MKWBRgjGSPBo4ZHCnQXOYiKX3cUdexUvUn3Yrwq5tcxWm2BKrIx9y8vXMfUPsjq1EwwzN4YCYqVWC3g8Y8ImE+uHBbzRu+DdwDnQeFuE0hbP9gIOaWd/jMhlizUwq4FaCUZ8zt8UHRBizQobsRyyx3qHgMS2hJPkGbkITU8REpBALmJ5LNbAPA3USrBH6IkXCndcoHuIdZzA6LFOzoGG3hcxOGQtulfi84hMZtRbk3idi49LA7HhKhxf+2whyZuekWk5Qp+4EZF7AJAopinZRw9ihMVDPkUI5cYnMTo4LuXmLjsoDZAAiWVEyGh2mcrsJXwttvWEq7xbhZ8XeQHhHJCR3BZNhHPQGMhF4GSKcEA25Lom5SKXHaUG2PIhlQQBl6FI66yIZqZeeDTECm5FuCZxjEudJNtd98e4kkqui3QN6zVyJlisgZAGcFJ4vhBzmOA5KneQEH0QOyPYtrrg06FazPyOBwRRv6fWRDK+KKz5Yo7Gma4a+TQYjTn0zWINxGiAlN+HCzguhIT+zj5qtAcQBMO4wUgBM1OEfTL2lU6vmGQ7635MQTnNPUW+qMJLQnS+hJSbu+xgNbCJWsYxTOTKDAmzPRwc1mbuDV0wSRmAAYG0ZoTLp8gNKowJnHwG5IorKzSSiOL7JdyIfHTkSoBcX064zkWtATTAaZWfFMjyHBLyzp8WKjT9+3ROjtX64c0Cp/iFFnvT92B/Ce8K5qe5JON5pM4i8DE1UeVXdA2HbGNCtVgDKRpg35fUEtgTQsJgQtZivJuiZTbpzaa6ktB5zJahBJ3TDyHF2cEC+06pJOM57EPw3NR0y+xHYMbHfGqxBlI1QP4W8iE+NuLCT6kMxpCkc6bnZZXaXDdhTrpdIsmuVXk2dTGRx1oXIRcjF9NCMqumyNBOr09pu8uW1wCzpl0E9r9ihCUU1vakcwtWStvGQc+Q7FFCynSREYX9J/YL5ubqnmoJ5OLLwWELD4hp4VQZNvqwFpIf3GIN5GgAlzsODsS5PSR4L7Ene1ao4Ozvi/Iibl2Q7GGJN8XQwD7UJQuug7TkK2dauFXi/SEXJGbItlgDORpg7wu7AaNSjOBKyFptTUzh6TKhxKMc5IzhAbKlCGsiKoR1b54QFgO5HpJyU5XF7YlpKEfGWKyBXA3Q/xi9QrnouT82BSJN8ChKlhDBuCEb0UwXOR0jRZi+4aQ761HxcP0N0mIeTZHrVJivzrkpF7msNTCjAQx5HJQXOzO7QmUxpGVZqWMIRv0wREAyKpciGDywvKwpLmIkxCQKyVKEOK5DBL46FmsgVwPs9/JxZysqRnBqxwmDzGMhm8Lc+8USjIt3LMjBWVsp8vGCHLikkIMwZlE5fX/8vvAYIfDTYg3kaoD9VU7awQ0vVji7mz1WRrEsSSEYD6ByBF9ulPg09seQHRKvY+TC94twf4s1kKsBPurEMm6XcANiIPG3pb9nSyrBeBABZ1hfyANfp5A7gwaSqMZiDeRogEiMpwuHCqm+rcyYMKjdmPPgyTU5BONajBfHCqm5MWLrStYnPJzfI8RuWsfe2+WGrwE+/hjnME48RUjxSkI7uN9hBb+0rKpyCcZz8b7HdBk6FTC1juQrZMcccqW6XaU+y+WHowH6IZZuUmBsL2wnkFY9VYgSwYG9kmVJGYLxVXixQMqB1BRqKzWa4RiLDZl3u0ou1p+4dZFZ2KNravetrjyOuvQ78rSsFrBw48u6hZDifTRdI9ZdywJO79EhKYuaVIZg3JeMVIcVpFiUNCdGrZNIaSyNlTQu5qEJZfhCsuVAajtIhjdAluk24ZkuurIG0D9nE5ByMJRLI0aP9LljBI4dvjnmgpgyZQnGMzC/Y+kj1zuEyxGODmK3nLBt8jF2TSAXVijigXKmHV1rj+uzrgbwNYRcGO9uqlI5VRCM+tABMUr8pZC6oCShKeRiA7CNlHAhfTIVYXvifULuByT0DP/engYI/ydTGh93kjpVKlURjEoxVJMYlLiwWGG0WhbwS+xC5uB59SbSlaOOMPdahqUB0kuQBoCQlVo+7lUSDNVjtmeojUkDR1wN813Kd/no1tWqH87FqcGgw+qKw2sNpGJKiCm+NoNa1QTjNfDFxwpD/MxKArkYlvHz6jK5qD8EO18gENXSfw0wamFIO1Oo/ZSdOgjGK8DKBoH2nvM+sNYwlSQtW9fJRfU3FAgK3aP/fWvULSB3DBnQcBi/XEiKTM7VXF0Eoz7sT5AkBAMB0yuGYZwnyYGAb2JX11yzusRogzcAL2be4Ri5uvd1zWgAR90zBKb5Vzbd7+okGOqjQ95TmOyRYQJlM7lvG7TUH+MNa8ZUK2kz3chPmWiADznTQCLeJ6T6nv7dyIg1+xrqJtiQXjsmenKIkK6AYFE2OXmZ3mxu5y0Tq8USAzM7BCKzGcQiiv56AZM7lsFW38//A5IYRIDUvrRPAAAAAElFTkSuQmCC"/>
</defs>
</svg>

         <p style="font-family:'montserrat';font-weight:bolder;font-size:15px;margin-top:15px;">Your Purchase Power calculation has completed.</p>
         <button class="black-button-ok" @click="hideModalUpdatedPP('updated-purchase-power')">OK</button>
        </div>
        </b-modal>
      </div>
      <!-----------------------  MODAL FOR UPDATED PURCHASE POWER  !----------------->
  </div>

</template>
<script>
import { db } from "../../firebaseConfig.js";
import api from "../../api/account.js";
import Loading from "vue-loading-overlay";
import constants from "../Common/constant.js";
import RealAccountNumberModal from './RealAccountNumberModal.vue';
import NoPrimaryAccountModal from './NoPrimaryAccountModal.vue';
import ProblematicAccountModal from "../Modal/ProblematicAccountModal.vue"
import MxConnectWidget from "../MxConnectWidget.vue";
import CanPayLoader from '../CustomLoader/CanPayLoader.vue'; 
import NonActionalActionStatusModal from "../Modal/NonActionalActionStatusModal.vue";
import NonActionalResolveModal from "../Modal/NonActionalResolveModal.vue";
import moment from 'moment';
export default {
  name: "BankList",
  /**
   * @description -
   * @returns {any}
   * qr_url => this will store the qr-url from generate qrcode api
   * qr_code => this will store the qr code in base64 format
   * payment_pin => this will store the payment pin
   */

  data() {
    let self = this;
    return {
      isLoading: false,
      fullPage: true,
      currentUser: null,
      bank_changed: false,
      account_no: "",
      bank_id: "",
      banking_solution: "",
      bank_list: [],
      show_refresh_btn: false,
      show_prev_ref_btn:false,
      darkAccordion:false,
      account_id: "",
      error_message: "",
      selected_bank: JSON.parse(localStorage.getItem('selected_bank')),
      blacklistedAccountNumber: constants.blacklistedAccountNumber,
      blockedRoutingNumber: constants.blockedRoutingNumber,
      activeAccordion: 'active_bank', // Set the active accordion to the active bank by default
      currentBankName: '',
      isOpen: null, // Use a single variable to store the index of the open accordion
      animation_fixed_timeout: process.env.VUE_APP_WHOLE_ANIMATION_TIME_IN_MS,
      show_deplay_update_pp_modal: true,
      problematic_account_details:{
        bank_name:'',
        account_no:'',
        active_bank_id:''
      },
      show_problematic_modal:false,
      fix_connection:false,
      mxConnectUrl:null,
      cur_account_id:'',
      error_text:'',
      showMxNonActionableStatusResolved:false,
      showMxNonUserActionalStatusDeteced:false,
      lite_consumer: constants.lite_consumer
    };
  },
  created() {
    
  },
  mounted() {
    this.$root.$emit("changeWhiteBackground", [true, false, ""]);
    var app = document.getElementById("app");
    if (app) {
      app.style.setProperty("background-color", "#ffffff");
    }
    var element = document.getElementsByClassName("content-wrap");
    if (element[0]) {
      element[0].style.setProperty("background-color", "#ffffff");
      element[0].style.height = "114vh";
      if(window.innerWidth>1200){
        element[0].style.height = "121vh";
      }
    }
    this.currentUser = localStorage.getItem("consumer_login_response")
      ? JSON.parse(localStorage.getItem("consumer_login_response"))
      : null;
   
    this.getBankAccountList();
    setTimeout(() => {
      this.$refs.RealAccountNumberModal.checkForRealAccountNumber();
    }, 2000);
    this.getdata();
  },
  components: {
    Loading,
    RealAccountNumberModal,
    NoPrimaryAccountModal,
    CanPayLoader,
    ProblematicAccountModal,
    MxConnectWidget,
    NonActionalResolveModal,
    NonActionalActionStatusModal
  },
  watch: {
    bank_id: function (newval, oldval) {
      this.bank_changed = false;
      if (newval != oldval) {
        this.bank_changed = true;
      }
    },
  },
  methods: {
    hideModal_refresh_pp(modal){
      let self = this;
      self.hideModal(modal);
      self.$router.push("/pay")
    },
  showModalMxError(modal,account_id){
    let self = this;
    self.cur_account_id = account_id;
    self.showModal(modal);
  },
    handleCanPayLoader(value){
      this.isLoading = value;
    },
    connectMXBankAccount() {
      let self = this;
      self.fix_connection = true;
      this.isLoading = true;
      let request = {
            active_bank_id:this.problematic_account_details.active_bank_id
      };
      api
        .directLinkGenerateForMx(request)
        .then((response) => {
          if (response.code == 200) {
            const finicityConnectUrl = response.data;
            window.finicityConnect.launch(finicityConnectUrl, {
              selector: "#connect-container",
              overlay: "rgba(255,255,255, 0)",
              success: function (data) {
                console.log("Yay! We got data", data);
                if (data.code == 200) {
                  if (self.update_bank == true) {
                    // self.updateBankDetails();
                  } else {
                    //now store the details at canpay end
                    self.storeBankDetails();
                    setTimeout(() => {
                      self.$refs.RealAccountNumberModal.checkForRealAccountNumber();
                    }, 2000);
                  }
                }
              },
              cancel: function () {
                self.isLoading = false;
                console.log("The user cancelled the iframe");
              },
              error: function (err) {
                console.log(err);
                self.isLoading = false;
                console.error(
                  "Some runtime error was generated during Finicity Connect",
                  err
                );
              },
              loaded: function () {
                self.isLoading = false;
                console.log(
                  "This gets called only once after the iframe has finished loading"
                );
              },
              route: function (event) {
                self.isLoading = false;
                console.log(
                  "This is called as the user progresses through Connect"
                );
              },
              user: function (event) {
                if (event.data.errorCode) {
                  console.log(event.data.data.institutionId);
                  setTimeout(() => {
                    window.finicityConnect.destroy();
                    //if error code is present then call the connect fix api
                    self.generateConnectFix(
                      event.data.data.institutionId,
                      null
                    );
                  }, 2000);
                }
              },
            });
          } else {
            self.isLoading = false;
          }
        })
        .catch((err) => {
          self.fix_connection = false;
          self.isLoading = false;
          console.log(err);
        });
    },
    handleWidgetEvent(event) {
      var self = this;
      console.log('MX PostMessage: ', event);
      if (event.type === 'mx/connect/memberConnected') {
        console.log("Got here");
        self.$router.push({ name: 'mx-success', params: { 'user_guid': event.metadata.user_guid, 'member_guid':event.metadata.member_guid, 'mx_user_type':'challenged', 'fix_connection': self.fix_connection, 'active_bank_id': self.problematic_account_details.active_bank_id } })
        if(self.fix_connection == true){
          self.fix_connection = false;
        }
      } else if (event.type === 'mx/connect/loaded') {
        // Handle widget loading completion
      }
    },
    showModalProblematicModal(){
      this.$refs.ProblematicAccountModal.showModal();
    },
    changeOneTimeRefreshModal(value){
      if(this.show_prev_ref_btn == true)
        this.show_refresh_btn = value;
      this.darkAccordion = value;
    },
    fixMxConnectCall(curr_active_bank_id){
      let self = this;
      self.hideModal('error-message-mx')
      self.problematic_account_details.active_bank_id = curr_active_bank_id;
      self.connectMXBankAccount();
    },
    hideModalUpdatedPP(modal){
      var self = this;
      self.hideModal(modal);
      self.$router.push("/pay");
    },
    toggleAccordion(target,institution_id) {
      // Toggle the open/close state for the clicked accordion
      this.isOpen = this.isOpen === target ? null : target;
      console.log(this.isOpen,target);
      this.activeAccordion = this.activeAccordion === target ? null : target;
      if (target === 'active_bank') {
        // Set currentBankName only if the active bank accordion is clicked
        this.currentBankName = Object.keys(this.bank_list.active_bank)[0] || '';
      }else if(this.isOpen >= 0){
        this.getOtherBanks(target,institution_id);
      }
    },
    ucfirst(str) {
      return str.charAt(0).toUpperCase() + str.slice(1);
    },
    showValidationModal(msg) {
      this.error_message = msg;
      this.$refs["validation-modal"].show();
    },
    hidevalidationModal() {
      this.error_message = "";
      this.$refs["validation-modal"].hide();
    },
    getOtherBanks(index,institution_id) {
      var self = this;
      self.isLoading = true;
      var request = {
        institution_id: institution_id,
        type: 'others',
      };
      api
        .getBankAccountList(request)
        .then((response) => {
          let newDataArray = response.data;
          if (self.bank_list.other_banks[index] && self.bank_list.other_banks[index].hasOwnProperty('accounts')) {
            self.bank_list.other_banks[index].accounts.push(newDataArray);
          }
          self.isLoading = false;
        })
        .catch(function (error) {
          self.isLoading = false;
        });
    },
    getBankAccountList() {
      var self = this;
      self.isLoading = true;
      api
        .getBankAccountList()
        .then((response) => {
          self.bank_list = response.data.data;
          // Initialize currentBankName with the first bank name
          const activeBankName = Object.keys(this.bank_list.active_bank)[0];
          self.currentBankName = activeBankName || '';
          if (self.bank_list.length == 0) {
            self.$refs.NoPrimaryAccountModal.getUserdetails(true);
          } else {
            if (self.currentUser.consumer_type != self.lite_consumer){
              this.show_refresh_btn = response.data.refresh;
              this.show_prev_ref_btn = response.data.refresh;
            }
          }
          self.isLoading = false;
        })
        .catch(function (error) {
          self.isLoading = false;
        });
    },
    save() {
      var self = this;
      var request = {
        id: self.bank_id,
        banking_solution: self.banking_solution,
      };
      self.isLoading = true;
      api
        .saveBankDetailsFromList(request)
        .then((response) => {
            self.currentUser.account_no = response.data.slice(-4);
            localStorage.setItem(
              "consumer_login_response",
              JSON.stringify(self.currentUser)
            );
            self.getBankAccountList(); 
            self.showModal("updated-purchase-power");
        })
        .catch(function (error) {
          self.isLoading = false;
          if (error.response.data.data == 1) {
            self.$refs["update-bank-modal"].show();
          }else if(error.response.data.code == 597){
            self.generateConnectFix(
              error.response.data.data.institutionId
            );
          } else if (error.response.data.code == 598) {
            self.bank_changed = false;
          }else if(error.response.data.code == 599){
            self.showModal('delay-in-refresh-balance')
          }
        });
    },
    getUserdetails(){
			var self = this;
			api
			.getUserdetails()
			.then(function (response) {
				if (response.code == 200) {
          localStorage.setItem(
            "consumer_login_response",
            JSON.stringify(response.data)
          );
          if (localStorage.getItem("redirect-return") != null) {
            setTimeout(() => {
              self.$refs.RealAccountNumberModal.checkForRealAccountNumber();
            }, 2000);
            localStorage.removeItem("redirect-return");
            self.$router.push("/returns");
          } else {
            self.$router.push("/pay");
          }
				}
			})
			.catch(function (error) {
			});
		},
    selectBank(account) {
      var target = document.getElementById(account.id);
      target.checked = true;
      this.bank_id = account.id;
      this.banking_solution = account.banking_solution_name;
      if (target.getAttribute("ischecked") == 0) {
        this.account_no = account.account_no;
      }
    },
    clickClose() {
      var self = this;
      if (localStorage.getItem("redirect-return") != null) {
        localStorage.removeItem("redirect-return");
        self.$router.push("/returns");
      } else {
        self.$router.push("/pay");
      }
    },
    //calls the api to change bank account through finicity portal
     changeBankAccount() {
      let self = this;
      self.$router.push("/banklinking");
    },
    storeBankDetails(is_exit=0) {
      var self = this;
      this.isLoading = true;
      api
        .updateBank()
        .then((response) => {
          if (response.code == 200) {
            localStorage.setItem(
              "consumer_login_response",
              JSON.stringify(response.data)
            );
            this.getBankAccountList();
            setTimeout(() => {
              this.$refs.RealAccountNumberModal.checkForRealAccountNumber();
            }, 2000);
          }
        })
        .catch(function (error) {
          self.isLoading = false;
          if (error.response.data.code == 598) {
            self.$refs.NoPrimaryAccountModal.showModal();
          } else if(error.response.data.code == 597 && is_exit == 0){
            self.generateConnectFix(error.response.data.data.institutionId);
          }else {
            self.showValidationModal(error.response.data.message);
          }
        });
    },
    generateConnectFix(id) {
      let self = this;
      this.isLoading = true;
      var request = {
        institution_id: id,
      };
      api
        .generateConnectFix(request)
        .then((response) => {
          if (response.code == 200) {
            const finicityConnectUrl = response.data.link;
            window.finicityConnect.launch(finicityConnectUrl, {
              selector: "#connect-container",
              overlay: "rgba(255,255,255, 0)",
              success: function (data) {
                console.log("Yay! We got data", data);
                if (data.code == 200) {
                  //now store the details at canpay end
                  self.storeBankDetails();
                }
              },
              cancel: function () {
                self.isLoading = false;
                console.log("The user cancelled the iframe");
              },
              error: function (err) {
                console.log(err);
                self.isLoading = false;
                console.error(
                  "Some runtime error was generated during Finicity Connect",
                  err
                );
              },
              loaded: function () {
                self.isLoading = false;
                console.log(
                  "This gets called only once after the iframe has finished loading"
                );
              },
              route: function (event) {
                self.isLoading = false;
                console.log(
                  "This is called as the user progresses through Connect"
                );
              },
              user: function (event) {
                if (event.data.errorCode) {
                  console.log(event.data.data.institutionId);
                  setTimeout(() => {
                    window.finicityConnect.destroy();
                    //if error code is present then call the connect fix api
                    self.generateConnectFix(event.data.data.institutionId);
                  }, 2000);
                }
              },
            });
          } else {
            self.isLoading = false;
          }
        })
        .catch((err) => {
          self.isLoading = false;
          console.log(err);
        });
    },
    
    showModal(modal) {
      this.$refs[modal].show();
    },
    hideModal(modal) {
      this.$refs[modal].hide();
    },
    hideUpdateModal(){
      this.hideModal('delay-in-refresh-balance');
    },
    refreshBalance() {
      var self = this;
      self.$refs["refresh-balance-modal"].hide();
      self.isLoading = true;
      api
        .refreshAccountBalance()
        .then((response) => {
          if(response.data && response.data.code == 594){
            self.error_text = response.data.message
            self.showModal('refresh-error-message')
          }else{
            self.getBankAccountList();
            if(response.data && response.data.code == 599){
              // dont show the success popup
            }else{
              self.showModal("updated-purchase-power");
            }

          }
        })
        .catch(function (error) {
          if(error.response.data.code == 597){
            self.generateConnectFix(
              error.response.data.data.institutionId
            );
          }else if(error.response.data.code == 599){
            self.showModal('delay-in-refresh-balance')
          }
          self.isLoading = false;
        });
    },
    disableBank() {
      var self = this;
      self.$refs["confirm-modal"].hide();
      var request = {
        id: self.account_id,
      };
      self.isLoading = true;
      api
        .disableBankAccount(request)
        .then((response) => {
          self.$refs["disable-bank-success-modal"].show();
          self.getBankAccountList();
        })
        .catch(function (error) {
          self.isLoading = false;
        });
    },
    confirmDelete(account){
      var self = this;
      self.account_id = account.id;
      self.$refs["confirm-modal"].show();
    },
    closeRemoveBankModal(){
      var self = this;
      self.$refs["disable-bank-success-modal"].hide();
    },
    checkRealAccNo(){
      var self = this;
      self.$refs.RealAccountNumberModal.checkForRealAccountNumber(1,self.bank_id);
    },
    getdata() {
      let ref = db
        .collection("users")
        .doc(String(String(this.currentUser.user_id)));
      ref.get().then((snapshot) => {
        if (snapshot.exists) {
          this.users = snapshot.data();
          const containsKey = (obj, key) => Object.keys(obj).includes(key);
          const mxActionNeeded = containsKey(this.users,"mx_action_needed");
          const mxNonActionalStatus = containsKey(this.users, "mx_non_actionable_status");
          const mxNonActionableStatusResolved = containsKey(this.users, "mx_non_actionable_status_resolved");
          if(mxActionNeeded == true){
            if(this.users.mx_action_needed && this.users.mx_action_needed.mx_user_action_needed == 1){
                this.problematic_account_details.bank_name = this.users.mx_action_needed.bank_name;
                this.problematic_account_details.account_no = this.users.mx_action_needed.account_no;
                this.problematic_account_details.active_bank_id = this.users.mx_action_needed.active_bank_id;
                this.show_problematic_modal = true;
                setTimeout(() => {
                  this.hideModal("updated-purchase-power");
                },100);
                this.$refs.ProblematicAccountModal.showModal();
            }
          }

            if(mxNonActionalStatus == true){
              if(this.users.mx_non_actionable_status && this.users.mx_non_actionable_status.mx_non_user_actionable_status_detected == 1){
                  this.$refs.NonActionalActionStatusModal.showModal();
                  this.setdata("mx_non_actionable_status");
              }
            }

              if(mxNonActionableStatusResolved == true){
                if(this.users.mx_non_actionable_status_resolved == 1){
                    this.$refs.NonActionalResolveModal.showModal();
                    this.setdata("mx_non_actionable_status_resolved");
                }
              }

        }

        ref.onSnapshot((convo) => {
          this.minimumSpendPoints = 0

          let source = convo.metadata.hasPendingWrites ? "Local" : "Server";
          // TODO: add messages to store
          let ref = db
            .collection("users")
            .doc(String(this.currentUser.user_id));
          ref.get().then((snapshot) => {
            if (snapshot.exists) {
              this.users = snapshot.data();
              this.users = snapshot.data();
              const containsKey = (obj, key) => Object.keys(obj).includes(key);
              const mxActionNeeded = containsKey(this.users,"mx_action_needed");
              const mxNonActionalStatus = containsKey(this.users, "mx_non_actionable_status");
              const mxNonActionableStatusResolved = containsKey(this.users, "mx_non_actionable_status_resolved");
              if(mxActionNeeded == true){
                if(this.users.mx_action_needed && this.users.mx_action_needed.mx_user_action_needed == 1){
                    this.problematic_account_details.bank_name = this.users.mx_action_needed.bank_name;
                    this.problematic_account_details.account_no = this.users.mx_action_needed.account_no;
                    this.problematic_account_details.active_bank_id = this.users.mx_action_needed.active_bank_id;
                    this.show_problematic_modal = true;
                    setTimeout(() => {
                      this.hideModal("updated-purchase-power");
                    },100);
                    this.$refs.ProblematicAccountModal.showModal();
                }
              }
            if(mxNonActionalStatus == true){
              if(this.users.mx_non_actionable_status && this.users.mx_non_actionable_status.mx_non_user_actionable_status_detected == 1){
                  this.$refs.NonActionalActionStatusModal.showModal();
                  this.hideModal("updated-purchase-power");
                  this.setdata("mx_non_actionable_status");
              }
            }

              if(mxNonActionableStatusResolved == true){
                if(this.users.mx_non_actionable_status_resolved == 1){
                    this.$refs.NonActionalResolveModal.showModal();
                    this.setdata("mx_non_actionable_status_resolved");

                }
              }

            }
          });
        });
      });
    },
    setdata(type) {
      let data = {}
      if(type == "mx_non_actionable_status_resolved"){
        data = {
          mx_non_actionable_status_resolved: null
        }
      }
      if(type == "mx_non_actionable_status"){
        data = {
          mx_non_actionable_status: null
        }
      }
      var self = this;
      var washingtonRef = db
        .collection("users")
        .doc(String(this.currentUser.user_id));
      // Set the "capital" field of the city 'DC'
      return washingtonRef
        .update(data)
        .then(function () {
          console.log("Document successfully updated!");
        })
        .catch(function (error) {
          // The document probably doesn't exist.
          console.error("Error updating document: ", error);
        });
    },
  },
};
</script>
<style lang="scss">
#pay-modal-center___BV_modal_content_ {
  border-radius: 10px;
  margin: 10px;
  background-color: #ffffff;
}
#pay-modal-center___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#refresh-balance-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#disable-bank-success-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#confirm-modal___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
.bank-list::-webkit-scrollbar {
  width: 10px;
}

.bank-list::-webkit-scrollbar-track {
  background: #ffffff; 
}
 
.bank-list::-webkit-scrollbar-thumb {
  background: #dfdfdf; 
}

.bank-list::-webkit-scrollbar-thumb:hover {
  background: #dfdfdf; 
}

.accordion-item {
  border: 1px solid #ccc;
  border-radius: 5px;
  margin-bottom: 10px;
  margin-top: 10px;
}

.accordion-header {
  background-color: #f0f0f0;
  padding: 10px;
  cursor: pointer;
  border-bottom: 1px solid #ccc;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.active-accordion {
  background-color: #149240;
  color: #ffffff; /* Added line to set text color to white */
}

.dark-accordion {
  background-color: #000000;
  color: #ffffff; /* Added line to set text color to white */
}

.accordion-header h3 {
  margin: 0;
  font-weight: bold;
}

.accordion-content {
  padding: 10px;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

.rotate-arrow {
  transition: transform 0.3s ease;
}

.rotate-arrow.rotate {
  transform: rotate(180deg);
}

ul {
  padding-left: 20px;
  margin: 0;
}

li {
  list-style-type: none;
}

.cancel-btn {
  width: 140px;
  margin-right: 10px;
  height: 50px !important;
  background-color: #000 !important;
  border-color: #000 !important;
  display: inline-block;
  vertical-align: top;
}

#delay-in-refresh-balance___BV_modal_content_{
  background-color:#ffffff;
}
#delay-in-refresh-balance___BV_modal_body_{
  background-color:#ffffff;
  border-radius:10px;
}
.black-button-ok{
 padding:8px 15px;
 width:100%;
 border-radius:10px;
 border:none;
 font-family: 'Montserrat';
 background-color: #000000;
 color:#ffffff;
 font-weight:bolder;
 margin-top:10px;
}
.green-button-fix{
padding:8px 15px;
 width:100%;
 border-radius:10px;
 border:none;
 font-family: 'Montserrat';
 background-color: #149240;
 color:#ffffff;
 font-weight:bolder;
 margin-top:10px;
}
#updated-purchase-power___BV_modal_content_{
  background-color:#ffffff;
}
#updated-purchase-power___BV_modal_body_{
  background-color:#ffffff;
  border-radius:10px;
}
#bank-link-success___BV_modal_content_{
  background-color:#ffffff;
}
#bank-link-success___BV_modal_body_{
  background-color:#ffffff;
  border-radius:10px;
}
#bank-link-error___BV_modal_content_{
  background-color:#ffffff;
}
#bank-link-error___BV_modal_body_{
  background-color:#ffffff;
  border-radius:10px;
}
#error-message-mx___BV_modal_content_{
  background-color:#ffffff;
}
#error-message-mx___BV_modal_body_{
  background-color:#ffffff;
  border-radius:10px;
}
#refresh-error-message___BV_modal_content_{
  background-color:#ffffff;
}
#refresh-error-message___BV_modal_body_{
  background-color:#ffffff;
  border-radius:10px;
}
.repair-font{
    border:none;
    width:100%;
    padding:4px;
    border-radius:5px;
    background-color:#149240;
    color:#ffffff;
}
</style>