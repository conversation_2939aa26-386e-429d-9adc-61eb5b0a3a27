.privacypolicy{
    margin-top: 10px !important;
    margin: auto;
    background-color:white;
    border-radius:8px;
}

.title{
    margin-top:30px;
    text-align: left;
}

.h6-text{
    text-align: left;
    margin-top:10px;
}
.privacypolicyheading {
    font-weight: 400;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: $cp-black;
    font-size: 15px;
    margin-left: -14px;
    margin-top: 33px;
    float: left;
}

.onboadring-consumer-privacy-notice-fact {

    font-size:1.6rem;
   background-color:#4497dc; 
   border: 0.15rem solid #ededed; 
   text-align:left;
    padding: 0.9rem;
    color: white; 
     float:left;
  }
  
  .onboadring-consumer-privacy-notice-contain {
  border: 0.1rem solid #ededed;
   text-align:left;
    float: left;
    padding: 0.9rem;
    font-size:1.5rem;
  
  }
  
  .onboadring-consumer-privacy-notice-sub-header {
   font-size:1.0rem;
   background-color:#3d95ce; 
   border: 0.15rem solid #ededed; 
   text-align:left;
    padding: 0.9rem;
    color: white; 
     float:left;
  }
  
  .onboadring-consumer-privacy-notice-sub-contain {
    border: 0.1rem solid #d8d7d2;
   text-align:left;
    float: left;
    padding: 0.9rem;
    font-size:1.0rem;
  }
  .color-onboarding-privacy-note-header-color{
    background-color: #eeece1;
      font-size:0.9rem;
  }
  .color-onboarding-privacy-note-header-color-two{
    background-color: #f2f2f2;
      font-size:0.9rem;
  }
  
@media only screen and (max-width: 900px) and (min-width: 360px){
    .privacypolicyheading{
        margin-left: -9px;
        margin-top: 12px;
    }
}
@media only screen and (min-width: 320px){
    .privacypolicyheading{
        margin-left: -9px;
        margin-top: 12px;
    }
}