<template>
  
  <div>
    <div>
      <CanPayLoader/>
    </div>
  </div>
</template>

<script>
import api from "../api/redirect.js";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"

export default {
  components:{
      CanPayLoader
  },
  created() {
    const code = this.$route.params.code;
    this.getRedirectUrl(code);
  },
  mounted() {
    this.$root.$emit("changeWhiteBackground", [false, false, ""]);
    var app = document.getElementById("app");
    if (app) {
      app.style.setProperty("background-color", "#ffffff");
    }
    var element = document.getElementsByClassName("content-wrap");
    if (element[0]) {
      element[0].style.setProperty("background-color", "#ffffff");
      element[0].style.height = "114vh";
      if(window.innerWidth>1200){
        element[0].style.height = "121vh";
      }
    }
  },
  methods: {
    getRedirectUrl(code) {
      api
        .getRedirectUrl(code)
        .then((response) => {
          if (response.code == 200) {
            window.location.href = response.data;
          } else {
            alert('Invalid or expired short URL');
            this.$router.push('/');
          }
        })
        .catch((error) => {
          alert('Invalid or expired short URL');
          this.$router.push('/');
        });
    }
  }
};
</script>
