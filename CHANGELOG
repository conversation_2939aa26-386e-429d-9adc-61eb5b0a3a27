# Release v11.5.0 on 2025-07-28 

    ~ Enhancement: Post Transactipon Tip screen designed
# Release v11.4.0 on 2025-07-22 

    ~ Fix: Edit icon alignment issue
    ~ Enhancement: Only one email icon for sending email to multiple contacts added
    ~ Enhancement: NPM has been updated
# Release v11.3.3 on 2025-07-15 

    ~ Enhancement: Add back arrow to registration page for improved navigation
# Release v11.3.2 on 2025-07-10 

    ~ Enhancement: CanPay Crew - App Updates
# Release v11.3.1 on 2025-07-08 

    ~ Enahancement: Updates in the Merchant Page
# Release v11.3.0 on 2025-07-03 

    ~ Enhancement: Enhance Admin Petitions Page with merchant link, email option, and contact info update
# Release v11.2.0 on 2025-06-24 

    ~ Enhancement: Implemented changes based on petition feedback for CanpayCrew
# Release v11.1.0 on 2025-06-23 

    ~ Enhancement: updated CanPay Crew Merchant Page with UI improvements and content changes
# Release v11.0.2 on 2025-06-10 

    ~ Enhancement:  Schedule a meeting with CanPay CEO button made active
# Release v11.0.1 on 2025-06-06 

    ~ Fix: Spelling corret for Petition
# Release v11.0.0 on 2025-06-04 

    ~ Enhancement: Canpay Crew implementation
# Release v10.18.1 on 2025-06-02 

    ~ Enhancement: pay page merchant related changes
# Release v10.18.0 on 2025-05-28 

    ~ Enhancement: Add tip on transaction success and transaction history pages.
# Release v10.17.7 on 2025-05-26 

    ~ Enhancement: NPM has been updated
# Release v10.17.6 on 2025-05-20 

    ~ Enhancement: NPM has been updated
# Release v10.17.5 on 2025-05-19 

    ~ Fix: Correct .con and .comm emails to .com
# Release v10.17.4 on 2025-05-13 

    ~ Enhancement: NPM has been updated
# Release v10.17.3 on 2025-05-05 

    ~ Enhancement: NPM has been updated
# Release v10.17.2 on 2025-04-28 

    ~ Enhancement: NPM has been updated
# Release v10.17.1 on 2025-04-21 

    ~ Enhancement: NPM has been updated
# Release v10.17.0 on 2025-04-14 

    ~ Enhancement: NPM has been updated
    ~ Enhancement: Remote pay transaction using points.
# Release v10.16.2 on 2025-04-08 

    ~ Fix: Enforce 5-digit format for ZIP code validation
# Release v10.16.1 on 2025-04-07 

    ~ Enhancement: NPM has been updated
# Release v10.16.0 on 2025-04-02 

    ~ Enahancemnt: NPM has been updated
    ~ Fix: NuggMD card mobile padding and alignment issues, update text content and link
# Release v10.15.10 on 2025-03-24 

    ~ Fix: Re-Pay option for insufficient funds
    ~ Enhancement: NPM has been updated
# Release v10.15.9 on 2025-03-18 

    ~ Enhancement: Regular Updates option added to merge request template
# Release v10.15.8 on 2025-03-11 

    ~ Enhancement: NPM has been updated
# Release v10.15.7 on 2025-03-04 

    ~ Enhancement: NPM has been updated
# Release v10.15.6 on 2025-02-24 

    ~ Enhancement: NPM has been updated
# Release v10.15.5 on 2025-02-20 

    ~ Enhancement: Nuggmd Link Change
# Release v10.15.4 on 2025-02-17 

    ~ Enhancement: NPM has been updated
# Release v10.15.3 on 2025-02-10 

    ~ Enhancement: NPM has been updated
# Release v10.15.2 on 2025-02-03 

    ~ Enhancement: NPM has been updated
# Release v10.15.1 on 2025-01-24 

    ~ FIx: Registration time manual bank link
# Release v10.15.0 on 2025-01-22 

    ~ Enhancement: A new user type, 'Lite User,' has been introduced, enabling payments using brand points.
# Release v10.14.3 on 2025-01-20 

    ~ Enhancement: NPM has been updated
# Release v10.14.2 on 2025-01-14 

    ~ Enhancement: NPM has been updated
# Release v10.14.1 on 2025-01-07 

    ~ FIx: Consumer Wheel Reward Name fix
# Release v10.14.0 on 2025-01-06 

    ~ Enhancement: NPM has been updated
    ~ Fix: Design change for pay page
# Release v10.13.0 on 2024-12-31 

    Revert "~ Enhancement: Nuggmd Wheel december only special"
# Release v10.12.0 on 2024-12-26 

    ~ Enhancement: Learn Brand Implementation
# Release v10.11.1 on 2024-12-17 

    ~ Enhancement: NPM has been updated
# Release v10.11.0 on 2024-12-10 

    ~ Enhancement: Nuggmd Wheel december only special
# Release v10.10.1 on 2024-12-10 

    ~ Enhancement: NPM has been updated
# Release v10.10.0 on 2024-12-09 

    ~ Enhancement: Changes for UI for store Locator
    ~ Fix: Label text align center
    ~ Fix: Resolve issue preventing resubmission for new phone verification
# Release v10.9.0 on 2024-12-02 

    ~ Enhancement: NPM has been updated
    ~ Fix: Relink popup not displaying
# Release v10.8.0 on 2024-11-18 

    ~ Enhancement: NPM has been updated
    ~ Fix: Replaced old favicon with a new one
    ~ Fix: During the repayment time, consumers using the manual link are unable to see their bank list.
# Release v10.7.2 on 2024-11-11 

    ~ Enhancement: NPM has been updated
# Release v10.7.1 on 2024-11-07 

    ~ Enhancement: Changed nuggMD website link
# Release v10.7.0 on 2024-11-05 

    ~ Fix: Changes the store locator details to popup, canpay icon bounce, fixed to consumer location and not available text consistent throught the app
# Release v10.6.1 on 2024-11-04 

    ~ Enhancement: NPM has been updated
# Release v10.6.0 on 2024-10-29 

    ~ Enhancement: NPM has been updated
    ~ Fix: Bank link accordian design
# Release v10.5.8 on 2024-10-23 

    ~ Fix: nuggmd copied fix
# Release v10.5.7 on 2024-10-23 

    ~ Fix: Added copied on click for coupon code
# Release v10.5.6 on 2024-10-21 

    ~ Enhancement: NPM has been updated
    ~ Fix: Sometimes the transaction success popup does not show.
# Release v10.5.5 on 2024-10-17 

    ~ Fix: Made changes for mobile number
# Release v10.5.4 on 2024-10-17 

    ~ Fix: Changes in store locator ui
# Release v10.5.3 on 2024-10-15 

    ~ Fix: Adjusting the reward wheel arrow button
# Release v10.5.2 on 2024-10-09 

    ~ Fix: The points don't work when the consumer clicks on the slider.
# Release v10.5.1 on 2024-10-08 

    ~ Fix: Current location marker icon size in google map
# Release v10.5.0 on 2024-10-07 

    ~ Enhancement: NPM has been updated
    ~ Enhancement: Added Non actional MX detect and success modal
    ~ Fix: Update Select Merchant design for improved UI
# Release v10.4.4 on 2024-10-05 

    ~ Fix: NiggMD Border
# Release v10.4.3 on 2024-10-04 

    ~ Fix: Recreate NuggMD wheel and removing mobile vieport dependency
    ~ Enhancement: API called to log the consumer visit to Nuggmd Site from member benifits
# Release v10.4.2 on 2024-10-02 

    ~ Fix: NuggMD changes for memberbenfit and nuggmd iframe and iphone screen
# Release v10.4.1 on 2024-10-01 

    ~ Fix: Restore the previous behavior to load the listing page with refreshing when navigating back from the details page.
# Release v10.4.0 on 2024-10-01 

    ~ Enhancement: NPM has been updated
    ~ Enhancment: Optimized store data retention on back navigation and enhanced map marker interactions with seamless store card scrolling and highlighting
# Release v10.3.0 on 2024-09-30 

    ~ Enhancement: Added reward wheel modal for nuggMD
    ~ Enhancement: Added Intermediate linking modal
# Release v10.2.2 on 2024-09-26 

    ~ Fix: Images not displaying on Canpay points page
# Release v10.2.1 on 2024-09-25 

    ~ Fix: Data not displaying on Select Merchant page
# Release v10.2.0 on 2024-09-24 

    ~ Fix: Merchant Points Feedback
# Release v10.1.3 on 2024-09-23 

    ~ Enhancement: NPM has been updated
# Release v10.1.2 on 2024-09-17 

    ~ Enhancement: NPM has been updated
# Release v10.1.1 on 2024-09-12 

    ~ Fix: Media screen issue
# Release v10.1.0 on 2024-09-12 

    ~ Fix: Change the message when store time is not available
    ~ Fix: Menu icon position and alignment
# Release v10.0.0 on 2024-09-11 

    ~ Enhancement: New changes on store locator
    ~ Enhancement: Merchant Points Program module integrated
# Release v9.9.0 on 2024-09-10 

    ~ Enhancement: NPM has been updated
    ~ Enahancement: Change the text for terms and conditions in signup page
# Release v9.8.1 on 2024-09-03 

    ~ Enhancement: NPM has been updated
# Release v9.8.0 on 2024-09-02 

    ~ Enhancement: Error Modal for One Time Refresh
# Release v9.7.0 on 2024-08-28 

    ~ Enhancement: NPM has been updated
    ~ Fix: Adding condition for secret fetch only in Production and staging environment
# Release v9.6.1 on 2024-08-27 

    Revert "~ Fix: Adding condition for secret fetch only in Production and staging environment"
# Release v9.6.0 on 2024-08-27 

    ~ Enhancement: Added Problematic bank modal and icon to bank listing page
# Release v9.5.3 on 2024-08-27 

    ~ Fix: Adding condition for secret fetch only in Production and staging environment
# Release v9.5.2 on 2024-08-26 

    ~ Fix: AWS SDK installed
# Release v9.5.1 on 2024-08-26 

    ~ Fix: Load .env.local first
# Release v9.5.0 on 2024-08-26 

    ~ Enhancement: fetch-secrets.js script to update .env.local without overwriting comments
# Release v9.4.1 on 2024-08-20 

    ~ Enhancement: NPM has been updated
# Release v9.4.0 on 2024-08-19 

    ~ Enhancement: Synchronous mx process
# Release v9.3.0 on 2024-08-16 

    ~ Fix: Bank delink popup text message
# Release v9.2.1 on 2024-08-13 

    ~ Enhancement: NPM has been updated
# Release v9.2.0 on 2024-08-12 

    ~ Enahancement: Added term and condition for text message
# Release v9.1.0 on 2024-08-05 

    ~ Enhancement: Switch primary bank account from bank list success page during update bank, onboarding or registration process
# Release v9.0.1 on 2024-08-01 

    ~ Fix: Enable make another payment modal
# Release v9.0.0 on 2024-07-23 

    ~ Enhancement: Added CanPay Loader Animation
# Release v8.6.9 on 2024-07-22 

    ~ Enhancement: NPM has been updated
# Release v8.6.8 on 2024-07-18 

    ~ Fix: Change the format of the rewards amount.
# Release v8.6.7 on 2024-07-15 

    ~ Enhancement: NPM has been updated
# Release v8.6.6 on 2024-07-04 

    ~ Fix: Change the text format of Pending Rewards Points.
# Release v8.6.5 on 2024-07-01 

    ~ Enhancement: NPM has been updated
# Release v8.6.4 on 2024-06-25 

    ~ Enhancement: NPM has been updated
# Release v8.6.3 on 2024-06-24 

    ~ Fix: Changed the text message
# Release v8.6.2 on 2024-06-18 

    ~ Enhancement: NPM has been updated
# Release v8.6.1 on 2024-06-11 

    ~ Enhancement: NPM has been updated
# Release v8.6.0 on 2024-06-04 

    ~ Enhancement: NPM has been updated
    ~ Fix: QR code generation fails if consumer does not have active bank account
# Release v8.5.0 on 2024-05-29 

    ~ Enhancement: NPM has been updated
# Release v8.4.1 on 2024-05-27 

    ~ Fix: If Akoya returns an error then retry using next banking solution.
# Release v8.4.0 on 2024-05-24 

    ~ Fix: If transaction declines for no active bank account, it will show option to relink bank
# Release v8.3.5 on 2024-05-21 

    ~ Fix: Check Transaction cooldown period before generating QRcode
# Release v8.3.4 on 2024-05-15 

    ~ Fix: Mx bank link error message changed as per client request
# Release v8.3.3 on 2024-05-08 

    ~ Fix: No need to check finicity bank account state
    ~ Fix: Log Akoya URL return params on registration
# Release v8.3.1 on 2024-05-03 

    ~ Enhancement: Log Akoya URL return params
# Release v8.3.0 on 2024-05-01 

    ~ Enhancement: Check If Current manual bank is available for direct link or not and showing different bank link button
    ~ Enhancement: Skip options will not be available for Finicity link consumers
# Release v8.2.0 on 2024-04-26 

    ~ Enhancement: If consumers already use the skip option, no more options will be available.
# Release v8.1.1 on 2024-04-22 

    ~ Enhancement: NPM has been updated
# Release v8.1.0 on 2024-04-21 

    ~ Enhancement: Skip finicity relink modal
# Release v8.0.2 on 2024-04-17 

    ~ Fix: Made changes in the modify section also updatetransaction
    ~ Fix: Message changes for modal with no active bank for consumer
# Release v8.0.1 on 2024-04-16 

    ~ Fix: Message changed for finicity delink.
# Release v8.0.0 on 2024-04-14 

    ~ Enhancement: Sposor implementation
# Release v7.2.0 on 2024-04-08 

    ~ Enhancement: NPM has been updated
    ~ Enhancement: Show the loser segment in the reward Points history page
# Release v7.1.1 on 2024-04-04 

    ~ Fix: Finicity to other bank link with special message
# Release v7.1.0 on 2024-04-03 

    ~ Fix: Tip button css fixed for banklisting popup
    ~ Enhancement: Finicity to other bank link with special message
# Release v7.0.0 on 2024-04-01 

    ~ Fix: Return Bank UI Updated
    ~ Enhancement: If the consumer is challenged, then disable transaction for that user
    ~ Enhancement: Finicity to other bank link time skip ACH call
    ~ Enhancement: Bank Listing page UI modified to show other banks with the active one
# Release v6.3.0 on 2024-03-28 

    ~ Enhancement: Bank report under banking section
# Release v6.2.4 on 2024-03-26 

    ~ Enhancement: NPM has been updated
# Release v6.2.3 on 2024-03-18 

    ~ Enhancement: NPM has been updated
# Release v6.2.2 on 2024-03-14 

    ~ Fix: Prevent unauthorized users from accessing the banklinking URL.
# Release v6.2.1 on 2024-03-05 

    ~ Enhancement: NPM has been updated
    ~ Enhancement: Readme preapred as per proposed format
# Release v6.2.0 on 2024-02-26 

    ~ Fix: Added load more button instead of scroll reloading.
    ~ Enhancement: NPM has been updated
# Release v6.1.0 on 2024-02-20 

    ~ Enhancement: Retry the bank link using another banking solution
# Release v6.0.5 on 2024-02-19 

    ~ Enhancement: NPM has been updated
    ~ Enhancement: Add Banking Alert in Free Spin Page
# Release v6.0.4 on 2024-02-14 

    Revert "~ Enhancement: Show the loser segment in the reward Points history page"
# Release v6.0.3 on 2024-02-13 

    ~ Enhancement: Show the loser segment in the reward Points history page
# Release v6.0.2 on 2024-02-06 

    ~ Fix: Password show/hide
    ~ Fix: The Bank account was delinked due to Akoya refresh token is invalid
# Release v6.0.1 on 2024-02-05 

    ~ Enhancement: Only Checking or Savings accounts allowed
# Release v6.0.0 on 2024-02-01 

    ~ Enhancement: MX Implementation
# Release v5.2.2 on 2024-01-31 

    ~ Fix: Consumer App Signup from Remotepay Widget
# Release v5.2.1 on 2024-01-30 

    ~ Enhancement: NPM has been updated
# Release v5.2.0 on 2024-01-29 

    ~ Fix: Loader stop after akoya closed
    ~ Enhancement: Remove primary account section from bank change page
# Release v5.1.0 on 2024-01-24 

    ~ Enhancement: Design fix for bank change page
    ~ Fix: Registration time no need to show terms and conditions modal
    ~ Enhancement: update log for bank search with event name and ID
# Release v5.0.3 on 2024-01-23 

    ~ Fix: Prevent API call on Finicity exit button click
# Release v5.0.2 on 2024-01-22 

    ~ Fix: Change Terms and Conditions
# Release v5.0.1 on 2024-01-22 

    ~ Enhancement: Change Terms and Conditions
# Release v5.0.0 on 2024-01-22 

    ~ Enhancement: Akoya Implementation
# Release v4.16.5 on 2023-12-11 

    ~ Enhancement: NPM has been updated
# Release v4.16.4 on 2023-11-28 

    ~ Fix: Suspended pop up theme fixed
# Release v4.16.3 on 2023-11-27 

    ~ Enhancement: NPM has been updated
# Release v4.16.2 on 2023-11-09 

    ~ Fix: Prevent multiple time login button click
# Release v4.16.1 on 2023-11-06 

    ~ Enhancement: NPM has been updated
# Release v4.16.0 on 2023-10-25 

    ~ Enhancement: Microbilt Implementation
# Release v4.15.12 on 2023-10-09 

    ~ Enhancement: NPM has been updated
    ~ Enahncement:readme file updated
# Release v4.15.11 on 2023-09-25 

    ~ Enhancement: NPM has been updated
# Release v4.15.10 on 2023-09-18 

    ~ Enhancement: NPM has been updated
# Release v4.15.9 on 2023-09-13 

    ~ Fix: Show bank posting amount in return represent popup
# Release v4.15.8 on 2023-09-11 

    ~ Enhancement: NPM has been updated
# Release v4.15.7 on 2023-09-04 

    ~ Enhancement: NPM has been updated
# Release v4.15.6 on 2023-08-21 

    ~ Fix: Pending Reward point text should show only for Merchant Transactions
# Release v4.15.5 on 2023-08-16 

    ~ Enhancement: NPM has been updated
    ~ Fix: The delinked bank cannot be activated
# Release v4.15.4 on 2023-08-07 

    ~ Enhancement: NPM has been updated
# Release v4.15.3 on 2023-07-25 

    ~ Fix: Redirect reward wheels in ascending order
# Release v4.15.2 on 2023-07-24 

    ~ Enhancement: NPM has been updated
# Release v4.15.1 on 2023-07-18 

    ~ Fix: If a Consumer has R03, R04 or similar returns where bank change is mandatory, in those scenarios they can select to either Manual link or Direct link their account
    ~ Fix: Spin count update on wheel stop
# Release v4.15.0 on 2023-07-17 

    ~ Fix: Change winning text static text to dynamic
    ~ Enhancement: NPM has been updated
# Release v4.14.2 on 2023-07-10 

    ~ Enhancement: NPM has been updated
# Release v4.14.1 on 2023-07-03 

    ~ Enhancement: NPM has been updated
# Release v4.14.0 on 2023-06-28 

    ~ Enhancement: Consumer multiple payment alert test change
# Release v4.13.7 on 2023-06-27 

    ~ Enhancement: NPM has been updated
# Release v4.13.6 on 2023-06-24 

    ~ FIx: Wheel Image bottom scroll issue fixed
# Release v4.13.5 on 2023-06-23 

    ~ Fix: Reward Wheel Background image load issue fixed
# Release v4.13.4 on 2023-06-23 

    ~ Fix: Menu background color fixed
# Release v4.13.3 on 2023-06-23 

    ~ Fix: Scroll issue in Wheel page fixed
# Release v4.13.2 on 2023-06-19 

    ~ Enhancement: NPM has been updated
    ~ Enhancement: Font family change
# Release v4.13.1 on 2023-06-12 

    ~ Fix: Pending transaction search
# Release v4.13.0 on 2023-06-07 

    ~ Enhancement: NPM has been updated
    ~ Enhancement: Force refresh app button
# Release v4.12.3 on 2023-06-05 

    ~ Fix: Show error modal wheel API throw exception & Remove reward wheel URL params and use vuex store for manage data
# Release v4.12.2 on 2023-05-27 

    ~ Fix: Wheel page spacing and font related design issues
# Release v4.12.1 on 2023-05-26 

    ~ Fix: Wheel background image and responsiveness
# Release v4.12.0 on 2023-05-26 

    ~ Enhancement: Reward wheel dynamically UI change
# Release v4.11.5 on 2023-05-25 

    ~ Enhancement: Overlay added in app refresh button to force the consumer to reload the app.
# Release v4.11.4 on 2023-05-23 

    ~ Enhancement: Spin event functionality change
# Release v4.11.3 on 2023-05-22 

    ~ Enhancement: RemotePay text changes
    ~ Enhancement: NPM has been updated
# Release v4.11.2 on 2023-05-16 

    ~ Fix: If daily spins available wheel should not redirect to old wheel
# Release v4.11.1 on 2023-05-15 

    ~ Enhancement: NPM has been updated
# Release v4.11.0 on 2023-05-09 

    ~ Fix: If consumer account number is less than 5 digit then show a pop up to consumer and let them update only the account number.
    ~ Fix: Wheel not found redirect to last three spin result page
# Release v4.10.1 on 2023-05-08 

    ~ Enhancement: NPM has been updated
# Release v4.10.0 on 2023-05-03 

    ~ Enhancemnet: Transaction cooldown time introduced to prevent duplicate transactions.
# Release v4.9.8 on 2023-05-02 

    ~ Enhancement: Add commas to all point and dollar values & Redirect to last spin when click on spin button
# Release v4.9.7 on 2023-05-01 

    ~ Enhancement: NPM has been updated
# Release v4.9.6 on 2023-04-25 

    ~ Enhancement: NPM has been updated
# Release v4.9.5 on 2023-04-24 

    ~ Enhacement: Slider UI & Segment should stop at middle point after stop spinning & Add information modal for canpay reward program
# Release v4.9.4 on 2023-04-11 

    ~ FIx: Jackpot value display upto 9 digit
# Release v4.9.3 on 2023-04-10 

    ~ Enhancement: NPM has been updated
# Release v4.9.2 on 2023-04-06 

    ~ Enhancement: Display last spin date
# Release v4.9.1 on 2023-04-05 

    ~ Fix: Abort pending axios request on internet connection lost
# Release v4.9.0 on 2023-04-04 

    ~ Enhancement: Display a message on the Reward Wheel Spin page indicating that the internet connection has been lost
# Release v4.8.2 on 2023-04-04 

    ~ Fix: Payment Finalized
# Release v4.8.1 on 2023-04-03 

    ~ Enhancement: NPM has been updated
# Release v4.8.0 on 2023-03-28 

    ~ Fix: Negative amount display on transaction success page, Reward wheel terms and condition page
# Release v4.7.1 on 2023-03-27 

    ~ Enhancement: NPM has been updated
# Release v4.7.0 on 2023-03-23 

    ~ Fix: Reduce googleplace details api calls
# Release v4.6.1 on 2023-03-21 

    ~ Enhancement: Success page free spin award button & Change text of transactions status & Pending transaction show bank paid amount
# Release v4.6.0 on 2023-03-20 

    ~ Enhancement: Invitation flow change
    ~ Enhancement: NPM has been updated
# Release v4.5.0 on 2023-03-17 

    ~ Enhancement: Call invitation API after transaction success & Call spin  count API after accept invitation
# Release v4.4.1 on 2023-03-15 

    ~ Fix: Remove All Locations part from locate retailer and participating merchant page
# Release v4.4.0 on 2023-03-15 

    ~ Fix: Google Place API unique configuration and google auto complete search fix with registration and update address
    ~ Enhancement: Reward history infinite scrolling & Slider range accelaration & Points comma seprarator & Beta tag added to pay and wheel page
# Release v4.3.2 on 2023-03-13 

    ~ Enhancement: NPM has been updated
# Release v4.3.1 on 2023-03-07 

    ~ Fix: lottery previous day winner details, pay page in a single screen and transation history show unpaid
# Release v4.3.0 on 2023-03-06 

    ~ FIx: UI related changes and Reward wheel functional issues
    ~ Enhancement: Increment decrement continiously on hold, fix slider smoothness and show slider if have minimum redeemable reward points
    ~ Fix: jackpot Counter and display 7 digit counter and dynamic text points and point
    ~ FIx: Account edit buttons height UI
    ~ Fix: Wheel image load timing
    ~ Fix: PP update on hold plus minus button besides the slider and QR code expiration popup issue fixed due to click on plus button
    ~ Fix: Canpay points history pending text color
    ~ Fix: Exchange rate notification
    ~ Enhancement: Lottery feature
    ~ Fix: Reward wheel back button issue & Default image show if s3 image deleted
    ~ Enhancement: Add win text to reward history
    ~ Fix: Add reason for loser on reward history
    ~ FIx: Jackpot counter not showing
    ~ Fix: backdrop enable and disable
    ~ FIx: reward history point
    ~ Fix: lottery instant notification
    ~ Fix: Get map key from ENV
    ~ Enhancement: NPM has been updated
# Release v4.2.3 on 2023-03-02 

    ~ Enhancement: Hide survey form for consumers
# Release v4.2.2 on 2023-02-27 

    ~ Enhancement: NPM has been updated
# Release v4.2.1 on 2023-02-22 

    ~ Enhancement: After last spin redirect to home page
# Release v4.2.0 on 2023-02-16 

    ~ Fix: Client feedback changes on reward wheel related text changes
# Release v4.1.0 on 2023-02-14 

    ~ Fix: Leave a  tip button removed from the success transaction page
    ~ Enhancement: Admin enable bank link & return
# Release v4.0.2 on 2023-02-12 

    ~ Fix: Axios interceptor issue fixed for Reward Wheel
# Release v4.0.1 on 2023-02-12 

    ~ Fix: Dummy text replaced for Reward Wheel Invitation Popup
# Release v4.0.0 on 2023-02-12 

    ~ Enhancement: Reward Wheel Integration
    ~ Enahncement: Pay with Points functionality implemented
    ~ Enhancement: Canpay Points History Added
# Release v3.4.0 on 2023-02-09 

    ~ Enhancement: Survey form created for consumers
# Release v3.3.7 on 2023-01-23 

    ~ Enhancement: NPM has been updated
# Release v3.3.6 on 2023-01-11 

    ~ FIx: screen break banklisitng and return banklisting
# Release v3.3.5 on 2023-01-03 

    ~ Enhancement: NPM has been updated
# Release v3.3.4 on 2023-01-02 

    ~ Fix: Real account number popup fixed
# Release v3.3.3 on 2022-12-20 

    ~ Enhancement: NPM has been updated
# Release v3.3.2 on 2022-12-15 

    ~ Enhancement: Method changed to post for separate log channel implemented to identify ACH calls
# Release v3.3.1 on 2022-12-15 

    ~ Fix: Change pending transaction fully clear text
# Release v3.3.0 on 2022-12-13 

    ~ Enhancement: Changes made for all banks in delinked state
# Release v3.2.3 on 2022-12-06 

    ~ Enhancement: NPM has been updated
# Release v3.2.2 on 2022-11-15 

    ~ Enhancement: NPM has been updated
# Release v3.2.1 on 2022-11-07 

    ~ Enhancement: NPM has been updated
# Release v3.2.0 on 2022-10-18 

    ~ Bug Fix: Address update from consumers
# Release v3.1.1 on 2022-10-13 

    ~ Fix: Pending Transaction's Expected Clearance Date
# Release v3.1.0 on 2022-10-11 

    ~ Enhancement: Changes made for Return Representment and bank list page
# Release v3.0.4 on 2022-10-01 

    ~ FIx: tip input decimal validation added
# Release v3.0.3 on 2022-09-29 

    ~ Fix: Hide Leave a tip button from transaction success page
# Release v3.0.2 on 2022-09-27 

    ~ Fix: Hide pending transactions when pending amount is 0
# Release v3.0.1 on 2022-09-27 

    ~ Fix: Corporate parent logo show in pending transaction
# Release v3.0.0 on 2022-09-27 

    ~ Enhancement: Consumer Ecommerce Transactions
    ~ Enhancement : Add redirection function on route
    ~ Enhancement: Transaction Modification
# Release v2.27.1 on 2022-09-22 

    ~ Bug Fix: Checking added before generation of QR code for real account number required
# Release v2.27.0 on 2022-09-20 

    ~ Enhancement: NPM has been updated
    ~ Fix: Leave TIP
# Release v2.26.0 on 2022-09-14 

    ~ Enhancement: Coview code snippet added for Intercom
# Release v2.25.0 on 2022-09-13 

    ~ Enhancement: Login unlock email link verification
# Release v2.24.1 on 2022-09-06 

    ~ Fix: Leave a Tip button removed
# Release v2.24.0 on 2022-09-06 

    ~ Enhancement: Separate Email and SMS channel has been introduced.
# Release v2.23.4 on 2022-09-06 

    ~ Enhancement: Consumer deactivation check, Multiple file upload and Leave a tip feature
# Release v2.23.3 on 2022-08-29 

    ~ Enhancement: NPM has been updated
# Release v2.23.2 on 2022-08-24 

    ~ Fix: Verification from email fixed during onboarding
# Release v2.23.1 on 2022-08-16 

    ~ Enhancement: NPM has been updated
# Release v2.23.0 on 2022-08-12 

    ~ Enhancement: Represent Transaction function called after submission of Real Account no. and routing no.
# Release v2.22.3 on 2022-08-08 

    ~ Enhancement: NPM has been updated
# Release v2.22.2 on 2022-08-02 

    ~ Enhancement: address update alert show datetime
# Release v2.22.1 on 2022-08-01 

    ~ Enhancement: NPM has been updated
# Release v2.22.0 on 2022-07-26 

    ~ Enhancement: Address validation
    Revert "Revert "~ Enhancement: Checking and bank data collection feature integrated for Instituions having TAN""
    ~ Enhancement: NPM has been updated
# Release v2.21.1 on 2022-07-20 

    Revert "~ Enhancement: Checking and bank data collection feature integrated for Instituions having TAN"
# Release v2.21.0 on 2022-07-19 

    ~ Enhancement: NPM has been updated.
    ~ Enhancement: Checking and bank data collection feature integrated for Instituions having TAN
# Release v2.20.0 on 2022-07-12 

    ~ Enhancement: in consumer app  represent return  flow  change implementation done
# Release v2.19.3 on 2022-07-11 

    ~ Enhancement: NPM has been updated
# Release v2.19.2 on 2022-07-05 

    ~ Enhancement: NPM has been updated.
# Release v2.19.1 on 2022-07-04 

    ~ Bug Fix: Blacklisted account message changed
# Release v2.19.0 on 2022-06-27 

    ~ Enhancement: NPM has been updated
    ~ Enhancement: Forgot password send email validation
# Release v2.18.0 on 2022-06-20 

    ~ Enhancement: NPM has been updated
    ~ Enhancement: Bank details marked as contains probable return if found in uploaded PDF from admin. Restrict those consumers from transaction.
# Release v2.17.2 on 2022-06-15 

    ~ Fix: 1003 and 1001 conflicting issue fixed
    Revert "Revert "Revert "~ Enhancement: in consumer app  represent return  flow  change implementation done"""
    Revert "Revert "~ Enhancement: in consumer app  represent return  flow  change implementation done""
# Release v2.17.1 on 2022-06-13 

    Revert "~ Enhancement: in consumer app  represent return  flow  change implementation done"
# Release v2.17.0 on 2022-06-13 

    ~ Enhancement: Npm has been updated on 10-06-2022
    ~ Enhancement: in consumer app  represent return  flow  change implementation done
    ~ Enhancement: Schedule Repayment Functionality added
# Release v2.16.4 on 2022-05-31 

    ~ Enhancement: Restriction added for state and age to block manual bank linking
# Release v2.16.3 on 2022-05-18 

    ~ Enhancement: Blocked IL state consumer age less than 25 from Manual linking during V2 registration
# Release v2.16.2 on 2022-05-17 

    ~ Enhancment: v1 & v2 user registration success page url change done
# Release v2.16.1 on 2022-05-11 

    ~ Bug Fix: Message changes for Error code 1003
    ~ Enhancement: Block routing number from banklinking
# Release v2.16.0 on 2022-05-02 

    ~ Bug Fix : Connect fix opened against aggregationstatus codes : 103, 185, 945, 946, 947 and 948
# Release v2.15.0 on 2022-04-25 

    ~ Enhancement: NPM has been updated
# Release v2.14.0 on 2022-04-06 

    ~ Enhancement : Add vue-gtm
    ~ Enhancement : add vue-gtm library
    ~ Enhancement : add gtm code
# Release v2.13.1 on 2022-04-04 

    ~ Enhancement: NPM has been updated
# Release v2.13.0 on 2022-03-29 

    ~ Enhancement: If an account number is balcklisted the show error code instead of purchase power
# Release v2.12.0 on 2022-03-28 

    ~ Enhancement: NPM has been updated
# Release v2.11.7 on 2022-01-25 

    ~ Enhancement : transaction histroy download option added implementation done
# Release v2.11.6 on 2022-01-24 

    ~ Bugfix: V1 historical transaction module bug  fixed
# Release v2.11.5 on 2022-01-18 

    ~ Enhanement : V1 historical transaction module implementation done
# Release v2.11.4 on 2021-12-21 

    Revert "~ Enhancement : Fraud Prevention PP decrease: Bank balance should override default limit implementation done"
# Release v2.11.3 on 2021-12-21 

    ~ Enhancement : Fraud Prevention PP decrease: Bank balance should override default limit implementation done
    ~ Enhancement: Disable/Enable bank account and delete financial institution fetaure added
# Release v2.11.2 on 2021-12-15 

    ~ Enhancement: Not Found routes redirected to login Page or Dashboard if already logged in
# Release v2.11.1 on 2021-12-14 

    ~ Enhancement: Connect Fix url opened when returning security question
# Release v2.11.0 on 2021-12-07 

    ~ Enhancement : Prevent  brute force attack  if  user failed 3  login attemt then there will be a 30 minute lock time. After the account l
    ~ Fix: JWT algo change issue fixed by loggin out the consumer
# Release v2.10.3 on 2021-11-22 

    ~ Enhancement : Copy alert implementation done
# Release v2.10.2 on 2021-11-18 

    ~ Enhancement: Status color changed to red
    ~ Enhancement : Consumer Bank Statement Upload module implementation done
# Release v2.10.1 on 2021-11-17 

    Revert "~ Enhancement : Prevent  brute force attack  if  user failed 3  login attemt then there will be a 30 minute lock time. After the account l"
# Release v2.10.0 on 2021-11-16 

    ~ Enhancement : Prevent  brute force attack  if  user failed 3  login attemt then there will be a 30 minute lock time. After the account l
# Release v2.9.12 on 2021-11-16 

    ~ Enhancement : Text change in Deactive Account Page implementation done
    ~ Enhancement : In Payment Page Payment Code Copy method implementation done
    ~ Enhancement: No stores available text modified for favourite stores.
    ~ BugFix: On the payment code screen, purchase power text aligned vertically with info icon.
    ~ BugFix: Edit icon(pencil) aligned vertically with the profile details in the edir profile section.
# Release v2.9.11 on 2021-10-27 

    ~ BugFix: Checking bank account state api getting called mutiple times issue resolved.
# Release v2.9.10 on 2021-10-25 

    ~ Enhancement: Finicity BoA migration will require canpay to represent consumer with the connect fix flow.
# Release v2.9.9 on 2021-10-22 

    ~ BugFix: Menu text alignment issue resolved.
    ~ Enhancement: If system could not retrieve bank details for more than last 2 days then show pop up to customer to update their bank details.
    ~ Bug Fix: Multiple time firebase read issue fixed
# Release v2.9.8 on 2021-10-11 

    ~ Bugfix: Delink pop up modal will be shown for d-link customer at the home screen
    ~ BugFix: Return reason sub title removed from return listing screen.
    ~ BugFix: Unable to update account number for users having 4 digit account number issue fixed.
# Release v2.9.7 on 2021-10-08 

    ~ Enhancement: Confirmation added before consumer trying to attempt re debit in the return flow.
    ~ Enhancement: New dynamic return title implemented for return screens.
# Release v2.9.6 on 2021-10-07 

    ~ Enhancement: Consumer will be able to refresh their account balance once a day.
    ~ Enhancement:  During Manual Bank Linking , Account Number field should accept alphabets along with numeric digits only.
# Release v2.9.5 on 2021-10-06 

    ~ Bugfix: Same account number along with routing number will be accepted, if used account holder isn't Active status or on other status
# Release v2.9.4 on 2021-10-05 

    ~ Enhancement: Finicity connect fix implemented to resolve error in bank linking.
    ~ Bugfix : consumer session id required bug fix
    ~ Enhancement: Add PIN screens for SSN registration, accounts section and forget PIN
# Release v2.9.3 on 2021-09-30 

    ~ Enhancement: Make the Account Number field maxlength as 17 characters without spaces
    ~ Enhancement: PIN entry screen modified for Consumer Registration and Onboarding
# Release v2.9.2 on 2021-09-28 

    ~ Enhancement: Remove the desktop validation for QR Code
    ~ BugFix: Re-pay now button not showing for consumers with R01 returns issue resolved.
# Release v2.9.1 on 2021-09-23 

    ~ Enhancement : In V1 consumer onboarding email verification through link implementation done
# Release v2.9.0 on 2021-09-22 

    ~ Enhancement: Return new flow implemented with new screens.
# Release v2.8.4 on 2021-09-16 

    ~ Enhancement: Purchase power update on first login feature removed.
    ~ Enhancement: If consumer has R03 and R04 return show modal to change their bank account manually with proper message.
# Release v2.8.3 on 2021-09-14 

    ~ Bug Fix: Redirection removed when already onboarded
# Release v2.8.2 on 2021-09-13 

     ~ Enhancement :  For v1 Onboarding process if user select manual banking and consumer status is activated by admin then the purchase power of this consumer will be 100 implementation done
# Release v2.8.1 on 2021-09-10 

    ~ Bug fix :    Lorem ipsum text delete from payment-code-expired modal
    ~ Bug fix :- DuckDuckGo browser is forcing the CanPay App to believe the browser is in desktop mode and it won’t create the Payment Code for customers.
    ~ Bug fix :-  In  onboarding Registration flow  manual bank  isse fixed
# Release v2.8.0 on 2021-09-09 

    ~ Enhancement: Purchase power logic enhanced for v1 consumers.
# Release v2.7.4 on 2021-09-08 

    ~ Bug fix : In consumer app manual banking page if routing number and account number is same then show alert implementation done
    ~ Bugfix : Pin and qr code change on Network problem or general issue at the time of transaction fixed
# Release v2.7.3 on 2021-09-02 

    ~ Enhancement : Consumer forgot pin approved through link via mail implementation done
    ~ Enhancement: Consumers changing banking details from return section should not include microbilt code checking.
# Release v2.7.1 on 2021-08-31 

    ~ BugFix: Transaction declined modal getting closed on clicking upon backdrop issue fixed.
    ~ Enhancement: Profile section UI and working upgraded.
    ~ Enhancement: Consumers now can link bank account through finicity directly from bank listing page.
    ~ Enhancement : For V2 consumer registration, if we get Code 29 from Microbilt then we are going to show 3 option to consumer implementation done
# Release v2.7.0 on 2021-08-26 

    ~ Bug fix : In Account number accepting non numeric value fixed
    ~ Enhancement: Redirect Onboarded consumers to Login page from update email page
    ~ Enhancement: Manual link button added over finicity portal for onboarding and enrollment flow.
    ~ Enhancement : Update the bank details screen to show Purchase Power instead of balance implementation done
    ~ Bug fix :  Resend verification code for v1 time interval change to  15 minute fixed
# Release v2.6.5 on 2021-08-24 

    ~ Enhancement: If consumer already onboarded with given email show them proper modal with login button.
# Release v2.6.4 on 2021-08-24 

    ~ Enhancement: For already upgraded users login link added in the onboarding screen.
    ~ Enhancement : Forget PIN: Make it 6 digit all number. Verification code sent to you over Phone and Email implementation done
    ~ Enhancement : For v1 consumer date of birth update only one time from consumer app implementation done
# Release v2.6.3 on 2021-08-20 

    ~ Enhancement: Flag added in users table to identify the v1 users who used their zipline bank account while onboarding
# Release v2.6.2 on 2021-08-19 

    ~ Bug Fix : User can not enter quick access pin in registration flow fixed
    ~ Enhancement: If consumer account number is less than 5 digit then show a pop up to consumer and let them update only the account number.
# Release v2.6.1 on 2021-08-17 

    ~ Bug fix : Quick access PIN page is accepting Alphabet fixed
    ~ Bug fix : In  manual banking routing number and account number only
    ~ Bug fix :  In registration flow if google no matching address found then user manually added address
    ~ Bug fix: Customer having issues with Zip Code Entry fixed
# Release v2.6.0 on 2021-08-04 

    ~ BugFix: Upon direct linking bank details last four digit account number is showing incorrect.
    ~ Enhancement: Logout Consumer if user status is changed from Admin Panel
# Release v2.5.0 on 2021-08-02 

    ~ Enhancement: For first time login a confirm primary bank account details overlay will be shown to users.
    ~ Enhancement: Return transactions with manually linked bank accounts can now change their bank accounts using both manual and direct llinking.
    ~ BugFix: On clicking remind me later button return transactionbs are not showing in the UI issue fixed.
    ~ Enhancement: V1 consumer onboarding bank account update screen modified.
    ~ Bug fix : Store Hours are not Showing Although they are present fixed
    ~ Bug fix : Search bar implement on locateretailer page  done
# Release v2.4.2 on 2021-07-30 

    ~ Enhancement: For consumers with manually linked bank accounts do not post returns into acheck21 before webhook call.
# Release v2.4.0 on 2021-07-29 

    ~ Enhancement: Consumer can have multiple active bank accounts and can select from a new page based on need.
# Release v2.3.0 on 2021-07-28 

    ~ Enhancement: app.php page created under V2 folder for V2 transaition of consumer app
# Release v2.2.8 on 2021-07-27 

    ~ Enhancement : V1-onboading if user status is ACTIVATE BY ADMIN then user force to link bank using finicity
# Release v2.2.7 on 2021-07-23 

    ~ BugFix: Global radar 401 unauthorized credential issue handled and proper error message showed to front end.
    ~ Enhancement: Any manual bank linking error from microbilt will redirect to bank selection page.
    ~ Bug fix : Manually enter city name, while unable to fetch city name from Google address fixed
    ~ Enhancement :  Zipline customer migrating from V1 to V2 has a status
# Release v2.2.6 on 2021-07-20 

    ~ Bug fix :  Enrollment not accepting December 31st issue  fixed
    ~ BugFix: On clicking back from v1 consumer onboarding step1 page is getting distorted issue fixed.
    ~ BugFix: Update account modal not showing for v1 consumers while trying to register issue fixed.
# Release v2.2.5 on 2021-07-19 

    ~ Enhancement :  In EnterOtp page Resend verfication code timer add done
    ~ Bug-fix :- duplicate registration entry issue fix
    ~ Bug fix :  Bank registration page design broken issue fixed
# Release v2.2.4 on 2021-07-16 

    ~ Bug fix :  “limits” to “limit” (only one CanPay spending limit) on the below Spending Limit description fixed
    ~ BugFix: Uppercase letter in email causing 403 validation error Invalid email format.
# Release v2.2.3 on 2021-07-16 

    ~ BugFix: Autocomplete in the fields turned off parmanently.
# Release v2.2.2 on 2021-07-14 

    ~ Bug fix :- In  Consumer Transaction History email should be hide fixed
# Release v2.2.1 on 2021-07-09 

    ~ BugFix: For all the email inputs value by default transformed into lowercase. Invalid email id issue resolved.
    ~ BugFix: Autocomplete switched off fo all the enrollment fields.
    ~ BugFix: Manual validation pending modal disappearing too fast issue resolved.
    ~ BugFixes: Manual review ID upload page text updated.
    ~ Enhancement: New CanPay App Icon integrated.
# Release v2.2.0 on 2021-07-06 

    ~ Enhancement: While enrolling if mobile number already exists pop up button should be Sign In button.
    ~ BugFix: If mobile number already exists, clicking on back button prefills input with existing mobile number issue fixed.
    ~ BugFixes: V1 consumer onboarding terms and conditions page fixed along with other cosmetic fixes.
    ~ Enhancement :  Consumer account closure option implementation done
# Release v2.1.1 on 2021-07-05 

    ~ Enhancement: Manually bank linked consumer can submit the representment into Acheck21 on clicking the Submit Now button.
    ~ BugFixes: Splash screen is distorted for laptop screens. Screen fixed.
# Release v2.1.0 on 2021-07-04 

    ~ Bug fix : in terms and conditions page un-checked next click would give a wrong message Prompt. fixed
    ~ Enhancement: Intercom integrated in Consumer App
# Release v2.0.3 on 2021-06-29 

    ~ Bug fix : Match spacing between tiles on Merchant Locations screen issue fixed
    ~ Bug fix : button alignment for manual bank linking customers fixed
# Release v2.0.2 on 2021-06-29 

    ~ Bug Fix: QR Code refreshed when transaction declined at the consumer end
    ~ Enhancement: Consumer cannot generate pin if consumer status is suspended
# Release v2.0.1 on 2021-06-27 

    ~ Enhancement: Add space between every 3 characters in payment pin
# Release v2.0.0 on 2021-06-25 

    ~ Enhancement : Privacy Policy page design implemented
    ~ Gradient has outdated direction syntax. Warning issue fixed.
    ~ ClientFeedback: For registration and v1 onboarding flow hitting on enter button should proceed to next step.
    ~ Enhancement: Manually bank linked consumer can change bank both manually and through finicity.
    ~ Enhancement: Remember me info modal created for consumer logic screen.
    ~ ClientFeedback: Remind me later button added for change bank account modal
    ~ BugFixes: Loader keeps on rotating when store list is empty for locate retailer section.
    ~ BugFix: Forgot password flow issues resolved and all the alerts removed.
    ~ Enhancement :- Consumer Onboarding screen  implemented
    ~ ClientFeedback: Return related performance and UX improved.
    ~ Enhancement: Participating merchant map should always point to the nearest store for all the tabs.
    ~ Enhancement: Locate retailer section on hitting back from store details page should open the last visited tab.
    ~ BugFixes: Profile section UI-UX issues fixed.
    ~ ClientFeedback: Alert removed and modal added for confirm paying for transaction message.
    ~ ClientFeedback: Hardcoded store name removed from participating merchant details page.
    ~ ClientFeedback: Add favorite UX improved. Alert removed.
    ~ ClientFeedback: By default nearby tab is opened for participating merchant page.
    ~ Enhancement: Back button added for retailer locator pages.
    ~ Enhancement: Transaction history not found text enhanced.
    ~ Enhancement: New purchase power/ spending limit modal implemented.
    ~ BugFixes: After exiting finicity iframe loader keeps on running issue fixed.
    ~ Enhancement: Purchase power section changed according to new designs given.
    ~ Enhancement: Button texts changed for consumer.
    ~ Bug Fix: Consumer logout issue on page refresh fixed
    ~ Bug Fixes: Pay Now button disabled css changed
    ~ Enhancement: Show proper transaction success and declined screens after transaction.
    ~ Enhancement: Pay button disabled if consumer purchase power is 0. i.e Consumer can not generate new pin.
    ~ Fix : Iphone related issues
    ~ Fix : #709 : Alignment issue paynow page
    ~ Fix : #674 Scroll-able pages and defects in iphones.
    ~ Fix : #670 Participating Merchants Icons alignment issues.
    ~ Fix : #612 Spacing missing between Link checking account and manual account number typing.
    ~ Fix : #696 When clicked on the Remember Me info icon, the checkbox is checked and unchecked
    ~ Fix : #613 Success screen spacing missing.
    ~ Fix : #614 Amount font is small and Payment Code text has been centre aligned.
    ~ Fix : #628 User Profile page icons: Icons are too small in size
    ~ Fix : #629 Quick access PIN setup page
    ~ Enhancement: Returned transaction listing implemented with transaction representment feature.
    ~ BugFixes: CanPay blurry logo replaced with the new CanPay logo
    ~ BugFixes: Broken splash screen issue fixed.
    ~ Fix : Alignment issue pay now page
    ~ Enhancement: Splash screen functionality added for the consumers not yet enrolled using the app.
    ~ ClientFeedback: Clicking on the store card now opens the store details page.
    ~ Clientfeedback: Store address state is pulling null issue resolved.
    ~ Fix : Side panal: Logout Option spacing missing
    ~ Fix : User Profile: Wallet info: popup Screen: wallet icon
    ~ Fix : Participating Merchants Icons alignment issues.
    ~ Fix : Password svg size increase in login page
    ~ Fix : Registration DOB month drop-down padding.
    ~ Fix : Registration Screen icon small issue & LastName, suffix spacing & Suffix arrow icon
    ~ Fix : Login screen remember me alignment break
    ~ ClientFeedback: QR code and payment pin loading is taking time issue resolved.
    ~ Enhancement: Redesign login page & Fit to screen for all devices
    ~ Enhancement : Splash screen UI design
    ~ ClientFeedback: Purchase power takes time to load issue resolved.
    ~ BugFixes: Manually link bank account while trying to link through finicity error message not showing.
    ~ ClientFeedback: Button and transaction history texts changed accorfing to the texts given by the client.
    ~ ClientFeedback: Autocomplete switched off for all the input fields in the enrollment procedure.
    ~ ClientFeedback: Developer messages removed from enrollment procedure.
    ~ ClientFeedback: Auto focus on the next element for ssn and quick access pin page upon input.
    ~ Enhancement: Global radar regular review changes implemented before trying to generate payment pin.
    ~ Enhancement: While trying to update bank details consumer must select only account from finicity. Feature implemented.
    ~ BugFixes: Unable to connect bank account through finicity after registration issue fixed.
    ~ Enhancement: Complete enrollment after trying to login with existing enrollment details feature implemented.
    ~ Enhancement: Consumer enrollment process modified and quick access PIN is now the second step.
    ~ Enhancement: Number keypad integrated for day and year input field in the enrollment process
    ~ BugFixes: Consumer enrollment loader keeps on showing issue resolved along with before login header logo.
# Release v1.9.0 on 2021-02-17 

    ~ Enhancement: No follow meta tag has been introduced in the index.html page
    ~ Enhancement: JWT aprroach enhanced and refresh token mechanism implemented.
# Release v1.8.0 on 2020-12-04 

    ~ Enhancement: QR codes are now fetched and shown using a temporary url from s3.
# Release v1.7.0 on 2020-10-14 

    ~ Enhancement: Finicity connect uograded to v2
    ~ BugFixes: Arrow icon does not show in iPhone issue fixed.
# Release v1.6.0 on 2020-08-18 

    ~ Enhancement: Tip transaction is posted in Acheck21 when before the settlement time
    ~ Enhancement: Logout API implemented and now alert shows after user logs out successfully.
    ~ BugFixes: OTP error message showing only once issue resolved along with all the input placeholder left aligned.
    ~ Enhancement: Manual bank details validation added with Acheck21 while enrolling into CanPay
    ~ Enhancement: Forgot password flow UI changed for enter code pages. Single input field added and masking removed
# Release v1.5.1 on 2020-08-08 

    ~ Bugfix: Update bank account details from account section loader issue fixed
# Release v1.5.0 on 2020-08-07 

    ~ Bank linking through finicity after login flow implemented and minor UI bugs fixed
    ~ BugFixes: Edit profile save DOB issue resolved forgot PIN changed to asterisks, Store open timing issues resolved.
# Release v1.4.0 on 2020-08-05 

    ~ Minor bugs fixed and apartment number added in the profile section
    ~ Enhancement: Asterix in password with delay and hide/show feature implemented
    ~ BugFixes: Autocomplete for all the input fields has been turned off and form value reset on wrong input issue resolved
    ~ Enhancement: Apartment or Unit Number field icon added
    ~ BugsFixes: UI issues and minor design related bugs resolved
    ~ Enhancement: Retailer locator flow created and minor forgot password section bugs fixed
    ~ Enhancement: Field apt or unit number implemented and minor bugs fixed
    ~ Enhancement: Filter and opening hours implemented for each stores and bugs fixed.
# Release v1.3.0 on 2020-07-20 

    ~ BugFixes: SSN and quick access PIN blocker resolved. Profile and registration success page issues resolved
    ~ Enhancement: Live search implemented on stores for participating merchant section and bugs fixed.
    ~ BugFixes: Consumer account section UI and flow related bug resolved
    ~ BugFixes: Registration flow and UI related bugs resolved
    ~ ClientFeedback:  After login pin is set consumer can complete the registration process after logging in. And remember me process implemented
    ~ ClientFeedback: Bank account type and account number added to the consumer profile section
    ~ ClientFeedback: Pin type changed into asterisks and web and phone both shows the pin for some time and then masks it
    ~ ClientFeedback: Splash screen removed and login page is the new landing page, fonts optimized, ssn input type changed into asterisks.
# Release v1.2.0 on 2020-07-06 

    ~ Enhancement: Participating merchant flow implemented and bugs resolved
    ~ Enhancement: Manual review flow changed. Camera opens on upload ID
    ~ Enhancement: Suffix modal implemented for registration process along with minor bug fixes.
    ~ Enhancement: Forgot Password Updated Flow Complete.
    ~ Enhancement: New Login and profile screens with registration flow screes implemented.
    ~ Enhancement: Registration flow updated with cognito implementation. Redirect to step if consumer left registration midway
    ~ Enhancement: Manual Review flow implemented
    ~ ClientFeedback: Phone number masked into US format
# Release v1.1.0 on 2020-05-22 

    ~ Enhancement: UI fir to screen issue along with alignment issues resolved and logo issue fixed
# Release v1.0.0 on 2020-05-15 

    ~ BugFix: No payment pin error message show
    ~ Enhancement: Participating Merchants UI Design
    ~ BugFix: Refresh button add in Consumer PWA
    ~ Enhancement: Transaction declined handle from consumer
    ~ Enhancement: Don't display payment PINs on desktop or laptop
    ~ Enhancement: Update phone number and email from  account.
    ~ Enhancement: Forgot password Functionality
    ~ BugFix: Transaction History UI Issues, search facility and Add QRCode image loader
    ~ Enchancement: Implementaion Tip functionality ,regeneration of expired QRCode and check phone number and email availability
    ~ Enchancement:- Duplicate email check & Manual bank linking implementation
    ~ BugFix: ID verification failure handel.
    ~ BugFix: Name error
    ~ BugFix: Demo meeting bug fix
    ~ Enhancement: Project Structure changed and ENV file added
    ~ Bug fix: No  Transaction History fix
    ~ Enchancement: Validation add and Google places API Key add
    ~ Bug Fixes: Bugs in Registration flow resolved
    ~ Enchanment: Transcation approve update with Firebase Observer
    ~ Enchancement: Consumer Registration Flow upto verify Phonenumber with OTP
    ~ Enhancement: Flow till Dashboard and Settings Page with Api and functionality
