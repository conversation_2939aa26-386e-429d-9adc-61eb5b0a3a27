.login-font {
  color: white;
  text-align: center;
  margin-left: 10px;
  margin-top: 20px;
  font-size: xx-large;
}

.inner-addon {
  position: relative;
}

/* style icon */
.inner-addon .glyphicon {
  position: absolute;
  padding: 10px;
  pointer-events: none;
}

.btn1-register {
  height: 50px;
  width: 48%;
  border-radius: 5px;
  margin-right: 4%;
  margin-top: 10%;
}

.btn-forgetpassword {
  height: 50px;
  width: 48%;
  border-radius: 5px;
  padding: unset;
}

.welcome-text-style {
  font-family: $cp-font;
  font-size: 22px;
  font-weight: 500;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: #ffffff;
}

.btn-login {
  height: 50px !important;
  border-radius: 6px !important;
  width: 100% !important;
  border-color: transparent !important;
  background-color: $cp-black !important;
  font-family: $cp-font-secondary;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: #ffffff;
  margin-top: 5px !important;
  cursor: pointer;
}

.locator-div-style{
  padding-left: 0px !important;
  padding-right: 0px !important;
}

.enroll-div-style{
  padding-right: 0px !important;
}

.remember-me-row-style {
  padding-left: 0px !important;
  padding-right: 0px!important;
}
.forgotpin-me-row-style {
  float: right !important;
  // padding-top: 10px;
  padding-left: 0px !important;
  padding-right: 0px !important;
}
.remember-me-label-style {
  cursor: pointer;
  font-family: $cp-font;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left;
  color: #ffffff;
  padding-left: 5px !important;
  float: left !important;
  padding-top: 2px !important;
}

.forgot-pin-style {
  font-family: $cp-font;
  font-size: 11px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left;
  color: #ffffff;
  float: right !important;
}

/* align icon */
.left-addon .glyphicon {
  left: 0px;
}
.right-addon .glyphicon {
  right: 0px;
}

/* add padding  */
.left-addon input {
  padding-left: 30px;
}
.right-addon input {
  padding-right: 30px;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}
.email-box-style {
  margin-left: 20px;
  margin-right: 20px;
  width: 90%;
}
.checkbox-style {
  float: left;
  margin-top: 4px !important;
}
.text-remember-me{
  // font-size: 11px;
  font-family: $cp-font;
  color: $cp-white;
  // margin-left: -11px;
  font-weight: 500 !important;
}
.forgot-pin-style {
  font-family: $cp-font;
  font-size: 11px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: left;
  color: #ffffff;
  float: right !important;
}
@media only screen and (min-width: 501px) and (max-width: 600px) {
  .login-row {
    margin-top: 15vh !important;
    margin-bottom: 16% !important;
  }
  .input-box-row {
    margin-top: 10px !important;
    margin-left: 15px !important;
    margin-right: 15px !important;
  }
  .input-box-row-signup {
    margin-top: 10px !important;
    margin-left: 30px !important;
    margin-right: 30px !important;
  }

  .login-email-box-style {
    margin-left: 20px;
    margin-right: 20px;
    width: 90%;
  }
  .bottom-row-style {
    margin-top: 15% !important;
    margin-left: 0px !important;
    margin-right: 0px !important;
    height: 100%;
  }
  .btn-enroll {
    height: 45px !important;
    width: 100%;
    border-radius: 8px !important;
    border-color: transparent !important;
    background-color: $cp-primary !important;
    font-family: $cp-font;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #ffffff;
    margin-top: 5px !important;
    cursor: pointer;
    border: solid 1px #ffffff !important;
    float: right !important;
    margin-bottom: 5px !important;
  }
  .btn-locator {
    height: 45px !important;
    width: 100%;
    border-radius: 8px !important;
    border-color: transparent !important;
    background-color: $cp-secondary !important;
    font-family: $cp-font;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #ffffff;
    margin-top: 5px !important;
    cursor: pointer;
    float: left !important;
    padding-top: 5px;
    margin-bottom: 5px !important;
  }
}
// --------------------------------------300px-600px----------------------

@media only screen and (min-width: 300px) and (max-width: 500px) {
  .login-row {
    margin-top: 18% !important;
    margin-bottom: 18% !important;
  }
  .input-box-row {
    margin-top: 10px !important;
    margin-left: 15px !important;
    margin-right: 15px !important;
  }
  .input-box-row-signup {
    margin-top: 10px !important;
    margin-left: 30px !important;
    margin-right: 30px !important;
  }

  .login-email-box-style {
    margin-left: 20px;
    margin-right: 20px;
    width: 90%;
  }
  .bottom-row-style {
    margin-top: 15% !important;
    margin-left: 0px !important;
    margin-right: 0px !important;
    height: 100%;
  }
  .btn-enroll {
    height: 45px !important;
    width: 100%;
    border-radius: 8px !important;
    border-color: transparent !important;
    background-color: $cp-primary !important;
    font-family: $cp-font;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #ffffff;
    margin-top: 5px !important;
    cursor: pointer;
    border: solid 1px #ffffff !important;
    float: right !important;
    margin-bottom: 5px !important;
  }
  .btn-locator {
    height: 45px !important;
    width: 100%;
    border-radius: 8px !important;
    border-color: transparent !important;
    background-color: $cp-secondary !important;
    font-family: $cp-font;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #ffffff;
    margin-top: 5px !important;
    cursor: pointer;
    float: left !important;
    padding-top: 5px;
    margin-bottom: 5px !important;
  }
}

.login-row {
  margin-top: 20% !important;
  margin-bottom: 20% !important;
}

// --------------------------------------300px-600px----------------------

@media only screen and (min-width: 601px) and (max-width: 700px) {
  .login-row {
    margin-top: 20% !important;
    margin-bottom: 20% !important;
  }
  .input-box-row {
    margin-top: 10px !important;
    margin-left: 15px !important;
    margin-right: 15px !important;
  }
  .input-box-row-signup {
    margin-top: 10px !important;
    margin-left: 30px !important;
    margin-right: 30px !important;
  }

  .login-email-box-style {
    margin-left: 20px;
    margin-right: 20px;
    width: 90%;
  }
  .bottom-row-style {
    margin-top: 15% !important;
    height: 100%;
  }
  .btn-enroll {
    height: 45px !important;
    width: 100%;
    border-radius: 8px !important;
    border-color: transparent !important;
    background-color: $cp-primary !important;
    font-family: $cp-font;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #ffffff;
    margin-top: 5px !important;
    cursor: pointer;
    border: solid 1px #ffffff !important;
    float: right !important;
  }
  .btn-locator {
    height: 45px !important;
    width: 100%;
    border-radius: 8px !important;
    border-color: transparent !important;
    background-color: $cp-secondary !important;
    font-family: $cp-font;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #ffffff;
    margin-top: 5px !important;
    cursor: pointer;
    float: left !important;
    padding-top: 10px;
  }
}
@media only screen and (min-width: 701px) and (max-width: 900px) {
  .login-row {
    margin-top: 22% !important;
    margin-bottom: 22% !important;
  }
  .input-box-row {
    margin-top: 10px !important;
    margin-left: 15px !important;
    margin-right: 15px !important;
  }
  .input-box-row-signup {
    margin-top: 10px !important;
    margin-left: 30px !important;
    margin-right: 30px !important;
  }

  .login-email-box-style {
    margin-left: 20px;
    margin-right: 20px;
    width: 90%;
  }
  .bottom-row-style {
    margin-top: 15% !important;
    height: 100%;
  }
  .btn-enroll {
    height: 45px !important;
    width: 100%;
    border-radius: 8px !important;
    border-color: transparent !important;
    background-color: $cp-primary !important;
    font-family: $cp-font;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #ffffff;
    margin-top: 5px !important;
    cursor: pointer;
    border: solid 1px #ffffff !important;
    float: right !important;
  }
  .btn-locator {
    height: 45px !important;
    width: 100%;
    border-radius: 8px !important;
    border-color: transparent !important;
    background-color: $cp-secondary !important;
    font-family: $cp-font;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #ffffff;
    margin-top: 5px !important;
    cursor: pointer;
    float: left !important;
    padding-top: 10px;
  }
}

// --------------------------------------360px-900px----------------------


// @media only screen and (min-width: 360px) and (max-width: 900px) {
//   .login-row {
//     margin-top: 22% !important;
//     margin-bottom: 22% !important;
//   }
//   .input-box-row {
//     margin-top: 10px !important;
//     margin-left: 15px !important;
//     margin-right: 15px !important;
//   }
//   .input-box-row-signup {
//     margin-top: 10px !important;
//     margin-left: 30px !important;
//     margin-right: 30px !important;
//   }

//   .login-email-box-style {
//     margin-left: 20px;
//     margin-right: 20px;
//     width: 90%;
//   }
//   .bottom-row-style {
//     margin-top: 20% !important;
//     height: 100%;
//   }
//   .btn-enroll {
//     height: 45px !important;
//     width: 100%;
//     border-radius: 8px !important;
//     border-color: transparent !important;
//     background-color: $cp-primary !important;
//     font-family: $cp-font;
//     font-size: 12px;
//     font-weight: normal;
//     font-stretch: normal;
//     font-style: normal;
//     line-height: normal;
//     letter-spacing: normal;
//     text-align: center;
//     color: #ffffff;
//     margin-top: 5px !important;
//     cursor: pointer;
//     border: solid 1px #ffffff !important;
//     float: right !important;
//   }
//   .btn-locator {
//     height: 45px !important;
//     width: 100%;
//     border-radius: 8px !important;
//     border-color: transparent !important;
//     background-color: $cp-secondary !important;
//     font-family: $cp-font;
//     font-size: 12px;
//     font-weight: normal;
//     font-stretch: normal;
//     font-style: normal;
//     line-height: normal;
//     letter-spacing: normal;
//     text-align: center;
//     color: #ffffff;
//     margin-top: 5px !important;
//     cursor: pointer;
//     float: left !important;
//   }

//   .text-remember-me{
//     // margin-left: -24px;
//     font-size: 12px;
//   }
//   .forgot-pin-style {
//     font-size: 12px;
//   }
// }
@media only screen and (min-width: 375px) and (max-width: 414px){
    .text-remember-me{
      // margin-left: -35px;
      font-size: 12px;
    }
}
@media only screen and (min-width: 768px) and (max-width: 799px){
  .text-remember-me{
    // margin-left: -255px;
    font-size: 13px;
  }
  .forgot-pin-style {
    font-size: 13px;
  }
}
// --------------------------------------800px-1500px----------------------

@media only screen and (min-width: 800px) and (max-width: 1500px) {
  .login-row {
    margin-top: 15vh !important;
    margin-bottom: 20% !important;
  }
  .input-box-row {
    margin-top: 10px !important;
    margin-left: 15px !important;
    margin-right: 15px !important;
  }
  .input-box-row-signup {
    margin-top: 10px !important;
    margin-left: 30px !important;
    margin-right: 30px !important;
  }
  .login-email-box-style {
    margin-left: 20px;
    margin-right: 20px;
    width: 90%;
  }
  .bottom-row-style {
    margin-top: 20% !important;
    height: 100%;
  }
  .btn-enroll {
    height: 45px !important;
    width: 100%;
    border-radius: 8px !important;
    border-color: transparent !important;
    background-color: $cp-primary !important;
    font-family: $cp-font;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #ffffff;
    margin-top: 5px !important;
    cursor: pointer;
    border: solid 1px #ffffff !important;
    float: right !important;
  }
  .btn-locator {
    height: 45px !important;
    width: 100%;
    border-radius: 8px !important;
    border-color: transparent !important;
    background-color: $cp-secondary !important;
    font-family: $cp-font;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #ffffff;
    margin-top: 5px !important;
    cursor: pointer;
    float: left !important;
  }
}


// --------------------------------------1024px-1400px----------------------
@media only screen and (min-width: 1024px) and (max-width: 1400px) {
  .login-row {
    margin-top: 20% !important;
    margin-bottom: 20% !important;
  }
  .input-box-row {
    margin-top: 10px !important;
    margin-left: 100px !important;
    margin-right: 100px !important;
  }
  .login-email-box-style {
    margin-left: 20px;
    margin-right: 20px;
    width: 90%;
  }
  .bottom-row-style {
    margin-top: 20% !important;
    height: 100%;
  }
  .btn-enroll {
    height: 45px !important;
    width: 100%;
    border-radius: 8px !important;
    border-color: transparent !important;
    background-color: $cp-primary !important;
    font-family: $cp-font;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #ffffff;
    margin-top: 5px !important;
    cursor: pointer;
    border: solid 1px #ffffff !important;
    float: right !important;
  }
  .btn-locator {
    height: 45px !important;
    width: 100%;
    border-radius: 8px !important;
    border-color: transparent !important;
    background-color: $cp-secondary !important;
    font-family: $cp-font;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #ffffff;
    margin-top: 5px !important;
    cursor: pointer;
    float: left !important;
  }
}




// @media only screen and (min-width: 360px) and (max-width: 900px) {
//   .login-row {
//     margin-top: 22% !important;
//     margin-bottom: 22% !important;
//   }
//   .input-box-row {
//     margin-top: 10px !important;
//     margin-left: 15px !important;
//     margin-right: 15px !important;
//   }
//   .input-box-row-signup {
//     margin-top: 10px !important;
//     margin-left: 30px !important;
//     margin-right: 30px !important;
//   }
//   .login-email-box-style {
//     margin-left: 20px;
//     margin-right: 20px;
//     width: 90%;
//   }
//   .bottom-row-style {
//     margin-top: 20% !important;
//     height: 100%;
//   }
//   .btn-enroll {
//     height: 45px !important;
//     width: 100%;
//     border-radius: 8px !important;
//     border-color: transparent !important;
//     background-color: $cp-primary !important;
//     font-family: $cp-font;
//     font-size: 12px;
//     font-weight: normal;
//     font-stretch: normal;
//     font-style: normal;
//     line-height: normal;
//     letter-spacing: normal;
//     text-align: center;
//     color: #ffffff;
//     margin-top: 5px !important;
//     cursor: pointer;
//     border: solid 1px #ffffff !important;
//     float: right !important;
//   }
//   .btn-locator {
//     height: 45px !important;
//     width: 100%;
//     border-radius: 8px !important;
//     border-color: transparent !important;
//     background-color: $cp-secondary !important;
//     font-family: $cp-font;
//     font-size: 12px;
//     font-weight: normal;
//     font-stretch: normal;
//     font-style: normal;
//     line-height: normal;
//     letter-spacing: normal;
//     text-align: center;
//     color: #ffffff;
//     margin-top: 5px !important;
//     cursor: pointer;
//     float: left !important;
//   }
// }

@media (min-width: 992px){
  .text-remember-me{
    // margin-left: -406px;
    font-size: 13px;
  }
  .forgot-pin-style {
    font-size: 13px;
  }
  .login-row {
    margin-top: 10% !important;
    margin-bottom: 10% !important;
  }
  .input-box-row {
    margin-top: 10px !important;
    margin-left: 50px !important;
    margin-right: 50px !important;
  }
  .input-box-row-signup {
    margin-top: 10px !important;
    margin-left: 30px !important;
    margin-right: 30px !important;
  }
  .login-email-box-style {
    margin-left: 20px;
    margin-right: 20px;
    width: 90%;
  }
  .bottom-row-style {
    margin-top: 10% !important;
    height: 100%;
  }
  .btn-enroll {
    height: 45px !important;
    width: 100%;
    border-radius: 8px !important;
    border-color: transparent !important;
    background-color: $cp-primary !important;
    font-family: $cp-font;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #ffffff;
    margin-top: 5px !important;
    cursor: pointer;
    border: solid 1px #ffffff !important;
    float: right !important;
  }
  .btn-locator {
    height: 45px !important;
    width: 100%;
    border-radius: 8px !important;
    border-color: transparent !important;
    background-color: $cp-secondary !important;
    font-family: $cp-font;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #ffffff;
    margin-top: 5px !important;
    cursor: pointer;
    float: left !important;
  }

}
button:focus {
  outline: 5px !important;
}
div:focus {
  outline: 5px !important;
}
.splash {
  background-color: white!important;
  
}
.splash-welcome-text-style {
  font-family: $cp-font;
  font-size: 18px;
  font-weight: 800;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: black;
}
.btn-signup {
  height: 60px !important;
  border-radius: 8px !important;
  width: 100% !important;
  border-color: transparent !important;
  background-color: $cp-primary !important;
  font-family: $cp-font-secondary;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: #ffffff;
  margin-top: 5px !important;
  cursor: pointer;
}
.btn-login-splash {
  height: 60px !important;
  border-radius: 6px !important;
  width: 100% !important;
  border-color: transparent !important;
  background-color: $cp-black !important;
  font-family: $cp-font-secondary;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: $cp-white;
  cursor: pointer;
}
.btn-signup-splash {
  height: 60px !important;
  border-radius: 6px !important;
  width: 100% !important;
  border-color: transparent !important;
  background-color: $cp-primary !important;
  font-family: $cp-font-secondary;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: $cp-white;
  cursor: pointer;
}
.signin-button-div{
  padding-left: 0!important;
  padding-right: 5px!important;
}
.signup-button-div{
  padding-left: 5px!important;
}
.login-col{
  padding:0 0 0 0 !important;
}
.remember-me-checkbox{
  color:white;
  float: left !important;
  // font-size: small;
  // margin-top: 5px;
  // margin-bottom: 0px;
  text-align: initial;
  word-wrap: break-word;
}
.forgot-pin-text{
  color:white;
  float: right !important;
  font-size: small;
}
.mt-checkbox{
  margin-top:7px !important;

}
.login-welcome-text-style {
  font-family: $cp-font;
  font-size: 18px;
  font-weight: 800;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: white;
}
.btn-login-signup {
  height: 50px !important;
  border-radius: 6px !important;
  width: 100% !important;
  border-color: transparent !important;
  background-color: #0e7532 !important;
  font-family: $cp-font-secondary;
  font-size: 14px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: #ffffff;
  margin-top: 5px !important;
  cursor: pointer;
}

.btn-transparent-border-white {
  height: 50px !important;
  border-radius: 6px !important;
  width: 100% !important;
  border-color: $cp-white !important;
  background-color: transparent !important;
  font-family: $cp-font-secondary;
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  color: #ffffff;
  cursor: pointer;
  border: 1px solid $cp-white;
}
.btn-modal-green {
  height: 55px !important;
  border-radius: 8px !important;
  border: 1px solid $cp-primary !important;
  width: 100% !important;
  background-color: $cp-primary !important;
  font-family: $cp-font-secondary;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
  cursor: pointer;
}

.btn-modal-black {
  height: 55px !important;
  border-radius: 8px !important;
  border: 1px solid $cp-black !important;
  width: 100% !important;
  background-color: $cp-black !important;
  font-family: $cp-font-secondary;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
  cursor: pointer;
}

.login-locator-text-style {
  font-family: $cp-font;
  font-size: 14px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: white;
}
.login-bottom-panel-margin {
  margin-bottom: 3rem;
}
.login-top-panel-margin {
  margin-top: 3rem;
}

@media screen and (max-device-height: 667px) {
  .login-bottom-panel-margin {
    margin-bottom: .5rem!important;
  }
  .login-top-panel-margin {
    margin-top: .5rem!important;
  }
}
@media screen and (min-device-height: 668px) and (max-device-height: 720px) {
  .login-bottom-panel-margin {
    margin-bottom: .5rem!important;
  }
  .login-top-panel-margin {
    margin-top: .5rem!important;
  }
}
@media screen and (min-device-height: 721px) and (max-device-height: 743px) {
  .login-bottom-panel-margin {
    margin-bottom: 2rem!important;
  }
  .login-top-panel-margin {
    margin-top: 2rem!important;
  }
}
.mli-4{
  margin-left: 4px;
}

// Login page eye icon fix
@media only screen and ( min-width:300px ) and ( max-width:350px ){
  .icon-inside-input-right {
    top: 10px !important;
  }
}
// Login page eye icon fix
@media only screen and ( min-width:351px ) and ( max-width:450px ){
  .icon-inside-input-right {
    top: 13px !important;
    right: 13px !important;
  }
}
// Login page eye icon fix
@media only screen and ( min-width:451px ) and ( max-width:550px ){
  .icon-inside-input-right {
    top: 13px !important;
    right: 13px !important;
  }
}
.rememberme-icon{
  position: absolute;
  // margin-top: -35px;
  // padding-left: 50px;
  
}
