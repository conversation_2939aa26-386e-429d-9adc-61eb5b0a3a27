# must be unique in a given SonarQube instance
sonar.projectKey=canpay:consumer
# this is the name displayed in the SonarQube UI
sonar.projectName=canpay:consumer
sonar.projectVersion=
 
# Path is relative to the sonar-project.properties file. Replace "\" by "/" on Windows.
# Since SonarQube 4.2, this property is optional if sonar.modules is set. 
# If not set, SonarQube starts looking for source code from the directory containing 
# the sonar-project.properties file.
sonar.sources=app/
sonar.host.url=http://sonar.bluecoppertech.com
 
# Encoding of the source code. Default is default system encoding
#sonar.sourceEncoding=UTF-8
