<template>
  <div>
    <div v-if="isLoading">
      <CanPayLoader/>
    </div>
    <div v-if="!isLoading && !showStoreCompleteDetail">
      <div>
        <!-------------------------- SEARCH BAR------------------->
        <div>
          <div class="collapse" id="collapseExample">
            <div>
              <div
                class="row"
                id="row-header"
                style="width: 100%; margin-left: 15px"
              >
                <div
                  class="col-10 col-md-11"
                  style="
                    background-color: rgb(236, 236, 236);
                    border-radius: 5px;
                  "
                >
                  <div
                    style="
                      z-index: 999999 !important;
                      padding: 0px;
                      height: 50px;
                      float: left;
                      width: 100%;
                      display: table;
                    "
                  >
                    <svg
                      version="1.1"
                      id="Layer_1"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                      x="0px"
                      y="0px"
                      viewBox="0 0 100 125"
                      style="
                        enable-background: new 0 0 100 125;
                        margin-top: 13px;
                        margin-left: -10px;
                      "
                      xml:space="preserve"
                      height="30"
                      width="30"
                    >
                      <path
                        d="M90.4,84L75.8,69.5C87.2,54.9,86,34.1,72.9,21C66,14.1,56.8,10.3,47,10.3S28,14.1,21.1,21S10.4,37.1,10.4,46.9
	s3.8,19,10.7,25.9S37.2,83.5,47,83.5c8.1,0,16.1-2.7,22.5-7.8L84,90.4c0.9,0.9,2,1.3,3.2,1.3s2.3-0.5,3.2-1.3
	C92.2,88.7,92.2,85.8,90.4,84z M74.6,46.9c0,7.4-2.9,14.3-8.1,19.5S54.3,74.5,47,74.5s-14.3-2.9-19.5-8.1s-8.1-12.2-8.1-19.5
	c0-7.4,2.9-14.3,8.1-19.5s12.2-8.1,19.5-8.1c7.4,0,14.3,2.9,19.5,8.1S74.6,39.5,74.6,46.9z"
                      />
                    </svg>
                    <div
                      class="input-group"
                      style="
                        border: none !important;
                        border-color: transparent !important;
                        width: 90% !important;
                        float: right;
                      "
                    >
                      <autocomplete
                        ref="autocomplete"
                        placeholder="Search"
                        :source="searchStore"
                        input-class="form-control search-background"
                        results-property="data"
                        :results-display="formattedDisplay"
                        :request-headers="authHeaders"
                        @selected="getStoreDetails"
                      ></autocomplete>
                      <!-- </gmap-autocomplete> -->
                    </div>
                  </div>
                </div>
                <div class="col-2 col-md-1" style="float: right">
                  <a v-on:click="clearSearch">
                    <svg
                      version="1.1"
                      id="Layer_1"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                      x="0px"
                      y="0px"
                      viewBox="0 0 100 125"
                      style="
                        enable-background: new 0 0 100 125;
                        margin-top: 15px;
                        margin-right: 15px;
                      "
                      xml:space="preserve"
                      height="15"
                      width="15"
                    >
                      <g>
                        <path
                          d="M85.7,14.3c-2.3-2.3-6.1-2.3-8.5,0L50,41.5L22.7,14.3c-2.3-2.3-6.1-2.3-8.5,0c-2.3,2.3-2.3,6.1,0,8.5L41.5,50L14.3,77.3
		c-2.3,2.3-2.3,6.1,0,8.5c1.2,1.2,2.7,1.8,4.2,1.8s3.1-0.6,4.2-1.8L50,58.5l27.3,27.3c1.2,1.2,2.7,1.8,4.2,1.8s3.1-0.6,4.2-1.8
		c2.3-2.3,2.3-6.1,0-8.5L58.5,50l27.3-27.3C88.1,20.4,88.1,16.6,85.7,14.3z"
                        />
                      </g>
                    </svg>
                  </a>
                </div>
              </div>

              <br />
            </div>
          </div>
        </div>

        <!--------------------------------->
        <!-- Map section -->
        <div>
        <button
        style="
          position: absolute;
          z-index: 99;
          right: 57px;
          top: 325px;
          line-height: 18px;
          background-color: rgb(255, 255, 255);
          font-family: 'Open Sans';
          border-radius: 28px;
          border: none;
          padding: 11px 24px;
          width: 87px;
          color:#000000;
           font-weight:600;
          box-shadow: rgba(0, 0, 0, 0.2) 0px 3px 5px -1px, rgba(0, 0, 0, 0.14) 0px 6px 10px 0px, rgba(0, 0, 0, 0.12) 0px 1px 18px 0px;
        "
        @click="showModal()"
        >
          Filter
        </button>
        <GmapMap
          ref="mapRef"
          :center="center"
          :zoom="10"
          map-type-id="terrain"
          style="width: 100%; height: 300px; margin-top: -7px"
        >
          <GmapMarker
            :key="index"
            v-for="(m, index) in markers"
            :position="m.position"
            :clickable="true"
            @click="handleMarkerClick(m.id)"
            :icon="m.position.icon"
            :ref="'marker-' + m.id"
          />
        </GmapMap>
        </div>
      </div>
      <tabs
        :tabs="tabs"
        :currentTab="currentTab"
        :wrapper-class="'default-tabs bottom-edge-shadow'"
        :tab-class="'default-tabs__item'"
        :tab-active-class="'default-tabs__item_active'"
        :line-class="'default-tabs__active-line'"
        @onClick="changeTab"
      />
      <div class="tab-content store-list-card">
        <!---------------------------------------------------------------------------------------->
        <!------------------------------------ Display Div Detail Start --------------------------------->
        <!------------------------------------------------------------------------------------------------>
        <div
          v-if="displayStoreDetail"
          style="
            width: 100%;
            margin-left: 0px;
            position: absolute;
            background-color: #ffffff;
            z-index: 999;
            top: 398px;
            height:447px;
          "
        >
        <!-------- Header Section Start --------->
        <div 
          style="
          background-color:#ffffff;
          width: 100%;
          "
        >
            <div class="row" style="width:100%;margin-left:0px;background-color:#ffffff;">
              <div class="col-12 text-left" style="font-weight:bold;margin-top:5px;">
                <svg @click="displayStoreDetail = false;handleMarkerClick(currStoreList.id)" style="margin-bottom:4px;margin-left: 4px;" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 51 42" fill="none">
                <path d="M1.79118 19.1875L20.0118 0.75C21 -0.25 22.6059 -0.25 23.5941 0.75C24.5824 1.75 24.5824 3.375 23.5941 4.375L9.69706 18.4375H47.8677C49.2882 18.4375 50.4 19.5625 50.4 21C50.4 22.4375 49.2882 23.5625 47.8677 23.5625H9.69706L23.5941 37.625C24.5824 38.625 24.5824 40.25 23.5941 41.25C23.1 41.75 22.4824 42 21.8029 42C21.1235 42 20.5059 41.75 20.0118 41.25L1.79118 22.8125L0 21L1.79118 19.1875Z" fill="black"/>
                </svg> &nbsp;
                {{currStoreList.retailer}}</div>
            </div>
        </div>
        <!-------- Header Section End ----------->
        <!-------- starting Store Content ------------->
        <div>
        <!-------- Starting Horizontal Content ----------->
          <hr class="storeHorizontalRowContent">
        <!-------- Ending Horizontal Content ------------->
        <!-------- Start Logo Content -------------------->
        <div style="display:flex;width:100%;">
          <svg v-if="currStoreList.is_generic == 1" style="margin-left: 21px;" xmlns="http://www.w3.org/2000/svg" width="43" height="43" viewBox="0 0 78 78" fill="none">
          <path d="M71.5422 38.7711C71.5422 56.8701 56.8701 71.5422 38.7711 71.5422C20.6721 71.5422 6 56.8701 6 38.7711C6 20.6721 20.6721 6 38.7711 6C56.8701 6 71.5422 20.6721 71.5422 38.7711Z" fill="black"/>
          <path d="M60.337 47.6219C63.0863 41.782 61.9611 36.1979 61.6426 34.7166C61.2817 33.0435 60.3688 29.0898 57.0464 25.5411C55.4639 23.8736 53.5846 22.5181 51.5054 21.5448C48.7031 20.234 46.3147 19.9783 45.0091 19.8504C39.6273 19.3176 35.1796 20.8948 32.6958 22.0244C33.9051 20.8612 35.2874 19.894 36.7931 19.1577C38.1237 18.5192 39.5341 18.064 40.986 17.8043C43.3633 17.3482 45.8006 17.3014 48.1935 17.6658C49.2975 17.8363 53.9044 18.6355 58.5006 22.2162C65.1986 27.4274 67.9479 34.6633 66.3875 42.9863C64.0098 55.7211 51.8451 63.1808 39.5105 59.8985C35.8802 58.9288 32.0589 56.6376 30.3605 54.3997C30.4454 54.3997 30.5409 54.3783 30.594 54.4103C31.0186 54.6874 31.4326 54.9858 31.8678 55.2629C38.7675 59.6108 45.837 59.6428 52.8641 55.7531C53.7539 55.2594 54.5992 54.6888 55.3905 54.048C58.4369 51.5756 59.8275 48.6983 60.337 47.6219Z" fill="white"/>
          <path d="M66.9711 48.3891C66.8967 48.8367 66.7694 49.4761 66.5677 50.2327C65.1028 55.6464 61.7591 59.0672 60.3261 60.4952C55.0824 65.7277 48.9788 67.241 46.187 67.9017C40.4444 69.2551 35.8057 68.6796 34.1391 68.4132C28.1417 67.4541 24.0231 65.0244 22.7599 64.2251C20.1829 62.6006 17.8731 60.5837 15.9133 58.2466C13.6245 55.5283 11.8314 52.4258 10.6165 49.0818C10.3086 48.2293 9.14099 44.883 8.84377 40.3006C8.56778 35.9314 9.22591 32.6704 9.38513 31.9138C9.90426 29.4419 10.7429 27.0487 11.8796 24.795C12.4741 23.6441 15.4993 18.0173 22.1867 13.8186C23.7365 12.8381 27.7914 10.5363 33.5341 9.66241C35.052 9.42796 48.1402 7.61631 57.5132 15.854C61.8547 19.6691 64.4129 24.7737 64.8162 25.5943C65.4372 26.8618 65.9762 28.1682 66.4297 29.5053C66.2811 29.3028 65.1771 27.747 65.1453 27.7043C61.0692 21.6406 56.3349 18.7846 56.3349 18.7846C54.5448 17.7288 52.62 16.9222 50.6135 16.3868C44.2339 14.6604 38.8628 16.195 37.589 16.5787C30.1161 18.8272 25.6896 24.8057 25.4349 25.1574C20.9235 31.4022 21.0191 37.9242 21.104 39.7358C21.3906 45.8315 24.0443 50.1368 25.2544 51.8739C25.35 51.9911 25.488 52.1723 25.6578 52.3854C25.7852 52.5559 25.9126 52.7264 26.0399 52.8969C29.2032 57.0744 33.3961 59.9837 38.056 61.433C41.5287 62.4991 45.1983 62.7538 48.7841 62.1775C52.3698 61.6012 55.7768 60.2093 58.7445 58.1081C61.3558 56.2538 62.9374 54.3249 63.914 53.1314C65.209 51.5542 66.1537 49.9983 66.4615 49.3802C66.504 49.3056 66.9498 48.3891 66.9711 48.3891Z" fill="#1B9142"/>
          <path d="M32.8439 48.4851C27.2392 48.4851 24.4369 44.7659 24.4369 39.0645C24.4369 33.1713 27.3878 29.6226 33.1624 29.6226C35.3915 29.6226 37.1854 30.1555 38.5654 31.2851C39.8604 32.4467 40.5822 34.0239 40.7627 36.0274H38.5123C37.7056 36.0274 37.1217 35.6544 36.7714 34.919C36.1558 33.6083 34.9457 32.9369 33.173 32.9369C29.7125 32.9369 28.1734 35.3879 28.1734 39.0752C28.1734 42.6559 29.6488 45.1815 33.0562 45.1815C35.3915 45.1815 36.729 43.8921 37.1324 41.8673H40.752C40.423 46.0874 37.4614 48.4851 32.8439 48.4851Z" fill="white"/>
          <path d="M52.1951 29.6652H45.8368C44.7647 29.6652 43.9155 30.603 43.9155 31.6793V48.4211H47.7369V41.7393H52.4711C56.2182 41.7393 58.2244 39.3948 58.2244 35.6969C58.2244 31.7965 56.1651 29.6652 52.1951 29.6652ZM51.7599 38.3504H48.0341V32.9155H51.951C53.7343 32.9155 54.6472 33.8213 54.6472 35.6436C54.6578 37.4659 53.7025 38.3824 51.7599 38.3504Z" fill="#1B9142"/>
          <path d="M38.7711 74.5422C58.5269 74.5422 74.5422 58.5269 74.5422 38.7711C74.5422 19.0153 58.5269 3 38.7711 3C19.0153 3 3 19.0153 3 38.7711C3 58.5269 19.0153 74.5422 38.7711 74.5422Z" stroke="white" stroke-width="6"/>
          </svg>
          <svg v-if="currStoreList.is_generic == 0" style="margin-left: 25px;" xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 75 75" fill="none">
          <path d="M75 37.5C75 58.2107 58.2107 75 37.5 75C16.7893 75 0 58.2107 0 37.5C0 16.7893 16.7893 0 37.5 0C58.2107 0 75 16.7893 75 37.5Z" fill="black"/>
          <path d="M62.1779 47.6281C65.3239 40.9455 64.0363 34.5555 63.6719 32.8605C63.2589 30.9459 62.2143 26.4217 58.4124 22.3609C56.6016 20.4528 54.4511 18.9017 52.0719 17.788C48.8651 16.288 46.1321 15.9954 44.6381 15.849C38.4797 15.2393 33.3903 17.0441 30.548 18.3367C31.9318 17.0057 33.5136 15.899 35.2366 15.0564C36.7592 14.3258 38.3731 13.8049 40.0345 13.5077C42.7549 12.9858 45.5438 12.9322 48.2821 13.3492C49.5453 13.5443 54.817 14.4589 60.0765 18.5562C67.7411 24.5194 70.887 32.7995 69.1015 42.3234C66.3806 56.8959 52.4606 65.4321 38.3461 61.6762C34.192 60.5665 29.8192 57.9447 27.8757 55.3838C27.9729 55.3838 28.0822 55.3594 28.1429 55.396C28.6288 55.7131 29.1025 56.0545 29.6005 56.3716C37.4959 61.347 45.5855 61.3835 53.6266 56.9325C54.6448 56.3676 55.6121 55.7147 56.5175 54.9814C60.0036 52.1523 61.5948 48.8597 62.1779 47.6281Z" fill="white"/>
          <path d="M69.7693 48.5059C69.6843 49.0181 69.5385 49.7498 69.3077 50.6156C67.6315 56.8104 63.8053 60.7249 62.1655 62.359C56.165 68.3465 49.1807 70.0781 45.9861 70.8342C39.4148 72.3829 34.1067 71.7244 32.1996 71.4195C25.3368 70.322 20.6239 67.5417 19.1784 66.6271C16.2295 64.7682 13.5864 62.4602 11.3438 59.7859C8.72471 56.6753 6.67285 53.1251 5.28264 49.2986C4.93039 48.323 3.59425 44.4939 3.25415 39.2503C2.93833 34.2505 3.69143 30.519 3.87363 29.6531C4.46767 26.8246 5.42728 24.0861 6.72809 21.5072C7.40831 20.1902 10.8701 13.7514 18.5225 8.94678C20.2959 7.82488 24.9359 5.19085 31.5073 4.1909C33.2443 3.92262 48.2211 1.84954 58.9466 11.2759C63.9146 15.6416 66.8419 21.4828 67.3035 22.4218C68.0141 23.8722 68.6308 25.3671 69.1498 26.8972C68.9797 26.6655 67.7165 24.8851 67.6801 24.8363C63.0157 17.8976 57.5983 14.6294 57.5983 14.6294C55.5499 13.4213 53.3474 12.4983 51.0513 11.8857C43.7511 9.91015 37.6049 11.6662 36.1473 12.1052C27.5961 14.6782 22.5309 21.5194 22.2394 21.9218C17.0771 29.0678 17.1864 36.5309 17.2835 38.604C17.6115 45.5792 20.6482 50.5058 22.0329 52.4936C22.1422 52.6277 22.3001 52.835 22.4945 53.0789C22.6402 53.274 22.786 53.4691 22.9317 53.6642C26.5515 58.4445 31.3494 61.7736 36.6818 63.4321C40.6556 64.652 44.8547 64.9435 48.9579 64.284C53.0611 63.6246 56.9597 62.0317 60.3556 59.6274C63.3437 57.5055 65.1535 55.2983 66.271 53.9325C67.7529 52.1277 68.834 50.3473 69.1862 49.64C69.2348 49.5547 69.745 48.5059 69.7693 48.5059Z" fill="#FF9F20"/>
          <path d="M30.7176 48.6158C24.3041 48.6158 21.0974 44.3599 21.0974 37.8358C21.0974 31.0922 24.4742 27.0314 31.082 27.0314C33.6328 27.0314 35.6855 27.6411 37.2646 28.9338C38.7465 30.263 39.5725 32.0678 39.779 34.3604H37.2039C36.2807 34.3604 35.6127 33.9335 35.2118 33.0921C34.5073 31.5922 33.1226 30.8239 31.0941 30.8239C27.1343 30.8239 25.373 33.6287 25.373 37.848C25.373 41.9454 27.0614 44.8355 30.9605 44.8355C33.6328 44.8355 35.1632 43.3599 35.6248 41.043H39.7668C39.3903 45.872 36.0014 48.6158 30.7176 48.6158Z" fill="white"/>
          <path d="M52.8611 27.0801H45.5853C44.3585 27.0801 43.3867 28.1532 43.3867 29.3848V48.5425H47.7595V40.8965H53.1769C57.4647 40.8965 59.7604 38.2137 59.7604 33.9822C59.7604 29.519 57.404 27.0801 52.8611 27.0801ZM52.3631 37.0186H48.0996V30.7994H52.5818C54.6224 30.7994 55.667 31.8359 55.667 33.9212C55.6792 36.0065 54.586 37.0552 52.3631 37.0186Z" fill="#FF9F20"/>
          </svg>
          <div>
          <svg v-if="currStoreList.is_generic == 1" style="position: relative; top: 5px; left: 4px;" @click="showStoreCompleteDetail = true; canpayDisplay = true" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 38 22" fill="none">
          <path d="M17.2322 20.7678C18.2085 21.7441 19.7915 21.7441 20.7678 20.7678L36.6777 4.85786C37.654 3.88155 37.654 2.29864 36.6777 1.32233C35.7014 0.346019 34.1184 0.346019 33.1421 1.32233L19 15.4645L4.85786 1.32233C3.88155 0.34602 2.29864 0.34602 1.32233 1.32233C0.346019 2.29864 0.346019 3.88155 1.32233 4.85787L17.2322 20.7678ZM16.5 17L16.5 19L21.5 19L21.5 17L16.5 17Z" fill="black"/>
          </svg>
          <svg v-if="currStoreList.is_generic == 0" style="position: relative; top: 3px; left: 6px;" @click="showStoreCompleteDetail = true; canpayDisplay = true" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 38 22" fill="none">
          <path d="M17.2322 20.7678C18.2085 21.7441 19.7915 21.7441 20.7678 20.7678L36.6777 4.85786C37.654 3.88155 37.654 2.29864 36.6777 1.32233C35.7014 0.346019 34.1184 0.346019 33.1421 1.32233L19 15.4645L4.85786 1.32233C3.88155 0.34602 2.29864 0.34602 1.32233 1.32233C0.346019 2.29864 0.346019 3.88155 1.32233 4.85787L17.2322 20.7678ZM16.5 17L16.5 19L21.5 19L21.5 17L16.5 17Z" fill="black"/>
          </svg>
          </div>
          <svg v-if="currStoreList.is_generic == 1" style="margin-left: 30px;
                margin-top: 4px;" xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 67 67" fill="none">
          <path d="M33.5272 33.6119C31.8424 35.2808 30.1707 36.9496 28.4859 38.6184C22.5173 44.5767 16.5356 50.535 10.567 56.5064C10.3711 56.7019 10.1621 56.8845 9.87478 57.1452C8.97361 56.1022 8.05939 55.1374 7.24964 54.0944C3.08337 48.788 0.732498 42.7775 0.197022 36.05C0.170901 35.8414 0.131719 35.6328 0.0794778 35.4242V33.4685C11.2331 33.4685 22.3736 33.4685 33.5272 33.4555V33.6119Z" fill="black"/>
          <path d="M66.9615 33.4692C55.8602 33.4692 44.7458 33.4692 33.6444 33.4561C33.6053 33.4561 33.553 33.4692 33.5138 33.4692C33.6836 33.2736 33.8403 33.065 34.0232 32.8825C41.5329 25.3857 49.0427 17.8889 56.5524 10.3922C56.7222 10.2227 56.892 10.0662 57.1532 9.83154C57.9107 10.692 58.6812 11.4743 59.3734 12.3348C63.5136 17.4196 65.982 23.2084 66.7134 29.7273C66.7917 30.4183 66.8832 31.0963 66.9615 31.7742V33.4692Z" fill="black"/>
          <path d="M57.1271 9.79142C49.2778 17.6272 41.4154 25.4629 33.5661 33.2987C33.54 33.3247 33.5138 33.3638 33.4877 33.403C33.4616 33.1422 33.4224 32.8815 33.4224 32.6207C33.4224 22.0209 33.4224 11.4212 33.4224 0.821384C33.4224 0.586703 33.4355 0.352022 33.4485 0C34.5979 0.0651892 35.6949 0.0912649 36.792 0.195568C43.3222 0.860498 49.1602 3.22035 54.293 7.31423C54.8284 7.74448 55.377 8.16169 55.9255 8.59194L57.1271 9.79142Z" fill="#0DD668"/>
          <path d="M9.80837 9.79221C17.6577 17.628 25.507 25.4767 33.3563 33.3125C33.3824 33.3386 33.4216 33.3646 33.4608 33.3907C33.1996 33.4168 32.9383 33.4559 32.6771 33.4559C22.059 33.4559 11.4409 33.4559 0.822806 33.4559C0.587718 33.4559 0.352631 33.4429 0 33.4298C0.065302 32.2825 0.0914228 31.1873 0.195906 30.0921C0.861987 23.5732 3.22592 17.7453 7.32689 12.6214C7.75788 12.0869 8.17581 11.5393 8.60681 10.9917L9.80837 9.79221Z" fill="#D3FCC8"/>
          <path d="M9.87506 57.2086C17.7244 49.3728 25.5867 41.5371 33.436 33.7013C33.4622 33.6753 33.4883 33.6362 33.5144 33.597C33.5405 33.8578 33.5797 34.1186 33.5797 34.3793C33.5797 44.9791 33.5797 55.5788 33.5797 66.1786C33.5797 66.4133 33.5667 66.648 33.5536 67C32.4043 66.9348 31.3072 66.9087 30.2101 66.8044C23.6799 66.1395 17.8419 63.7797 12.7092 59.6858C12.1737 59.2555 11.6252 58.8383 11.0766 58.4081L9.87506 57.2086Z" fill="#0DD668"/>
          <path d="M57.1786 57.1176C49.3293 49.2819 41.48 41.4331 33.6437 33.5843C33.6176 33.5582 33.5784 33.5322 33.5392 33.5061C33.8004 33.48 34.0616 33.4409 34.3229 33.4409C44.941 33.4409 55.5591 33.4409 66.1772 33.4539C66.4123 33.4539 66.6474 33.467 67 33.48C66.9347 34.6273 66.9086 35.7225 66.8041 36.8177C66.138 43.3366 63.7741 49.1645 59.6731 54.2884C59.2421 54.823 58.8242 55.3706 58.3932 55.9182L57.1786 57.1176Z" fill="#D3FCC8"/>
          <path d="M33.4089 0.0119587C33.4089 11.0941 33.4089 22.1893 33.3958 33.2715C33.3958 33.3106 33.4089 33.3628 33.4089 33.4019C33.213 33.2324 33.004 33.0759 32.8211 32.8934C25.3114 25.3966 17.8017 17.8999 10.2919 10.4031C10.1222 10.2336 9.96544 10.0641 9.73035 9.80338C10.5923 9.04719 11.376 8.27795 12.2379 7.58695C17.3315 3.45395 23.1303 0.989797 29.6605 0.259678C30.3527 0.181451 31.0319 0.0901858 31.711 0.0119587H33.4089Z" fill="#007EE5"/>
          <path d="M33.5269 66.8442C33.5138 66.6225 33.5007 66.4139 33.5007 66.1923C33.5007 55.5143 33.5007 44.8363 33.5007 34.1713C33.5007 33.9758 33.5138 33.7802 33.5269 33.5846C33.6705 33.702 33.8403 33.8193 33.9709 33.9497C41.546 41.4986 49.1079 49.0606 56.6699 56.6095C56.7875 56.7268 56.905 56.8572 57.0617 57.0397C56.9311 57.1831 56.8136 57.3396 56.683 57.47C51.7331 62.1897 45.9081 65.1623 39.1559 66.2966C37.7193 66.5313 36.2695 66.6616 34.8329 66.8442H33.5269Z" fill="#29576C"/>
          </svg>
          <svg v-if="currStoreList.is_generic == 0" style="margin-left: 35px;" xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 67 67" fill="none">
          <path d="M33.5272 33.6119C31.8424 35.2808 30.1707 36.9496 28.4859 38.6184C22.5173 44.5767 16.5356 50.535 10.567 56.5064C10.3711 56.7019 10.1621 56.8845 9.87478 57.1452C8.97361 56.1022 8.05939 55.1374 7.24964 54.0944C3.08337 48.788 0.732498 42.7775 0.197022 36.05C0.170901 35.8414 0.131719 35.6328 0.0794778 35.4242V33.4685C11.2331 33.4685 22.3736 33.4685 33.5272 33.4555V33.6119Z" fill="black"/>
          <path d="M66.9615 33.4692C55.8602 33.4692 44.7458 33.4692 33.6444 33.4561C33.6053 33.4561 33.553 33.4692 33.5138 33.4692C33.6836 33.2736 33.8403 33.065 34.0232 32.8825C41.5329 25.3857 49.0427 17.8889 56.5524 10.3922C56.7222 10.2227 56.892 10.0662 57.1532 9.83154C57.9107 10.692 58.6812 11.4743 59.3734 12.3348C63.5136 17.4196 65.982 23.2084 66.7134 29.7273C66.7917 30.4183 66.8832 31.0963 66.9615 31.7742V33.4692Z" fill="black"/>
          <path d="M57.1271 9.79142C49.2778 17.6272 41.4154 25.4629 33.5661 33.2987C33.54 33.3247 33.5138 33.3638 33.4877 33.403C33.4616 33.1422 33.4224 32.8815 33.4224 32.6207C33.4224 22.0209 33.4224 11.4212 33.4224 0.821384C33.4224 0.586703 33.4355 0.352022 33.4485 0C34.5979 0.0651892 35.6949 0.0912649 36.792 0.195568C43.3222 0.860498 49.1602 3.22035 54.293 7.31423C54.8284 7.74448 55.377 8.16169 55.9255 8.59194L57.1271 9.79142Z" fill="#0DD668"/>
          <path d="M9.80837 9.79221C17.6577 17.628 25.507 25.4767 33.3563 33.3125C33.3824 33.3386 33.4216 33.3646 33.4608 33.3907C33.1996 33.4168 32.9383 33.4559 32.6771 33.4559C22.059 33.4559 11.4409 33.4559 0.822806 33.4559C0.587718 33.4559 0.352631 33.4429 0 33.4298C0.065302 32.2825 0.0914228 31.1873 0.195906 30.0921C0.861987 23.5732 3.22592 17.7453 7.32689 12.6214C7.75788 12.0869 8.17581 11.5393 8.60681 10.9917L9.80837 9.79221Z" fill="#D3FCC8"/>
          <path d="M9.87506 57.2086C17.7244 49.3728 25.5867 41.5371 33.436 33.7013C33.4622 33.6753 33.4883 33.6362 33.5144 33.597C33.5405 33.8578 33.5797 34.1186 33.5797 34.3793C33.5797 44.9791 33.5797 55.5788 33.5797 66.1786C33.5797 66.4133 33.5667 66.648 33.5536 67C32.4043 66.9348 31.3072 66.9087 30.2101 66.8044C23.6799 66.1395 17.8419 63.7797 12.7092 59.6858C12.1737 59.2555 11.6252 58.8383 11.0766 58.4081L9.87506 57.2086Z" fill="#0DD668"/>
          <path d="M57.1786 57.1176C49.3293 49.2819 41.48 41.4331 33.6437 33.5843C33.6176 33.5582 33.5784 33.5322 33.5392 33.5061C33.8004 33.48 34.0616 33.4409 34.3229 33.4409C44.941 33.4409 55.5591 33.4409 66.1772 33.4539C66.4123 33.4539 66.6474 33.467 67 33.48C66.9347 34.6273 66.9086 35.7225 66.8041 36.8177C66.138 43.3366 63.7741 49.1645 59.6731 54.2884C59.2421 54.823 58.8242 55.3706 58.3932 55.9182L57.1786 57.1176Z" fill="#D3FCC8"/>
          <path d="M33.4089 0.0119587C33.4089 11.0941 33.4089 22.1893 33.3958 33.2715C33.3958 33.3106 33.4089 33.3628 33.4089 33.4019C33.213 33.2324 33.004 33.0759 32.8211 32.8934C25.3114 25.3966 17.8017 17.8999 10.2919 10.4031C10.1222 10.2336 9.96544 10.0641 9.73035 9.80338C10.5923 9.04719 11.376 8.27795 12.2379 7.58695C17.3315 3.45395 23.1303 0.989797 29.6605 0.259678C30.3527 0.181451 31.0319 0.0901858 31.711 0.0119587H33.4089Z" fill="#007EE5"/>
          <path d="M33.5269 66.8442C33.5138 66.6225 33.5007 66.4139 33.5007 66.1923C33.5007 55.5143 33.5007 44.8363 33.5007 34.1713C33.5007 33.9758 33.5138 33.7802 33.5269 33.5846C33.6705 33.702 33.8403 33.8193 33.9709 33.9497C41.546 41.4986 49.1079 49.0606 56.6699 56.6095C56.7875 56.7268 56.905 56.8572 57.0617 57.0397C56.9311 57.1831 56.8136 57.3396 56.683 57.47C51.7331 62.1897 45.9081 65.1623 39.1559 66.2966C37.7193 66.5313 36.2695 66.6616 34.8329 66.8442H33.5269Z" fill="#29576C"/>
          </svg>
        </div>
        <!-------- End Logo Content ---------------------->
        </div>
        <!-------- Start Logo Content ---------------------->
        <div style="display:flex;">
          <div style="font-size:12px;margin-left:19px;font-family:Open Sans;font-weight:600;">Rewards</div>

          <div style="font-size:12px;padding-left:41px;font-family:Open Sans;font-weight:600;">Wheel</div>
        </div>
        <!-------- End Logo Content ---------------------->
        <!-------- ending Store Content ------------->
        <!-------- Starting Horizontal Content ----------->
          <hr class="storeHorizontalRowContent">
        <!-------- Ending Horizontal Content ------------->
        <!-------- Starting Clock Hour --------->
        <div style="width:100%;display:flex;padding-left: 20px;">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="15" height="18" viewBox="0 0 46 46" fill="none">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H46V46H0V0Z" fill="url(#pattern0_14505_715)"/>
            <defs>
            <pattern id="pattern0_14505_715" patternContentUnits="objectBoundingBox" width="1" height="1">
            <use xlink:href="#image0_14505_715" transform="scale(0.0217391)"/>
            </pattern>
            <image id="image0_14505_715" width="46" height="46" xlink:href="data:image/png;base64,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"/>
            </defs>
            </svg>
            <!---- start timing text ----->
            <span v-if="getOpeningHoursStatus(currStoreList) == 'Hours currently unavailable. Contact the merchant'" style="padding-left:12px;font-size: 12px;font-weight:600;padding-bottom:12px;"><span class="text-time-green"> {{getOpeningHoursStatus(currStoreList)}}</span><span class="text-time-black"> {{getOpeningHoursTiming(currStoreList)}} </span></span>
            <span v-else style="padding-left:12px;font-size: 12px;font-weight:600;"><span class="text-time-green"> {{getOpeningHoursStatus(currStoreList)}}</span><span class="text-time-black"> {{getOpeningHoursTiming(currStoreList)}} </span></span>
            <!---- end starting text ----->
            <!---- Start Nav Accordian ------>
            <div v-if="currStoreList && currStoreList.timing_arr && currStoreList.timing_arr.length > 0" style="
                padding-left: 10px;
                position: relative;
                top: -7px;
            ">
            <svg @click="showStoreCompleteDetail = true; canpayTimingDetail = true" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 38 22" fill="none">
            <path d="M17.2322 20.7678C18.2085 21.7441 19.7915 21.7441 20.7678 20.7678L36.6777 4.85786C37.654 3.88155 37.654 2.29864 36.6777 1.32233C35.7014 0.346019 34.1184 0.346019 33.1421 1.32233L19 15.4645L4.85786 1.32233C3.88155 0.34602 2.29864 0.34602 1.32233 1.32233C0.346019 2.29864 0.346019 3.88155 1.32233 4.85787L17.2322 20.7678ZM16.5 17L16.5 19L21.5 19L21.5 17L16.5 17Z" fill="black"/>
            </svg>
            </div>
            <!---- End   Nav Accordian ------>
        </div>
        <!-------- Ending Clock Hour --------->
        <!-------- Starting Horizontal Content ----------->
          <hr class="storeHorizontalRowContent" style="margin-top:5px!important;">
        <!-------- Ending Horizontal Content ------------->
        <!-------- Stating the address ------------------->
        <div style="display:flex;">
          <div class="text-left">
          <p style="font-size:13px;font-weight:bold;margin-bottom:0px;margin-left:20px;font-family:'Open Sans';width:290px;" v-html="storeAddress(currStoreList)"></p>
          </div>
          <div @click="openGoogleMap(currStoreList)" style="position:absolute;right:16px;">
              <svg style="position:relative;right: 7px;" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="25" height="40" viewBox="0 0 68 76" fill="none">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H68V76H0V0Z" fill="url(#pattern0_14505_713)"/>
              <defs>
              <pattern id="pattern0_14505_713" patternContentUnits="objectBoundingBox" width="1" height="1">
              <use xlink:href="#image0_14505_713" transform="scale(0.0147059 0.0131579)"/>
              </pattern>
              <image id="image0_14505_713" width="68" height="76" xlink:href="data:image/png;base64,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"/>
              </defs>
              </svg>
          </div>
        </div>
        <!-------- Ending  the address ------------------->
        <!-------- Starting Location --------------------->
        <div style="display:flex;">
          <div style="padding-left:15px;padding-top: 5px;">
          <span>
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="30" height="20" viewBox="0 0 40 50" fill="none">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H40V50H0V0Z" fill="url(#pattern0_14505_712)"/>
              <defs>
              <pattern id="pattern0_14505_712" patternContentUnits="objectBoundingBox" width="1" height="1">
              <use xlink:href="#image0_14505_712" transform="scale(0.025 0.02)"/>
              </pattern>
              <image id="image0_14505_712" width="40" height="50" xlink:href="data:image/png;base64,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"/>
              </defs>
              </svg><span style="font-size: 15px;font-weight:500;">{{
                distance(
                  currStoreList.lat,
                  currStoreList.long,
                  currentLat,
                  currentLong,
                  ""
                )
                }}</span>
          </span>
          </div>
        </div>
        <!-------- Ending Location  ---------------------->
        <!-------- Starting Horizontal Content ----------->
          <hr class="storeHorizontalRowContent" style="margin-top:12px;">
        <!-------- Ending Horizontal Content ------------->
        <!-------- Start Phone Details ------------------->
        <div style="display:flex;">
          <div style="padding-left: 20px;">
            <span style="
              font-size: 17px;
              font-weight: 700;
              font-family: 'Open Sans';
            ">
            {{maskValue(currStoreList.contact_no,'(###) ###-####')}}
            </span>
          </div>
          <div v-if="currStoreList.contact_no != 'Hours currently unavailable. Contact the merchant'" style="
              position: absolute;
              right: 23px;
          ">
            <a :href="`tel:+${currStoreList.contact_no}`" style="color: #000000">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="25" height="25" viewBox="0 0 64 64" fill="none">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H64V64H0V0Z" fill="url(#pattern0_14505_660)"/>
            <defs>
            <pattern id="pattern0_14505_660" patternContentUnits="objectBoundingBox" width="1" height="1">
            <use xlink:href="#image0_14505_660" transform="scale(0.015625)"/>
            </pattern>
            <image id="image0_14505_660" width="64" height="64" xlink:href="data:image/png;base64,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"/>
            </defs>
            </svg>
            </a>
          </div>
        </div>
        <!-------- End   Phone Details ------------------->
        </div>
        <!---------------------------------------------------------------------------------------->
        <!------------------------------------ Display Div Detail End --------------------------------->
        <!---------------------------------------------------------------------------------------------->
        <!--------------------------------------------------------------------------->
        <!------------------------------------ TAB 1 --------------------------------->
        <!------------------------------------------------------------------------------>

        <div v-if="currentTab === 'nearby' && !displayStoreDetail">
          <div class="white-body">
            <div class="card content-div" v-if="!storeList.length">
              <div class="no-store-style" id="headingTwo">
                <div class="row justify-content-center align-self-center">
                  <div class="col-sm-6">
                    <label>
                      <b>No Stores Available!</b>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="mx-auto history-div" v-if="storeList.length">
            <div
              class="accordion accordian-style"
              id="accordionExample"
              v-for="(terminals, index) in storeList"
              :key="index"
            >
              <div
                :id="'nearby-store-card-' + terminals.id"
                v-bind:class="[
                  terminals.is_generic == 1
                    ? 'card content-div side-green'
                    : 'card content-div side-yellow',
                  '',
                ]"
              >
                <div
                  class="card-header"
                  id="headingTwo"
                  v-on:click="clickOnStore(terminals)"
                  style="
                    margin-left: 10px;
                    background-color: #ffffff;
                    border-bottom: transparent !important;
                  "
                >
                  <div class="row">
                    <div class="col-10" style="padding-left: 0px !important">
                      <label
                        class="transaction-amount-heading"
                        style="text-align: left"
                        >{{ terminals.retailer }}</label
                      >
                    </div>
                  </div>
                  <div class="row h-25">
                    <label class="store-name-label" v-html="storeAddress(terminals)"></label>
                  </div>

                  <div
                    class="row h-25"
                    style="
                      margint-top: 5px;
                      margint-bottom: 5px;
                      margin-top: 5px;
                    "
                  >
                    <div
                      class="col-10"
                      style="padding-right: 0px; padding-left: 0px"
                    >
                      <svg
                        version="1.1"
                        id="Layer_1"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        x="0px"
                        y="0px"
                        viewBox="0 0 307 495"
                        style="
                          enable-background: new 0 0 307 495;
                          float: left !important;
                        "
                        xml:space="preserve"
                        height="20"
                        width="20"
                        fill="#7f7f7f"
                      >
                        <g>
                          <path
                            d="M153,0c85,0,154,69,154,153c0,88-90,191-154,243C90,344,0,241,0,153C0,69,69,0,153,0z M153,27C84,27,27,84,27,153
		c0,71,76,164,126,208c51-44,127-137,127-208C280,84,223,27,153,27z"
                          />
                          <path
                            d="M153,88c36,0,66,30,66,65c0,36-30,66-66,66c-35,0-65-30-65-66C88,118,118,88,153,88z M153,115c-21,0-38,17-38,38
		c0,22,17,38,38,38c22,0,38-16,38-38C191,132,175,115,153,115z"
                          />
                        </g>
                      </svg>

                      <label
                        class="Open-style"
                        style="color: #7f7f7f; margin-left: 3px !important"
                        >{{
                          distance(
                            terminals.lat,
                            terminals.long,
                            currentLat,
                            currentLong,
                            ""
                          )
                        }}</label
                      >
                    </div>
                    <!----1-->
                    <div
                      class="col-10"
                      style="padding-right: 0px; padding-left: 0px"
                    >
                      <svg
                        version="1.1"
                        id="Layer_1"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        x="0px"
                        y="0px"
                        viewBox="0 0 24 30"
                        style="enable-background: new 0 0 24 30; float: left"
                        xml:space="preserve"
                        height="18"
                        width="18"
                        fill="#7f7f7f"
                      >
                        <path
                          class="st0"
                          d="M12,24C5.4,24,0,18.6,0,12S5.4,0,12,0s12,5.4,12,12S18.6,24,12,24z M12,2C6.5,2,2,6.5,2,12s4.5,10,10,10
	s10-4.5,10-10S17.5,2,12,2z M14.2,15.7l-2.9-2.9C11.1,12.6,11,12.3,11,12V7c0-0.6,0.4-1,1-1s1,0.4,1,1v4.5l2.7,2.7
	c0.4,0.4,0.4,1.1,0,1.5C15.3,16.1,14.6,16.1,14.2,15.7L14.2,15.7z"
                        />
                      </svg>
                      <label v-if="terminals.timing_arr" class="Open-time-text-style">{{
                        getOpeningHoursStatus(terminals)
                      }}</label>
                      <label v-if="!terminals.timing_arr" class="not-available-time-text-style">{{
                        getOpeningHoursStatus(terminals)
                      }}</label>
                      <label v-if="terminals.timing_arr" class="Open-style"
                        >{{ getOpeningHoursTiming(terminals) }}</label
                      >
                    </div>
                    <!---3-->
                    <div class="col-2" style="float: right">
                      <a v-on:click="clickOnStore(terminals)">
                        <svg
                          version="1.1"
                          id="Layer_1"
                          xmlns="http://www.w3.org/2000/svg"
                          xmlns:xlink="http://www.w3.org/1999/xlink"
                          x="0px"
                          y="0px"
                          viewBox="0 0 2540 3175"
                          style="
                            enable-background: new 0 0 2540 3175;
                            -webkit-transform: scaleX(-1);
                            transform: scaleX(-1);
                          "
                          xml:space="preserve"
                          height="20"
                          width="20"
                          fill="#7f7f7f"
                        >
                          <g>
                            <path
                              d="M1878,2270c39,39,39,104,0,143c-40,40-104,40-144,0L662,1342c-39-40-39-104,0-144L1734,127c40-40,104-40,144,0
		c39,39,39,104,0,143L878,1270L1878,2270z"
                            />
                          </g>
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!--------------------------------------------------------------------------->
        <!------------------------------------ TAB 2 --------------------------------->
        <!------------------------------------------------------------------------------>
        <div v-if="currentTab === 'all' && !displayStoreDetail">
          <div class="white-body">
            <div class="card content-div" v-if="!storeList.length">
              <div class="no-store-style" id="headingTwo">
                <div class="row justify-content-center align-self-center">
                  <div class="col-sm-6">
                    <label>
                      <b>No Stores Available!</b>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="mx-auto history-div">
            <div
              class="accordion accordian-style"
              id="accordionExample"
              v-for="(terminals, index) in storeList"
              :key="index"
            >
              <div
                v-bind:class="[
                  terminals.is_generic == 1
                    ? 'card content-div side-green'
                    : 'card content-div side-yellow',
                  '',
                ]"
                style="height: 400px !important"
                v-if="!storeList.length"
              >
                <div
                  class="card-header"
                  id="headingTwo"
                  style="
                    margin-left: 10px;
                    background-color: #ffffff;
                    border-bottom: transparent !important;
                  "
                >
                  <div class="row">
                    <div
                      class="col-9 col-md-10"
                      style="padding-left: 0px !important; padding-right: 0px"
                    >
                      <label
                        class="transaction-amount-heading"
                        style="text-align: left"
                        >No Stores Available!</label
                      >
                    </div>
                  </div>
                </div>
              </div>

              <div
                :id="'all-store-card-' + terminals.id"
                v-if="storeList.length"
                v-bind:class="[
                  terminals.is_generic == 1
                    ? 'card content-div side-green'
                    : 'card content-div side-yellow',
                  '',
                ]"
              >
                <div
                  class="card-header"
                  id="headingTwo"
                  v-on:click="clickOnStore(terminals)"
                  style="
                    margin-left: 10px;
                    background-color: #ffffff;
                    border-bottom: transparent !important;
                  "
                >
                  <div class="row">
                    <div class="col-10" style="padding-left: 0px !important">
                      <label
                        class="transaction-amount-heading"
                        style="text-align: left"
                        >{{ terminals.retailer }}</label
                      >
                    </div>
                  </div>

                  <div class="row h-25">
                    <label class="store-name-label" v-html="storeAddress(terminals)"></label>
                  </div>
                  <div
                    class="row h-25"
                    style="
                      margint-top: 5px;
                      margint-bottom: 5px;
                      margin-top: 5px;
                    "
                  >
                    <div
                      class="col-10"
                      style="padding-right: 0px; padding-left: 0px"
                    >
                      <svg
                        version="1.1"
                        id="Layer_1"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        x="0px"
                        y="0px"
                        viewBox="0 0 307 495"
                        style="
                          enable-background: new 0 0 307 495;
                          float: left !important;
                        "
                        xml:space="preserve"
                        height="20"
                        width="20"
                        fill="#7f7f7f"
                      >
                        <g>
                          <path
                            d="M153,0c85,0,154,69,154,153c0,88-90,191-154,243C90,344,0,241,0,153C0,69,69,0,153,0z M153,27C84,27,27,84,27,153
		c0,71,76,164,126,208c51-44,127-137,127-208C280,84,223,27,153,27z"
                          />
                          <path
                            d="M153,88c36,0,66,30,66,65c0,36-30,66-66,66c-35,0-65-30-65-66C88,118,118,88,153,88z M153,115c-21,0-38,17-38,38
		c0,22,17,38,38,38c22,0,38-16,38-38C191,132,175,115,153,115z"
                          />
                        </g>
                      </svg>

                      <label
                        class="Open-style"
                        style="color: #7f7f7f; margin-left: 3px !important"
                        >{{
                          distance(
                            terminals.lat,
                            terminals.long,
                            currentLat,
                            currentLong,
                            ""
                          )
                        }}</label
                      >
                    </div>
                    <!----1-->
                    <div
                      class="col-10"
                      style="padding-right: 0px; padding-left: 0px"
                    >
                      <svg
                        version="1.1"
                        id="Layer_1"
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        x="0px"
                        y="0px"
                        viewBox="0 0 24 30"
                        style="enable-background: new 0 0 24 30; float: left"
                        xml:space="preserve"
                        height="18"
                        width="18"
                        fill="#7f7f7f"
                      >
                        <path
                          class="st0"
                          d="M12,24C5.4,24,0,18.6,0,12S5.4,0,12,0s12,5.4,12,12S18.6,24,12,24z M12,2C6.5,2,2,6.5,2,12s4.5,10,10,10
	s10-4.5,10-10S17.5,2,12,2z M14.2,15.7l-2.9-2.9C11.1,12.6,11,12.3,11,12V7c0-0.6,0.4-1,1-1s1,0.4,1,1v4.5l2.7,2.7
	c0.4,0.4,0.4,1.1,0,1.5C15.3,16.1,14.6,16.1,14.2,15.7L14.2,15.7z"
                        />
                      </svg>
                      <label v-if="terminals.timing_arr" class="Open-time-text-style">{{
                        getOpeningHoursStatus(terminals)
                      }}</label>
                      <label v-if="!terminals.timing_arr" class="not-available-time-text-style">{{
                        getOpeningHoursStatus(terminals)
                      }}</label>
                      <label v-if="terminals.timing_arr" class="Open-style"
                        >{{ getOpeningHoursTiming(terminals) }}</label>
                    </div>
                    <!---3-->

                    <div class="col-2" style="float: right">
                      <a v-on:click="clickOnStore(terminals)">
                        <svg
                          version="1.1"
                          id="Layer_1"
                          xmlns="http://www.w3.org/2000/svg"
                          xmlns:xlink="http://www.w3.org/1999/xlink"
                          x="0px"
                          y="0px"
                          viewBox="0 0 2540 3175"
                          style="
                            enable-background: new 0 0 2540 3175;
                            -webkit-transform: scaleX(-1);
                            transform: scaleX(-1);
                          "
                          xml:space="preserve"
                          height="20"
                          width="20"
                          fill="#7f7f7f"
                        >
                          <g>
                            <path
                              d="M1878,2270c39,39,39,104,0,143c-40,40-104,40-144,0L662,1342c-39-40-39-104,0-144L1734,127c40-40,104-40,144,0
		c39,39,39,104,0,143L878,1270L1878,2270z"
                            />
                          </g>
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-------------------------- FILTER MODAL ---------------------------->
      <div>
        <b-modal
          ref="filter-modal"
          hide-footer
          v-b-modal.modal-center
          modal-backdrop
          hide-header
          no-close-on-backdrop
          id="pay-modal-center"
          centered
          title="BootstrapVue"
        >
          <div class="color">
            <div class="purchaserpower-modal-text">
              <div class="row">
                <div class="col-1">
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 59 55" height="20" width="20" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H58.1132V55H0V0Z" fill="url(#pattern0_14505_609)"></path><defs><pattern id="pattern0_14505_609" patternContentUnits="objectBoundingBox" width="1" height="1"><use xlink:href="#image0_14505_609" transform="scale(0.0151515 0.015873)"></use></pattern><image id="image0_14505_609" width="66" height="63" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEIAAAA/CAYAAABU6B73AAAABHNCSVQICAgIfAhkiAAABSdJREFUeF7tm0mIVEcYx2eixjUR1IOoIJ70ILkK8SJ48eKOoiJqDO77vosrqIh4EBHDhCCCmIOi4MHdgwvu+4Z6UHA5iKigEqPR/0+qoPOsqu7pfm+m6a4P/tPdr95Uf/Wvb6t61bU1NTUthIHC70IX4aNQDdJMg3wn/C3U1erPCGG30KQaRu8Z4yaIOC30rmISGPpDiLgp9KxyIp5CxBJhbZW7xl6IaC9sEkYLPwYs463anhvCmpa5Bf0n/f4RfhY6CYzTJ5fVMMne0FYfVgnTBKKpS57q4lLjSnxBuQpj+iB8EpYLgwKKXlLbVOFiLlM/GTKm69VlGV90neyCK0FKuctvUhCX7+xRFBKY+Au0J02mja6tFOYJP3g6OKHrkHW3TJlgEhcaa2ju0fGKrk8UcItv4vIdzH6FMFvwxYJjapsl3CkzMnDrRQJW28qjGxYww1pCiAjaLBlYhi/QQMbMMrIMJm2xIQLLdgkkTBauJhtD0ZQAipvMDcz6YbXhZ48a2TJwY9xhmeAjATeYIlx06RoiwloGnc8XfDEDy8DU7jUSGehFNlsg+LKZ1xKszvmI4D4YXiXMCZBx3LD9oBHIgATigo8EssME4VpIt0KI4P9bCwRQ3MRVZ5BaySa4yf0GIgPdsdaQJZwzE3Q9n06FEkE/pKL1Am7gqzNOqo0CJWsyWCmTGVIhgcHVhwjubymsM4NlHyMpWMYRgdSaFRmQgCuE3AFLoNahXihI6ksEnWING4VJhhjXFx3VRVJr2gGU7yZwkyaphF0Tcd5M1HcpMsRIMUTQH9ZgLQMrcUnaFSjfSYrEEnzFEpaAawYDo0vZYomgLxszyM0+xdIquuifrEWGCJFAsXQjNPO+tlKIsJaxRm8IoK6YwT0EUNzkVjEKmoFjCbiDb+1AnUCKLIoE9CqVCEsGqzzI8Cla7NqEtI0lkCZ9RFMxstJkp61oSYMIvtxmE8jw7WdQdGEZhS7UcAHWOpAQWkWOK5WEtCzCzgKKWzfx7XRhGaxqb+eZOvoiO4QCIxUjS+l6ZQff96ZlEbZ/zHe1QKFT7KoVd6CCDVlCyTEhSUjaRNA/AyFmMPO+/reojZL9vWOG+uraAdOPawKJCVhCwcVSHuv71pwFEfRLscN+IdHeJY91kc3iMwLVqBViDSmS/3XJWV2kasUtUpWsiEBJVq226HIFUNLdXwKbrFbYUd8uDHeMkmIJEpz7CaWykiUR6Mbmzh/CMIeimPefwuectg56v1MYnLj/pT6TcfaUOmDf/2dNBN+7QSD65wruwEPnXQ4iduja0MT9T/QZ4tgRy0Qagoit0hyTLoUIHh+whjiYCQvqNBJhmI1ERCL+72TRIqJFRItwJp7oGtE1omtE1whVpTFGxBgRY0SMETFGFLB2j8EyBssYLGOwLNdgOU6KcaSZA+RW2MV2bd5W7J4lAx9viPi32ongKRfPPA4Jr4xltNMrzzWS2/kVYRHbNDCOHbqEZ58cF8BFOFDCD+t40jUqcXNFELFZg+KcQ0jeqPGUsF8YIgyoRCL6GRfgp5T5hJ8fEjyTJ+YqwiIYPLPMMYFeQujnUj6iKoYIBthRGGlI+VWvvkPuLjIqigg7wK56018gM/TJ5yum/ZleefbJAZJMpCEWXT7Fu6mBoMhZCCwkJC/USObZlwkL6rQxibBj6q43HBcaIxBDXMKxgLHCqUomwo6th7EQfqv+i5D7W/U6feaU3etqIIIxcsQIq8ANODXD+UoKLc5ipX5uKpfUrxbmc4OgFonbAAAAAElFTkSuQmCC"></image></defs></svg>
                </div>
                <div class="col-9" style="padding-left: 5px; margin-top: 3px">
                  <label class="store-address ml-3">Filter</label>
                </div>
              </div>
              <hr
              style="
                margin-top: 0.5rem;
                color:#000000;
                margin-bottom: 0.5rem;"
              >
              <div class="row"
                style="color:#000000;
                font-weight:700;"
              >
                  <span
                  style="margin-left:21px;">
                    Store Hours
                  </span>
              </div>
              <div class="row mt-3">
                <div 
                  style="display:flex;"
                >
                  <button :class="open24HoursEnable?'store-button-option-selected':'store-button-option-not-selected'" @click="open24HoursEnable=!open24HoursEnable">
                    Open 24 Hours Per Day
                  </button>
                  <button :class="openNowEnable?'store-button-option-selected':'store-button-option-not-selected'" @click="openNowEnable =! openNowEnable">
                      Open Now
                  </button>
                </div>
              </div>
              <div 
                @click="hideModal()"
                style="
                  padding: 17px 30px;
                  text-align: center;
                  background-color: rgb(0, 0, 0);
                  color: rgb(255, 255, 255);
                  border-radius: 7px;
                  margin-top: 17px;
                  font-weight:600;
                  font-family: Montserrat;
                ">
                  Show Stores
              </div>
            </div>
          </div>
        </b-modal>
      </div>
    </div>
    <div v-if="!isLoading && showStoreCompleteDetail"
    style="
      position: absolute;
      top: 85px;
      width: 100%;
    ">
        <!---------------------------------------------------------------------------------------->
        <!------------------------------------ Display Div Detail Start --------------------------------->
        <!------------------------------------------------------------------------------------------------>
        <div
        class="container"
          v-if="displayStoreDetail"
          style="
            background-color:#ffffff;
          "
        >
        <!-------- Header Section Start --------->
        <div>
            <div class="row" style="width:100%;margin-left:0px;background-color:#ffffff;">
              <div class="col-12 text-left" style="font-weight:bold;">{{currStoreList.retailer}}</div>
            </div>
        </div>
        <!-------- Header Section End ----------->
        <!-------- starting Store Content ------------->  
        <div>
        <!-------- Starting Horizontal Content ----------->
          <hr class="storeHorizontalRowContent1">
        <!-------- Ending Horizontal Content ------------->
        <!-------- Start Logo Content -------------------->
        <div style="display:flex;width:100%;">
          <svg v-if="currStoreList.is_generic == 1" style="margin-left: 20px;" xmlns="http://www.w3.org/2000/svg" width="43" height="43" viewBox="0 0 78 78" fill="none">
          <path d="M71.5422 38.7711C71.5422 56.8701 56.8701 71.5422 38.7711 71.5422C20.6721 71.5422 6 56.8701 6 38.7711C6 20.6721 20.6721 6 38.7711 6C56.8701 6 71.5422 20.6721 71.5422 38.7711Z" fill="black"/>
          <path d="M60.337 47.6219C63.0863 41.782 61.9611 36.1979 61.6426 34.7166C61.2817 33.0435 60.3688 29.0898 57.0464 25.5411C55.4639 23.8736 53.5846 22.5181 51.5054 21.5448C48.7031 20.234 46.3147 19.9783 45.0091 19.8504C39.6273 19.3176 35.1796 20.8948 32.6958 22.0244C33.9051 20.8612 35.2874 19.894 36.7931 19.1577C38.1237 18.5192 39.5341 18.064 40.986 17.8043C43.3633 17.3482 45.8006 17.3014 48.1935 17.6658C49.2975 17.8363 53.9044 18.6355 58.5006 22.2162C65.1986 27.4274 67.9479 34.6633 66.3875 42.9863C64.0098 55.7211 51.8451 63.1808 39.5105 59.8985C35.8802 58.9288 32.0589 56.6376 30.3605 54.3997C30.4454 54.3997 30.5409 54.3783 30.594 54.4103C31.0186 54.6874 31.4326 54.9858 31.8678 55.2629C38.7675 59.6108 45.837 59.6428 52.8641 55.7531C53.7539 55.2594 54.5992 54.6888 55.3905 54.048C58.4369 51.5756 59.8275 48.6983 60.337 47.6219Z" fill="white"/>
          <path d="M66.9711 48.3891C66.8967 48.8367 66.7694 49.4761 66.5677 50.2327C65.1028 55.6464 61.7591 59.0672 60.3261 60.4952C55.0824 65.7277 48.9788 67.241 46.187 67.9017C40.4444 69.2551 35.8057 68.6796 34.1391 68.4132C28.1417 67.4541 24.0231 65.0244 22.7599 64.2251C20.1829 62.6006 17.8731 60.5837 15.9133 58.2466C13.6245 55.5283 11.8314 52.4258 10.6165 49.0818C10.3086 48.2293 9.14099 44.883 8.84377 40.3006C8.56778 35.9314 9.22591 32.6704 9.38513 31.9138C9.90426 29.4419 10.7429 27.0487 11.8796 24.795C12.4741 23.6441 15.4993 18.0173 22.1867 13.8186C23.7365 12.8381 27.7914 10.5363 33.5341 9.66241C35.052 9.42796 48.1402 7.61631 57.5132 15.854C61.8547 19.6691 64.4129 24.7737 64.8162 25.5943C65.4372 26.8618 65.9762 28.1682 66.4297 29.5053C66.2811 29.3028 65.1771 27.747 65.1453 27.7043C61.0692 21.6406 56.3349 18.7846 56.3349 18.7846C54.5448 17.7288 52.62 16.9222 50.6135 16.3868C44.2339 14.6604 38.8628 16.195 37.589 16.5787C30.1161 18.8272 25.6896 24.8057 25.4349 25.1574C20.9235 31.4022 21.0191 37.9242 21.104 39.7358C21.3906 45.8315 24.0443 50.1368 25.2544 51.8739C25.35 51.9911 25.488 52.1723 25.6578 52.3854C25.7852 52.5559 25.9126 52.7264 26.0399 52.8969C29.2032 57.0744 33.3961 59.9837 38.056 61.433C41.5287 62.4991 45.1983 62.7538 48.7841 62.1775C52.3698 61.6012 55.7768 60.2093 58.7445 58.1081C61.3558 56.2538 62.9374 54.3249 63.914 53.1314C65.209 51.5542 66.1537 49.9983 66.4615 49.3802C66.504 49.3056 66.9498 48.3891 66.9711 48.3891Z" fill="#1B9142"/>
          <path d="M32.8439 48.4851C27.2392 48.4851 24.4369 44.7659 24.4369 39.0645C24.4369 33.1713 27.3878 29.6226 33.1624 29.6226C35.3915 29.6226 37.1854 30.1555 38.5654 31.2851C39.8604 32.4467 40.5822 34.0239 40.7627 36.0274H38.5123C37.7056 36.0274 37.1217 35.6544 36.7714 34.919C36.1558 33.6083 34.9457 32.9369 33.173 32.9369C29.7125 32.9369 28.1734 35.3879 28.1734 39.0752C28.1734 42.6559 29.6488 45.1815 33.0562 45.1815C35.3915 45.1815 36.729 43.8921 37.1324 41.8673H40.752C40.423 46.0874 37.4614 48.4851 32.8439 48.4851Z" fill="white"/>
          <path d="M52.1951 29.6652H45.8368C44.7647 29.6652 43.9155 30.603 43.9155 31.6793V48.4211H47.7369V41.7393H52.4711C56.2182 41.7393 58.2244 39.3948 58.2244 35.6969C58.2244 31.7965 56.1651 29.6652 52.1951 29.6652ZM51.7599 38.3504H48.0341V32.9155H51.951C53.7343 32.9155 54.6472 33.8213 54.6472 35.6436C54.6578 37.4659 53.7025 38.3824 51.7599 38.3504Z" fill="#1B9142"/>
          <path d="M38.7711 74.5422C58.5269 74.5422 74.5422 58.5269 74.5422 38.7711C74.5422 19.0153 58.5269 3 38.7711 3C19.0153 3 3 19.0153 3 38.7711C3 58.5269 19.0153 74.5422 38.7711 74.5422Z" stroke="white" stroke-width="6"/>
          </svg>
          <svg v-if="currStoreList.is_generic == 0" style="margin-left:20px;" xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 75 75" fill="none">
          <path d="M75 37.5C75 58.2107 58.2107 75 37.5 75C16.7893 75 0 58.2107 0 37.5C0 16.7893 16.7893 0 37.5 0C58.2107 0 75 16.7893 75 37.5Z" fill="black"/>
          <path d="M62.1779 47.6281C65.3239 40.9455 64.0363 34.5555 63.6719 32.8605C63.2589 30.9459 62.2143 26.4217 58.4124 22.3609C56.6016 20.4528 54.4511 18.9017 52.0719 17.788C48.8651 16.288 46.1321 15.9954 44.6381 15.849C38.4797 15.2393 33.3903 17.0441 30.548 18.3367C31.9318 17.0057 33.5136 15.899 35.2366 15.0564C36.7592 14.3258 38.3731 13.8049 40.0345 13.5077C42.7549 12.9858 45.5438 12.9322 48.2821 13.3492C49.5453 13.5443 54.817 14.4589 60.0765 18.5562C67.7411 24.5194 70.887 32.7995 69.1015 42.3234C66.3806 56.8959 52.4606 65.4321 38.3461 61.6762C34.192 60.5665 29.8192 57.9447 27.8757 55.3838C27.9729 55.3838 28.0822 55.3594 28.1429 55.396C28.6288 55.7131 29.1025 56.0545 29.6005 56.3716C37.4959 61.347 45.5855 61.3835 53.6266 56.9325C54.6448 56.3676 55.6121 55.7147 56.5175 54.9814C60.0036 52.1523 61.5948 48.8597 62.1779 47.6281Z" fill="white"/>
          <path d="M69.7693 48.5059C69.6843 49.0181 69.5385 49.7498 69.3077 50.6156C67.6315 56.8104 63.8053 60.7249 62.1655 62.359C56.165 68.3465 49.1807 70.0781 45.9861 70.8342C39.4148 72.3829 34.1067 71.7244 32.1996 71.4195C25.3368 70.322 20.6239 67.5417 19.1784 66.6271C16.2295 64.7682 13.5864 62.4602 11.3438 59.7859C8.72471 56.6753 6.67285 53.1251 5.28264 49.2986C4.93039 48.323 3.59425 44.4939 3.25415 39.2503C2.93833 34.2505 3.69143 30.519 3.87363 29.6531C4.46767 26.8246 5.42728 24.0861 6.72809 21.5072C7.40831 20.1902 10.8701 13.7514 18.5225 8.94678C20.2959 7.82488 24.9359 5.19085 31.5073 4.1909C33.2443 3.92262 48.2211 1.84954 58.9466 11.2759C63.9146 15.6416 66.8419 21.4828 67.3035 22.4218C68.0141 23.8722 68.6308 25.3671 69.1498 26.8972C68.9797 26.6655 67.7165 24.8851 67.6801 24.8363C63.0157 17.8976 57.5983 14.6294 57.5983 14.6294C55.5499 13.4213 53.3474 12.4983 51.0513 11.8857C43.7511 9.91015 37.6049 11.6662 36.1473 12.1052C27.5961 14.6782 22.5309 21.5194 22.2394 21.9218C17.0771 29.0678 17.1864 36.5309 17.2835 38.604C17.6115 45.5792 20.6482 50.5058 22.0329 52.4936C22.1422 52.6277 22.3001 52.835 22.4945 53.0789C22.6402 53.274 22.786 53.4691 22.9317 53.6642C26.5515 58.4445 31.3494 61.7736 36.6818 63.4321C40.6556 64.652 44.8547 64.9435 48.9579 64.284C53.0611 63.6246 56.9597 62.0317 60.3556 59.6274C63.3437 57.5055 65.1535 55.2983 66.271 53.9325C67.7529 52.1277 68.834 50.3473 69.1862 49.64C69.2348 49.5547 69.745 48.5059 69.7693 48.5059Z" fill="#FF9F20"/>
          <path d="M30.7176 48.6158C24.3041 48.6158 21.0974 44.3599 21.0974 37.8358C21.0974 31.0922 24.4742 27.0314 31.082 27.0314C33.6328 27.0314 35.6855 27.6411 37.2646 28.9338C38.7465 30.263 39.5725 32.0678 39.779 34.3604H37.2039C36.2807 34.3604 35.6127 33.9335 35.2118 33.0921C34.5073 31.5922 33.1226 30.8239 31.0941 30.8239C27.1343 30.8239 25.373 33.6287 25.373 37.848C25.373 41.9454 27.0614 44.8355 30.9605 44.8355C33.6328 44.8355 35.1632 43.3599 35.6248 41.043H39.7668C39.3903 45.872 36.0014 48.6158 30.7176 48.6158Z" fill="white"/>
          <path d="M52.8611 27.0801H45.5853C44.3585 27.0801 43.3867 28.1532 43.3867 29.3848V48.5425H47.7595V40.8965H53.1769C57.4647 40.8965 59.7604 38.2137 59.7604 33.9822C59.7604 29.519 57.404 27.0801 52.8611 27.0801ZM52.3631 37.0186H48.0996V30.7994H52.5818C54.6224 30.7994 55.667 31.8359 55.667 33.9212C55.6792 36.0065 54.586 37.0552 52.3631 37.0186Z" fill="#FF9F20"/>
          </svg>
          <div>
          <svg v-if="canpayDisplay" @click="canpayDisplay = !canpayDisplay" 
          style="
          transform: rotate(180deg);
          position: relative;
          top: 8px;
          left: 5px;
          " xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 38 22" fill="none">
          <path d="M17.2322 20.7678C18.2085 21.7441 19.7915 21.7441 20.7678 20.7678L36.6777 4.85786C37.654 3.88155 37.654 2.29864 36.6777 1.32233C35.7014 0.346019 34.1184 0.346019 33.1421 1.32233L19 15.4645L4.85786 1.32233C3.88155 0.34602 2.29864 0.34602 1.32233 1.32233C0.346019 2.29864 0.346019 3.88155 1.32233 4.85787L17.2322 20.7678ZM16.5 17L16.5 19L21.5 19L21.5 17L16.5 17Z" fill="black"/>
          </svg>
          <svg v-if="!canpayDisplay" @click="canpayDisplay = !canpayDisplay"
          style="
          position: relative;
          top: 8px;
          left: 5px;
          "
            xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 38 22" fill="none">
          <path d="M17.2322 20.7678C18.2085 21.7441 19.7915 21.7441 20.7678 20.7678L36.6777 4.85786C37.654 3.88155 37.654 2.29864 36.6777 1.32233C35.7014 0.346019 34.1184 0.346019 33.1421 1.32233L19 15.4645L4.85786 1.32233C3.88155 0.34602 2.29864 0.34602 1.32233 1.32233C0.346019 2.29864 0.346019 3.88155 1.32233 4.85787L17.2322 20.7678ZM16.5 17L16.5 19L21.5 19L21.5 17L16.5 17Z" fill="black"/>
          </svg>
          </div>
          <svg  v-if="currStoreList.is_generic == 0" style="margin-left:37px;position:relative;" xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 67 67" fill="none">
          <path d="M33.5272 33.6119C31.8424 35.2808 30.1707 36.9496 28.4859 38.6184C22.5173 44.5767 16.5356 50.535 10.567 56.5064C10.3711 56.7019 10.1621 56.8845 9.87478 57.1452C8.97361 56.1022 8.05939 55.1374 7.24964 54.0944C3.08337 48.788 0.732498 42.7775 0.197022 36.05C0.170901 35.8414 0.131719 35.6328 0.0794778 35.4242V33.4685C11.2331 33.4685 22.3736 33.4685 33.5272 33.4555V33.6119Z" fill="black"/>
          <path d="M66.9615 33.4692C55.8602 33.4692 44.7458 33.4692 33.6444 33.4561C33.6053 33.4561 33.553 33.4692 33.5138 33.4692C33.6836 33.2736 33.8403 33.065 34.0232 32.8825C41.5329 25.3857 49.0427 17.8889 56.5524 10.3922C56.7222 10.2227 56.892 10.0662 57.1532 9.83154C57.9107 10.692 58.6812 11.4743 59.3734 12.3348C63.5136 17.4196 65.982 23.2084 66.7134 29.7273C66.7917 30.4183 66.8832 31.0963 66.9615 31.7742V33.4692Z" fill="black"/>
          <path d="M57.1271 9.79142C49.2778 17.6272 41.4154 25.4629 33.5661 33.2987C33.54 33.3247 33.5138 33.3638 33.4877 33.403C33.4616 33.1422 33.4224 32.8815 33.4224 32.6207C33.4224 22.0209 33.4224 11.4212 33.4224 0.821384C33.4224 0.586703 33.4355 0.352022 33.4485 0C34.5979 0.0651892 35.6949 0.0912649 36.792 0.195568C43.3222 0.860498 49.1602 3.22035 54.293 7.31423C54.8284 7.74448 55.377 8.16169 55.9255 8.59194L57.1271 9.79142Z" fill="#0DD668"/>
          <path d="M9.80837 9.79221C17.6577 17.628 25.507 25.4767 33.3563 33.3125C33.3824 33.3386 33.4216 33.3646 33.4608 33.3907C33.1996 33.4168 32.9383 33.4559 32.6771 33.4559C22.059 33.4559 11.4409 33.4559 0.822806 33.4559C0.587718 33.4559 0.352631 33.4429 0 33.4298C0.065302 32.2825 0.0914228 31.1873 0.195906 30.0921C0.861987 23.5732 3.22592 17.7453 7.32689 12.6214C7.75788 12.0869 8.17581 11.5393 8.60681 10.9917L9.80837 9.79221Z" fill="#D3FCC8"/>
          <path d="M9.87506 57.2086C17.7244 49.3728 25.5867 41.5371 33.436 33.7013C33.4622 33.6753 33.4883 33.6362 33.5144 33.597C33.5405 33.8578 33.5797 34.1186 33.5797 34.3793C33.5797 44.9791 33.5797 55.5788 33.5797 66.1786C33.5797 66.4133 33.5667 66.648 33.5536 67C32.4043 66.9348 31.3072 66.9087 30.2101 66.8044C23.6799 66.1395 17.8419 63.7797 12.7092 59.6858C12.1737 59.2555 11.6252 58.8383 11.0766 58.4081L9.87506 57.2086Z" fill="#0DD668"/>
          <path d="M57.1786 57.1176C49.3293 49.2819 41.48 41.4331 33.6437 33.5843C33.6176 33.5582 33.5784 33.5322 33.5392 33.5061C33.8004 33.48 34.0616 33.4409 34.3229 33.4409C44.941 33.4409 55.5591 33.4409 66.1772 33.4539C66.4123 33.4539 66.6474 33.467 67 33.48C66.9347 34.6273 66.9086 35.7225 66.8041 36.8177C66.138 43.3366 63.7741 49.1645 59.6731 54.2884C59.2421 54.823 58.8242 55.3706 58.3932 55.9182L57.1786 57.1176Z" fill="#D3FCC8"/>
          <path d="M33.4089 0.0119587C33.4089 11.0941 33.4089 22.1893 33.3958 33.2715C33.3958 33.3106 33.4089 33.3628 33.4089 33.4019C33.213 33.2324 33.004 33.0759 32.8211 32.8934C25.3114 25.3966 17.8017 17.8999 10.2919 10.4031C10.1222 10.2336 9.96544 10.0641 9.73035 9.80338C10.5923 9.04719 11.376 8.27795 12.2379 7.58695C17.3315 3.45395 23.1303 0.989797 29.6605 0.259678C30.3527 0.181451 31.0319 0.0901858 31.711 0.0119587H33.4089Z" fill="#007EE5"/>
          <path d="M33.5269 66.8442C33.5138 66.6225 33.5007 66.4139 33.5007 66.1923C33.5007 55.5143 33.5007 44.8363 33.5007 34.1713C33.5007 33.9758 33.5138 33.7802 33.5269 33.5846C33.6705 33.702 33.8403 33.8193 33.9709 33.9497C41.546 41.4986 49.1079 49.0606 56.6699 56.6095C56.7875 56.7268 56.905 56.8572 57.0617 57.0397C56.9311 57.1831 56.8136 57.3396 56.683 57.47C51.7331 62.1897 45.9081 65.1623 39.1559 66.2966C37.7193 66.5313 36.2695 66.6616 34.8329 66.8442H33.5269Z" fill="#29576C"/>
          </svg>
          <svg  v-if="currStoreList.is_generic == 1" style="margin-left:29px;position:relative;top:4px;" xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 67 67" fill="none">
          <path d="M33.5272 33.6119C31.8424 35.2808 30.1707 36.9496 28.4859 38.6184C22.5173 44.5767 16.5356 50.535 10.567 56.5064C10.3711 56.7019 10.1621 56.8845 9.87478 57.1452C8.97361 56.1022 8.05939 55.1374 7.24964 54.0944C3.08337 48.788 0.732498 42.7775 0.197022 36.05C0.170901 35.8414 0.131719 35.6328 0.0794778 35.4242V33.4685C11.2331 33.4685 22.3736 33.4685 33.5272 33.4555V33.6119Z" fill="black"/>
          <path d="M66.9615 33.4692C55.8602 33.4692 44.7458 33.4692 33.6444 33.4561C33.6053 33.4561 33.553 33.4692 33.5138 33.4692C33.6836 33.2736 33.8403 33.065 34.0232 32.8825C41.5329 25.3857 49.0427 17.8889 56.5524 10.3922C56.7222 10.2227 56.892 10.0662 57.1532 9.83154C57.9107 10.692 58.6812 11.4743 59.3734 12.3348C63.5136 17.4196 65.982 23.2084 66.7134 29.7273C66.7917 30.4183 66.8832 31.0963 66.9615 31.7742V33.4692Z" fill="black"/>
          <path d="M57.1271 9.79142C49.2778 17.6272 41.4154 25.4629 33.5661 33.2987C33.54 33.3247 33.5138 33.3638 33.4877 33.403C33.4616 33.1422 33.4224 32.8815 33.4224 32.6207C33.4224 22.0209 33.4224 11.4212 33.4224 0.821384C33.4224 0.586703 33.4355 0.352022 33.4485 0C34.5979 0.0651892 35.6949 0.0912649 36.792 0.195568C43.3222 0.860498 49.1602 3.22035 54.293 7.31423C54.8284 7.74448 55.377 8.16169 55.9255 8.59194L57.1271 9.79142Z" fill="#0DD668"/>
          <path d="M9.80837 9.79221C17.6577 17.628 25.507 25.4767 33.3563 33.3125C33.3824 33.3386 33.4216 33.3646 33.4608 33.3907C33.1996 33.4168 32.9383 33.4559 32.6771 33.4559C22.059 33.4559 11.4409 33.4559 0.822806 33.4559C0.587718 33.4559 0.352631 33.4429 0 33.4298C0.065302 32.2825 0.0914228 31.1873 0.195906 30.0921C0.861987 23.5732 3.22592 17.7453 7.32689 12.6214C7.75788 12.0869 8.17581 11.5393 8.60681 10.9917L9.80837 9.79221Z" fill="#D3FCC8"/>
          <path d="M9.87506 57.2086C17.7244 49.3728 25.5867 41.5371 33.436 33.7013C33.4622 33.6753 33.4883 33.6362 33.5144 33.597C33.5405 33.8578 33.5797 34.1186 33.5797 34.3793C33.5797 44.9791 33.5797 55.5788 33.5797 66.1786C33.5797 66.4133 33.5667 66.648 33.5536 67C32.4043 66.9348 31.3072 66.9087 30.2101 66.8044C23.6799 66.1395 17.8419 63.7797 12.7092 59.6858C12.1737 59.2555 11.6252 58.8383 11.0766 58.4081L9.87506 57.2086Z" fill="#0DD668"/>
          <path d="M57.1786 57.1176C49.3293 49.2819 41.48 41.4331 33.6437 33.5843C33.6176 33.5582 33.5784 33.5322 33.5392 33.5061C33.8004 33.48 34.0616 33.4409 34.3229 33.4409C44.941 33.4409 55.5591 33.4409 66.1772 33.4539C66.4123 33.4539 66.6474 33.467 67 33.48C66.9347 34.6273 66.9086 35.7225 66.8041 36.8177C66.138 43.3366 63.7741 49.1645 59.6731 54.2884C59.2421 54.823 58.8242 55.3706 58.3932 55.9182L57.1786 57.1176Z" fill="#D3FCC8"/>
          <path d="M33.4089 0.0119587C33.4089 11.0941 33.4089 22.1893 33.3958 33.2715C33.3958 33.3106 33.4089 33.3628 33.4089 33.4019C33.213 33.2324 33.004 33.0759 32.8211 32.8934C25.3114 25.3966 17.8017 17.8999 10.2919 10.4031C10.1222 10.2336 9.96544 10.0641 9.73035 9.80338C10.5923 9.04719 11.376 8.27795 12.2379 7.58695C17.3315 3.45395 23.1303 0.989797 29.6605 0.259678C30.3527 0.181451 31.0319 0.0901858 31.711 0.0119587H33.4089Z" fill="#007EE5"/>
          <path d="M33.5269 66.8442C33.5138 66.6225 33.5007 66.4139 33.5007 66.1923C33.5007 55.5143 33.5007 44.8363 33.5007 34.1713C33.5007 33.9758 33.5138 33.7802 33.5269 33.5846C33.6705 33.702 33.8403 33.8193 33.9709 33.9497C41.546 41.4986 49.1079 49.0606 56.6699 56.6095C56.7875 56.7268 56.905 56.8572 57.0617 57.0397C56.9311 57.1831 56.8136 57.3396 56.683 57.47C51.7331 62.1897 45.9081 65.1623 39.1559 66.2966C37.7193 66.5313 36.2695 66.6616 34.8329 66.8442H33.5269Z" fill="#29576C"/>
          </svg>
        </div>
        <!-------- End Logo Content ---------------------->
        </div>
        <!-------- Start Logo Content ---------------------->
        <div style="display:flex;">
          <div style="font-size:12px;margin-left:15px;font-family:Open Sans;font-weight:600;">Rewards</div>

          <div style="font-size:12px;padding-left:43px;font-family:Open Sans;font-weight:600;">Wheel</div>
        </div>
        <!-------- End Logo Content ---------------------->
        <!-------- Start Reward Wheel Content ------------->
        <div class="text-left" v-if="canpayDisplay">
          <div style="margin-top:10px;">
            <div v-for="(item, index) in showCanPayDetail" :key="index">
            <!----- First Point Start -------->
            <div style="margin-top:10px;">
              <p style="width:100%;margin-bottom:0px;margin-left:15px;font-size:12px;"><span style="font-weight:700;">{{item.cashback_program_name}} </span>   
            <svg v-if="currStoreList.is_generic == 1" xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 78 78" fill="none">
            <path d="M71.5422 38.7711C71.5422 56.8701 56.8701 71.5422 38.7711 71.5422C20.6721 71.5422 6 56.8701 6 38.7711C6 20.6721 20.6721 6 38.7711 6C56.8701 6 71.5422 20.6721 71.5422 38.7711Z" fill="black"/>
            <path d="M60.337 47.6219C63.0863 41.782 61.9611 36.1979 61.6426 34.7166C61.2817 33.0435 60.3688 29.0898 57.0464 25.5411C55.4639 23.8736 53.5846 22.5181 51.5054 21.5448C48.7031 20.234 46.3147 19.9783 45.0091 19.8504C39.6273 19.3176 35.1796 20.8948 32.6958 22.0244C33.9051 20.8612 35.2874 19.894 36.7931 19.1577C38.1237 18.5192 39.5341 18.064 40.986 17.8043C43.3633 17.3482 45.8006 17.3014 48.1935 17.6658C49.2975 17.8363 53.9044 18.6355 58.5006 22.2162C65.1986 27.4274 67.9479 34.6633 66.3875 42.9863C64.0098 55.7211 51.8451 63.1808 39.5105 59.8985C35.8802 58.9288 32.0589 56.6376 30.3605 54.3997C30.4454 54.3997 30.5409 54.3783 30.594 54.4103C31.0186 54.6874 31.4326 54.9858 31.8678 55.2629C38.7675 59.6108 45.837 59.6428 52.8641 55.7531C53.7539 55.2594 54.5992 54.6888 55.3905 54.048C58.4369 51.5756 59.8275 48.6983 60.337 47.6219Z" fill="white"/>
            <path d="M66.9711 48.3891C66.8967 48.8367 66.7694 49.4761 66.5677 50.2327C65.1028 55.6464 61.7591 59.0672 60.3261 60.4952C55.0824 65.7277 48.9788 67.241 46.187 67.9017C40.4444 69.2551 35.8057 68.6796 34.1391 68.4132C28.1417 67.4541 24.0231 65.0244 22.7599 64.2251C20.1829 62.6006 17.8731 60.5837 15.9133 58.2466C13.6245 55.5283 11.8314 52.4258 10.6165 49.0818C10.3086 48.2293 9.14099 44.883 8.84377 40.3006C8.56778 35.9314 9.22591 32.6704 9.38513 31.9138C9.90426 29.4419 10.7429 27.0487 11.8796 24.795C12.4741 23.6441 15.4993 18.0173 22.1867 13.8186C23.7365 12.8381 27.7914 10.5363 33.5341 9.66241C35.052 9.42796 48.1402 7.61631 57.5132 15.854C61.8547 19.6691 64.4129 24.7737 64.8162 25.5943C65.4372 26.8618 65.9762 28.1682 66.4297 29.5053C66.2811 29.3028 65.1771 27.747 65.1453 27.7043C61.0692 21.6406 56.3349 18.7846 56.3349 18.7846C54.5448 17.7288 52.62 16.9222 50.6135 16.3868C44.2339 14.6604 38.8628 16.195 37.589 16.5787C30.1161 18.8272 25.6896 24.8057 25.4349 25.1574C20.9235 31.4022 21.0191 37.9242 21.104 39.7358C21.3906 45.8315 24.0443 50.1368 25.2544 51.8739C25.35 51.9911 25.488 52.1723 25.6578 52.3854C25.7852 52.5559 25.9126 52.7264 26.0399 52.8969C29.2032 57.0744 33.3961 59.9837 38.056 61.433C41.5287 62.4991 45.1983 62.7538 48.7841 62.1775C52.3698 61.6012 55.7768 60.2093 58.7445 58.1081C61.3558 56.2538 62.9374 54.3249 63.914 53.1314C65.209 51.5542 66.1537 49.9983 66.4615 49.3802C66.504 49.3056 66.9498 48.3891 66.9711 48.3891Z" fill="#1B9142"/>
            <path d="M32.8439 48.4851C27.2392 48.4851 24.4369 44.7659 24.4369 39.0645C24.4369 33.1713 27.3878 29.6226 33.1624 29.6226C35.3915 29.6226 37.1854 30.1555 38.5654 31.2851C39.8604 32.4467 40.5822 34.0239 40.7627 36.0274H38.5123C37.7056 36.0274 37.1217 35.6544 36.7714 34.919C36.1558 33.6083 34.9457 32.9369 33.173 32.9369C29.7125 32.9369 28.1734 35.3879 28.1734 39.0752C28.1734 42.6559 29.6488 45.1815 33.0562 45.1815C35.3915 45.1815 36.729 43.8921 37.1324 41.8673H40.752C40.423 46.0874 37.4614 48.4851 32.8439 48.4851Z" fill="white"/>
            <path d="M52.1951 29.6652H45.8368C44.7647 29.6652 43.9155 30.603 43.9155 31.6793V48.4211H47.7369V41.7393H52.4711C56.2182 41.7393 58.2244 39.3948 58.2244 35.6969C58.2244 31.7965 56.1651 29.6652 52.1951 29.6652ZM51.7599 38.3504H48.0341V32.9155H51.951C53.7343 32.9155 54.6472 33.8213 54.6472 35.6436C54.6578 37.4659 53.7025 38.3824 51.7599 38.3504Z" fill="#1B9142"/>
            <path d="M38.7711 74.5422C58.5269 74.5422 74.5422 58.5269 74.5422 38.7711C74.5422 19.0153 58.5269 3 38.7711 3C19.0153 3 3 19.0153 3 38.7711C3 58.5269 19.0153 74.5422 38.7711 74.5422Z" stroke="white" stroke-width="6"/>
            </svg>
            <svg v-if="currStoreList.is_generic == 0" xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="0 0 52 52" fill="none"><path d="M52 26C52 40.3594 40.3594 52 26 52C11.6406 52 0 40.3594 0 26C0 11.6406 11.6406 0 26 0C40.3594 0 52 11.6406 52 26Z" fill="black"></path><path d="M43.11 33.0221C45.2912 28.3888 44.3985 23.9585 44.1459 22.7832C43.8595 21.4558 43.1353 18.3191 40.4993 15.5036C39.2438 14.1806 37.7528 13.1052 36.1032 12.333C33.8798 11.293 31.9849 11.0901 30.9491 10.9887C26.6793 10.5659 23.1506 11.8172 21.1799 12.7135C22.1394 11.7906 23.2361 11.0233 24.4307 10.4391C25.4864 9.93256 26.6053 9.57138 27.7573 9.36533C29.6434 9.00348 31.5771 8.96631 33.4756 9.25542C34.3514 9.39069 38.0065 10.0248 41.653 12.8657C46.9671 17.0001 49.1483 22.741 47.9104 29.3442C46.0239 39.4479 36.3726 45.3663 26.5866 42.7622C23.7064 41.9928 20.6746 40.175 19.3271 38.3994C19.3945 38.3994 19.4703 38.3825 19.5124 38.4079C19.8493 38.6277 20.1777 38.8645 20.523 39.0843C25.9971 42.5339 31.606 42.5593 37.1811 39.4732C37.8871 39.0815 38.5577 38.6289 39.1855 38.1204C41.6025 36.1589 42.7058 33.8761 43.11 33.0221Z" fill="white"></path><path d="M48.3734 33.6308C48.3144 33.9859 48.2134 34.4932 48.0533 35.0935C46.8911 39.3886 44.2383 42.1026 43.1014 43.2355C38.9411 47.3869 34.0986 48.5875 31.8837 49.1117C27.3276 50.1855 23.6473 49.7289 22.3251 49.5175C17.5668 48.7566 14.2992 46.8289 13.297 46.1948C11.2524 44.9059 9.41988 43.3057 7.86505 41.4516C6.04913 39.2949 4.62651 36.8334 3.66263 34.1804C3.4184 33.504 2.49202 30.8491 2.25621 27.2135C2.03725 23.747 2.55939 21.1598 2.68572 20.5595C3.09758 18.5984 3.76291 16.6997 4.66481 14.9116C5.13643 13.9985 7.53661 9.53433 12.8423 6.2031C14.0718 5.42525 17.2889 3.59899 21.8451 2.90569C23.0494 2.71968 33.4333 1.28235 40.8696 7.81799C44.3141 10.8448 46.3437 14.8947 46.6638 15.5458C47.1565 16.5514 47.584 17.5878 47.9439 18.6487C47.826 18.4881 46.9501 17.2536 46.9248 17.2198C43.6909 12.409 39.9348 10.1431 39.9348 10.1431C38.5146 9.30543 36.9875 8.66546 35.3955 8.24073C30.3341 6.87104 26.0727 8.08854 25.0621 8.39292C19.1333 10.1769 15.6214 14.9201 15.4193 15.1991C11.8401 20.1537 11.9159 25.3281 11.9833 26.7654C12.2106 31.6016 14.3161 35.0174 15.2761 36.3955C15.3519 36.4885 15.4614 36.6323 15.5962 36.8014C15.6972 36.9366 15.7983 37.0719 15.8993 37.2072C18.409 40.5215 21.7356 42.8297 25.4327 43.9796C28.1879 44.8254 31.0992 45.0275 33.9441 44.5702C36.789 44.113 39.4921 43.0087 41.8466 41.3416C43.9183 39.8705 45.1731 38.3402 45.9479 37.3932C46.9754 36.1419 47.7249 34.9075 47.9691 34.4171C48.0028 34.3579 48.3565 33.6308 48.3734 33.6308Z" fill="#FF9F20"></path><path d="M21.2975 33.707C16.8508 33.707 14.6275 30.7562 14.6275 26.2328C14.6275 21.5573 16.9688 18.7418 21.5502 18.7418C23.3187 18.7418 24.742 19.1645 25.8368 20.0607C26.8642 20.9823 27.4369 22.2337 27.5801 23.8232H25.7947C25.1546 23.8232 24.6914 23.5273 24.4135 22.9439C23.9251 21.9039 22.965 21.3713 21.5586 21.3713C18.8131 21.3713 17.592 23.3159 17.592 26.2413C17.592 29.0821 18.7626 31.0859 21.4659 31.0859C23.3187 31.0859 24.3798 30.0629 24.6999 28.4565H27.5717C27.3106 31.8046 24.9609 33.707 21.2975 33.707Z" fill="white"></path><path d="M36.6504 18.7755H31.6058C30.7552 18.7755 30.0815 19.5195 30.0815 20.3735V33.6561H33.1133V28.3549H36.8693C39.8422 28.3549 41.4339 26.4948 41.4339 23.561C41.4339 20.4665 39.8001 18.7755 36.6504 18.7755ZM36.3051 25.6663H33.3491V21.3543H36.4567C37.8715 21.3543 38.5958 22.0729 38.5958 23.5187C38.6042 24.9645 37.8463 25.6916 36.3051 25.6663Z" fill="#FF9F20"></path></svg> {{item.cashback_price_value}}% back.
              </p>
            </div>  
            </div>
          <div v-if="showCanPayDetail.length == 0" style="margin-top:8px">
            <p style="margin-left:17px;">
              <span
                style="font-size: 12px;
                  font-family: 'Open Sans';
                  font-weight: 600;
                  margin-left: -2px;"
              >No points program available for the store.</span>
            </p>
          </div>
          </div>
        </div>
        <!-------- End   Reward Wheel Content ------------->
        <!-------- ending Store Content ------------->
        <!-------- Starting Horizontal Content ----------->
          <hr class="storeHorizontalRowContent1">
        <!-------- Ending Horizontal Content ------------->
        <!-------- Starting Clock Hour --------->
        <div style="width:100%;display:flex;padding-left: 14px;">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16" viewBox="0 0 46 46" fill="none">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H46V46H0V0Z" fill="url(#pattern0_14505_715)"/>
            <defs>
            <pattern id="pattern0_14505_715" patternContentUnits="objectBoundingBox" width="1" height="1">
            <use xlink:href="#image0_14505_715" transform="scale(0.0217391)"/>
            </pattern>
            <image id="image0_14505_715" width="46" height="46" xlink:href="data:image/png;base64,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"/>
            </defs>
            </svg>
            <!---- start timing text ----->
            <span v-if="getOpeningHoursStatus(currStoreList) == 'Hours currently unavailable. Contact the merchant'" style="padding-left:10px;font-size: 12px;font-weight:600;padding-bottom:12px;position:relative;top:-1px;text-align:left;"><span class="text-time-green"> {{getOpeningHoursStatus(currStoreList)}}</span><span class="text-time-black"> {{getOpeningHoursTiming(currStoreList)}} </span></span>
            <span v-else style="padding-left:12px;font-size: 12px;font-weight:600;position: relative;top: -1px;"><span class="text-time-green"> {{getOpeningHoursStatus(currStoreList)}}</span><span class="text-time-black"> {{getOpeningHoursTiming(currStoreList)}} </span></span>
            <!---- end starting text ----->
            <!---- Start Nav Accordian ------>
            <div v-if="getOpeningHoursStatus(currStoreList) != 'Hours currently unavailable. Contact the merchant' && canpayTimingDetail" 
                @click="canpayTimingDetail=!canpayTimingDetail" style="
                padding-right: 10px;
                padding-left: 10px;
                position: relative;
                top: -2px;
                transform:rotate(180deg)">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 38 22" fill="none">
            <path d="M17.2322 20.7678C18.2085 21.7441 19.7915 21.7441 20.7678 20.7678L36.6777 4.85786C37.654 3.88155 37.654 2.29864 36.6777 1.32233C35.7014 0.346019 34.1184 0.346019 33.1421 1.32233L19 15.4645L4.85786 1.32233C3.88155 0.34602 2.29864 0.34602 1.32233 1.32233C0.346019 2.29864 0.346019 3.88155 1.32233 4.85787L17.2322 20.7678ZM16.5 17L16.5 19L21.5 19L21.5 17L16.5 17Z" fill="black"/>
            </svg>
            </div>
            <div v-if="getOpeningHoursStatus(currStoreList) != 'Hours currently unavailable. Contact the merchant' && !canpayTimingDetail" 
                @click="canpayTimingDetail=!canpayTimingDetail" style="
                padding-right: 10px;
                padding-left: 10px;
                position: relative;
                top: -4px;">
            <svg  xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 38 22" fill="none">
            <path d="M17.2322 20.7678C18.2085 21.7441 19.7915 21.7441 20.7678 20.7678L36.6777 4.85786C37.654 3.88155 37.654 2.29864 36.6777 1.32233C35.7014 0.346019 34.1184 0.346019 33.1421 1.32233L19 15.4645L4.85786 1.32233C3.88155 0.34602 2.29864 0.34602 1.32233 1.32233C0.346019 2.29864 0.346019 3.88155 1.32233 4.85787L17.2322 20.7678ZM16.5 17L16.5 19L21.5 19L21.5 17L16.5 17Z" fill="black"/>
            </svg>
            </div>
            <!---- End   Nav Accordian ------>
        </div>
        <!-------- start listing timing -------->
        <div v-if="canpayTimingDetail">
          <div v-if="currStoreList && currStoreList.timing_arr && currStoreList.timing_arr.length > 0">
          <div  v-for="(item, index) in currStoreList.timing_arr" :key="index">
            <div style="font-size:12px;margin-bottom:10px;">
                <p v-if="index == 0" style="margin-bottom:0px;display:flex;justify-content: space-between;
                      justify-content: space-between;
                      color:#1B9142;
                      font-family:'Open Sans';
                      margin-left: 44px;
                      margin-right: 17px;
                      font-weight:400;
                      margin-top:8px;
                ">
                  <span>{{item.name}}</span>
                  <span>{{item.timing_text}}</span>
                </p>
                <p v-else style="margin-bottom:0px;display:flex;justify-content: space-between;
                      justify-content: space-between;
                      font-family:'Open Sans';
                      margin-left: 44px;
                      margin-right: 17px;
                      font-weight:400;
                      margin-top:8px;
                ">
                  <span>{{item.name}}</span>
                  <span>{{item.timing_text}}</span>
                </p>
            </div>
          </div>
          </div>
          <div v-else>
            <p
              style="
                      margin-bottom:0px;
                      display:flex;
                      justify-content: space-between;
                      justify-content: space-between;
                      color:#1B9142;
                      font-family:'Open Sans';
                      margin-left: 44px;
                      margin-right: 17px;
                      font-weight:400;
                      margin-top:8px;
              "
            >Hours currently unavailable. Contact the merchant</p>
          </div>
        </div>
        <!-------- end   listing timing -------->
        <div></div>
        <!-------- Ending Clock Hour --------->
        <!-------- Starting Horizontal Content ----------->
          <hr class="storeHorizontalRowContent1" style="margin-top:5px!important;">
        <!-------- Ending Horizontal Content ------------->
        <!-------- Stating the address ------------------->
        <div style="display:flex;justify-content:space-between;">
          <div class="pl-3 text-left">
          <p style="font-size:13px;font-weight:bold;margin-bottom:0px;" v-html="storeAddress(currStoreList)"></p>
          </div>
          <div class="pr-3">
              <svg @click="openGoogleMap(currStoreList)" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="25" height="40" viewBox="0 0 68 76" fill="none">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H68V76H0V0Z" fill="url(#pattern0_14505_713)"/>
              <defs>
              <pattern id="pattern0_14505_713" patternContentUnits="objectBoundingBox" width="1" height="1">
              <use xlink:href="#image0_14505_713" transform="scale(0.0147059 0.0131579)"/>
              </pattern>
              <image id="image0_14505_713" width="68" height="76" xlink:href="data:image/png;base64,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"/>
              </defs>
              </svg>
          </div>
        </div>
        <!-------- Ending  the address ------------------->
        <!-------- Starting Location --------------------->
        <div style="display:flex;">
          <div style="padding-left:10px;padding-top: 5px;">
          <span>
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="30" height="20" viewBox="0 0 40 50" fill="none">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H40V50H0V0Z" fill="url(#pattern0_14505_712)"/>
              <defs>
              <pattern id="pattern0_14505_712" patternContentUnits="objectBoundingBox" width="1" height="1">
              <use xlink:href="#image0_14505_712" transform="scale(0.025 0.02)"/>
              </pattern>
              <image id="image0_14505_712" width="40" height="50" xlink:href="data:image/png;base64,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"/>
              </defs>
              </svg> &nbsp;<span style="font-size: 15px;font-weight:500;">{{
                distance(
                  currStoreList.lat,
                  currStoreList.long,
                  currentLat,
                  currentLong,
                  ""
                )
                }}</span>
          </span>
          </div>
        </div>
        <!-------- Ending Location  ---------------------->
        <!-------- Starting Horizontal Content ----------->
          <hr class="storeHorizontalRowContent1" style="margin-top:12px;">
        <!-------- Ending Horizontal Content ------------->
        <!-------- Start Phone Details ------------------->
        <div style="margin-bottom:10px;">
        <div style="display:flex;justify-content:space-between">
          <div style="padding-left: 15px;padding-bottom:10px;">
            <span style="
              font-size: 17px;
              font-weight: 700;
              font-family: 'Open Sans';
            ">
            {{maskValue(currStoreList.contact_no,'(###) ###-####')}}
            </span>
          </div>
          <div style="padding-right:15px;" v-if="currStoreList.contact_no != 'Hours currently unavailable. Contact the merchant'">
            <a :href="`tel:+${currStoreList.contact_no}`" style="color: #000000">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="25" height="25" viewBox="0 0 64 64" fill="none">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H64V64H0V0Z" fill="url(#pattern0_14505_660)"/>
            <defs>
            <pattern id="pattern0_14505_660" patternContentUnits="objectBoundingBox" width="1" height="1">
            <use xlink:href="#image0_14505_660" transform="scale(0.015625)"/>
            </pattern>
            <image id="image0_14505_660" width="64" height="64" xlink:href="data:image/png;base64,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"/>
            </defs>
            </svg>
            </a>
          </div>
        </div>
        <!-------- End Phone Details ------------------->
        </div>
        </div>
        <!---------------------------------------------------------------------------------------->
        <!------------------------------------ Display Div Detail End --------------------------------->
        <!---------------------------------------------------------------------------------------------->
    </div>
  </div>
</template>

<script>
import Autocomplete from "vuejs-auto-complete";
import moment from "moment";
import Tabs from "vue-tabs-with-active-line";
import MarkerClusterer from "marker-clusterer-plus";
import api from "../../api/participantmerchant.js";
import Loading from "vue-loading-overlay";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue";
import RetailerDetails from "./RetailerDetails.vue";
const TABS = [
  {
    title: "Nearby",
    value: "nearby",
  },
  {
    title: "All",
    value: "all",
  }
];
const properties = ["zoom", "periods", "favourite", "opening_hours", "day_array", "opens_at", "closes_at", "isLoading", "searchPlace", "store_type", "favcolor", "listModel", "tabs", "currentTab", "storeList", "center", "markers", "places", "currentPlace", "currentLat", "currentLong", "authHeaders", "listOptions", "currentUser", "markerOptions", "storeId"];
const mapDefaultMarker = require("../../assets/images/Default_marker.png");
const mapMerchantMarker = require("../../assets/images/Merchant_marker.png");
const currentLocationMarker = require("../../assets/images/User_location.png");
export default {
  name: "RetailerLocator",
  components: {
    Tabs,
    Autocomplete,
    Loading,
    CanPayLoader,
    RetailerDetails
  },
  data: () => ({
    zoom: 7,
    periods: [],
    opening_hours: [],
    day_array: [
      { key: 1, name: "Mon" },
      { key: 2, name: "Tue" },
      { key: 3, name: "Wed" },
      { key: 4, name: "Thu" },
      { key: 5, name: "Fri" },
      { key: 6, name: "Sat" },
      { key: 7, name: "Sun" },
    ],
    opens_at: "",
    closes_at: "",
    isLoading: false,
    searchPlace: "",
    store_type: "nearby",
    favcolor: "#000000",
    listModel: "",
    currentLat: "",
    currentLong: "",
    tabs: TABS,
    currentTab: "nearby",
    storeList: [],
    center: {},
    markers: [],
    places: [],
    currentPlace: null,
    authHeaders: {
      Authorization: localStorage.getItem("consumer_token"),
    },
    currStoreList:{},
    listOptions: [
      {
        id: 0,
        value: "All Locations",
      },
      {
        id: 1,
        value: "Open",
      },
    ],
    markerOptions: {},
    storeId: '',
    show_modal:false,
    displayStoreDetail:false,
    showStoreCompleteDetail:false,
    canpayTimingDetail:false,
    canpayDisplay:false,
    fStoreList:[],
    open24HoursEnable: false,
    openNowEnable: false,
    showCanPayDetail:[]
  }),
  watch:{
    showStoreCompleteDetail(newValue,oldValue){
      let self = this;
      if(newValue == true){
        self.fetchStorePointsBackProgram();
      }

    },
    openNowEnable(newValue,oldValue){
      let self = this;
      if(newValue == true){
        self.open24HoursEnable = false
      }
    },
    open24HoursEnable(newValue,oldValue){
      let self = this;
      if(newValue == true){
        self.openNowEnable = false;
      }
    },
    canpayTimingDetail(newValue, oldValue){
      let self = this;
      if(newValue == true){
        self.canpayDisplay = false;
      }
    },
    canpayDisplay(newValue, oldValue){
      let self = this;
      if(newValue == true){
        self.canpayTimingDetail = false;
      }
    }
  },
  methods: {
    openGoogleMap(item) {
      const urlSuffix =
        item.address +
        "," +
        item.city +
        "," +
        item.zip;

      window.open(
        "https://www.google.com/maps/search/?api=1&query=" + urlSuffix,
        "_blank"
      );
    },
    fetchStorePointsBackProgram(){
      let self = this;
      const payload = {
        store_id: self.currStoreList.id,
        use_as_generic_points: self.currStoreList.is_generic
      }
      self.isLoading = true;
      api
      .getStorePointsBackProgram(payload)
      .then((res)=>{
        self.showCanPayDetail = res.data;
        self.isLoading = false;
      })
      .catch((err) => {
        self.isLoading = false;
      })
    },
    maskValue(value, pattern) {
      let number = "";
      for(let currVal of value){
         if(currVal >= '0' && currVal <= '9'){
          number+=currVal;
         }
      }
      value = number;
      let convertedPattern = "";
      let value_idx = 0;
      for(let pattern_idx = 0; pattern_idx<pattern.length; pattern_idx++){
        if(pattern[pattern_idx] == '#'){
          if(value_idx < value.length){
            convertedPattern += value[value_idx];
            value_idx++;
          }else{
            convertedPattern += ' ';
          }

        }else{
          convertedPattern += pattern[pattern_idx];
        }
      }
      return convertedPattern;
    },
    showModal() {
      var self = this;
      self.$refs["filter-modal"].show();
    },
    hideModal() {
      let self = this;
      if(self.open24HoursEnable == true){
        self.filterAllStores("open_24_hours");
      }else if (self.openNowEnable == true){
        self.filterAllStores("open_store");
      }else {
        self.filterAllStores("no_filter");
      }
      if(self.displayStoreDetail == true)
          self.displayStoreDetail = false;
      this.$refs["filter-modal"].hide();
    },
    // close the retailer modal
    closeRetailerModal(){
      let self = this;
      self.show_modal = false;
    },
    //search store name from search bar
    searchStore(input) {
      return process.env.VUE_APP_API_URL + "/stores/search?keyword=" + input;
    },
    //getting store details after searching from search bar
    getStoreDetails(store) {
      var self = this;
      self.isLoading = true;
      var request = {
        id: store.value,
      };
      api
        .getStoreDetails(request)
        .then((response) => {
          self.isLoading = false;
          if (response.code == 200) {
            self.storeList = [];
            self.storeList.push(response.data);
            self.currentTab = "nearby";
            self.markers = [];
            if (response.data.lat != null && response.data.long != null) {
              self.addMarker(response.data.lat, response.data.long);
            }
          }
        })
        .catch(function (error) {
          self.isLoading = false;
        });
    },
    //showing the formatted store name in the suggestion list of the search bar
    formattedDisplay(result) {
      return result.name;
    },
    //returns the store address
    storeAddress(terminals) {
      var full_address = "";
      full_address =
        full_address +
        (terminals.address != null ? terminals.address + "<br>" : "");
      full_address =
        full_address + (terminals.city != null ? terminals.city + ", " : "");
      full_address =
        full_address + (terminals.state != null ? terminals.state + " " : "");
      full_address =
        full_address + (terminals.zip != null ? terminals.zip + " " : "");
      return full_address;
    },
    /**
     * functions to show and hide the filter modal
     */
    showModal() {
      var self = this;
      self.$refs["filter-modal"].show();
    },
    //when clicked on store arrow it should redirect to store details page
    clickOnStore(data) {
      let self = this;
      self.storeId = data.id;
      const stateToSave = {};
      properties.forEach(prop => {
          stateToSave[prop] = this[prop]; // Save each property
      });
      localStorage.setItem("details", JSON.stringify(data));
      self.currStoreList = data;
      let selectedDiv = '';
      if(self.currentTab == 'all'){
        selectedDiv = document.getElementById(`all-store-card-${data.id}`);
      }else{
        selectedDiv = document.getElementById(`nearby-store-card-${data.id}`)
      }
      if(data.is_generic == 1){
          selectedDiv.style.borderTop = "2.5px solid #1b9142";
          selectedDiv.style.borderBottom = "2.5px solid #1b9142";
          selectedDiv.style.borderRight = "2.5px solid #1b9142";
      }else{
          selectedDiv.style.borderTop = "2.5px solid #FF9F20";
          selectedDiv.style.borderBottom = "2.5px solid #FF9F20";
          selectedDiv.style.borderRight = "2.5px solid #FF9F20";  
      }
      if(data.lat != null && data.long != null){
      this.$nextTick(() => {
        setTimeout(() => {
          const markerDiv = this.$refs['marker-' + self.storeId][0].$markerObject;
          if (markerDiv) {
              markerDiv.setAnimation(google.maps.Animation.BOUNCE);
              setTimeout(() => {
                markerDiv.setAnimation(null); // Stop bouncing after 2 seconds
              }, 500);
          }
        },100);

      });
        self.center = {
          lat: parseFloat(data.lat),
          lng: parseFloat(data.long),
        };
      }
      setTimeout(()=>{
          selectedDiv.style.borderTop = null;
          selectedDiv.style.borderBottom = null;
          selectedDiv.style.borderRight = null;
      },100);
      setTimeout(()=>{
          selectedDiv.style.borderTop = null;
          selectedDiv.style.borderBottom = null;
          selectedDiv.style.borderRight = null;
          self.displayStoreDetail = true;
      },200);
    },
    //changes the tab content
    changeTab(newTab) {
      var self = this;
      self.$refs.autocomplete.clear();
      self.currentTab = newTab;
      self.getallStores(newTab);  
    },
    addMarker(lat, lng, storeId) {
      var self = this;
      const marker = {
        lat: parseFloat(lat),
        lng: parseFloat(lng),
        icon: self.markerOptions,
      };
      self.markers.push({ position: marker, id: storeId });
      self.center = {
        lat: parseFloat(lat),
        lng: parseFloat(lng),
      };
    },
    clearSearch() {
      this.$refs.autocomplete.clear();
    },
    distance(lat1, lon1, lat2, lon2, unit) {
      if (lat1 == lat2 && lon1 == lon2) {
        return 0;
      } else {
        var radlat1 = (Math.PI * lat1) / 180;
        var radlat2 = (Math.PI * lat2) / 180;
        var theta = lon1 - lon2;
        var radtheta = (Math.PI * theta) / 180;
        var dist =
          Math.sin(radlat1) * Math.sin(radlat2) +
          Math.cos(radlat1) * Math.cos(radlat2) * Math.cos(radtheta);
        if (dist > 1) {
          dist = 1;
        }
        dist = Math.acos(dist);
        dist = (dist * 180) / Math.PI;
        dist = dist * 60 * 1.1515;
        if (unit == "K") {
          dist = dist * 1.609344;
        }
        if (unit == "N") {
          dist = dist * 0.8684;
        }
        return dist.toFixed(1) + " mi";
      }
    },

    getallStores(storetype) {
      let self = this;
      self.isLoading = true;
      self.center.lat = parseFloat(localStorage.getItem("current_lat"));
      self.center.lng = parseFloat(localStorage.getItem("current_long"));
      if (localStorage.getItem("last_visited_tab") != null) {
        storetype = localStorage.getItem("last_visited_tab");
        self.currentTab = localStorage.getItem("last_visited_tab");
        localStorage.removeItem("last_visited_tab");
      }
      var request = {
        type: storetype,
        lat: parseFloat(this.center.lat),
        long: parseFloat(this.center.lng),
      };
      api
        .getstorelistingforConsumer(request)
        .then((response) => {
          self.markers = [];
          self.markerOptions = {
            url: currentLocationMarker,
            size: { width: 30, height: 30, f: "px", b: "px" },
            scaledSize: { width: 30, height: 30, f: "px", b: "px" },
          };
          if (self.currentLat != null && self.currentLong != null && process.env.VUE_APP_CURRENT_LOCATION == 'enable') {
            self.addMarker(self.currentLat, self.currentLong, 'current_location');
          }
          
          if (response.data.length == 0) {
            self.storeList = response.data;
            self.isLoading = false;
          } else {
            var iconPath = mapDefaultMarker;
            response.data.forEach(function (item) {
              iconPath = item.is_generic == 0 ? mapMerchantMarker : mapDefaultMarker;
              self.markerOptions = {
                url: iconPath,
                size: { width: 30, height: 35, f: "px", b: "px" },
                scaledSize: { width: 30, height: 35, f: "px", b: "px" },
              };
              if (item.lat != null && item.long != null) {
                self.addMarker(item.lat, item.long, item.id);
              }
            });
            setTimeout(function () {
              self.fStoreList = response.data;
             if(self.openNowEnable == false && self.open24HoursEnable == false){
              self.filterAllStores("no_filter");
             }else if(self.openNowEnable == true) {
              self.filterAllStores("open_store");
             }else {
              self.filterAllStores("open_24_hours");
             }
              self.center = {
                lat: parseFloat(self.currentLat),
                lng: parseFloat(self.currentLong),
              };
              self.isLoading = false;
            }, 300);
          }
        })
        .catch(function (error) {});
    },
isWithinTimeRange(status) {
  // status shall come like below example
  // Closed - Open @ or Open - Closes @
  // if status first char is O that means store is Open
  // else closed
  if(status && status[0] == "C"){
    return false;
  }else return true;
},
  filterAllStores(type) {
    let self = this;
    self.markers =[];
    if(type == "no_filter"){
      self.storeList = self.fStoreList;
      var iconPath = mapDefaultMarker;
      self.storeList.forEach(function (item) {
        iconPath = item.is_generic == 0 ? mapMerchantMarker : mapDefaultMarker;
        self.markerOptions = {
          url: iconPath,
          size: { width: 30, height: 35, f: "px", b: "px" },
          scaledSize: { width: 30, height: 35, f: "px", b: "px" },
        };
        if (item.lat != null && item.long != null) {
          self.addMarker(item.lat, item.long, item.id);
        }
      });
    }else if(type == "open_store"){
      let temp = [];
      self.fStoreList.forEach(function (item) {
        console.log(item);
        var iconPath = mapDefaultMarker;
        iconPath = item.is_generic == 0 ? mapMerchantMarker : mapDefaultMarker;
        self.markerOptions = {
          url: iconPath,
          size: { width: 30, height: 35, f: "px", b: "px" },
          scaledSize: { width: 30, height: 35, f: "px", b: "px" },
        };
        if(item.timing_arr != null && self.isWithinTimeRange(item.status)){
          if (item.lat != null && item.long != null) {
            self.addMarker(item.lat, item.long, item.id);
          }
          temp.push(item);
        }
      });
      self.storeList = temp;
    }else if(type == "open_24_hours"){
      let temp = [];
      self.fStoreList.forEach(function (item) {
        var iconPath = mapDefaultMarker;
        iconPath = item.is_generic == 0 ? mapMerchantMarker : mapDefaultMarker;
        self.markerOptions = {
          url: iconPath,
          size: { width: 30, height: 35, f: "px", b: "px" },
          scaledSize: { width: 30, height: 35, f: "px", b: "px" },
        };
        if(item.timing_arr != null && item.store_open_all_day == 1){
          if (item.lat != null && item.long != null) {
            self.addMarker(item.lat, item.long, item.id);
          }
          temp.push(item);
        }
      });
      self.storeList = temp;
    }
    self.markerOptions = {
      url: currentLocationMarker,
      size: { width: 30, height: 30, f: "px", b: "px" },
      scaledSize: { width: 30, height: 30, f: "px", b: "px" },
    };
    self.addMarker(self.currentLat, self.currentLong, 'current_location');
    self.center = {
      lat: parseFloat(self.currentLat),
      lng: parseFloat(self.currentLong),
    };
  },
    filterStores(value) {
      var self = this;
      self.$refs["filter-modal"].hide();
      self.isLoading = true;
      self.currentTab = "all";
      if (value == "Open") {
        var stores = self.storeList;
        self.storeList = [];
        var temp = [];
        stores.forEach(function (item) {
          var opening_status = self.opening_hours.find((p) => p.id == item.id);
          if (typeof opening_status != "undefined") {
            if (opening_status.isopen) {
              temp.push(item);
            }
          }
        });
        self.storeList = temp;
        self.isLoading = false;
      } else {
        self.getallStores("all");
      }
    },
    // returns the opening time of a store
    getOpeningTime(day) {
      if (day != 7) {
        var res = null;
        this.periods.every(function (item) {
          if (item.open.day >= day) {
            res = item;
            return false;
          } else {
            return true;
          }
        });
        if (res == null) {
          this.getOpeningTime(7);
        } else {
          var day_name = this.day_array.find((p) => p.key == res.open.day);
          this.opens_at =
            day_name.name +
            " " +
            moment(res.open.hours + ":" + res.open.minutes, "HH:mm").format(
              "hh:mm a"
            );
        }
      } else {
        var day_name = this.day_array.find(
          (p) => p.key == this.periods[0].open.day
        );
        this.opens_at =
          day_name.name +
          " " +
          moment(
            this.periods[0].open.hours + ":" + this.periods[0].open.minutes,
            "HH:mm"
          ).format("hh:mm a");
      }
    },
    getOpeningHoursStatus(store) {
      if (store.status) {
        return store.status.split('-')[0];
      } else {
        return "Hours currently unavailable. Contact the merchant";
      }
    },
    getOpeningHoursTiming(store) {
      if (store.status) {
        return '- '+ store.status.split('-')[1];
      } else {
        return "";
      }
    },
    getScrollableParent(node) {
      if (node == null) {
        return null;
      }

      const overflowY = window.getComputedStyle(node).overflowY;
      const isScrollable = (overflowY === 'auto' || overflowY === 'scroll') && node.scrollHeight > node.clientHeight;

      if (isScrollable) {
        return node;
      }

      return this.getScrollableParent(node.parentNode) || document.documentElement; // Fallback to document
    },
    handleMarkerClick(storeId) {
      var self = this;
      // Center the map to the marker's position (this assumes that `storeId` corresponds to a marker with position info)
      const marker = this.markers.find(marker => marker.id === storeId);
      if (marker) {
        this.center = marker.position;
      }
      // Scroll to the store card in both tabs
      this.$nextTick(() => {
        const markerDiv = this.$refs['marker-' + storeId][0].$markerObject;
        if (markerDiv) {
            markerDiv.setAnimation(google.maps.Animation.BOUNCE);
            setTimeout(() => {
              markerDiv.setAnimation(null); // Stop bouncing after 2 seconds
            }, 500);
        }
        let card;
        if(self.currentTab == 'all') {
          card = document.getElementById(`all-store-card-${storeId}`);
        } else {
          card = document.getElementById(`nearby-store-card-${storeId}`);
        }
        if (card) {
          const scrollableParent = this.getScrollableParent(card);

          if (scrollableParent) {
            // Calculate the card's position relative to the scrollable parent
            const cardRect = card.getBoundingClientRect();
            const parentRect = scrollableParent.getBoundingClientRect();

            // Offset of 50px from the top
            const offset = 50;

            // Calculate how much to scroll by checking the card's top relative to the parent
            const scrollTo = cardRect.top - parentRect.top + scrollableParent.scrollTop - offset;

            // Scroll the parent to the desired position
            scrollableParent.scrollTo({
              top: scrollTo,
              behavior: 'smooth'
            });
          }
        }
      });
    },
    storeLocator(){
      let self = this;
      if(self.showStoreCompleteDetail == true)
          self.showStoreCompleteDetail = !self.showStoreCompleteDetail;
      else this.$router.push("/login");
    }
  },
  mounted() {
    let self = this;
    var element = document.getElementsByClassName("content-wrap");
    if (element[0]) {
      element[0].style.setProperty("background-color", "#ffffff");
      element[0].style.height = "114vh";
      if(window.innerWidth>1200){
        element[0].style.height = "121vh";
      }
      // important for iphone SE
      if(window.innerHeight<727){
        element[0].style.height = "127vh";
      }
    }
    this.$root.$emit("show_header", 2);
    self.$root.$on("fliterOn", function (data) {
      setTimeout(function () {
        self.showModal();
      }, 300);
    });
    var e2 = document.getElementsByClassName("wrapper");
    var element3 = document.getElementsByClassName("common-nav");
    if(element3[0]){
      element3[0].style.setProperty("border-bottom-left-radius","0px");
      element3[0].style.setProperty("border-bottom-right-radius","0px");
    }
    if (e2[0]) {
      e2[0].style.setProperty("background-color", "#ffffff");
    }
    document
      .getElementById("app")
      .style.setProperty("background-color", "#ffffff");

    //getting the the allow location permission
    navigator.geolocation.getCurrentPosition((position) => {
      self.center = {
        lat: parseFloat(position.coords.latitude),
        lng: parseFloat(position.coords.longitude),
      };
      self.currentLat = self.center.lat;
      self.currentLong = self.center.lng;
      localStorage.setItem("current_lat", parseFloat(self.center.lat));
      localStorage.setItem("current_long", parseFloat(self.center.lng));
      self.getallStores("nearby");
    });
    this.$root.$on("backToMerchantMiniDetailLocator", self.storeLocator);
  },
  created() {},
  beforeDestroy() {
    // Clean up the listener when the component is destroyed
    this.$root.$off('backToMerchantMiniDetailLocator', this.storeLocator);
  },
};
</script>

<style lang="scss">
.modal-backdrop {
  background: rgba(0, 0, 0, 0.3);
}
@media only screen and ( min-width:270px) and ( max-width:700px) {
  .not-available-time-text-style{
    font-family: "Open Sans";
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 2.25;
    letter-spacing: normal;
    color: #666666;
    float: left;
    padding-left: 23px;
    margin-top: -23px;
    color: #1b9142;
    text-align: left;
  }
}

@media only screen and ( min-width:600px) {
  .not-available-time-text-style{
    font-family: "Open Sans";
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 2.25;
    letter-spacing: normal;
    color: #666666;
    float: left;
    padding-left: 6px;
    margin-top: -7px;
    color: #1b9142;
    text-align: left;
  }
}
.store-list-card {
  height: 430px; /* Adjust height as needed */
  overflow-y: auto; /* Enable vertical scrolling */
}
.card {
  transition: opacity 0.3s ease, height 0.3s ease; /* Smooth transitions */
}

.card.hidden {
  opacity: 0; /* Hide card */
  height: 0; /* Optional: reduce height to zero */
  overflow: hidden; /* Hide overflow content */
}

/* Optional: styling for the selected card */
.card.selected {
  opacity: 1; /* Fully visible */
  height: auto; /* Reset height */
}

.default-tabs.bottom-edge-shadow {
  position: sticky;
  top: 0; /* Stick the tabs to the top when scrolling */
  z-index: 10; /* Ensure tabs stay above the content */
}
.store-button-option-not-selected{
    border: none;
    border-radius: 30px;
    padding: 12px 18px;
    font-family: Open sans;
    line-height: 17px;
    font-size: 12px;
    font-weight: 600;
    color:#000000;
    background-color:#EFEFEF;
    margin-left:12px;
}
.store-button-option-selected{
      border: none;
    border-radius: 30px;
    padding: 12px 18px;
    font-family: Open sans;
    line-height: 17px;
    font-size: 12px;
    font-weight: 600;
    background-color:#1B9142;
    color:#FFFFFF;
    margin-left:12px;
}
#pay-modal-center___BV_modal_content_ {
  border-radius: 10px;
  margin: 10px;
  background-color: #ffffff;
}
#pay-modal-center___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
.text-time-black{
  color: #000000;
}

.text-time-green{
  color:#1B9142;
}
.storeHorizontalRowContent{
  border: 1px solid lightgrey;
    margin-left: 20px;
    margin-right: 20px;
}
.storeHorizontalRowContent1{
  border: 1px solid lightgrey;
    margin-left: 13px;
    margin-right: 13px;
}
</style>
