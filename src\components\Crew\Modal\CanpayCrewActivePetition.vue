<template>
    <div>
        <b-modal
            ref="canpay-crewactive-petition"
            hide-footer
            v-b-modal.modal-center
            modal-backdrop
            hide-header
            id="canpay-crewactive-petition"
            centered
            title="BootstrapVue"
        >
            <div class="text-center" style="font-family:'Open Sans'">
                <div class="mt-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 151 154" fill="none">
                <mask id="path-1-outside-1_16205_163" maskUnits="userSpaceOnUse" x="-0.81665" y="0" width="152" height="154" fill="black">
                <rect fill="white" x="-0.81665" width="152" height="154"/>
                <path d="M3.0781 153.029C2.35441 153.029 1.63072 153.029 0.907036 152.292C0.18335 151.555 0.18335 150.081 0.18335 148.607L14.6571 107.328C14.6571 106.591 15.3808 105.854 16.1045 105.117C16.8281 104.38 16.8281 104.38 17.5518 103.643L112.355 7.08118C120.315 -1.02706 134.789 -1.02706 142.75 7.08118C150.71 15.1894 150.71 29.1945 142.75 38.0399L47.9467 135.339C47.223 136.076 46.4993 136.813 45.7756 136.813C45.0519 137.55 45.0519 138.287 44.3282 138.287L3.80178 153.029H3.0781ZM19.7229 111.014L8.1439 144.921L41.4335 132.39L42.1572 131.653C42.8809 131.653 43.6046 130.916 44.3282 130.179L138.408 33.6172C144.197 27.7203 144.197 17.4008 138.408 11.5039C132.618 5.60695 122.486 5.60695 116.697 11.5039L21.894 108.066C21.1703 109.54 21.1703 110.277 19.7229 111.014Z"/>
                <path d="M3.0781 153.029C2.35441 153.029 1.63072 153.029 0.907036 152.292C0.18335 151.555 0.18335 150.081 0.18335 148.607L14.6571 107.328C14.6571 106.591 15.3808 105.854 16.1045 105.117C16.8281 104.38 16.8281 104.38 17.5518 103.643L97.1574 22.5607C105.118 14.4525 119.592 14.4525 127.552 22.5607C136.236 30.669 136.236 44.6741 127.552 53.5194L47.9467 134.602C47.223 135.339 46.4993 136.076 45.7756 136.076C45.0519 136.813 45.0519 137.55 44.3282 137.55L3.80178 152.292C3.80178 153.03 3.0781 153.029 3.0781 153.029ZM19.7229 111.014L8.1439 144.921L41.4335 132.39L42.1572 131.653C42.8809 131.653 43.6046 130.916 44.3282 130.179L123.934 49.0968C129.723 43.1999 129.723 32.8803 123.934 26.9834C118.144 21.0865 108.013 21.0865 102.223 26.9834L22.6176 108.066C21.1703 109.54 21.1703 110.277 19.7229 111.014Z"/>
                <path d="M3.0781 153.029C2.35441 153.029 1.63072 153.029 0.907036 152.292C0.18335 151.555 0.18335 150.081 0.18335 148.607L14.6571 107.328C14.6571 107.328 15.3808 106.591 15.3808 105.854C24.065 99.2204 36.3677 99.9575 43.6046 107.329C51.5651 115.437 52.2888 127.968 45.0519 136.813C44.3282 137.55 44.3282 137.55 43.6046 137.55L3.80178 152.292C3.80178 153.03 3.0781 153.029 3.0781 153.029ZM19.7229 111.014L8.1439 144.921L41.4335 133.128C45.7756 127.231 45.0519 118.385 39.9861 113.226C34.1966 108.066 25.5124 107.328 19.7229 111.014Z"/>
                <path d="M3.0781 153.029C2.35441 153.029 1.63072 153.029 0.907036 152.292C0.18335 151.555 0.18335 150.081 0.18335 148.607L10.315 119.859C11.0387 119.122 11.7623 117.648 13.2097 117.648C18.9992 117.648 24.065 119.859 28.4071 124.282C32.0256 128.704 34.1966 133.864 34.1966 139.761C34.1966 141.235 33.4729 141.972 32.0256 142.709L3.80178 153.029H3.0781ZM15.3808 124.282L8.14391 144.184L28.4071 137.55C27.6834 133.864 26.2361 130.916 24.065 128.704C21.1703 126.493 18.2755 125.019 15.3808 124.282Z"/>
                <path d="M113.079 153.029H45.052C43.6046 153.029 42.1572 151.555 42.1572 150.081C42.1572 147.87 43.6046 147.133 45.052 147.133H113.079C117.421 147.133 121.039 143.447 121.039 139.024C121.039 134.602 117.421 130.916 113.079 130.916H97.8811C96.4337 130.916 94.9863 129.442 94.9863 127.968C94.9863 126.493 96.4337 125.019 97.8811 125.019H113.079C121.039 125.019 127.552 131.653 127.552 139.024C127.552 147.133 121.039 153.029 113.079 153.029Z"/>
                <path d="M109.46 130.917H94.2626C86.302 130.917 79.7889 124.282 79.7889 116.911C79.7889 108.803 86.302 102.169 94.2626 102.169H122.486C123.934 102.169 125.381 103.643 125.381 105.118C125.381 107.329 123.934 108.066 122.486 108.066H94.2626C89.9205 108.066 86.302 111.752 86.302 116.174C86.302 120.597 89.9205 124.282 94.2626 124.282H109.46C110.907 124.282 112.355 125.757 112.355 127.231C112.355 129.442 111.631 130.917 109.46 130.917Z"/>
                <path d="M135.513 108.066H106.565C105.118 108.066 103.671 106.592 103.671 105.118C103.671 103.643 105.118 102.169 106.565 102.169H135.513C139.855 102.169 143.473 99.2206 143.473 94.0608C143.473 89.6381 139.855 85.9526 135.513 85.9526H120.315C118.868 85.9526 117.421 84.4784 117.421 83.0041C117.421 80.7928 118.868 80.0557 120.315 80.0557H135.513C143.473 80.0557 149.987 86.6897 149.987 94.0608C149.263 102.169 142.75 108.066 135.513 108.066Z"/>
                <path d="M26.2361 153.03H24.7887C23.3413 153.03 21.894 151.556 21.894 150.082C21.894 147.87 23.3413 147.133 24.7887 147.133H26.2361C27.6835 147.133 29.1308 148.607 29.1308 150.082C29.1308 151.556 27.6835 153.03 26.2361 153.03Z"/>
                <path d="M35.644 153.03H34.1967C32.7493 153.03 31.3019 151.556 31.3019 150.082C31.3019 147.87 32.7493 147.133 34.1967 147.133H35.644C37.0914 147.133 38.5388 148.607 38.5388 150.082C39.2625 151.556 37.8151 153.03 35.644 153.03Z"/>
                </mask>
                <path d="M3.0781 153.029C2.35441 153.029 1.63072 153.029 0.907036 152.292C0.18335 151.555 0.18335 150.081 0.18335 148.607L14.6571 107.328C14.6571 106.591 15.3808 105.854 16.1045 105.117C16.8281 104.38 16.8281 104.38 17.5518 103.643L112.355 7.08118C120.315 -1.02706 134.789 -1.02706 142.75 7.08118C150.71 15.1894 150.71 29.1945 142.75 38.0399L47.9467 135.339C47.223 136.076 46.4993 136.813 45.7756 136.813C45.0519 137.55 45.0519 138.287 44.3282 138.287L3.80178 153.029H3.0781ZM19.7229 111.014L8.1439 144.921L41.4335 132.39L42.1572 131.653C42.8809 131.653 43.6046 130.916 44.3282 130.179L138.408 33.6172C144.197 27.7203 144.197 17.4008 138.408 11.5039C132.618 5.60695 122.486 5.60695 116.697 11.5039L21.894 108.066C21.1703 109.54 21.1703 110.277 19.7229 111.014Z" fill="#179346"/>
                <path d="M3.0781 153.029C2.35441 153.029 1.63072 153.029 0.907036 152.292C0.18335 151.555 0.18335 150.081 0.18335 148.607L14.6571 107.328C14.6571 106.591 15.3808 105.854 16.1045 105.117C16.8281 104.38 16.8281 104.38 17.5518 103.643L97.1574 22.5607C105.118 14.4525 119.592 14.4525 127.552 22.5607C136.236 30.669 136.236 44.6741 127.552 53.5194L47.9467 134.602C47.223 135.339 46.4993 136.076 45.7756 136.076C45.0519 136.813 45.0519 137.55 44.3282 137.55L3.80178 152.292C3.80178 153.03 3.0781 153.029 3.0781 153.029ZM19.7229 111.014L8.1439 144.921L41.4335 132.39L42.1572 131.653C42.8809 131.653 43.6046 130.916 44.3282 130.179L123.934 49.0968C129.723 43.1999 129.723 32.8803 123.934 26.9834C118.144 21.0865 108.013 21.0865 102.223 26.9834L22.6176 108.066C21.1703 109.54 21.1703 110.277 19.7229 111.014Z" fill="#179346"/>
                <path d="M3.0781 153.029C2.35441 153.029 1.63072 153.029 0.907036 152.292C0.18335 151.555 0.18335 150.081 0.18335 148.607L14.6571 107.328C14.6571 107.328 15.3808 106.591 15.3808 105.854C24.065 99.2204 36.3677 99.9575 43.6046 107.329C51.5651 115.437 52.2888 127.968 45.0519 136.813C44.3282 137.55 44.3282 137.55 43.6046 137.55L3.80178 152.292C3.80178 153.03 3.0781 153.029 3.0781 153.029ZM19.7229 111.014L8.1439 144.921L41.4335 133.128C45.7756 127.231 45.0519 118.385 39.9861 113.226C34.1966 108.066 25.5124 107.328 19.7229 111.014Z" fill="#179346"/>
                <path d="M3.0781 153.029C2.35441 153.029 1.63072 153.029 0.907036 152.292C0.18335 151.555 0.18335 150.081 0.18335 148.607L10.315 119.859C11.0387 119.122 11.7623 117.648 13.2097 117.648C18.9992 117.648 24.065 119.859 28.4071 124.282C32.0256 128.704 34.1966 133.864 34.1966 139.761C34.1966 141.235 33.4729 141.972 32.0256 142.709L3.80178 153.029H3.0781ZM15.3808 124.282L8.14391 144.184L28.4071 137.55C27.6834 133.864 26.2361 130.916 24.065 128.704C21.1703 126.493 18.2755 125.019 15.3808 124.282Z" fill="#179346"/>
                <path d="M113.079 153.029H45.052C43.6046 153.029 42.1572 151.555 42.1572 150.081C42.1572 147.87 43.6046 147.133 45.052 147.133H113.079C117.421 147.133 121.039 143.447 121.039 139.024C121.039 134.602 117.421 130.916 113.079 130.916H97.8811C96.4337 130.916 94.9863 129.442 94.9863 127.968C94.9863 126.493 96.4337 125.019 97.8811 125.019H113.079C121.039 125.019 127.552 131.653 127.552 139.024C127.552 147.133 121.039 153.029 113.079 153.029Z" fill="#179346"/>
                <path d="M109.46 130.917H94.2626C86.302 130.917 79.7889 124.282 79.7889 116.911C79.7889 108.803 86.302 102.169 94.2626 102.169H122.486C123.934 102.169 125.381 103.643 125.381 105.118C125.381 107.329 123.934 108.066 122.486 108.066H94.2626C89.9205 108.066 86.302 111.752 86.302 116.174C86.302 120.597 89.9205 124.282 94.2626 124.282H109.46C110.907 124.282 112.355 125.757 112.355 127.231C112.355 129.442 111.631 130.917 109.46 130.917Z" fill="#179346"/>
                <path d="M135.513 108.066H106.565C105.118 108.066 103.671 106.592 103.671 105.118C103.671 103.643 105.118 102.169 106.565 102.169H135.513C139.855 102.169 143.473 99.2206 143.473 94.0608C143.473 89.6381 139.855 85.9526 135.513 85.9526H120.315C118.868 85.9526 117.421 84.4784 117.421 83.0041C117.421 80.7928 118.868 80.0557 120.315 80.0557H135.513C143.473 80.0557 149.987 86.6897 149.987 94.0608C149.263 102.169 142.75 108.066 135.513 108.066Z" fill="#179346"/>
                <path d="M26.2361 153.03H24.7887C23.3413 153.03 21.894 151.556 21.894 150.082C21.894 147.87 23.3413 147.133 24.7887 147.133H26.2361C27.6835 147.133 29.1308 148.607 29.1308 150.082C29.1308 151.556 27.6835 153.03 26.2361 153.03Z" fill="#179346"/>
                <path d="M35.644 153.03H34.1967C32.7493 153.03 31.3019 151.556 31.3019 150.082C31.3019 147.87 32.7493 147.133 34.1967 147.133H35.644C37.0914 147.133 38.5388 148.607 38.5388 150.082C39.2625 151.556 37.8151 153.03 35.644 153.03Z" fill="#179346"/>
                <path d="M0.18335 148.607L0.0889826 148.574L0.0833496 148.59V148.607H0.18335ZM14.6571 107.328L14.7515 107.362L14.7571 107.345V107.328H14.6571ZM8.1439 144.921L8.04927 144.889L7.98078 145.089L8.17913 145.015L8.1439 144.921ZM41.4335 132.39L41.4687 132.484L41.4894 132.476L41.5048 132.46L41.4335 132.39ZM42.1572 131.653V131.553H42.1152L42.0858 131.583L42.1572 131.653ZM3.0781 152.929C2.35617 152.929 1.67098 152.928 0.978394 152.222L0.835679 152.362C1.59047 153.131 2.35265 153.129 3.0781 153.129V152.929ZM0.978394 152.222C0.639252 151.877 0.462345 151.35 0.372841 150.712C0.283539 150.075 0.28335 149.346 0.28335 148.607H0.0833496C0.0833496 149.342 0.0831602 150.087 0.17478 150.74C0.266198 151.392 0.451134 151.971 0.835679 152.362L0.978394 152.222ZM0.277717 148.64L14.7515 107.362L14.5627 107.295L0.0889826 148.574L0.277717 148.64ZM14.7571 107.328C14.7571 106.992 14.9229 106.645 15.1899 106.282C15.4563 105.92 15.8127 105.557 16.1758 105.187L16.0331 105.047C15.6725 105.414 15.3053 105.788 15.0289 106.163C14.7532 106.538 14.5571 106.928 14.5571 107.328H14.7571ZM16.1758 105.187C16.5377 104.819 16.7186 104.634 16.8995 104.45C17.0804 104.266 17.2613 104.082 17.6232 103.713L17.4805 103.573C17.1186 103.941 16.9377 104.126 16.7568 104.31C16.5759 104.494 16.3949 104.679 16.0331 105.047L16.1758 105.187ZM3.80178 152.929H3.0781V153.129H3.80178V152.929ZM19.6283 110.982L8.04927 144.889L8.23854 144.954L19.8175 111.046L19.6283 110.982ZM8.17913 145.015L41.4687 132.484L41.3983 132.297L8.10868 144.828L8.17913 145.015ZM41.5048 132.46L42.2285 131.723L42.0858 131.583L41.3621 132.32L41.5048 132.46ZM42.1572 131.753C42.5522 131.753 42.936 131.553 43.3034 131.272C43.672 130.99 44.0391 130.616 44.3996 130.249L44.2569 130.109C43.8937 130.479 43.5371 130.842 43.182 131.113C42.8258 131.385 42.4858 131.553 42.1572 131.553V131.753ZM3.70178 152.292C3.70178 152.46 3.66102 152.578 3.60504 152.664C3.54871 152.75 3.47303 152.808 3.39458 152.848C3.31569 152.888 3.23563 152.909 3.17446 152.919C3.14411 152.924 3.11904 152.927 3.10196 152.928C3.09344 152.929 3.08695 152.929 3.08286 152.929C3.08081 152.929 3.07936 152.929 3.07855 152.929C3.07815 152.929 3.07791 152.929 3.07784 152.929C3.0778 152.929 3.07781 152.929 3.07785 152.929C3.07788 152.929 3.07791 152.929 3.07795 152.929C3.07798 152.929 3.07802 152.929 3.07803 152.929C3.07807 152.929 3.07812 152.929 3.0781 153.029C3.07807 153.129 3.07813 153.129 3.07818 153.129C3.0782 153.129 3.07826 153.129 3.07831 153.129C3.07839 153.129 3.07849 153.129 3.07861 153.129C3.07883 153.129 3.0791 153.129 3.07941 153.129C3.08003 153.129 3.08082 153.129 3.08179 153.129C3.08373 153.129 3.08634 153.129 3.08959 153.129C3.0961 153.129 3.10516 153.129 3.11643 153.128C3.13892 153.126 3.17039 153.123 3.20789 153.117C3.28241 153.104 3.38327 153.078 3.4853 153.026C3.58777 152.974 3.69301 152.895 3.77237 152.773C3.85209 152.652 3.90178 152.494 3.90178 152.292H3.70178ZM112.355 7.08118L112.497 7.22129L112.355 7.08118ZM142.75 38.0399L142.893 38.1796L142.898 38.1737L142.75 38.0399ZM47.9467 135.339L48.0894 135.479L48.0899 135.478L47.9467 135.339ZM45.7756 136.813V136.613H45.6917L45.6329 136.673L45.7756 136.813ZM44.3282 138.287V138.087H44.293L44.2599 138.099L44.3282 138.287ZM3.80178 153.029L3.7331 152.842L3.87015 153.217L3.80178 153.029ZM19.7229 111.014L19.6155 110.845L19.8137 111.192L19.7229 111.014ZM44.3282 130.179L44.185 130.039L44.471 130.319L44.3282 130.179ZM138.408 33.6172L138.265 33.4771L138.264 33.4777L138.408 33.6172ZM116.697 11.5039L116.84 11.644L116.697 11.5039ZM21.894 108.066L21.7512 107.925L21.7286 107.948L21.7144 107.977L21.894 108.066ZM97.1574 22.5607L97.3001 22.7008L97.1574 22.5607ZM127.552 22.5607L127.409 22.701L127.416 22.7069L127.552 22.5607ZM127.552 53.5194L127.695 53.6595L127.552 53.5194ZM47.9467 134.602L48.0894 134.742L47.9467 134.602ZM45.7756 136.076V135.876H45.6917L45.6329 135.936L45.7756 136.076ZM44.3282 137.55V137.35H44.293L44.2599 137.362L44.3282 137.55ZM3.80178 152.292L3.73232 152.105L3.87015 152.48L3.80178 152.292ZM123.934 49.0968L124.077 49.2369L123.934 49.0968ZM102.223 26.9834L102.08 26.8433L102.223 26.9834ZM22.6176 108.066L22.7604 108.206L22.6176 108.066ZM15.3808 105.854L15.2594 105.695L15.1808 105.755V105.854H15.3808ZM43.6046 107.329L43.4618 107.469L43.6046 107.329ZM45.0519 136.813L45.1946 136.953L45.201 136.947L45.2067 136.94L45.0519 136.813ZM43.6046 137.55V137.35H43.5687L43.5351 137.363L43.6046 137.55ZM41.4335 133.128L41.5003 133.316L41.5581 133.296L41.5945 133.246L41.4335 133.128ZM39.9861 113.226L40.1292 113.085L40.1192 113.076L39.9861 113.226ZM10.315 119.859L10.1722 119.719L10.1411 119.751L10.1263 119.792L10.315 119.859ZM28.4071 124.282L28.5619 124.155L28.5562 124.148L28.5498 124.141L28.4071 124.282ZM32.0256 142.709L32.0942 142.897L32.1056 142.893L32.1163 142.888L32.0256 142.709ZM15.3808 124.282L15.4301 124.088L15.2547 124.043L15.1928 124.213L15.3808 124.282ZM8.14391 144.184L7.95595 144.115L7.81547 144.502L8.20613 144.374L8.14391 144.184ZM28.4071 137.55L28.4694 137.74L28.6375 137.685L28.6034 137.511L28.4071 137.55ZM24.065 128.704L24.2077 128.564L24.1977 128.554L24.1864 128.545L24.065 128.704ZM149.987 94.0608L150.186 94.0786L150.187 94.0697V94.0608H149.987ZM38.5388 150.082H38.3388V150.128L38.3592 150.17L38.5388 150.082ZM17.6945 103.783L112.497 7.22129L112.212 6.94106L17.4091 103.503L17.6945 103.783ZM112.497 7.22129C120.38 -0.807097 134.725 -0.807097 142.607 7.22129L142.892 6.94106C134.853 -1.24702 120.251 -1.24702 112.212 6.94106L112.497 7.22129ZM142.607 7.22129C150.488 15.2485 150.496 29.1333 142.601 37.9061L142.898 38.1737C150.924 29.2558 150.932 15.1303 142.892 6.94106L142.607 7.22129ZM142.606 37.9003L47.8034 135.199L48.0899 135.478L142.893 38.1795L142.606 37.9003ZM47.804 135.199C47.4395 135.57 47.088 135.927 46.7397 136.193C46.3891 136.461 46.071 136.613 45.7756 136.613V137.013C46.2039 137.013 46.6095 136.796 46.9826 136.511C47.3579 136.224 47.7302 135.845 48.0894 135.479L47.804 135.199ZM45.6329 136.673C45.2526 137.06 45.0511 137.463 44.8846 137.717C44.7979 137.849 44.7239 137.939 44.6418 137.998C44.5656 138.052 44.4713 138.087 44.3282 138.087V138.487C44.547 138.487 44.7241 138.43 44.874 138.323C45.0181 138.221 45.125 138.08 45.2193 137.936C45.4146 137.638 45.5749 137.303 45.9183 136.953L45.6329 136.673ZM44.2599 138.099L3.73341 152.841L3.87015 153.217L44.3966 138.475L44.2599 138.099ZM44.4715 130.319L138.551 33.7568L138.264 33.4777L44.185 130.039L44.4715 130.319ZM138.55 33.7573C144.416 27.7826 144.416 17.3384 138.55 11.3637L138.265 11.644C143.978 17.4631 143.978 27.658 138.265 33.4771L138.55 33.7573ZM138.55 11.3637C132.682 5.38699 122.422 5.38699 116.554 11.3637L116.84 11.644C122.551 5.82691 132.554 5.82691 138.265 11.644L138.55 11.3637ZM116.554 11.3637L21.7512 107.925L22.0367 108.206L116.84 11.644L116.554 11.3637ZM21.7144 107.977C21.5309 108.351 21.393 108.679 21.2694 108.965C21.1448 109.254 21.0361 109.496 20.9075 109.715C20.6576 110.139 20.3259 110.482 19.6321 110.836L19.8137 111.192C20.5673 110.808 20.9593 110.415 21.2521 109.918C21.3949 109.675 21.5124 109.411 21.6366 109.124C21.7617 108.834 21.8952 108.517 22.0735 108.154L21.7144 107.977ZM17.6945 103.783L97.3001 22.7008L97.0147 22.4206L17.4091 103.503L17.6945 103.783ZM97.3001 22.7008C105.182 14.6724 119.527 14.6724 127.41 22.7008L127.695 22.4206C119.656 14.2325 105.054 14.2325 97.0147 22.4206L97.3001 22.7008ZM127.416 22.7069C136.012 30.7328 136.021 44.6082 127.41 53.3793L127.695 53.6595C136.452 44.74 136.461 30.6051 127.689 22.4145L127.416 22.7069ZM127.41 53.3793L47.804 134.462L48.0894 134.742L127.695 53.6595L127.41 53.3793ZM47.804 134.462C47.4395 134.833 47.088 135.19 46.7397 135.456C46.3891 135.724 46.071 135.876 45.7756 135.876V136.276C46.2039 136.276 46.6095 136.059 46.9826 135.774C47.3579 135.487 47.7302 135.108 48.0894 134.742L47.804 134.462ZM45.6329 135.936C45.2526 136.323 45.0511 136.726 44.8846 136.98C44.7979 137.112 44.7239 137.202 44.6418 137.261C44.5656 137.315 44.4713 137.35 44.3282 137.35V137.75C44.547 137.75 44.7241 137.693 44.874 137.586C45.0181 137.484 45.1251 137.343 45.2193 137.199C45.4146 136.901 45.5749 136.566 45.9183 136.216L45.6329 135.936ZM44.2599 137.362L3.73341 152.105L3.87015 152.48L44.3966 137.738L44.2599 137.362ZM44.471 130.319L124.077 49.2369L123.791 48.9566L44.1855 130.039L44.471 130.319ZM124.077 49.2369C129.942 43.2622 129.942 32.818 124.077 26.8433L123.791 27.1235C129.504 32.9426 129.504 43.1375 123.791 48.9566L124.077 49.2369ZM124.077 26.8433C118.209 20.8665 107.948 20.8665 102.08 26.8433L102.366 27.1235C108.077 21.3065 118.08 21.3065 123.791 27.1235L124.077 26.8433ZM102.08 26.8433L22.4749 107.926L22.7604 108.206L102.366 27.1235L102.08 26.8433ZM22.4749 107.926C21.7409 108.673 21.3657 109.244 21.013 109.693C20.6645 110.136 20.3241 110.483 19.6321 110.836L19.8137 111.192C20.5691 110.808 20.9523 110.418 21.3275 109.94C21.6985 109.468 22.047 108.932 22.7604 108.206L22.4749 107.926ZM14.6571 107.328C14.7998 107.469 14.7998 107.469 14.7999 107.469C14.7999 107.468 14.7999 107.468 14.8 107.468C14.8 107.468 14.8001 107.468 14.8002 107.468C14.8004 107.468 14.8006 107.468 14.8009 107.467C14.8014 107.467 14.8021 107.466 14.803 107.465C14.8047 107.463 14.8071 107.461 14.8102 107.458C14.8163 107.451 14.8249 107.442 14.8356 107.43C14.8571 107.407 14.8873 107.373 14.9232 107.331C14.9948 107.245 15.0904 107.124 15.1863 106.977C15.3738 106.691 15.5808 106.282 15.5808 105.854H15.1808C15.1808 106.164 15.0259 106.492 14.8516 106.758C14.7665 106.888 14.6812 106.997 14.6171 107.073C14.5852 107.111 14.5588 107.14 14.5407 107.16C14.5317 107.17 14.5247 107.177 14.5202 107.182C14.518 107.185 14.5163 107.186 14.5153 107.187C14.5148 107.188 14.5145 107.188 14.5144 107.188C14.5143 107.188 14.5142 107.188 14.5142 107.188C14.5142 107.188 14.5143 107.188 14.5143 107.188C14.5143 107.188 14.5143 107.188 14.5143 107.188C14.5144 107.188 14.5144 107.188 14.6571 107.328ZM15.5022 106.013C24.111 99.4369 36.2998 100.174 43.4618 107.469L43.7473 107.188C36.4356 99.7412 24.019 99.0038 15.2594 105.695L15.5022 106.013ZM43.4618 107.469C51.3525 115.506 52.0679 127.922 44.8971 136.686L45.2067 136.94C52.5097 128.014 51.7777 115.368 43.7473 107.188L43.4618 107.469ZM44.9092 136.673C44.7268 136.859 44.5954 136.993 44.4871 137.092C44.3792 137.191 44.3043 137.246 44.2375 137.28C44.1082 137.346 43.9817 137.35 43.6046 137.35V137.75C43.9511 137.75 44.1865 137.755 44.419 137.636C44.5331 137.578 44.6391 137.495 44.7574 137.387C44.8753 137.279 45.0152 137.136 45.1946 136.953L44.9092 136.673ZM43.5351 137.363L3.73232 152.105L3.87125 152.48L43.674 137.738L43.5351 137.363ZM8.21069 145.11L41.5003 133.316L41.3667 132.939L8.07712 144.733L8.21069 145.11ZM41.5945 133.246C45.9958 127.269 45.2623 118.314 40.1288 113.085L39.8434 113.366C44.8416 118.457 45.5554 127.192 41.2724 133.009L41.5945 133.246ZM40.1192 113.076C34.2682 107.862 25.4863 107.108 19.6155 110.845L19.8303 111.183C25.5385 107.549 34.125 108.27 39.8531 113.375L40.1192 113.076ZM0.371978 148.673L10.5036 119.925L10.1263 119.792L-0.00527866 148.54L0.371978 148.673ZM10.4577 119.999C10.6476 119.806 10.8359 119.566 11.0256 119.327C11.2182 119.084 11.4158 118.839 11.6337 118.617C12.0685 118.174 12.5629 117.848 13.2097 117.848V117.448C12.4092 117.448 11.818 117.858 11.3482 118.337C11.1138 118.576 10.9043 118.837 10.7125 119.078C10.5177 119.323 10.3441 119.544 10.1722 119.719L10.4577 119.999ZM13.2097 117.848C18.9415 117.848 23.957 120.034 28.2644 124.422L28.5498 124.141C24.1731 119.684 19.0569 117.448 13.2097 117.448V117.848ZM28.2523 124.408C31.8462 128.801 33.9966 133.917 33.9966 139.761H34.3966C34.3966 133.811 32.2049 128.608 28.5619 124.155L28.2523 124.408ZM33.9966 139.761C33.9966 140.462 33.826 140.969 33.4966 141.388C33.1618 141.814 32.6501 142.167 31.9348 142.531L32.1163 142.888C32.8484 142.515 33.4223 142.13 33.8111 141.635C34.2054 141.133 34.3966 140.534 34.3966 139.761H33.9966ZM31.9569 142.522L3.7331 152.842L3.87047 153.217L32.0942 142.897L31.9569 142.522ZM15.1928 124.213L7.95595 144.115L8.33186 144.252L15.5687 124.35L15.1928 124.213ZM8.20613 144.374L28.4694 137.74L28.3449 137.36L8.08168 143.994L8.20613 144.374ZM28.6034 137.511C27.8738 133.795 26.4115 130.809 24.2077 128.564L23.9223 128.844C26.0606 131.022 27.4931 133.933 28.2109 137.588L28.6034 137.511ZM24.1864 128.545C21.2748 126.321 18.3561 124.833 15.4301 124.088L15.3314 124.475C18.195 125.205 21.0657 126.665 23.9436 128.863L24.1864 128.545ZM113.079 152.829H45.052V153.229H113.079V152.829ZM45.052 152.829C44.3946 152.829 43.7196 152.493 43.2045 151.968C42.6897 151.444 42.3572 150.754 42.3572 150.081H41.9572C41.9572 150.882 42.3484 151.667 42.9191 152.248C43.4896 152.829 44.2619 153.229 45.052 153.229V152.829ZM42.3572 150.081C42.3572 149.022 42.7016 148.346 43.1912 147.93C43.6868 147.509 44.3588 147.333 45.052 147.333V146.933C44.2978 146.933 43.5224 147.124 42.9324 147.625C42.3365 148.131 41.9572 148.929 41.9572 150.081H42.3572ZM45.052 147.333H113.079V146.933H45.052V147.333ZM113.079 147.333C117.535 147.333 121.239 143.554 121.239 139.024H120.839C120.839 143.34 117.307 146.933 113.079 146.933V147.333ZM121.239 139.024C121.239 134.495 117.535 130.716 113.079 130.716V131.116C117.307 131.116 120.839 134.709 120.839 139.024H121.239ZM113.079 130.716H97.8811V131.116H113.079V130.716ZM97.8811 130.716C97.2238 130.716 96.5487 130.379 96.0337 129.855C95.5188 129.33 95.1863 128.641 95.1863 127.968H94.7863C94.7863 128.769 95.1776 129.554 95.7482 130.135C96.3187 130.716 97.091 131.116 97.8811 131.116V130.716ZM95.1863 127.968C95.1863 127.294 95.5188 126.605 96.0337 126.081C96.5487 125.556 97.2238 125.219 97.8811 125.219V124.819C97.091 124.819 96.3187 125.219 95.7482 125.8C95.1776 126.382 94.7863 127.167 94.7863 127.968H95.1863ZM97.8811 125.219H113.079V124.819H97.8811V125.219ZM113.079 125.219C120.932 125.219 127.352 131.767 127.352 139.024H127.752C127.752 131.54 121.146 124.819 113.079 124.819V125.219ZM127.352 139.024C127.352 147.012 120.939 152.829 113.079 152.829V153.229C121.139 153.229 127.752 147.253 127.752 139.024H127.352ZM109.46 130.717H94.2626V131.117H109.46V130.717ZM94.2626 130.717C86.4093 130.717 79.9889 124.169 79.9889 116.911H79.5889C79.5889 124.396 86.1948 131.117 94.2626 131.117V130.717ZM79.9889 116.911C79.9889 108.91 86.416 102.369 94.2626 102.369V101.969C86.1881 101.969 79.5889 108.696 79.5889 116.911H79.9889ZM94.2626 102.369H122.486V101.969H94.2626V102.369ZM122.486 102.369C123.144 102.369 123.819 102.706 124.334 103.231C124.849 103.755 125.181 104.444 125.181 105.118H125.581C125.581 104.317 125.19 103.532 124.619 102.95C124.049 102.369 123.276 101.969 122.486 101.969V102.369ZM125.181 105.118C125.181 106.176 124.837 106.853 124.347 107.269C123.852 107.689 123.18 107.866 122.486 107.866V108.266C123.241 108.266 124.016 108.074 124.606 107.574C125.202 107.068 125.581 106.27 125.581 105.118H125.181ZM122.486 107.866H94.2626V108.266H122.486V107.866ZM94.2626 107.866C89.8066 107.866 86.102 111.645 86.102 116.174H86.502C86.502 111.859 90.0344 108.266 94.2626 108.266V107.866ZM86.102 116.174C86.102 120.704 89.8066 124.482 94.2626 124.482V124.082C90.0344 124.082 86.502 120.49 86.502 116.174H86.102ZM94.2626 124.482H109.46V124.082H94.2626V124.482ZM109.46 124.482C110.117 124.482 110.792 124.819 111.307 125.344C111.822 125.868 112.155 126.558 112.155 127.231H112.555C112.555 126.43 112.164 125.645 111.593 125.064C111.022 124.483 110.25 124.082 109.46 124.082V124.482ZM112.155 127.231C112.155 128.322 111.975 129.193 111.558 129.788C111.151 130.369 110.495 130.717 109.46 130.717V131.117C110.596 131.117 111.388 130.727 111.885 130.018C112.373 129.323 112.555 128.351 112.555 127.231H112.155ZM135.513 107.866H106.565V108.266H135.513V107.866ZM106.565 107.866C105.908 107.866 105.233 107.529 104.718 107.004C104.203 106.48 103.871 105.791 103.871 105.118H103.471C103.471 105.918 103.862 106.703 104.433 107.285C105.003 107.866 105.775 108.266 106.565 108.266V107.866ZM103.871 105.118C103.871 104.444 104.203 103.755 104.718 103.231C105.233 102.706 105.908 102.369 106.565 102.369V101.969C105.775 101.969 105.003 102.369 104.433 102.95C103.862 103.532 103.471 104.317 103.471 105.118H103.871ZM106.565 102.369H135.513V101.969H106.565V102.369ZM135.513 102.369C137.729 102.369 139.771 101.616 141.26 100.195C142.75 98.7711 143.673 96.6899 143.673 94.0608H143.273C143.273 96.5915 142.387 98.5645 140.983 99.9052C139.577 101.248 137.639 101.969 135.513 101.969V102.369ZM143.673 94.0608C143.673 89.5312 139.969 85.7526 135.513 85.7526V86.1526C139.741 86.1526 143.273 89.7451 143.273 94.0608H143.673ZM135.513 85.7526H120.315V86.1526H135.513V85.7526ZM120.315 85.7526C119.658 85.7526 118.983 85.4157 118.468 84.8911C117.953 84.3667 117.621 83.6775 117.621 83.0041H117.221C117.221 83.805 117.612 84.59 118.183 85.1713C118.753 85.7523 119.525 86.1526 120.315 86.1526V85.7526ZM117.621 83.0041C117.621 81.9453 117.965 81.2688 118.455 80.8531C118.95 80.4325 119.622 80.2557 120.315 80.2557V79.8557C119.561 79.8557 118.786 80.0474 118.196 80.5482C117.6 81.0539 117.221 81.8516 117.221 83.0041H117.621ZM120.315 80.2557H135.513V79.8557H120.315V80.2557ZM135.513 80.2557C143.366 80.2557 149.787 86.8033 149.787 94.0608H150.187C150.187 86.5761 143.581 79.8557 135.513 79.8557V80.2557ZM149.787 94.043C149.072 102.055 142.64 107.866 135.513 107.866V108.266C142.86 108.266 149.453 102.283 150.186 94.0786L149.787 94.043ZM26.2361 152.83H24.7887V153.23H26.2361V152.83ZM24.7887 152.83C24.1314 152.83 23.4564 152.493 22.9413 151.968C22.4264 151.444 22.094 150.755 22.094 150.082H21.694C21.694 150.882 22.0852 151.667 22.6559 152.249C23.2263 152.83 23.9987 153.23 24.7887 153.23V152.83ZM22.094 150.082C22.094 149.023 22.4383 148.346 22.928 147.931C23.4235 147.51 24.0955 147.333 24.7887 147.333V146.933C24.0345 146.933 23.2591 147.125 22.6691 147.626C22.0733 148.131 21.694 148.929 21.694 150.082H22.094ZM24.7887 147.333H26.2361V146.933H24.7887V147.333ZM26.2361 147.333C26.8934 147.333 27.5684 147.67 28.0835 148.195C28.5984 148.719 28.9308 149.408 28.9308 150.082H29.3308C29.3308 149.281 28.9396 148.496 28.3689 147.914C27.7985 147.333 27.0261 146.933 26.2361 146.933V147.333ZM28.9308 150.082C28.9308 150.755 28.5984 151.444 28.0835 151.968C27.5684 152.493 26.8934 152.83 26.2361 152.83V153.23C27.0261 153.23 27.7985 152.83 28.3689 152.249C28.9396 151.667 29.3308 150.882 29.3308 150.082H28.9308ZM35.644 152.83H34.1967V153.23H35.644V152.83ZM34.1967 152.83C33.5393 152.83 32.8643 152.493 32.3492 151.968C31.8344 151.444 31.5019 150.755 31.5019 150.082H31.1019C31.1019 150.882 31.4931 151.667 32.0638 152.249C32.6343 152.83 33.4066 153.23 34.1967 153.23V152.83ZM31.5019 150.082C31.5019 149.023 31.8463 148.346 32.3359 147.931C32.8315 147.51 33.5035 147.333 34.1967 147.333V146.933C33.4425 146.933 32.6671 147.125 32.0771 147.626C31.4812 148.131 31.1019 148.929 31.1019 150.082H31.5019ZM34.1967 147.333H35.644V146.933H34.1967V147.333ZM35.644 147.333C36.3014 147.333 36.9764 147.67 37.4914 148.195C38.0063 148.719 38.3388 149.408 38.3388 150.082H38.7388C38.7388 149.281 38.3476 148.496 37.7769 147.914C37.2064 147.333 36.4341 146.933 35.644 146.933V147.333ZM38.3592 150.17C38.6789 150.821 38.5255 151.468 38.0342 151.968C37.5365 152.475 36.6875 152.83 35.644 152.83V153.23C36.7717 153.23 37.7319 152.847 38.3196 152.249C38.9139 151.643 39.1223 150.816 38.7183 149.993L38.3592 150.17Z" fill="#179346" mask="url(#path-1-outside-1_16205_163)"/>
                </svg>
                </div>
                <div class="canpay-crew-text-700 canpay-crew-text-font-18 mt-4">
                    Sign Petition 
                    <svg
                        v-on:click="openDisplayIcon()"
                    style="
                    position: relative;
                    top: -1px;
                    margin-left: 4px;
                    "
                     xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 54 54" fill="none">
                        <path d="M27 0C12.06 0 0 12.06 0 27C0 41.94 12.06 54 27 54C41.94 54 54 41.94 54 27C54 12.12 41.88 0 27 0ZM27 49.2C14.76 49.2 4.8 39.24 4.8 27C4.8 14.76 14.76 4.8 27 4.8C39.24 4.8 49.2 14.76 49.2 27C49.2 39.24 39.24 49.2 27 49.2ZM24 12C24 10.32 25.32 9 27 9C28.68 9 30 10.32 30 12C30 13.68 28.68 15 27 15C25.32 15 24 13.68 24 12ZM33.6 39.24V40.8C33.6 41.46 33.06 42 32.4 42H21.6C20.94 42 20.4 41.46 20.4 40.8V39.24C20.4 38.7 20.76 38.28 21.24 38.1L23.7 37.32C23.88 37.26 24 37.08 24 36.9V24.6H22.14C21.18 24.6 20.4 23.82 20.4 22.86C20.4 22.14 20.88 21.48 21.6 21.24L28.44 19.08C29.1 18.9 29.76 19.26 29.94 19.86C30 19.98 30 20.1 30 20.22V36.9C30 37.08 30.12 37.26 30.3 37.32L32.76 38.1C33.24 38.22 33.6 38.7 33.6 39.24Z" fill="black"/>
                        </svg>
                </div>

                <div class="mt-4">
                    <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-500">{{storeData.store_name}} - {{storeData.city}}</p>
                    <p class="canpay-crew-p-margin-bottom-1 canpay-crew-text-500" v-html="storeAddress(storeData)"></p>
                </div>
                <div class="mt-4">
                    <svg
                        style="position: relative;top:-1px;"                    
                     xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 40 47" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M20 4.7C16.0317 4.7 12.8149 7.8564 12.8149 11.75C12.8149 15.6436 16.0317 18.8 20 18.8C23.9681 18.8 27.1851 15.6436 27.1851 11.75C27.1851 7.8564 23.9681 4.7 20 4.7ZM8.02485 11.75C8.02485 5.26066 13.3863 0 20 0C26.6136 0 31.9751 5.26066 31.9751 11.75C31.9751 18.2393 26.6136 23.5 20 23.5C13.3863 23.5 8.02485 18.2393 8.02485 11.75Z" fill="black"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M20 30.55C11.7552 30.55 7.42111 33.4165 5.25893 36.8894C4.50646 38.0982 4.69572 39.2382 5.61438 40.2856C6.6165 41.4282 8.42863 42.3 10.4199 42.3H29.5801C31.5713 42.3 33.3834 41.4282 34.3855 40.2856C35.3042 39.2382 35.4934 38.0982 34.7411 36.8894C32.5789 33.4165 28.2446 30.55 20 30.55ZM1.17123 34.4393C4.40286 29.2486 10.5081 25.85 20 25.85C29.492 25.85 35.5971 29.2486 38.8287 34.4393C40.8345 37.6609 40.0667 41.0136 38.0163 43.3514C36.049 45.5942 32.8797 47 29.5801 47H10.4199C7.1202 47 3.95082 45.5942 1.98376 43.3514C-0.0667851 41.0136 -0.834462 37.6609 1.17123 34.4393Z" fill="black"/>
                    </svg> <span class="canpay-crew-text-700" v-if="storeData.signed_users_count>=0">{{storeData.signed_users_count}}</span><span class="canpay-crew-text-700" v-if="storeData.total_signer>=0">{{storeData.total_signer}}</span> of <span class="canpay-crew-text-700">{{max_consumer_allowed}}</span> Signed
                </div>

                <div class="mt-4">
                    <button class="canpay-crew-sign-petition-modal-button" v-on:click="signThePetition()">
                        Sign Petition
                    </button>
                    <button class="canpay-crew-sign-petition-not-ok-modal-button mt-2" v-on:click="closeSignActivePetition()">
                        Not Right Now
                    </button>
                </div>
            </div>

        </b-modal>

        <b-modal
            ref="canpay-crewactive-fill-petition"
            hide-footer
            v-b-modal.modal-center
            no-close-on-backdrop
            modal-backdrop
            hide-header
            id="canpay-crewactive-fill-petition"
            centered
            title="BootstrapVue"
        >
            <div  v-if="stepOFEdit == 'CanpayCrewInvite'" style="font-family:Open Sans" class="text-center">
                <div v-if="shareType != 'mayor'">
                <div>
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="80" height="80" viewBox="0 0 115 110" fill="none">
                    <rect width="114.535" height="109.847" fill="url(#pattern0_375_304)"/>
                    <defs>
                    <pattern id="pattern0_375_304" patternContentUnits="objectBoundingBox" width="1" height="1">
                    <use xlink:href="#image0_375_304" transform="matrix(0.00198976 0 0 0.00207469 -0.014353 0)"/>
                    </pattern>
                    <image id="image0_375_304" width="517" height="482" preserveAspectRatio="none" xlink:href="data:image/png;base64,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"/>
                    </defs>
                    </svg>
                </div>
                <div class="canpay-crew-text-700 canpay-crew-text-font-18 mt-3">
                    Thank you for signing 
                </div>
                <div class="canpay-crew-text-700 canpay-crew-text-font-18">
                    this petition!
                </div>

                <div class="mt-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 40 47" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M20 4.7C16.0317 4.7 12.8149 7.8564 12.8149 11.75C12.8149 15.6436 16.0317 18.8 20 18.8C23.9681 18.8 27.1851 15.6436 27.1851 11.75C27.1851 7.8564 23.9681 4.7 20 4.7ZM8.02485 11.75C8.02485 5.26066 13.3863 0 20 0C26.6136 0 31.9751 5.26066 31.9751 11.75C31.9751 18.2393 26.6136 23.5 20 23.5C13.3863 23.5 8.02485 18.2393 8.02485 11.75Z" fill="black"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M20 30.55C11.7552 30.55 7.42111 33.4165 5.25893 36.8894C4.50646 38.0982 4.69572 39.2382 5.61438 40.2856C6.6165 41.4282 8.42863 42.3 10.4199 42.3H29.5801C31.5713 42.3 33.3834 41.4282 34.3855 40.2856C35.3042 39.2382 35.4934 38.0982 34.7411 36.8894C32.5789 33.4165 28.2446 30.55 20 30.55ZM1.17123 34.4393C4.40286 29.2486 10.5081 25.85 20 25.85C29.492 25.85 35.5971 29.2486 38.8287 34.4393C40.8345 37.6609 40.0667 41.0136 38.0163 43.3514C36.049 45.5942 32.8797 47 29.5801 47H10.4199C7.1202 47 3.95082 45.5942 1.98376 43.3514C-0.0667851 41.0136 -0.834462 37.6609 1.17123 34.4393Z" fill="black"/>
                    </svg> <span class="canpay-crew-text-700" v-if="storeData.signed_users_count>=0">{{storeData.signed_users_count+1}}</span><span class="canpay-crew-text-700" v-if="storeData.total_signer>=0">{{storeData.total_signer+1}}</span> of <span class="canpay-crew-text-700">{{max_consumer_allowed}}</span> Signed
                </div>
                
                <hr style="border:0.5px solid #DFDFDF;">
                <div>
                    <p class="canpay-crew-text-700 canpay-crew-text-font-16 canpay-crew-p-margin-bottom-1">Get 5,000 CanPay Points</p>
                    <p class="canpay-crew-text-700 canpay-crew-text-font-16 canpay-crew-p-margin-bottom">For you and your friends</p>
                <div class="mt-4">
                    <p class="canpay-crew-p-margin-bottom-1">Share with a friend and if they sign the</p>
                    <p class="canpay-crew-p-margin-bottom-1">Petition too, you both receive an extra</p>
                    <p class="canpay-crew-p-margin-bottom-1"><span class="canpay-crew-text-700">5,000</span> CanPay points when this store</p>
                    <p class="canpay-crew-p-margin-bottom-1">starts accepting CanPay.</p>
                </div>
                </div>
                <div class="mt-3">
                    <input type="email"
                     maxlength="50"      
                     class="canpay-crew-general-input-box-1"
                     :style="petitionEmail == null || !isValidMail?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}"
                      placeholder="Friend's Email/Phone"
                       v-model="petitionEmail" @input="onEmailInput">
                       <p class="text-red-crew mt-2" v-if="petitionEmail == null && !isValidMail">{{mailError}}</p>
                       <p class="text-red-crew mt-2" v-else-if="!isValidMail">{{mailInvalidError}}</p>
                </div>
                <div class="mt-4">
                    <button class="canpay-crew-sign-petition-modal-button" v-on:click="sendPetition()">
                        Send
                    </button>
                    <button class="canpay-crew-sign-petition-not-ok-modal-button mt-2" v-on:click="closeCanPayCrewfillPetition()">
                        Not Right Now
                    </button>
                </div>
                </div>
                <div v-else>
                    <svg xmlns="http://www.w3.org/2000/svg" width="50" height="90" viewBox="0 0 308 426" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M154 101.1C123.444 101.1 98.6747 125.344 98.6747 155.25C98.6747 185.156 123.444 209.4 154 209.4C184.554 209.4 209.325 185.156 209.325 155.25C209.325 125.344 184.554 101.1 154 101.1ZM61.7913 155.25C61.7913 105.406 103.075 65 154 65C204.925 65 246.208 105.406 246.208 155.25C246.208 205.094 204.925 245.5 154 245.5C103.075 245.5 61.7913 205.094 61.7913 155.25Z" fill="black"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M154 299.65C90.5152 299.65 57.1426 321.667 40.4938 348.342C34.6998 357.627 36.157 366.383 43.2307 374.428C50.9471 383.203 64.9004 389.9 80.233 389.9H227.767C243.099 389.9 257.052 383.203 264.768 374.428C271.842 366.383 273.299 357.627 267.507 348.342C250.857 321.667 217.484 299.65 154 299.65ZM9.01844 329.523C33.902 289.654 80.9124 263.55 154 263.55C227.088 263.55 274.098 289.654 298.981 329.523C314.426 354.267 308.514 380.019 292.726 397.976C277.578 415.202 253.174 426 227.767 426H80.233C54.8255 426 30.4213 415.202 15.275 397.976C-0.514246 380.019 -6.42536 354.267 9.01844 329.523Z" fill="black"/>
                    <path d="M155.231 6C160.388 6.00003 164.568 10.089 164.568 15.1328C164.568 18.4292 162.782 21.317 160.105 22.9229C160.26 23.1595 160.4 23.413 160.523 23.6836L184.26 75.7031L232.125 33.4229C231.613 32.2926 231.326 31.0425 231.326 29.7266C231.326 24.6827 235.506 20.5938 240.663 20.5938C245.82 20.5938 250 24.6827 250 29.7266C250 34.5157 246.231 38.44 241.436 38.8242V125.469C241.436 128.523 238.96 131 235.905 131H71.4961C68.4416 131 65.9658 128.523 65.9658 125.469V38.1377C60.9817 37.9464 57.001 33.9364 57.001 29.0146C57.001 23.971 61.1812 19.8821 66.3379 19.8818C71.4947 19.8818 75.6757 23.9709 75.6758 29.0146C75.6758 30.2798 75.412 31.4845 74.9365 32.5801C74.9604 32.5991 74.9851 32.6172 75.0088 32.6367L126.9 75.3135L150.46 23.6836C150.546 23.4943 150.641 23.3133 150.742 23.1406C147.853 21.5878 145.895 18.5847 145.895 15.1328C145.895 10.089 150.075 6 155.231 6Z" fill="black"/>
                    </svg>
                    <p class="canpay-crew-text-font-18 canpay-crew-text-700 mt-3 canpay-crew-p-margin-bottom-1">You are now the Mayor of</p>
                    <p class="canpay-crew-text-font-18 canpay-crew-text-700 mb-4">{{storeData.store_name}} - {{storeData.city}}!</p>

                    <hr style="border:0.5px solid #DFDFDF; margin:16px 0px;"/>
                    <p class="canpay-crew-text-font-14 canpay-crew-text-700 canpay-crew-p-margin-bottom-1 mt-4">Get 5,000 CanPay Points</p>
                    <p class="canpay-crew-text-font-14 canpay-crew-text-700">For you and your friend</p>

                    <p class="canpay-crew-text-font-14 canpay-crew-p-margin-bottom-1">Share with a friend and if they sign the</p>
                    <p class="canpay-crew-text-font-14 canpay-crew-p-margin-bottom-1">Petition too, you both receive an extra</p>
                    <p class="canpay-crew-text-font-14 canpay-crew-p-margin-bottom-1"><b>5,000</b> CanPay Points when this store</p>
                    <p class="canpay-crew-text-font-14">starts accepting CanPay.</p>

                    <input type="text" maxlength="50"
                    :style="petitionEmail == null || !isValidMail?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}"
                    class="canpay-crew-general-input-box-1 canpay-crew-p-margin-bottom-2 mt-3"
                    placeholder="Friend's Email/Phone" v-model="petitionEmail" @input="onEmailInput">
                    <p class="text-red-crew mt-2" v-if="petitionEmail == null && !isValidMail">{{mailError}}</p>
                    <p class="text-red-crew mt-2" v-else-if="!isValidMail">{{mailInvalidError}}</p>
                    <button class="canpay-crew-sign-petition-modal-button mt-3" v-on:click="sendPetition()">
                        Send
                    </button>
                        <button class="canpay-crew-sign-petition-not-ok-modal-button mt-2 mb-5" v-on:click="closeCanPayCrewfillPetition()">
                            Not Right Now
                        </button>
                </div>
            </div>
            <div style="font-family:Open Sans" v-if="stepOFEdit == 'CanpayCrewSuccessShare'" class="text-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="55" height="55" viewBox="0 0 160 159" fill="none">
                <mask id="path-1-outside-1_16385_131" maskUnits="userSpaceOnUse" x="0" y="0" width="160" height="159" fill="black">
                <rect fill="white" width="160" height="159"/>
                <path d="M159 98.5756C159 91.7189 154.084 85.9171 147.589 84.6864C149.696 82.2251 150.924 79.0605 150.924 75.5442C150.924 67.8085 144.604 61.4793 136.88 61.4793H101.242L104.753 36.8656C106.509 24.5588 98.0822 13.8343 89.3044 9.9664C85.0911 8.03247 81.7556 8.03247 79.1222 9.61478C75.4356 11.9003 74.5578 17.3505 74.3822 24.5588C74.2067 34.9317 68.94 73.4345 48.5756 73.4345H43.1333V63.4132C43.1333 62.0067 41.9044 60.776 40.5 60.776H3.63333C2.22889 60.776 1 62.0067 1 63.4132V155.363C1 156.769 2.22889 158 3.63333 158H40.5C41.9044 158 43.1333 156.769 43.1333 155.363V146.396C45.5911 147.275 48.2244 148.506 50.8578 149.737C59.2844 153.605 68.94 158 81.7556 158H126.171C133.72 158 139.864 151.847 139.864 144.287C139.864 141.122 138.811 138.133 136.88 135.848C144.604 135.848 150.924 129.518 150.924 121.783C150.924 118.267 149.696 115.102 147.589 112.641C154.084 111.234 159 105.432 159 98.5756ZM37.8667 152.726H6.26667V66.0504H37.8667V152.726ZM144.956 107.366H121.08C119.676 107.366 118.447 108.597 118.447 110.003C118.447 111.41 119.676 112.641 121.08 112.641H136.704C141.62 112.641 145.482 116.684 145.482 121.431C145.482 126.354 141.444 130.222 136.704 130.222H118.447C117.042 130.222 115.813 131.452 115.813 132.859C115.813 134.265 117.042 135.496 118.447 135.496H126.171C130.911 135.496 134.598 139.364 134.598 143.935C134.598 148.682 130.736 152.374 126.171 152.374H81.58C69.8178 152.374 60.8644 148.155 52.9644 144.638C49.6289 143.056 46.2933 141.649 42.9578 140.419V78.7088H48.4C77.3667 78.7088 79.4733 25.262 79.4733 24.7346C79.4733 22.449 79.6489 15.4166 81.7556 14.0101C82.6333 13.4826 84.3889 13.6585 86.8467 14.8891C92.4644 17.3505 100.716 25.262 99.3111 36.1624L95.6244 63.7648C95.4489 64.4681 95.8 65.3471 96.3267 65.8746C96.8533 66.402 97.5556 66.7536 98.2578 66.7536H136.88C141.796 66.7536 145.658 70.7973 145.658 75.5442C145.658 80.2912 141.62 84.3348 136.88 84.3348H121.256C119.851 84.3348 118.622 85.5655 118.622 86.972C118.622 88.3785 119.851 89.6092 121.256 89.6092H144.956C149.871 89.6092 153.733 93.6528 153.733 98.3998C153.733 103.498 149.696 107.366 144.956 107.366Z"/>
                <path d="M21.1889 25.4379L33.3022 31.2396L39.0956 43.3707C39.4467 44.2497 40.5 44.953 41.5533 44.953C42.6067 44.953 43.4844 44.4255 44.0111 43.3707L49.8044 31.2396L61.9178 25.4379C62.7956 25.0862 63.4978 24.0314 63.4978 22.9765C63.4978 21.9216 62.9711 21.0426 61.9178 20.5151L49.8044 14.7133L44.0111 2.58231C43.66 1.70325 42.6067 1 41.5533 1C40.5 1 39.6222 1.52743 39.0956 2.58231L33.3022 14.7133L21.1889 20.5151C20.3111 20.8667 19.6089 21.9216 19.6089 22.9765C19.6089 24.0314 20.3111 25.0862 21.1889 25.4379ZM36.4622 19.2844C36.9889 19.1086 37.5156 18.5812 37.6911 18.0538L41.5533 9.9664L45.4156 18.0538C45.5911 18.5812 46.1178 19.1086 46.6444 19.2844L54.72 23.1523L46.6444 26.8443C46.1178 27.0202 45.5911 27.5476 45.4156 28.075L41.5533 36.1624L37.6911 28.075C37.5156 27.5476 36.9889 27.0202 36.4622 26.8443L28.3867 22.9765L36.4622 19.2844Z"/>
                <path d="M121.782 35.1075H127.576V40.9093C127.576 42.3158 128.804 43.5465 130.209 43.5465C131.613 43.5465 132.842 42.3158 132.842 40.9093V35.1075H138.636C140.04 35.1075 141.269 33.8768 141.269 32.4703C141.269 31.0638 140.04 29.8331 138.636 29.8331H132.842V24.0314C132.842 22.6249 131.613 21.3942 130.209 21.3942C128.804 21.3942 127.576 22.6249 127.576 24.0314V29.8331H121.782C120.378 29.8331 119.149 31.0638 119.149 32.4703C119.149 33.8768 120.378 35.1075 121.782 35.1075Z"/>
                </mask>
                <path d="M159 98.5756C159 91.7189 154.084 85.9171 147.589 84.6864C149.696 82.2251 150.924 79.0605 150.924 75.5442C150.924 67.8085 144.604 61.4793 136.88 61.4793H101.242L104.753 36.8656C106.509 24.5588 98.0822 13.8343 89.3044 9.9664C85.0911 8.03247 81.7556 8.03247 79.1222 9.61478C75.4356 11.9003 74.5578 17.3505 74.3822 24.5588C74.2067 34.9317 68.94 73.4345 48.5756 73.4345H43.1333V63.4132C43.1333 62.0067 41.9044 60.776 40.5 60.776H3.63333C2.22889 60.776 1 62.0067 1 63.4132V155.363C1 156.769 2.22889 158 3.63333 158H40.5C41.9044 158 43.1333 156.769 43.1333 155.363V146.396C45.5911 147.275 48.2244 148.506 50.8578 149.737C59.2844 153.605 68.94 158 81.7556 158H126.171C133.72 158 139.864 151.847 139.864 144.287C139.864 141.122 138.811 138.133 136.88 135.848C144.604 135.848 150.924 129.518 150.924 121.783C150.924 118.267 149.696 115.102 147.589 112.641C154.084 111.234 159 105.432 159 98.5756ZM37.8667 152.726H6.26667V66.0504H37.8667V152.726ZM144.956 107.366H121.08C119.676 107.366 118.447 108.597 118.447 110.003C118.447 111.41 119.676 112.641 121.08 112.641H136.704C141.62 112.641 145.482 116.684 145.482 121.431C145.482 126.354 141.444 130.222 136.704 130.222H118.447C117.042 130.222 115.813 131.452 115.813 132.859C115.813 134.265 117.042 135.496 118.447 135.496H126.171C130.911 135.496 134.598 139.364 134.598 143.935C134.598 148.682 130.736 152.374 126.171 152.374H81.58C69.8178 152.374 60.8644 148.155 52.9644 144.638C49.6289 143.056 46.2933 141.649 42.9578 140.419V78.7088H48.4C77.3667 78.7088 79.4733 25.262 79.4733 24.7346C79.4733 22.449 79.6489 15.4166 81.7556 14.0101C82.6333 13.4826 84.3889 13.6585 86.8467 14.8891C92.4644 17.3505 100.716 25.262 99.3111 36.1624L95.6244 63.7648C95.4489 64.4681 95.8 65.3471 96.3267 65.8746C96.8533 66.402 97.5556 66.7536 98.2578 66.7536H136.88C141.796 66.7536 145.658 70.7973 145.658 75.5442C145.658 80.2912 141.62 84.3348 136.88 84.3348H121.256C119.851 84.3348 118.622 85.5655 118.622 86.972C118.622 88.3785 119.851 89.6092 121.256 89.6092H144.956C149.871 89.6092 153.733 93.6528 153.733 98.3998C153.733 103.498 149.696 107.366 144.956 107.366Z" fill="#179346"/>
                <path d="M21.1889 25.4379L33.3022 31.2396L39.0956 43.3707C39.4467 44.2497 40.5 44.953 41.5533 44.953C42.6067 44.953 43.4844 44.4255 44.0111 43.3707L49.8044 31.2396L61.9178 25.4379C62.7956 25.0862 63.4978 24.0314 63.4978 22.9765C63.4978 21.9216 62.9711 21.0426 61.9178 20.5151L49.8044 14.7133L44.0111 2.58231C43.66 1.70325 42.6067 1 41.5533 1C40.5 1 39.6222 1.52743 39.0956 2.58231L33.3022 14.7133L21.1889 20.5151C20.3111 20.8667 19.6089 21.9216 19.6089 22.9765C19.6089 24.0314 20.3111 25.0862 21.1889 25.4379ZM36.4622 19.2844C36.9889 19.1086 37.5156 18.5812 37.6911 18.0538L41.5533 9.9664L45.4156 18.0538C45.5911 18.5812 46.1178 19.1086 46.6444 19.2844L54.72 23.1523L46.6444 26.8443C46.1178 27.0202 45.5911 27.5476 45.4156 28.075L41.5533 36.1624L37.6911 28.075C37.5156 27.5476 36.9889 27.0202 36.4622 26.8443L28.3867 22.9765L36.4622 19.2844Z" fill="#179346"/>
                <path d="M121.782 35.1075H127.576V40.9093C127.576 42.3158 128.804 43.5465 130.209 43.5465C131.613 43.5465 132.842 42.3158 132.842 40.9093V35.1075H138.636C140.04 35.1075 141.269 33.8768 141.269 32.4703C141.269 31.0638 140.04 29.8331 138.636 29.8331H132.842V24.0314C132.842 22.6249 131.613 21.3942 130.209 21.3942C128.804 21.3942 127.576 22.6249 127.576 24.0314V29.8331H121.782C120.378 29.8331 119.149 31.0638 119.149 32.4703C119.149 33.8768 120.378 35.1075 121.782 35.1075Z" fill="#179346"/>
                <path d="M159 98.5756C159 91.7189 154.084 85.9171 147.589 84.6864C149.696 82.2251 150.924 79.0605 150.924 75.5442C150.924 67.8085 144.604 61.4793 136.88 61.4793H101.242L104.753 36.8656C106.509 24.5588 98.0822 13.8343 89.3044 9.9664C85.0911 8.03247 81.7556 8.03247 79.1222 9.61478C75.4356 11.9003 74.5578 17.3505 74.3822 24.5588C74.2067 34.9317 68.94 73.4345 48.5756 73.4345H43.1333V63.4132C43.1333 62.0067 41.9044 60.776 40.5 60.776H3.63333C2.22889 60.776 1 62.0067 1 63.4132V155.363C1 156.769 2.22889 158 3.63333 158H40.5C41.9044 158 43.1333 156.769 43.1333 155.363V146.396C45.5911 147.275 48.2244 148.506 50.8578 149.737C59.2844 153.605 68.94 158 81.7556 158H126.171C133.72 158 139.864 151.847 139.864 144.287C139.864 141.122 138.811 138.133 136.88 135.848C144.604 135.848 150.924 129.518 150.924 121.783C150.924 118.267 149.696 115.102 147.589 112.641C154.084 111.234 159 105.432 159 98.5756ZM37.8667 152.726H6.26667V66.0504H37.8667V152.726ZM144.956 107.366H121.08C119.676 107.366 118.447 108.597 118.447 110.003C118.447 111.41 119.676 112.641 121.08 112.641H136.704C141.62 112.641 145.482 116.684 145.482 121.431C145.482 126.354 141.444 130.222 136.704 130.222H118.447C117.042 130.222 115.813 131.452 115.813 132.859C115.813 134.265 117.042 135.496 118.447 135.496H126.171C130.911 135.496 134.598 139.364 134.598 143.935C134.598 148.682 130.736 152.374 126.171 152.374H81.58C69.8178 152.374 60.8644 148.155 52.9644 144.638C49.6289 143.056 46.2933 141.649 42.9578 140.419V78.7088H48.4C77.3667 78.7088 79.4733 25.262 79.4733 24.7346C79.4733 22.449 79.6489 15.4166 81.7556 14.0101C82.6333 13.4826 84.3889 13.6585 86.8467 14.8891C92.4644 17.3505 100.716 25.262 99.3111 36.1624L95.6244 63.7648C95.4489 64.4681 95.8 65.3471 96.3267 65.8746C96.8533 66.402 97.5556 66.7536 98.2578 66.7536H136.88C141.796 66.7536 145.658 70.7973 145.658 75.5442C145.658 80.2912 141.62 84.3348 136.88 84.3348H121.256C119.851 84.3348 118.622 85.5655 118.622 86.972C118.622 88.3785 119.851 89.6092 121.256 89.6092H144.956C149.871 89.6092 153.733 93.6528 153.733 98.3998C153.733 103.498 149.696 107.366 144.956 107.366Z" stroke="#1B9146" stroke-width="2" mask="url(#path-1-outside-1_16385_131)"/>
                <path d="M21.1889 25.4379L33.3022 31.2396L39.0956 43.3707C39.4467 44.2497 40.5 44.953 41.5533 44.953C42.6067 44.953 43.4844 44.4255 44.0111 43.3707L49.8044 31.2396L61.9178 25.4379C62.7956 25.0862 63.4978 24.0314 63.4978 22.9765C63.4978 21.9216 62.9711 21.0426 61.9178 20.5151L49.8044 14.7133L44.0111 2.58231C43.66 1.70325 42.6067 1 41.5533 1C40.5 1 39.6222 1.52743 39.0956 2.58231L33.3022 14.7133L21.1889 20.5151C20.3111 20.8667 19.6089 21.9216 19.6089 22.9765C19.6089 24.0314 20.3111 25.0862 21.1889 25.4379ZM36.4622 19.2844C36.9889 19.1086 37.5156 18.5812 37.6911 18.0538L41.5533 9.9664L45.4156 18.0538C45.5911 18.5812 46.1178 19.1086 46.6444 19.2844L54.72 23.1523L46.6444 26.8443C46.1178 27.0202 45.5911 27.5476 45.4156 28.075L41.5533 36.1624L37.6911 28.075C37.5156 27.5476 36.9889 27.0202 36.4622 26.8443L28.3867 22.9765L36.4622 19.2844Z" stroke="#1B9146" stroke-width="2" mask="url(#path-1-outside-1_16385_131)"/>
                <path d="M121.782 35.1075H127.576V40.9093C127.576 42.3158 128.804 43.5465 130.209 43.5465C131.613 43.5465 132.842 42.3158 132.842 40.9093V35.1075H138.636C140.04 35.1075 141.269 33.8768 141.269 32.4703C141.269 31.0638 140.04 29.8331 138.636 29.8331H132.842V24.0314C132.842 22.6249 131.613 21.3942 130.209 21.3942C128.804 21.3942 127.576 22.6249 127.576 24.0314V29.8331H121.782C120.378 29.8331 119.149 31.0638 119.149 32.4703C119.149 33.8768 120.378 35.1075 121.782 35.1075Z" stroke="#1B9146" stroke-width="2" mask="url(#path-1-outside-1_16385_131)"/>
                </svg>
                <p class="canpay-crew-text-font-18 canpay-crew-text-700 canpay-crew-p-margin-bottom-1 mt-3">Thank you! Sent  {{isEmail ? 'an email' : 'a text'}}</p>
                <p class="canpay-crew-text-font-18 canpay-crew-text-700 canpay-crew-p-margin-bottom-1 mb-3">{{isEmail ? '' : 'message'}} to your friend.</p>
                 <p class="canpay-crew-text-font-14 canpay-crew-p-margin-bottom-1">Please share with additional friends</p>
                 <p class="canpay-crew-text-font-14 canpay-crew-p-margin-bottom-1">to help convince this store to accept</p>
                 <p class="canpay-crew-text-font-14 canpay-crew-p-margin-bottom-1">CanPay and you both get more </p>
                 <p class="canpay-crew-text-font-14 canpay-crew-p-margin-bottom-1">points.</p>
                 <button class="canpay-crew-sign-petition-modal-button mt-4" v-on:click="openShareModal()">Invite Another Friend</button>
                    <button class="canpay-crew-sign-petition-not-ok-modal-button mt-2 mb-3" v-on:click="closeCanPayCrewfillPetition()">
                        Not Right Now
                    </button>
            </div>
        </b-modal>

        <b-modal
            ref="display-icon-modal"
            hide-footer
            v-b-modal.modal-center
            no-close-on-backdrop
            modal-backdrop
            hide-header
            id="display-icon-modal"
            centered
            title="BootstrapVue"
        >
            <div class="text-center">
                <p style="text-align:justify;font-weight:500;padding:12px 12px 0px 12px;">
                    Signing your name to a petition lets the merchant know you want to be able to shop with CanPay at their business. As more CanPay Members add their names to the petition, it helps encourage that business to begin accepting CanPay. Once the business does start accepting CanPay, you and every other CanPay Member who has their name on the list will receive extra CanPay Points to use for discounts at that merchant immediately. You’ll also receive special Spin-to-Win wheel spins each month that the business continues accepting CanPay. You can have up to {{petition_per_consumer}} active petitions at any one time.
                </p>
                <button class="canpay-crew-petition-button mt-2 canpay-crew-text-font-14 w-100 canpay-crew-text-700 pt-2 pb-2" v-on:click="showActivePetition()">OK</button>
            </div>
        </b-modal>
    </div>
</template>
<script>
import constants from "../../Common/constant.js";
export default {
    name:"CanpayCrewActivePetition",
    data(){
        return {
            storeData:{},
            petitionEmail:"",
            isEmail:true,
            isValidMail:true,
            mailError:"Email/Phone is required",
            mailInvalidError:"Provide a valid email/phone",
            max_consumer_allowed:process.env.VUE_APP_MAX_CONSUMER_ALLOWED_TO_SIGN,
            stepOFEdit:'CanpayCrewInvite',
            shareType:'mayor',
            petition_per_consumer:process.env.VUE_APP_SIGN_MAX_PETITION_PER_CONSUMER
        }
    },
    props:{
        storeAddress:
        {
            type:Function
        },
        signPetition:{
            type:Function
        },
        shareThePetition:{
            type:Function
        },
        passPetitionStatistics:{
            type:Function
        },
        fetchPetition:{
            type:Function
        },
        increaseActivePetitionCount: {
            type: Boolean,
            default: false
        }
    },
    methods:{
        showActivePetitionCount() {
            const count = this.passPetitionStatistics().consumer_active_petition_count;
            return this.increaseActivePetitionCount ? count : count + 1;
        },
        checkForPotins(){
            if(this.shareType == 'mayor'){
                return constants.mayor_canpay_point_rewarded
            }else if(this.shareType == 'crew leader'){
                return constants.crew_leader_canpay_point_rewarded
            }else if(this.sharedType == 'crew member'){
                return constants.crew_member_canpay_point_rewarded
            }
        },
        openDisplayIcon(){
            let self = this;
            self.$refs['canpay-crewactive-petition'].hide();
            self.$refs['display-icon-modal'].show();

        },
        showActivePetition(){
            let self = this;
            self.$refs['display-icon-modal'].hide();
            self.$refs['canpay-crewactive-petition'].show();
        },
        onEmailInput(event){
            const liveInput = event.target.value;
            // Check if input is email or phone number
            if(!this.isValidMail) {
                // Try email validation first
                this.isValidMail = this.checkEmailValidation(liveInput) || this.checkPhoneValidation(liveInput);
            }
        },
        openShareModal(){
            this.petitionEmail = "";
            this.isValidMail = true;
            this.stepOFEdit = 'CanpayCrewInvite'
        },
        resetPetitionEmail(){
            let self = this;
            self.isValidMail = true;
             self.petitionEmail = "";
        },
        showCanPayCrewActivePetition(item){
            let self = this;
            self.resetPetitionEmail();
            self.storeData = item;
            this.$refs['canpay-crewactive-petition'].show();
        },
        closeSignActivePetition(){
            this.$refs['canpay-crewactive-petition'].hide();
        },
        showSignSharingPetition(item){
            this.shareType = item;
            this.stepOFEdit = 'CanpayCrewInvite';
            this.isValidMail = true;
            this.$refs['canpay-crewactive-fill-petition'].show();
        },
        signThePetition(){
            let payload = {
                petition_id: this.storeData.id
            }
            try{
            this.signPetition(payload);
            this.$refs['canpay-crewactive-petition'].hide();

            }catch(err){
                
            }

        },
        checkEmailValidation(email){
            const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return regex.test(email);
        },
        
        checkPhoneValidation(phone){
            // Basic phone validation - accepts formats like: 1234567890, ************, ************
            const regex = /^\d{10}$|^(\d{3}[-.]){2}\d{4}$/;
            return regex.test(phone);
        },
        checkMail(){
            this.petitionEmail = this.petitionEmail == ''?null:this.petitionEmail;
        },
        sendPetition(){
            // it should have at least one alphanumeric character
            let self = this;
            self.isValidMail = self.checkEmailValidation(self.petitionEmail) || self.checkPhoneValidation(self.petitionEmail);
            this.checkMail();
            if(self.isValidMail){
            if(self.petitionEmail != ""){
                self.isEmail = self.checkEmailValidation(self.petitionEmail);
                const payload = {
                    email:self.petitionEmail,
                    petition_id:self.storeData.id,
                    type: self.isEmail ? 'email' : 'phone'
                }
                try{
                    self.shareThePetition(payload,self.storeData);
                    self.stepOFEdit = 'CanpayCrewSuccessShare'
                }catch(err){

                }

            }
            }
        },

        closeCanPayCrewfillPetition(){
            this.$refs['canpay-crewactive-fill-petition'].hide();
        }
    }
}
</script>
<style scoped>

</style>
