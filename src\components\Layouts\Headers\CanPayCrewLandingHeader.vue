<template>
    <div class="crew-landing-header">
      <div class="container crew-landing-header__container">
        <div class="crew-landing-header__left">
          <svg
            v-if="currentUser != null"
            @click="clickBack"
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 67 56"
            fill="none"
            class="crew-landing-header__back-icon"
          >
            <path
              d="M2.38113 25.5833L26.6029 1C27.9167 -0.333333 30.0515 -0.333333 31.3652 1C32.6789 2.33333 32.6789 4.5 31.3652 5.83333L12.8909 24.5833H63.6336C65.5221 24.5833 67 26.0833 67 28C67 29.9167 65.5221 31.4167 63.6336 31.4167H12.8909L31.3652 50.1667C32.6789 51.5 32.6789 53.6667 31.3652 55C30.7083 55.6667 29.8873 56 28.9841 56C28.0809 56 27.2598 55.6667 26.6029 55L2.38113 30.4167L0 28L2.38113 25.5833Z"
              fill="white"
            />
          </svg>
          <span class="ml-2 crew-landing-header__store-name">
            <span v-if="storeName != null && storeShortName == null && logoUrl == null" class="crew-landing-header__store-text">
              {{ storeName }}
            </span>
            <span v-else-if="storeShortName != null && logoUrl == null">
              {{ storeShortName }}
            </span>
          </span>
        </div>
        <div class="crew-landing-header__logo-wrapper" v-if="logoUrl != null">
          <img
            :src="logoUrl"
            alt="Merchant Logo"
            class="crew-landing-header__logo"
          />
        </div>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    name: "CanPayCrewLandingHeader",
    created() {
      this.currentUser = localStorage.getItem("consumer_login_response")
        ? JSON.parse(localStorage.getItem("consumer_login_response"))
        : null;
    },
    data() {
      return {
        storeName: null,
        storeShortName: null,
        logoUrl: null,
        currentUser: null,
        crewBackButtonStep: null,
      };
    },
    mounted() {
      this.$root.$on("Show_current_petition_store_name", (data) => {
        if (data[0]) this.storeName = data[0];
        if (data[1]) this.logoUrl = data[1];
        if (data[2]) this.storeShortName = data[2];
      });
      if (localStorage.getItem("crewBackButtonStep") != null) {
        this.crewBackButtonStep = localStorage.getItem("crewBackButtonStep");
        localStorage.removeItem("crewBackButtonStep");
      }
    },
    methods: {
      clickBack() {
        const from = this.$previousRoute();
        console.log("from ", from.name);
        if (from && from.name === "login") {
          this.$router.replace({ name: "canpaycrew" });
        } else if (from && from.name === "canpaycrew") {
          localStorage.setItem("crewBackButtonStep", this.crewBackButtonStep);
          this.$router.go(-1);
        } else {
          this.$router.go(-1);
        }
      },
    },
  };
  </script>
  
  <style scoped>
  .crew-landing-header {
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, #117237, #0b6b32);
    border-radius: 0 0 15px 15px;
    position: relative;
    z-index: 1;
  }
  
  .crew-landing-header__container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .crew-landing-header__left {
    display: flex;
  }
  
  .crew-landing-header__back-icon {
    position: relative;
    top: 2px;
    cursor: pointer;
  }
  
  .crew-landing-header__store-name {
    color: #ffffff;
    font-size: 14px;
    font-weight: 600;
  }
  
  .crew-landing-header__logo-wrapper {
    width: 120px;
    height: 68px;
    overflow: hidden;
    border-radius: 8px;
  }
  
  .crew-landing-header__logo {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
  }
  .crew-landing-header__store-text {
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 70vw; /* default max width */
  }
  </style>
  