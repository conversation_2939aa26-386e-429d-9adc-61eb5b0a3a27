.progress-bar{
    background-color: $cp-white !important;
}
.suffix-label{
    font-weight: 600 !important;
}
.progress-bar-element{
    background-color: $cp-secondary !important;
}
.opt-suffix{
    font-weight: 500;
    font-size: 13px; 
    cursor: pointer;
}
.suffix-option{
    margin-top: 10px;
    margin-bottom: 10px;
    font-weight: 500;
    font-size: 13px;
    cursor: pointer;
}
#suffix-btn.dropdown-toggle::after {
    display:none !important;
    margin-left: 8px !important;
    margin-top: 3px !important;
    border-top: 0.7em solid !important;
    border-right: 0.4em solid transparent !important;
    border-left: 0.4em solid transparent !important;
    
}
#btn-month.dropdown-toggle::after {
    display: inline-block;
    margin-left: .255em;
    vertical-align: .255em;
    border-top: .7em solid;
    border-right: .4em solid transparent;
    border-bottom: 0;
    border-left: .4em solid transparent;
}
.text-month {
    float: left;
    font-weight: 500;
    margin-left: 20px;
    font-family: $cp-font !important;
    font-size: 14px !important;
}

@media only screen and ( width:375px ){
    .day-year {
      height: 50px !important;
      text-align: center !important;
      border-radius: 8px !important;
      width: 66px !important;
      margin-left: -17px !important;
  }
} 

.dob_year::placeholder{
    text-align: center !important;
}
.dob-day::placeholder{
    text-align: center !important;
}
@supports (-webkit-touch-callout: none) {
    /* CSS specific to iOS devices */ 
    @media only screen and ( min-width:375px ) and ( min-height:667px ){
        .label-row{
            margin-top: -9px!important;
        }
    }
  }
  


//for smaller phones like iphone 5/SE
@media only screen and ( min-width:270px ) and ( max-width:359px ){
    .dob-row{
        margin-top: 10px !important;
        margin-left: 0px !important;
        margin-right: 0px !important;
    }
    .dob-day {
        width: 53px !important;
        height: 43px !important;
        margin-left: -25px !important;
        border-radius: 9px !important;
        text-align: center !important;
    }
    .dob_year{
        width: 57px !important;
        margin-left: -15px !important;
        height: 43px !important;
        border-radius: 9px !important;
        text-align: center !important;
    }
    .text-suffix{
        // margin-left: -8px;
    }
    #suffix-btn.dropdown-toggle::after {
        margin-left: 17px !important;
        margin-top: 4px !important;
        margin-right: 16px;
        
    }
    .text-month {
        margin-left: 8px;
    }
    .font-email{
        font-size: 13px !important;
    }
    .font-email::placeholder{
        font-size: 13px !important;
    }
    #btn-month.dropdown-toggle::after{
        margin-left: 40px !important;
        margin-top: 7px !important;
    }
    .suffix-label{
        font-size: 10px;
        font-weight: 600 !important;
    }
    .info-icon{
        float: right;
        margin-top: -14px;
    }
    .progress-bar-row{
        margin-top: 5px;
    }
    .progress-label-20{
        color: $cp-white;
        font-size: 9px;
        float: left;
        margin-left: -12px;
    }
    .progress-label-40{
        color: $cp-white;
        font-size: 9px;
        float: left;
        margin-left: -7px;
    }
    .progress-label-60{
        color: $cp-white;
        font-size: 9px;
        float: left;
        margin-left: -2px;;
    }
    .progress-label-80{
        color: $cp-white;
        font-size: 9px;
        float: left;
        margin-left: 2px;
    }
    .progress-label-100{
        color: $cp-white;
        font-size: 9px;
        float: left;
        margin-left: 2px;
    }
    .enter-name-row-gap{
        margin-top: 10px;
        margin-left: 3px !important;
        margin-right: 3px !important;
    }
    .setup-label{
        color: $cp-white;
        font-weight: 400;
        font-size: 15px;
        font-family: inherit;
        letter-spacing: 1px;
    }
    .label-row{
        margin-top: -9px;
    }
    .terms-checkbox{
        font-size: 11px;
        color: $cp-white;
    }
    .next-btn-row{
        margin-left: 3px !important;
        margin-right: 3px !important;
    }
    .suffix-div{
        background-color: $cp-white;
        border-radius: 7px;
        max-width: 56px !important;
    }
}
@media only screen and ( min-width:360px ) and (max-width:375px){
    .label-row{
        margin-top: -30px;
        margin-bottom: -20px!important;
    }
    .next-btn-row{
        margin-left: 5px !important;
        margin-right: 5px !important;
        margin-top: 5px;
        margin-bottom: 8px;
    }
    .pre-header-height{
        // height: 4rem!important;
        
    }
    // .pre-header-logo {
    //     height: 3.5rem!important;
    // }
 }
//for phone like galaxy note 2 note 2 S 3
@media only screen and ( min-width:360px ){
    .label-row{
        margin-top: -20px;
    }
    .dob-row{
        margin-top: 10px !important;
        margin-left: 0px !important;
        margin-right: 0px !important;
    }
    .dob-day {
        width: 58px !important;
        height: 51px !important;
        margin-left: -25px !important;
        border-radius: 9px !important;
        text-align: center !important;
    }
    .dob_year{
        width: 69px !important;
        margin-left: -18px !important;
        height: 51px !important;
        border-radius: 9px !important;
        text-align: center !important;
    }

    .font-email{
        font-size: 13px !important;
    }
    .font-email::placeholder{
        font-size: 13px !important;       
    }
    .info-icon{
        float: right;
    }
    .text-month {
        margin-left: 7px;
        font-weight: 500;
    }
    .progress-bar-row{
        margin-top: 20px;
    }
    .progress-label-20{
        color: $cp-white;
        font-size: 11px;
        float: left;
        margin-left: -15px;
    }
    .progress-label-40{
        color: $cp-white;
        font-size: 11px;
        float: left;
        margin-left: -7px;
    }
    .progress-label-60{
        color: $cp-white;
        font-size: 11px;
        float: left;
        margin-left: 4px;
    }
    #btn-month.dropdown-toggle::after{
        margin-left: 40px !important;
        margin-top: 7px !important;
    }
    .progress-label-80{
        color: $cp-white;
        font-size: 11px;
        float: left;
        margin-left: 3px;
    }
    .progress-label-100{
        color: $cp-white;
        font-size: 11px;
        margin-left: 35px;
        float: right;
    }
    .enter-name-row-gap{
        margin-top: 10px;
        margin-left: 5px !important;
        margin-right: 5px !important;
    }
    .first-row-gap{
        margin-top: 30px !important; 
    }
    .setup-label{
        color: $cp-white;
        font-weight: 400;
        font-size: 17px;
        font-family: inherit;
        letter-spacing: 1px;
    }

    .terms-checkbox{
        font-size: 11px;
        color: $cp-white;
    }
    .next-btn-row{
        margin-left: 5px !important;
        margin-right: 5px !important;
        margin-top: 5px;
    }
    .suffix-div{
        background-color: $cp-white;
        border-radius: 7px;
        max-width: 64px !important;
    }
    .text-suffix{
        margin-left: -5px;
    }
    #suffix-btn.dropdown-toggle::after {
        margin-left: 20px !important;
        margin-top: 3px !important;
        margin-right: 23px !important;
        font-size: 15px;
        
    }
    .suffix-label{
        font-size: 10px;
        font-weight: 600 !important;
    }
}

@media only screen and ( min-width:360px ) and ( min-height:700px ){
    .info-icon{
        float: right;
        margin-top: 0px;
    }
    .suffix-label{
        font-size: 13px;
        font-weight: 500 !important;
    }
    .suffix-div{
        background-color: $cp-white;
        border-radius: 7px;
        max-width: 77px !important;
    }
    .progress-bar-row{
        margin-top: 20px;
    }
    .progress-label-20{
        color: $cp-white;
        font-size: 11px;
        float: left;
        margin-left: -15px;
    }
    .progress-label-40{
        color: $cp-white;
        font-size: 11px;
        float: left;
        margin-left: -4px;
    }
    .progress-label-60{
        color: $cp-white;
        font-size: 11px;
        float: left;
        margin-left: 3px;
    }
    .progress-label-80{
        color: $cp-white;
        font-size: 11px;
        float: left;
        margin-left: 11px;
    }
    .progress-label-100{
        color: $cp-white;
        font-size: 11px;
        margin-left: 35px;
        float: right;
    }
    .enter-name-row-gap{
        margin-top: 10px;
        margin-left: 5px !important;
        margin-right: 5px !important;
    }
    .first-row-gap{
        margin-top: 40px !important; 
    }
    .setup-label{
        color: $cp-white;
        font-weight: 400;
        font-size: 18px;
        font-family: inherit;
        letter-spacing: 1px;
    }
    .label-row{
        margin-top: 20px;
    }
    .terms-checkbox{
        font-size: 11px;
        color: $cp-white;
    }
    .next-btn-row{
        margin-left: 5px !important;
        margin-right: 5px !important;
        margin-top: 30px;
    }
}
@media only screen and ( width:384px ){
    .suffix-div {
        background-color: $cp-white;
        border-radius: 7px;
        max-width: 70px !important;
    }
    #suffix-btn.dropdown-toggle::after {
        margin-left: 8px !important;
        margin-top: 3px !important;
        
    }
    .dob-row{
        margin-top: 10px !important;
        margin-left: 0px !important;
        margin-right: 0px !important;
    }
    .dob-day {
        width: 57px !important;
        height: 51px !important;
        margin-left: -25px !important;
        border-radius: 9px !important;
        text-align: center !important;
    }
    .dob_year{
        width: 78px !important;
        margin-left: -22px !important;
        height: 51px !important;
        border-radius: 9px !important;
        text-align: center !important;
    }

    .font-email{
        font-size: 13px !important;
    }
    .font-email::placeholder{
        font-size: 13px !important;       
    }
}

@media only screen and ( min-width:411px ) and ( min-height:412px ){
    .suffix-div{
        background-color: $cp-white;
        border-radius: 7px;
        max-width: 76px !important;
    }
    .text-suffix{
        margin-left: -2px;
    }
    #suffix-btn.dropdown-toggle::after {
        margin-left: 26px !important;
        margin-right: 26px !important;
        margin-top: 3px !important;
        
    }
    .suffix-label{
        font-size: 11px;
        font-weight: 600 !important;
    }
    #btn-month.dropdown-toggle::after {
        margin-left: 91px !important;
        margin-top: 6px !important;
    }
    .dob-row{
        margin-top: 10px !important;
        margin-left: 0px !important;
        margin-right: 0px !important;
    }
    .dob-day {
        width: 62px !important;
        height: 51px !important;
        margin-left: -25px !important;
        border-radius: 9px !important;
        text-align: center !important;
    }
    .dob_year{
        width: 86px !important;
        margin-left: -22px !important;
        height: 51px !important;
        border-radius: 9px !important;
        text-align: center !important;
    }

    .font-email{
        font-size: 13px !important;
    }
    .font-email::placeholder{
        font-size: 13px !important;       
    }
}
@media only screen and ( width:375px ) and ( height:812px ){
    .suffix-div{
        background-color: $cp-white;
        border-radius: 7px;
        max-width: 68px !important;
    }
    .text-suffix{
        margin-left: -2px;
    }
    #suffix-btn.dropdown-toggle::after {
        margin-left: 7px !important;
        margin-right: 9px !important;
        margin-top: 9px !important;
        
    }
    .suffix-label{
        font-size: 11px;
        font-weight: 500 !important;
    }
    #btn-month.dropdown-toggle::after{
        margin-left: 63px !important;
        margin-top: 7px !important;
    }
    .dob-row{
        margin-top: 10px !important;
        margin-left: 0px !important;
        margin-right: 0px !important;
    }
    .dob-day {
        width: 57px !important;
        height: 51px !important;
        margin-left: -25px !important;
        border-radius: 9px !important;
        text-align: center !important;
    }
    .dob_year{
        width: 78px !important;
        margin-left: -22px !important;
        height: 51px !important;
        border-radius: 9px !important;
        text-align: center !important;
    }

    .font-email{
        font-size: 13px !important;
    }
    .font-email::placeholder{
        font-size: 13px !important;       
    }
}
@media only screen and ( min-width:768px ) and ( min-height:1024px ){
    .info-icon{
        float: right;
        margin-top: 0px;
    }
    .suffix-label{
        font-size: 13px;
        font-weight: 500 !important;
    }
    .suffix-div{
        background-color: $cp-white;
        border-radius: 7px;
        max-width: 153px !important;
    }    
    .text-suffix{
        margin-left: -2px;
    }
    #suffix-btn.dropdown-toggle::after {
        margin-left: 64px !important;
        margin-right: 64px !important;
        margin-top: 3px !important;
        
    }
    #btn-month.dropdown-toggle::after{
        margin-left: 265px !important;
        margin-top: 6px !important;
    }
    .text-month {
        margin-left: 9px;
        font-weight: 500;
    }
    .progress-bar-row{
        margin-top: 40px;
    }
    .progress-label-20{
        color: $cp-white;
        font-size: 13px;
        float: left;
        margin-left: -5px;
    }
    .progress-label-40{
        color: $cp-white;
        font-size: 11px;
        float: left;
        margin-left: 15px;
    }
    .progress-label-60{
        color: $cp-white;
        font-size: 11px;
        float: left;
        margin-left: 33px;
    }
    .progress-label-80{
        color: $cp-white;
        font-size: 11px;
        float: left;
        margin-left: 52px;
    }
    .progress-label-100{
        color: $cp-white;
        font-size: 11px;
        margin-left: 35px;
        float: right;
    }
    .first-row-gap{
        margin-top: 50px !important; 
    }
    .enter-name-row-gap{
        margin-top: 20px;
    }
    .suffix{
        width: 109%;
    }
    .setup-label{
        color: $cp-white;
        font-weight: 400;
        font-size: 20px;
        font-family: inherit;
        letter-spacing: 1px;
    }
    .label-row{
        margin-top: 40px;
    }
    .terms-checkbox{
        font-size: 15px;
        color: $cp-white;
    }
    .next-btn-row{
        margin-left: 5px !important;
        margin-right: 5px !important;
        margin-top: 30px;
    }

    .dob-row{
        margin-top: 10px !important;
        margin-left: 0px !important;
        margin-right: 0px !important;
    }
    .dob-day {
        width: 104px !important;
        height: 51px !important;
        margin-left: -25px !important;
        border-radius: 9px !important;
        text-align: center !important;
    }
    .dob_year{
        width: 173px !important;
        margin-left: -31px !important;
        height: 51px !important;
        border-radius: 9px !important;
        text-align: center !important;
    }

    .font-email{
        font-size: 13px !important;
    }
    .font-email::placeholder{
        font-size: 13px !important;       
    }
}
@media only screen and (width:400px){
    #suffix-btn.dropdown-toggle::after {
        margin-left: 10px !important;
        margin-top: 7px !important;
        margin-right: 12px !important;
        
    }
    .suffix-div{
        background-color: $cp-white;
        border-radius: 7px;
        max-width: 73px !important;
    }
}
@media only screen and (width: 575px){
    #suffix-btn.dropdown-toggle::after {
        margin-left: 10px !important;
        margin-top: 7px !important;
        margin-right: 12px !important;
        
    }
    .suffix-div{
        background-color: $cp-white;
        border-radius: 7px;
        max-width: 73px !important;
    }
}
@media only screen and ( min-width:1024px ) and ( max-width:1500px ){
    .dob-row{
        margin-top: 10px;
        margin-left: 50px !important;
        margin-right: 50px !important;
    }
    .info-icon{
        float: right;
        margin-top: 0px;
    }
    .suffix-label{
        font-size: 13px;
        font-weight: 500 !important;
    }
    .suffix-div{
        background-color: $cp-white;
        border-radius: 7px;
        max-width: 228px !important;
    }
    .text-suffix{
        margin-left: -2px;
    }
    #suffix-btn.dropdown-toggle::after {
        margin-left: 102px !important;
        margin-right: 102px !important;
        margin-top: 6px !important;
        
    }
    .progress-bar-row{
        margin-top: 40px;
    }
    .progress-label-20{
        color: $cp-white;
        font-size: 13px;
        float: left;
        margin-left: 4px;
    }
    .progress-label-40{
        color: $cp-white;
        font-size: 13px;
        float: left;
        margin-left: 32px;
    }
    .progress-label-60{
        color: $cp-white;
        font-size: 13px;
        float: left;
        margin-left: 58px;
    }
    .progress-label-80{
        color: $cp-white;
        font-size: 13px;
        float: left;
        margin-left: 87px;
    }
    .progress-label-100{
        color: $cp-white;
        font-size: 13px;
        margin-left: 35px;
        float: right;
    }
    .first-row-gap{
        margin-top: 50px !important; 
    }
    .enter-name-row-gap{
        margin-top: 20px;
    }
    .suffix{
        width: 109%;
    }
    .setup-label{
        color: $cp-white;
        font-weight: 400;
        font-size: 20px;
        font-family: inherit;
        letter-spacing: 1px;
    }
    .label-row{
        margin-top: 40px;
    }
    .terms-checkbox{
        font-size: 15px;
        color: $cp-white;
    }
    .next-btn-row{
        margin-left: 5px !important;
        margin-right: 5px !important;
        margin-top: 30px;
    }
    #btn-month.dropdown-toggle::after{
        margin-left: 368px !important;
        margin-top: 7px !important;
    }
    
    .dob-day {
        width: 126px !important;
        height: 51px !important;
        margin-left: -19px !important;
        border-radius: 9px !important;
        text-align: center !important;
    }
    .dob_year{
        width: 224px !important;
        margin-left: -31px !important;
        height: 51px !important;
        border-radius: 9px !important;
        text-align: center !important;
    }

    .font-email{
        font-size: 14px !important;
    }
    .font-email::placeholder{
        font-size: 14px !important;       
    }
}
.checkbox {
    label {
        margin-right: 8px;
    }
}
@media only screen and ( min-width:300px ) and ( max-width:340px ){
.checkbox {
    label {
        margin-right: 2px;
    }
}
}
@media only screen and ( min-width:341px ) and ( max-width:360px ){
    .checkbox {
        label {
            margin-right: 5px;
        }
    }
}
@media only screen and ( min-width:361px ) {
    .checkbox {
        label {
            // margin-right: 33px;
        }
    }
}
/*for checkbox*/
.checkbox {
    label {
        position: relative;
        display: block;
        padding-left: 24px;
        
        font-weight: normal;
        span {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }
        &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            margin: 5px;
            width: 15px;
            height: 15px;
            transition: transform 0.28s ease;
            border-radius: 3px;
            border: 1px solid $cp-white;
            margin-left:0px;
        }
        &:after {
            content: '';
            display: block;
            width: 10px;
            height: 5px;
            border-bottom: 1px solid $cp-white;
            border-left: 1px solid $cp-white;
            transform: rotate(-45deg) scale(0);
            transition: transform ease 0.25s;
            position: absolute;
            top: 8px;
            left: 2px;
        }
    }
    input[type="checkbox"] {
        width: auto;
        opacity:0;
        position: absolute;
        &:checked ~ label{
            &:before {
                border: 1px solid $cp-white;
            }
            &:after {
                transform: rotate(-45deg) scale(1);
            }
        }
        &:focus + label::before {
            outline: 0;
        }
    }
}
.suffix-padding{
    padding: 0 16px 0 0!important;
}
.icon-center-participant{
    justify-content: center;display: flex;
}
.drawer-wrap{
    height: 100%;
}
.logout-nav{
    margin-left: 20px;
    position: absolute!important;
    display: inline-block;
    bottom: 35px;
}
.day-placeholder::placeholder{
    text-align: center !important;
}