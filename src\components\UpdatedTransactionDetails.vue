<template>
<div>
  <div v-if="isLoading">
    <CanPayLoader/>
  </div>
  <div v-else>
    <transition name="slide" type="animation">
    <div v-show="showMerchantPoint" class="merchant-options-card" id="merchantOptionsCard">
        <MerchantPoints v-model="selectedMerchant" :merchant-points="wheelPoints.other_points" :search-term="''">
           <template v-slot:backbtn="">
            <a class="merchant-header-btn" href="javascript:void(0)" @click="redirectBackToDashboard">
              <svg width="30" viewBox="0 0 74 52" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M2.13235 23.5735L23.8235 1.88235C25 0.705882 26.9118 0.705882 28.0882 1.88235C29.2647 3.05882 29.2647 4.97059 28.0882 6.14706L11.5441 22.6912H56.9853C58.6765 22.6912 60 24.0147 60 25.7059C60 27.3971 58.6765 28.7206 56.9853 28.7206H11.5441L28.0882 45.2647C29.2647 46.4412 29.2647 48.3529 28.0882 49.5294C27.5 50.1176 26.7647 50.4118 25.9559 50.4118C25.1471 50.4118 24.4118 50.1176 23.8235 49.5294L2.13235 27.8382L0 25.7059L2.13235 23.5735Z" fill="black"/>
              </svg>
            </a>
          </template>
        </MerchantPoints>
    </div>
    </transition>
    <div class="container updated-transaction-container mt-3" style="height:100%!important;">
      <div v-if="showModifyHistory" :style="updated_data.length > 1 ? 'height: 730px;' : 'height: 730px;'" :class="updated_data.length > 1 ? 'updated-transaction-slider' : 'updated-transaction-slider w-100'">
        <div 
        v-for="(up_data, index) in updated_data"
        :key="index"
        :class="updated_data.length > 1 ? 'updated-transaction-slider-card position-relative border-radius-modification' : 'updated-transaction-slider-card position-relative mr-0 w-100 border-radius-modification'"
        >
          <div class="modification-alert-header p-2 border-radius-modification-top">
            <div class="mt-2 mb-2">
              <div :style="updated_data.length > 1 ? 'margin-right:0px;' : 'margin-right:50px;'" >
              <svg class="mt-2" xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 90 90" fill="none">
                <path d="M54.6429 35.3572C55.4954 35.3572 56.3129 35.0185 56.9157 34.4157C57.5185 33.8129 57.8572 32.9954 57.8572 32.1429C57.8572 31.2904 57.5185 30.4728 56.9157 29.87C56.3129 29.2672 55.4954 28.9286 54.6429 28.9286H48.2143V25.7143C48.2143 24.8618 47.8757 24.0443 47.2729 23.4415C46.6701 22.8387 45.8525 22.5 45 22.5C44.1475 22.5 43.33 22.8387 42.7272 23.4415C42.1244 24.0443 41.7857 24.8618 41.7857 25.7143V28.9286H38.5714C36.8665 28.9286 35.2313 29.6059 34.0258 30.8115C32.8202 32.0171 32.1429 33.6522 32.1429 35.3572V41.7857C32.1429 43.4907 32.8202 45.1258 34.0258 46.3314C35.2313 47.537 36.8665 48.2143 38.5714 48.2143H51.4286V54.6429H35.3572C34.5047 54.6429 33.6871 54.9815 33.0843 55.5843C32.4815 56.1871 32.1429 57.0047 32.1429 57.8571C32.1429 58.7096 32.4815 59.5272 33.0843 60.13C33.6871 60.7328 34.5047 61.0714 35.3572 61.0714H41.7857V64.2857C41.7857 65.1382 42.1244 65.9558 42.7272 66.5586C43.33 67.1614 44.1475 67.5 45 67.5C45.8525 67.5 46.6701 67.1614 47.2729 66.5586C47.8757 65.9558 48.2143 65.1382 48.2143 64.2857V61.0714H51.4286C53.1335 61.0714 54.7687 60.3941 55.9743 59.1885C57.1799 57.983 57.8572 56.3478 57.8572 54.6429V48.2143C57.8572 46.5093 57.1799 44.8742 55.9743 43.6686C54.7687 42.463 53.1335 41.7857 51.4286 41.7857H38.5714V35.3572H54.6429Z" fill="black"/>
                <path d="M76.2107 58.7893C75.9119 58.488 75.5564 58.2489 75.1647 58.0857C74.773 57.9225 74.3529 57.8385 73.9286 57.8385C73.5042 57.8385 73.0841 57.9225 72.6924 58.0857C72.3007 58.2489 71.9452 58.488 71.6464 58.7893L58.7893 71.6464C58.3433 72.0984 58.0412 72.6724 57.921 73.296C57.8009 73.9195 57.8681 74.5646 58.1143 75.15C58.3554 75.737 58.7649 76.2395 59.2912 76.5941C59.8174 76.9488 60.4369 77.1397 61.0714 77.1429H64.2857V86.7857C64.2857 87.6382 64.6244 88.4558 65.2272 89.0586C65.83 89.6614 66.6475 90 67.5 90C68.3525 90 69.1701 89.6614 69.7729 89.0586C70.3756 88.4558 70.7143 87.6382 70.7143 86.7857V73.9286C70.7164 73.2718 70.5173 72.6302 70.1438 72.09C69.7703 71.5498 69.2402 71.137 68.625 70.9071L73.9286 65.6036L79.2321 70.9071C78.6169 71.137 78.0869 71.5498 77.7134 72.09C77.3398 72.6302 77.1407 73.2718 77.1429 73.9286V86.7857C77.1429 87.6382 77.4815 88.4558 78.0843 89.0586C78.6871 89.6614 79.5047 90 80.3571 90C81.2096 90 82.0272 89.6614 82.63 89.0586C83.2328 88.4558 83.5714 87.6382 83.5714 86.7857V77.1429H86.7857C87.4203 77.1397 88.0397 76.9488 88.566 76.5941C89.0922 76.2395 89.5017 75.737 89.7429 75.15C89.989 74.5646 90.0563 73.9195 89.9361 73.296C89.816 72.6724 89.5139 72.0984 89.0679 71.6464L76.2107 58.7893Z" fill="black"/>
                <path d="M53.9036 82.5429C50.9848 83.2249 47.9974 83.57 45 83.5714C37.3713 83.5714 29.9139 81.3092 23.5709 77.071C17.2278 72.8327 12.284 66.8087 9.36467 59.7607C6.44529 52.7126 5.68144 44.9572 7.16973 37.4751C8.65802 29.993 12.3316 23.1202 17.7259 17.7259C23.1202 12.3316 29.993 8.65802 37.4751 7.16973C44.9572 5.68144 52.7127 6.44529 59.7607 9.36466C66.8087 12.284 72.8327 17.2278 77.071 23.5709C81.3093 29.9139 83.5714 37.3713 83.5714 45C83.578 48.5114 83.0911 52.0063 82.125 55.3821C81.9829 55.7947 81.9263 56.2319 81.9586 56.6671C81.991 57.1022 82.1116 57.5263 82.3131 57.9133C82.5146 58.3004 82.7929 58.6423 83.1308 58.9183C83.4688 59.1944 83.8594 59.3987 84.2789 59.5189C84.6984 59.639 85.138 59.6725 85.5708 59.6173C86.0037 59.5621 86.4208 59.4193 86.7967 59.1977C87.1726 58.976 87.4994 58.6802 87.7572 58.3281C88.0151 57.9761 88.1985 57.5752 88.2964 57.15C89.4264 53.1989 89.9998 49.1095 90 45C90 36.0999 87.3608 27.3996 82.4161 19.9994C77.4715 12.5991 70.4434 6.83138 62.2208 3.42544C53.9981 0.0194977 44.9501 -0.871652 36.221 0.864682C27.4918 2.60102 19.4736 6.88685 13.1802 13.1802C6.88685 19.4736 2.60102 27.4918 0.864682 36.2209C-0.871652 44.9501 0.0194977 53.9981 3.42544 62.2208C6.83138 70.4434 12.5991 77.4715 19.9994 82.4161C27.3996 87.3608 36.0999 90 45 90C48.4967 90.0017 51.9814 89.5917 55.3821 88.7786C56.1519 88.5306 56.7998 88.0016 57.1969 87.2971C57.5939 86.5926 57.7107 85.7644 57.5242 84.9775C57.3376 84.1907 56.8613 83.503 56.1902 83.0518C55.5192 82.6005 54.7027 82.4188 53.9036 82.5429Z" fill="black"/>
              </svg>
                <span   class="ml-2"  v-if="reasonTypeIncrease(up_data)" >
                Payment Modification Alert
              </span>
                <span class="ml-2" v-else>Prepayment Modification Alert</span>
              </div>
              <div>
                <p :style="updated_data.length > 1 ? 'margin-left:26px;' : 'margin-right:25px;'"><span class="modification-alert-text1">Reason: </span> <span class="modification-alert-text2">{{reasonVIew(up_data.reason, 50)}}</span> <a v-if="reasonVIewMoreButton(up_data.reason, 50)" @click="showingFullTex(up_data.reason)" class="btn-link">More</a></p>
              </div>
            </div>

              
          </div>
          <div class="updated-transaction-slider-card-body" style="padding-top:0px!important; padding-bottom:2px!important;">

            <div class="text-left">
              <div class="modification_text_group">

                <p style="white-space: break-spaces;text-align: center;font-size: 1rem;" v-if="additionalReasonChk(up_data.additional_reason)"><span class="text-muted">{{reasonVIew(up_data.additional_reason, 25)}}</span> <a v-if="reasonVIewMoreButton(up_data.additional_reason, 25)" @click="showingFullTex(up_data.additional_reason)" class="btn-link">More</a></p>
              </div>
              <div class="modification_text_group">
                <p>
                  <span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 45 45" fill="none">
                    <mask id="path-1-outside-1_13114_592" maskUnits="userSpaceOnUse" x="0" y="0" width="45" height="45" fill="black">
                      <rect fill="white" width="45" height="45"/>
                      <path d="M27.4998 24C26.5483 24.0099 25.6063 23.8087 24.7418 23.411C23.8773 23.0133 23.1117 22.429 22.5 21.7C21.8838 22.4237 21.1172 23.0043 20.2537 23.4016C19.3903 23.7988 18.4506 24.003 17.5001 24C16.5486 24.0099 15.6066 23.8087 14.7421 23.411C13.8776 23.0133 13.112 22.429 12.5003 21.7C12.3159 21.9212 12.1153 22.1285 11.9003 22.32C11.227 22.9326 10.4315 23.3956 9.56619 23.6782C8.70091 23.9609 7.78554 24.057 6.88043 23.96C5.24227 23.7581 3.73626 22.9587 2.6512 21.7148C1.56613 20.471 0.978365 18.8705 1.00061 17.22V15.8C0.998981 14.7808 1.16104 13.7679 1.48059 12.8L4.6205 3.4C4.85161 2.7019 5.29671 2.09433 5.89265 1.66352C6.48858 1.23271 7.20506 1.00056 7.9404 1H37.0595C37.7949 1.00056 38.5113 1.23271 39.1073 1.66352C39.7032 2.09433 40.1483 2.7019 40.3794 3.4L43.5193 12.8C43.8389 13.7679 44.0009 14.7808 43.9993 15.8V17.22C44.0227 18.8644 43.4405 20.4599 42.3637 21.7028C41.2868 22.9457 39.7904 23.7491 38.1595 23.96C37.2543 24.0596 36.3382 23.9648 35.4725 23.6819C34.6069 23.3991 33.8115 22.9348 33.1396 22.32C32.9197 22.12 32.6997 21.88 32.4997 21.66C31.8864 22.3899 31.1213 22.9774 30.2579 23.3816C29.3944 23.7857 28.4532 23.9967 27.4998 24ZM7.9404 4C7.83542 4.00159 7.73339 4.03492 7.64772 4.09561C7.56204 4.15629 7.49675 4.24149 7.46041 4.34L4.34051 13.74C4.12384 14.3849 4.00913 15.0597 4.00052 15.74V17.16C3.97117 18.0629 4.27494 18.945 4.85396 19.6384C5.43298 20.3318 6.24681 20.7879 7.14043 20.92C7.6227 20.9699 8.11008 20.9191 8.57169 20.7708C9.03329 20.6225 9.45904 20.3798 9.82194 20.0583C10.1848 19.7367 10.477 19.3433 10.6798 18.9029C10.8827 18.4625 10.9918 17.9848 11.0003 17.5C11.0003 17.1022 11.1583 16.7206 11.4396 16.4393C11.7209 16.158 12.1025 16 12.5003 16C12.8981 16 13.2796 16.158 13.5609 16.4393C13.8422 16.7206 14.0002 17.1022 14.0002 17.5C13.9773 17.9657 14.0522 18.431 14.22 18.866C14.3877 19.301 14.6447 19.6961 14.9744 20.0258C15.3041 20.3555 15.6991 20.6125 16.1341 20.7803C16.5691 20.948 17.0344 21.0229 17.5001 21C18.4267 20.9948 19.3139 20.6243 19.9691 19.9691C20.6243 19.3138 20.9948 18.4266 21 17.5C21 17.1022 21.158 16.7206 21.4393 16.4393C21.7206 16.158 22.1021 16 22.5 16C22.8978 16 23.2793 16.158 23.5606 16.4393C23.8419 16.7206 23.9999 17.1022 23.9999 17.5C23.977 17.9657 24.0519 18.431 24.2197 18.866C24.3874 19.301 24.6444 19.6961 24.9741 20.0258C25.3038 20.3555 25.6988 20.6125 26.1338 20.7803C26.5688 20.948 27.0341 21.0229 27.4998 21C28.4264 20.9948 29.3136 20.6243 29.9688 19.9691C30.624 19.3138 30.9945 18.4266 30.9997 17.5C30.9997 17.1022 31.1577 16.7206 31.439 16.4393C31.7203 16.158 32.1019 16 32.4997 16C32.8975 16 33.279 16.158 33.5603 16.4393C33.8416 16.7206 33.9996 17.1022 33.9996 17.5C33.9998 17.9899 34.1028 18.4742 34.302 18.9218C34.5012 19.3693 34.7921 19.7701 35.156 20.0981C35.5198 20.4261 35.9484 20.6741 36.4141 20.826C36.8798 20.9779 37.3722 21.0304 37.8595 20.98C38.7531 20.8479 39.5669 20.3918 40.146 19.6984C40.725 19.005 41.0288 18.1229 40.9994 17.22V15.8C40.9908 15.1197 40.8761 14.4449 40.6594 13.8L37.5395 4.4C37.5032 4.30149 37.4379 4.21629 37.3522 4.15561C37.2665 4.09492 37.1645 4.06159 37.0595 4.06L7.9404 4Z"/>
                      <path d="M38.4995 44H6.50044C5.57383 43.9948 4.68667 43.6243 4.03145 42.9691C3.37622 42.3138 3.0058 41.4266 3.00055 40.5V21.46C3.00055 21.0622 3.15858 20.6806 3.43988 20.3993C3.72117 20.118 4.10269 19.96 4.5005 19.96C4.89832 19.96 5.27984 20.118 5.56113 20.3993C5.84243 20.6806 6.00046 21.0622 6.00046 21.46V40.5C6.00046 40.6326 6.05314 40.7598 6.1469 40.8536C6.24067 40.9473 6.36784 41 6.50044 41H38.4995C38.6321 41 38.7593 40.9473 38.853 40.8536C38.9468 40.7598 38.9995 40.6326 38.9995 40.5V21.44C38.9995 21.0422 39.1575 20.6606 39.4388 20.3793C39.7201 20.098 40.1016 19.94 40.4994 19.94C40.8972 19.94 41.2788 20.098 41.5601 20.3793C41.8414 20.6606 41.9994 21.0422 41.9994 21.44V40.5C41.9941 41.4266 41.6237 42.3138 40.9685 42.9691C40.3133 43.6243 39.4261 43.9948 38.4995 44Z"/>
                      <path d="M29.4998 44H15.5002C15.104 43.9948 14.7255 43.8351 14.4453 43.5549C14.1651 43.2747 14.0054 42.8962 14.0002 42.5V32.5C14.0055 31.5734 14.3759 30.6862 15.0311 30.0309C15.6863 29.3757 16.5735 29.0053 17.5001 29H27.4998C28.4264 29.0053 29.3136 29.3757 29.9688 30.0309C30.624 30.6862 30.9945 31.5734 30.9997 32.5V42.5C30.9945 42.8962 30.8348 43.2747 30.5547 43.5549C30.2745 43.8351 29.896 43.9948 29.4998 44ZM17.0001 41H27.9998V32.5C27.9998 32.3674 27.9471 32.2402 27.8534 32.1464C27.7596 32.0527 27.6324 32 27.4998 32H17.5001C17.3675 32 17.2403 32.0527 17.1466 32.1464C17.0528 32.2402 17.0001 32.3674 17.0001 32.5V41Z"/>
                    </mask>
                    <path d="M27.4998 24C26.5483 24.0099 25.6063 23.8087 24.7418 23.411C23.8773 23.0133 23.1117 22.429 22.5 21.7C21.8838 22.4237 21.1172 23.0043 20.2537 23.4016C19.3903 23.7988 18.4506 24.003 17.5001 24C16.5486 24.0099 15.6066 23.8087 14.7421 23.411C13.8776 23.0133 13.112 22.429 12.5003 21.7C12.3159 21.9212 12.1153 22.1285 11.9003 22.32C11.227 22.9326 10.4315 23.3956 9.56619 23.6782C8.70091 23.9609 7.78554 24.057 6.88043 23.96C5.24227 23.7581 3.73626 22.9587 2.6512 21.7148C1.56613 20.471 0.978365 18.8705 1.00061 17.22V15.8C0.998981 14.7808 1.16104 13.7679 1.48059 12.8L4.6205 3.4C4.85161 2.7019 5.29671 2.09433 5.89265 1.66352C6.48858 1.23271 7.20506 1.00056 7.9404 1H37.0595C37.7949 1.00056 38.5113 1.23271 39.1073 1.66352C39.7032 2.09433 40.1483 2.7019 40.3794 3.4L43.5193 12.8C43.8389 13.7679 44.0009 14.7808 43.9993 15.8V17.22C44.0227 18.8644 43.4405 20.4599 42.3637 21.7028C41.2868 22.9457 39.7904 23.7491 38.1595 23.96C37.2543 24.0596 36.3382 23.9648 35.4725 23.6819C34.6069 23.3991 33.8115 22.9348 33.1396 22.32C32.9197 22.12 32.6997 21.88 32.4997 21.66C31.8864 22.3899 31.1213 22.9774 30.2579 23.3816C29.3944 23.7857 28.4532 23.9967 27.4998 24ZM7.9404 4C7.83542 4.00159 7.73339 4.03492 7.64772 4.09561C7.56204 4.15629 7.49675 4.24149 7.46041 4.34L4.34051 13.74C4.12384 14.3849 4.00913 15.0597 4.00052 15.74V17.16C3.97117 18.0629 4.27494 18.945 4.85396 19.6384C5.43298 20.3318 6.24681 20.7879 7.14043 20.92C7.6227 20.9699 8.11008 20.9191 8.57169 20.7708C9.03329 20.6225 9.45904 20.3798 9.82194 20.0583C10.1848 19.7367 10.477 19.3433 10.6798 18.9029C10.8827 18.4625 10.9918 17.9848 11.0003 17.5C11.0003 17.1022 11.1583 16.7206 11.4396 16.4393C11.7209 16.158 12.1025 16 12.5003 16C12.8981 16 13.2796 16.158 13.5609 16.4393C13.8422 16.7206 14.0002 17.1022 14.0002 17.5C13.9773 17.9657 14.0522 18.431 14.22 18.866C14.3877 19.301 14.6447 19.6961 14.9744 20.0258C15.3041 20.3555 15.6991 20.6125 16.1341 20.7803C16.5691 20.948 17.0344 21.0229 17.5001 21C18.4267 20.9948 19.3139 20.6243 19.9691 19.9691C20.6243 19.3138 20.9948 18.4266 21 17.5C21 17.1022 21.158 16.7206 21.4393 16.4393C21.7206 16.158 22.1021 16 22.5 16C22.8978 16 23.2793 16.158 23.5606 16.4393C23.8419 16.7206 23.9999 17.1022 23.9999 17.5C23.977 17.9657 24.0519 18.431 24.2197 18.866C24.3874 19.301 24.6444 19.6961 24.9741 20.0258C25.3038 20.3555 25.6988 20.6125 26.1338 20.7803C26.5688 20.948 27.0341 21.0229 27.4998 21C28.4264 20.9948 29.3136 20.6243 29.9688 19.9691C30.624 19.3138 30.9945 18.4266 30.9997 17.5C30.9997 17.1022 31.1577 16.7206 31.439 16.4393C31.7203 16.158 32.1019 16 32.4997 16C32.8975 16 33.279 16.158 33.5603 16.4393C33.8416 16.7206 33.9996 17.1022 33.9996 17.5C33.9998 17.9899 34.1028 18.4742 34.302 18.9218C34.5012 19.3693 34.7921 19.7701 35.156 20.0981C35.5198 20.4261 35.9484 20.6741 36.4141 20.826C36.8798 20.9779 37.3722 21.0304 37.8595 20.98C38.7531 20.8479 39.5669 20.3918 40.146 19.6984C40.725 19.005 41.0288 18.1229 40.9994 17.22V15.8C40.9908 15.1197 40.8761 14.4449 40.6594 13.8L37.5395 4.4C37.5032 4.30149 37.4379 4.21629 37.3522 4.15561C37.2665 4.09492 37.1645 4.06159 37.0595 4.06L7.9404 4Z" fill="black"/>
                    <path d="M38.4995 44H6.50044C5.57383 43.9948 4.68667 43.6243 4.03145 42.9691C3.37622 42.3138 3.0058 41.4266 3.00055 40.5V21.46C3.00055 21.0622 3.15858 20.6806 3.43988 20.3993C3.72117 20.118 4.10269 19.96 4.5005 19.96C4.89832 19.96 5.27984 20.118 5.56113 20.3993C5.84243 20.6806 6.00046 21.0622 6.00046 21.46V40.5C6.00046 40.6326 6.05314 40.7598 6.1469 40.8536C6.24067 40.9473 6.36784 41 6.50044 41H38.4995C38.6321 41 38.7593 40.9473 38.853 40.8536C38.9468 40.7598 38.9995 40.6326 38.9995 40.5V21.44C38.9995 21.0422 39.1575 20.6606 39.4388 20.3793C39.7201 20.098 40.1016 19.94 40.4994 19.94C40.8972 19.94 41.2788 20.098 41.5601 20.3793C41.8414 20.6606 41.9994 21.0422 41.9994 21.44V40.5C41.9941 41.4266 41.6237 42.3138 40.9685 42.9691C40.3133 43.6243 39.4261 43.9948 38.4995 44Z" fill="black"/>
                    <path d="M29.4998 44H15.5002C15.104 43.9948 14.7255 43.8351 14.4453 43.5549C14.1651 43.2747 14.0054 42.8962 14.0002 42.5V32.5C14.0055 31.5734 14.3759 30.6862 15.0311 30.0309C15.6863 29.3757 16.5735 29.0053 17.5001 29H27.4998C28.4264 29.0053 29.3136 29.3757 29.9688 30.0309C30.624 30.6862 30.9945 31.5734 30.9997 32.5V42.5C30.9945 42.8962 30.8348 43.2747 30.5547 43.5549C30.2745 43.8351 29.896 43.9948 29.4998 44ZM17.0001 41H27.9998V32.5C27.9998 32.3674 27.9471 32.2402 27.8534 32.1464C27.7596 32.0527 27.6324 32 27.4998 32H17.5001C17.3675 32 17.2403 32.0527 17.1466 32.1464C17.0528 32.2402 17.0001 32.3674 17.0001 32.5V41Z" fill="black"/>
                    <path d="M27.4998 24C26.5483 24.0099 25.6063 23.8087 24.7418 23.411C23.8773 23.0133 23.1117 22.429 22.5 21.7C21.8838 22.4237 21.1172 23.0043 20.2537 23.4016C19.3903 23.7988 18.4506 24.003 17.5001 24C16.5486 24.0099 15.6066 23.8087 14.7421 23.411C13.8776 23.0133 13.112 22.429 12.5003 21.7C12.3159 21.9212 12.1153 22.1285 11.9003 22.32C11.227 22.9326 10.4315 23.3956 9.56619 23.6782C8.70091 23.9609 7.78554 24.057 6.88043 23.96C5.24227 23.7581 3.73626 22.9587 2.6512 21.7148C1.56613 20.471 0.978365 18.8705 1.00061 17.22V15.8C0.998981 14.7808 1.16104 13.7679 1.48059 12.8L4.6205 3.4C4.85161 2.7019 5.29671 2.09433 5.89265 1.66352C6.48858 1.23271 7.20506 1.00056 7.9404 1H37.0595C37.7949 1.00056 38.5113 1.23271 39.1073 1.66352C39.7032 2.09433 40.1483 2.7019 40.3794 3.4L43.5193 12.8C43.8389 13.7679 44.0009 14.7808 43.9993 15.8V17.22C44.0227 18.8644 43.4405 20.4599 42.3637 21.7028C41.2868 22.9457 39.7904 23.7491 38.1595 23.96C37.2543 24.0596 36.3382 23.9648 35.4725 23.6819C34.6069 23.3991 33.8115 22.9348 33.1396 22.32C32.9197 22.12 32.6997 21.88 32.4997 21.66C31.8864 22.3899 31.1213 22.9774 30.2579 23.3816C29.3944 23.7857 28.4532 23.9967 27.4998 24ZM7.9404 4C7.83542 4.00159 7.73339 4.03492 7.64772 4.09561C7.56204 4.15629 7.49675 4.24149 7.46041 4.34L4.34051 13.74C4.12384 14.3849 4.00913 15.0597 4.00052 15.74V17.16C3.97117 18.0629 4.27494 18.945 4.85396 19.6384C5.43298 20.3318 6.24681 20.7879 7.14043 20.92C7.6227 20.9699 8.11008 20.9191 8.57169 20.7708C9.03329 20.6225 9.45904 20.3798 9.82194 20.0583C10.1848 19.7367 10.477 19.3433 10.6798 18.9029C10.8827 18.4625 10.9918 17.9848 11.0003 17.5C11.0003 17.1022 11.1583 16.7206 11.4396 16.4393C11.7209 16.158 12.1025 16 12.5003 16C12.8981 16 13.2796 16.158 13.5609 16.4393C13.8422 16.7206 14.0002 17.1022 14.0002 17.5C13.9773 17.9657 14.0522 18.431 14.22 18.866C14.3877 19.301 14.6447 19.6961 14.9744 20.0258C15.3041 20.3555 15.6991 20.6125 16.1341 20.7803C16.5691 20.948 17.0344 21.0229 17.5001 21C18.4267 20.9948 19.3139 20.6243 19.9691 19.9691C20.6243 19.3138 20.9948 18.4266 21 17.5C21 17.1022 21.158 16.7206 21.4393 16.4393C21.7206 16.158 22.1021 16 22.5 16C22.8978 16 23.2793 16.158 23.5606 16.4393C23.8419 16.7206 23.9999 17.1022 23.9999 17.5C23.977 17.9657 24.0519 18.431 24.2197 18.866C24.3874 19.301 24.6444 19.6961 24.9741 20.0258C25.3038 20.3555 25.6988 20.6125 26.1338 20.7803C26.5688 20.948 27.0341 21.0229 27.4998 21C28.4264 20.9948 29.3136 20.6243 29.9688 19.9691C30.624 19.3138 30.9945 18.4266 30.9997 17.5C30.9997 17.1022 31.1577 16.7206 31.439 16.4393C31.7203 16.158 32.1019 16 32.4997 16C32.8975 16 33.279 16.158 33.5603 16.4393C33.8416 16.7206 33.9996 17.1022 33.9996 17.5C33.9998 17.9899 34.1028 18.4742 34.302 18.9218C34.5012 19.3693 34.7921 19.7701 35.156 20.0981C35.5198 20.4261 35.9484 20.6741 36.4141 20.826C36.8798 20.9779 37.3722 21.0304 37.8595 20.98C38.7531 20.8479 39.5669 20.3918 40.146 19.6984C40.725 19.005 41.0288 18.1229 40.9994 17.22V15.8C40.9908 15.1197 40.8761 14.4449 40.6594 13.8L37.5395 4.4C37.5032 4.30149 37.4379 4.21629 37.3522 4.15561C37.2665 4.09492 37.1645 4.06159 37.0595 4.06L7.9404 4Z" stroke="black" stroke-width="0.8" mask="url(#path-1-outside-1_13114_592)"/>
                    <path d="M38.4995 44H6.50044C5.57383 43.9948 4.68667 43.6243 4.03145 42.9691C3.37622 42.3138 3.0058 41.4266 3.00055 40.5V21.46C3.00055 21.0622 3.15858 20.6806 3.43988 20.3993C3.72117 20.118 4.10269 19.96 4.5005 19.96C4.89832 19.96 5.27984 20.118 5.56113 20.3993C5.84243 20.6806 6.00046 21.0622 6.00046 21.46V40.5C6.00046 40.6326 6.05314 40.7598 6.1469 40.8536C6.24067 40.9473 6.36784 41 6.50044 41H38.4995C38.6321 41 38.7593 40.9473 38.853 40.8536C38.9468 40.7598 38.9995 40.6326 38.9995 40.5V21.44C38.9995 21.0422 39.1575 20.6606 39.4388 20.3793C39.7201 20.098 40.1016 19.94 40.4994 19.94C40.8972 19.94 41.2788 20.098 41.5601 20.3793C41.8414 20.6606 41.9994 21.0422 41.9994 21.44V40.5C41.9941 41.4266 41.6237 42.3138 40.9685 42.9691C40.3133 43.6243 39.4261 43.9948 38.4995 44Z" stroke="black" stroke-width="0.8" mask="url(#path-1-outside-1_13114_592)"/>
                    <path d="M29.4998 44H15.5002C15.104 43.9948 14.7255 43.8351 14.4453 43.5549C14.1651 43.2747 14.0054 42.8962 14.0002 42.5V32.5C14.0055 31.5734 14.3759 30.6862 15.0311 30.0309C15.6863 29.3757 16.5735 29.0053 17.5001 29H27.4998C28.4264 29.0053 29.3136 29.3757 29.9688 30.0309C30.624 30.6862 30.9945 31.5734 30.9997 32.5V42.5C30.9945 42.8962 30.8348 43.2747 30.5547 43.5549C30.2745 43.8351 29.896 43.9948 29.4998 44ZM17.0001 41H27.9998V32.5C27.9998 32.3674 27.9471 32.2402 27.8534 32.1464C27.7596 32.0527 27.6324 32 27.4998 32H17.5001C17.3675 32 17.2403 32.0527 17.1466 32.1464C17.0528 32.2402 17.0001 32.3674 17.0001 32.5V41Z" stroke="black" stroke-width="0.8" mask="url(#path-1-outside-1_13114_592)"/>
                  </svg>
                  </span>
                  <span class="modification-alert-text1 ml-2">Merchant Name:</span> 
                  <span class="modification-alert-text2"> {{up_data.store_name}}</span>
                  </p>
                 <p>
                  <span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 47 47" fill="none">
                      <path d="M33.84 46.1976L33.8401 46.1976C34.8976 46.2239 35.9493 46.0341 36.9305 45.6397C37.9114 45.2454 38.801 44.6551 39.5444 43.905C39.5446 43.9048 39.5448 43.9047 39.5449 43.9045L44.2608 39.1961L44.261 39.196C45.5027 37.9533 46.2 36.2706 46.2 34.5163C46.2 32.762 45.5027 31.0793 44.261 29.8367L44.2608 29.8365L41.8627 27.4423L41.8628 27.4423L41.8593 27.439C40.5949 26.2358 38.9142 25.5645 37.1665 25.5645C35.4188 25.5645 33.7381 26.2358 32.4737 27.439L32.4737 27.4389L32.4704 27.4422C32.0203 27.8908 31.4261 28.1672 30.7919 28.2227C30.1576 28.2782 29.5241 28.1093 29.0025 27.7457L29.0025 27.7456L28.9983 27.7429C26.9072 26.3619 24.9256 24.8226 23.0712 23.1387C21.6163 21.5159 20.2925 19.7808 19.1122 17.9497L19.1123 17.9497L19.1104 17.9469C18.7567 17.4179 18.6005 16.7821 18.6687 16.1502C18.737 15.5183 19.0255 14.9302 19.4841 14.4883L19.4841 14.4883L19.4865 14.486C20.1032 13.8714 20.5924 13.1417 20.9262 12.3386C21.26 11.5354 21.4318 10.6745 21.4318 9.80514C21.4318 8.93574 21.26 8.07487 20.9262 7.27171C20.5924 6.46856 20.1032 5.73887 19.4865 5.12432L19.4864 5.12425L17.0883 2.73708L16.9472 2.87882L17.0883 2.73707C15.8421 1.49661 14.1531 0.8 12.3923 0.8C10.6315 0.8 8.94253 1.49661 7.69628 2.73707L7.69608 2.73728L3.79589 6.63074L3.79508 6.63156C2.41885 8.02134 1.46486 9.77087 1.04331 11.6782C0.621766 13.5855 0.749877 15.5728 1.41284 17.4107L1.41273 17.4107L1.41526 17.417C3.7479 23.253 7.18887 28.5851 11.5492 33.1205L11.5492 33.1205L11.5518 33.1231C12.9091 34.4842 15.6011 36.8557 18.7441 39.1956C21.8845 41.5337 25.4893 43.8511 28.6718 45.0943C30.311 45.7782 32.0635 46.1523 33.84 46.1976ZM10.4774 5.50622L10.4775 5.50614C10.7286 5.25529 11.0269 5.05625 11.3553 4.92043C11.6837 4.78461 12.0358 4.71469 12.3914 4.71469C12.747 4.71469 13.0991 4.78461 13.4275 4.92043C13.7559 5.05625 14.0542 5.25529 14.3053 5.50614L14.3056 5.50644L16.7107 7.8989L16.7111 7.89923C16.963 8.14869 17.1628 8.44549 17.2988 8.7724C17.4349 9.09931 17.5044 9.44985 17.5036 9.80372C17.5026 10.1685 17.4279 10.5294 17.284 10.8649C17.1401 11.2003 16.9298 11.5035 16.6658 11.7563L16.6657 11.7562L16.6627 11.7593C15.5741 12.8471 14.8985 14.2783 14.7516 15.8081C14.6047 17.3371 14.9952 18.8695 15.8561 20.1432C17.1667 22.1777 18.6442 24.1004 20.2736 25.8915L20.2796 25.8981L20.2861 25.9041C22.3045 27.761 24.4692 29.4533 26.7595 30.9646C28.0377 31.8557 29.5899 32.2693 31.1435 32.1328C32.6971 31.9964 34.153 31.3185 35.2552 30.2182C35.7716 29.728 36.4575 29.4545 37.1709 29.4545C37.8842 29.4545 38.57 29.728 39.0864 30.2181L41.4774 32.6123C41.983 33.1186 42.2668 33.8039 42.2668 34.5181C42.2668 35.2322 41.9831 35.9174 41.4776 36.4237C41.4775 36.4238 41.4775 36.4239 41.4774 36.4239L36.7572 41.1314L36.7558 41.1328L36.7467 41.1417C36.4808 41.4017 35.9664 41.9047 34.97 42.1392C33.9566 42.3777 32.4183 42.3439 30.106 41.4405L30.1059 41.4404C27.3937 40.3835 24.1574 38.3209 21.2401 36.1618C18.3259 34.005 15.7452 31.7633 14.3441 30.358C10.3846 26.2261 7.25234 21.379 5.11537 16.0767C4.70215 14.9398 4.62 13.7094 4.87843 12.5279C5.13706 11.3454 5.72621 10.2601 6.57781 9.39738C6.57798 9.39721 6.57815 9.39704 6.57831 9.39687L10.4774 5.50622Z" fill="black" stroke="black" stroke-width="0.4"/>
                    </svg>
                  </span>
                  <span class="modification-alert-text1 ml-2">Merchant Phone:</span> 
                  <a class="modification-alert-text2 only-green" :href="'tel:'+up_data.store_contact_no"> {{up_data.store_contact_no}}</a></p>
                  <hr>
              </div>
              <div class="modification_text_group">
                <p>
                  <span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 50 50" fill="none">
                      <path d="M49.2 25.0002V25C49.2 20.2137 47.7807 15.5349 45.1216 11.5552C42.4624 7.57554 38.6829 4.47377 34.2609 2.64213C29.839 0.810485 24.9732 0.331245 20.2788 1.26501C15.5845 2.19877 11.2725 4.5036 7.88803 7.88803C4.5036 11.2725 2.19877 15.5845 1.26501 20.2788C0.331245 24.9732 0.810485 29.839 2.64213 34.2609C4.47377 38.6829 7.57554 42.4624 11.5552 45.1216C15.5349 47.7807 20.2137 49.2 25 49.2H25.0002C31.4161 49.1926 37.5672 46.6406 42.1039 42.1039C46.6406 37.5672 49.1926 31.4161 49.2 25.0002ZM13.8288 8.28107C17.1355 6.07161 21.0231 4.89232 25 4.89232C30.3309 4.89867 35.4417 7.01919 39.2113 10.7887C42.9809 14.5583 45.1014 19.6692 45.1077 25.0002C45.1076 28.9771 43.9284 32.8646 41.7189 36.1712C39.5095 39.4779 36.3691 42.0552 32.6949 43.5771C29.0207 45.099 24.9777 45.4972 21.0772 44.7213C17.1767 43.9455 13.5938 42.0304 10.7817 39.2183C7.96961 36.4062 6.05454 32.8233 5.27868 28.9228C4.50282 25.0223 4.90102 20.9793 6.42292 17.3051C7.94483 13.6309 10.5221 10.4905 13.8288 8.28107ZM24.3187 16.0363C24.5428 15.9918 24.7697 15.9695 24.996 15.9692L25 34.0308C24.5436 34.0308 24.0893 33.9413 23.6636 33.7649C23.0254 33.5006 22.48 33.053 22.0963 32.4787C21.7125 31.9044 21.5077 31.2292 21.5077 30.5385C21.5077 29.9958 21.2921 29.4753 20.9084 29.0916C20.5247 28.7079 20.0042 28.4923 19.4615 28.4923C18.9189 28.4923 18.3984 28.7079 18.0147 29.0916C17.631 29.4753 17.4154 29.9958 17.4154 30.5385C17.4154 32.55 18.2145 34.4792 19.6369 35.9016C20.5658 36.8305 21.7109 37.4936 22.9539 37.8419V39.7692C22.9539 40.3119 23.1694 40.8324 23.5532 41.2161C23.9369 41.5998 24.4573 41.8154 25 41.8154C25.5427 41.8154 26.0631 41.5998 26.4469 41.2161C26.8306 40.8324 27.0462 40.3119 27.0462 39.7692V37.8419C28.2892 37.4936 29.4342 36.8305 30.3631 35.9016C31.7855 34.4792 32.5846 32.55 32.5846 30.5385C32.5846 28.5269 31.7855 26.5977 30.3631 25.1753C28.9407 23.7529 27.0116 22.9539 25 22.9539C24.3093 22.9539 23.6341 22.749 23.0598 22.3653C22.4855 21.9816 22.0379 21.4361 21.7735 20.798C21.5092 20.1599 21.4401 19.4577 21.5748 18.7802C21.7096 18.1028 22.0422 17.4805 22.5306 16.9921C23.019 16.5037 23.6412 16.1711 24.3187 16.0363ZM25.6813 33.9637C25.4569 34.0083 25.2297 34.0306 25.0031 34.0308L25 15.9692C25.4564 15.9692 25.9107 16.0587 26.3365 16.2351C26.9746 16.4994 27.52 16.947 27.9038 17.5213C28.2875 18.0956 28.4923 18.7708 28.4923 19.4615C28.4923 20.0042 28.7079 20.5247 29.0916 20.9084C29.4753 21.2921 29.9958 21.5077 30.5385 21.5077C31.0811 21.5077 31.6016 21.2921 31.9853 20.9084C32.369 20.5247 32.5846 20.0042 32.5846 19.4615C32.5846 17.9615 32.1398 16.495 31.3064 15.2478C30.473 14.0005 29.2884 13.0283 27.9025 12.4543C27.6222 12.3382 27.3362 12.2394 27.0462 12.1581V10.2308C27.0462 9.6881 26.8306 9.16766 26.4469 8.78393C26.0631 8.4002 25.5427 8.18462 25 8.18462C24.4573 8.18462 23.9369 8.4002 23.5532 8.78393C23.1694 9.16766 22.9539 9.6881 22.9539 10.2308V12.1581C21.7031 12.5085 20.5599 13.1754 19.6369 14.0984C18.5761 15.1591 17.8538 16.5106 17.5611 17.9819C17.2685 19.4531 17.4187 20.9781 17.9927 22.3641C18.5668 23.75 19.5389 24.9345 20.7862 25.7679C22.0335 26.6013 23.4999 27.0462 25 27.0462C25.6907 27.0462 26.3659 27.251 26.9402 27.6347C27.5145 28.0185 27.9622 28.5639 28.2265 29.202C28.4908 29.8402 28.56 30.5423 28.4252 31.2198C28.2905 31.8972 27.9578 32.5195 27.4694 33.0079C26.981 33.4963 26.3588 33.8289 25.6813 33.9637Z" fill="black" stroke="black" stroke-width="0.4"/>
                    </svg>
                  </span>
                  <span class="modification-alert-text1 ml-2">Original Payment Amount:</span> 
                  <span class="modification-alert-text2"> ${{up_data.amount}}</span> 
                </p>
                <!-- <p><span class="text-black">Reward Amount Used :</span> <span class="text-muted">${{up_data.reward_amount_used}}</span> </p> -->
                <p>
                  <span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 42 45" fill="none">
                      <path d="M32.5508 44.2H32.5511C34.805 44.1973 36.966 43.3044 38.56 41.7168C40.1541 40.1292 41.051 37.9766 41.0537 35.731V35.7308V17.5385V12.5769V12.5767C41.051 10.3311 40.1541 8.17848 38.56 6.59089C36.966 5.00331 34.805 4.11038 32.5511 4.10769H32.5508H31.0902V2.65385C31.0902 2.16191 30.894 1.69029 30.545 1.34269C30.196 0.995119 29.7229 0.8 29.2297 0.8C28.7365 0.8 28.2633 0.995119 27.9143 1.34269C27.5653 1.69029 27.3691 2.16191 27.3691 2.65385V4.10769H22.7874V2.65385C22.7874 2.16191 22.5912 1.69029 22.2422 1.34269C21.8932 0.995119 21.42 0.8 20.9268 0.8C20.4336 0.8 19.9605 0.995119 19.6115 1.34269C19.2625 1.69029 19.0663 2.16191 19.0663 2.65385V4.10769H14.4846V2.65385C14.4846 2.16191 14.2883 1.69029 13.9393 1.34269C13.5903 0.995119 13.1172 0.8 12.624 0.8C12.1308 0.8 11.6576 0.995119 11.3087 1.34269C10.9596 1.69029 10.7634 2.16191 10.7634 2.65385V4.10769H9.30285L9.30261 4.10769C7.04867 4.11038 4.88765 5.00331 3.29363 6.59089C1.69957 8.17848 0.802701 10.3311 0.8 12.5767V12.5769V17.5385L0.8 35.7308L0.8 35.731C0.802701 37.9766 1.69957 40.1292 3.29363 41.7168C4.88765 43.3044 7.04867 44.1973 9.30261 44.2H9.30285H32.5508ZM37.3325 15.6846H4.52114V12.5769C4.52114 11.3143 5.02473 10.1033 5.92138 9.21029C6.81806 8.31724 8.0344 7.81538 9.30285 7.81538H10.7634V9.26923C10.7634 9.76116 10.9596 10.2328 11.3087 10.5804C11.6576 10.928 12.1308 11.1231 12.624 11.1231C13.1172 11.1231 13.5903 10.928 13.9393 10.5804C14.2883 10.2328 14.4846 9.76116 14.4846 9.26923V7.81538H19.0663V9.26923C19.0663 9.76116 19.2625 10.2328 19.6115 10.5804C19.9605 10.928 20.4336 11.1231 20.9268 11.1231C21.42 11.1231 21.8932 10.928 22.2422 10.5804C22.5912 10.2328 22.7874 9.76116 22.7874 9.26923V7.81538H27.3691V9.26923C27.3691 9.76116 27.5653 10.2328 27.9143 10.5804C28.2633 10.928 28.7365 11.1231 29.2297 11.1231C29.7229 11.1231 30.196 10.928 30.545 10.5804C30.894 10.2328 31.0902 9.76116 31.0902 9.26923V7.81538H32.5508C33.8193 7.81538 35.0356 8.31724 35.9323 9.21029C36.8289 10.1033 37.3325 11.3143 37.3325 12.5769V15.6846ZM4.52114 19.3923H37.3325V35.7308C37.3325 36.9933 36.8289 38.2044 35.9323 39.0974C35.0356 39.9904 33.8193 40.4923 32.5508 40.4923H9.30285C8.0344 40.4923 6.81806 39.9904 5.92138 39.0974C5.02473 38.2044 4.52114 36.9933 4.52114 35.7308V19.3923Z" fill="black" stroke="black" stroke-width="0.4"/>
                    </svg>
                  </span>
                  <span class="modification-alert-text1 ml-2">Date:</span> 
                  <span class="modification-alert-text2"> {{transactiontime(up_data)}}</span>
                </p>
              </div>

            </div>
          </div>
          <div style="padding:0px 10px;">
              <div class=" modification_text_highlight text-left" style="padding:5px 16px; margin-bottom:0.5rem;">
                <p class="modification-alert-text1" v-if="up_data.previous_approval_amount && !reasonTypeIncrease(up_data)"><span class="modification-alert-text2">Prior Approved Amount :</span> <span class="text-muted">${{up_data.previous_approval_amount}}</span> </p>
                <p class="modification-alert-text1" v-if="up_data.previous_approval_amount && reasonTypeIncrease(up_data)"><span class="modification-alert-text2">Prior Payment Amount :</span> <span class="text-muted">${{up_data.previous_approval_amount}}</span> </p>
                <p class="modification-alert-text1" v-if="up_data.last_updated_amount && !reasonTypeIncrease(up_data)"><span class="modification-alert-text2">New Approved Amount :</span> <span class="text-muted">${{up_data.last_updated_amount}}</span> </p>
                <p class="modification-alert-text3 margin-zero" v-if="reasonTypeIncrease(up_data)"><span class="text-black">New Payment Amount:&nbsp;</span> <span> ${{up_data.updated_amount}}</span></p>
                <p class="row m-0" v-if="reasonTypeIncrease(up_data)">
                  <span class="modification-alert-text1">Time Left For Approval:</span>
                  <strong class="modification-alert-text2 ml-1" v-if="reasonTypeIncrease(up_data)">
                    <vue-countdown-timer
                      :start-time="up_data.current_store_time"
                      :end-time="up_data.expiration_datetime"
                      :interval="1000"
                      :end-text="'Expired'"
                      :day-txt=false
                      :hour-txt="':'"
                      :minutes-txt="':'"
                      secondsTxt="">
                    </vue-countdown-timer>
                  </strong>
                </p>
                <p class="modification-alert-text1 margin-zero" v-if="reasonTypeIncrease(up_data)"><span class="text-black">Paid With Bank: </span> <span class="modification-alert-text2 ml-1 "> ${{up_data.bank_posting_amount}}</span></p>
                <p class="modification-alert-text1 margin-zero" v-if="reasonTypeIncrease(up_data)"><span class="text-black">Paid With Points: </span> <span class="modification-alert-text2 ml-1"> ${{up_data.reward_point_used}}</span></p>
              </div>
          </div>

            <div v-if="reasonTypeIncrease(up_data)" style="padding:0px 10px;">
              <div class="cp-point-card">
                <div class="px-3 py-2">
                  <div class="d-flex position-relative">
                    <svg class="cp-point-logo" width="35" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="35" cy="35" r="35" fill="black"/>
                    <path d="M58.0327 44.4525C60.969 38.2154 59.7673 32.2515 59.4272 30.6694C59.0417 28.8825 58.0667 24.66 54.5183 20.8699C52.8282 19.0889 50.821 17.6413 48.6004 16.6018C45.6075 15.2019 43.0567 14.9287 41.6622 14.7921C35.9144 14.223 31.1643 15.9075 28.5114 17.114C29.8031 15.8716 31.2794 14.8387 32.8875 14.0523C34.3086 13.3704 35.8149 12.8842 37.3656 12.6069C39.9046 12.1197 42.5076 12.0697 45.0633 12.4589C46.2423 12.641 51.1626 13.4946 56.0714 17.3188C63.225 22.8844 66.1613 30.6125 64.4947 39.5016C61.9553 53.1026 48.9632 61.0697 35.7897 57.5641C31.9125 56.5284 27.8312 54.0814 26.0173 51.6912C26.108 51.6912 26.2101 51.6685 26.2667 51.7026C26.7202 51.9985 27.1624 52.3172 27.6272 52.6131C34.9962 57.2568 42.5465 57.291 50.0515 53.1367C51.0019 52.6094 51.9047 52.0001 52.7497 51.3156C56.0034 48.6751 57.4885 45.6021 58.0327 44.4525Z" fill="white"/>
                    <path d="M65.1182 45.2722C65.0389 45.7502 64.9028 46.4331 64.6874 47.2412C63.1229 53.023 59.5518 56.6765 58.0213 58.2016C52.4209 63.79 45.9022 65.4062 42.9206 66.1119C36.7873 67.5573 31.8331 66.9427 30.0532 66.6582C23.6479 65.6338 19.2492 63.0388 17.9001 62.1852C15.1477 60.4502 12.6808 58.2961 10.5878 55.8001C8.14327 52.8969 6.22821 49.5834 4.93068 46.012C4.60191 45.1014 3.35485 41.5276 3.03741 36.6335C2.74266 31.9671 3.44554 28.4843 3.6156 27.6762C4.17003 25.0362 5.06567 22.4803 6.27977 20.0733C6.91463 18.8441 10.1456 12.8346 17.2879 8.35027C18.9431 7.30316 23.2738 4.84474 29.407 3.91145C31.0282 3.66105 45.0066 1.72618 55.0171 10.5242C59.6539 14.5988 62.386 20.0505 62.8168 20.9269C63.4801 22.2806 64.0557 23.6759 64.54 25.104C64.3813 24.8877 63.2023 23.226 63.1683 23.1805C58.8149 16.7044 53.7587 13.6541 53.7587 13.6541C51.8468 12.5265 49.7911 11.665 47.6481 11.0932C40.8346 9.24941 35.0981 10.8884 33.7377 11.2981C25.7565 13.6996 21.0291 20.0847 20.757 20.4603C15.9388 27.1299 16.0408 34.0954 16.1315 36.0303C16.4376 42.5406 19.2718 47.1387 20.5642 48.9939C20.6663 49.1191 20.8137 49.3126 20.995 49.5403C21.1311 49.7224 21.2671 49.9045 21.4032 50.0866C24.7816 54.5482 29.2596 57.6553 34.2365 59.2032C37.9454 60.3419 41.8646 60.6138 45.6942 59.9984C49.5239 59.3829 53.1626 57.8962 56.3321 55.6522C59.121 53.6718 60.8102 51.6117 61.8532 50.337C63.2363 48.6525 64.2453 46.9908 64.5741 46.3306C64.6194 46.251 65.0956 45.2722 65.1182 45.2722Z" fill="#007EE5"/>
                    <path d="M28.7061 45.6C22.7202 45.6 19.7273 41.6278 19.7273 35.5387C19.7273 29.2447 22.879 25.4546 29.0462 25.4546C31.427 25.4546 33.3429 26.0237 34.8167 27.2301C36.1998 28.4707 36.9707 30.1552 37.1634 32.2949H34.76C33.8984 32.2949 33.2749 31.8966 32.9008 31.1112C32.2432 29.7113 30.9508 28.9943 29.0576 28.9943C25.3617 28.9943 23.7179 31.612 23.7179 35.5501C23.7179 39.3743 25.2937 42.0717 28.9329 42.0717C31.427 42.0717 32.8554 40.6945 33.2862 38.532H37.1521C36.8007 43.0392 33.6377 45.6 28.7061 45.6Z" fill="white"/>
                    <path d="M49.3373 25.2747H42.5465C41.4015 25.2747 40.4945 26.2762 40.4945 27.4258V45.3063H44.5758V38.17H49.632C53.634 38.17 55.7766 35.6661 55.7766 31.7166C55.7766 27.551 53.5773 25.2747 49.3373 25.2747ZM48.8725 34.5507H44.8932V28.746H49.0765C50.9811 28.746 51.9561 29.7135 51.9561 31.6597C51.9674 33.606 50.9471 34.5848 48.8725 34.5507H48.8725Z" fill="#007EE5"/>
                    </svg>
                    <div class="align-items-center">
                      <p class="ml-2 mb-0 d-flex align-items-center cp-point-card-title">Pay with Points <span class="rw-beta-new-tag-1">Beta</span></p>
                      <!-- NEED LATER  -->
                      <div class="mx-0 ml-2 row justify-content-between align-items-center" v-if="up_data.other_points && up_data.other_points.length > 0">
                        <p class="rw-merchant-text mb-0" style="max-width: 55vw; text-align: left;"><span style="font-size:11px;font-weight:500!important;">Merchant </span> <span v-if="up_data.selectedMerchant && up_data.selectedMerchant.merchant_name!=null">: 
                          {{ up_data.selectedMerchant.merchant_name.length > 25 
                            ? up_data.selectedMerchant.merchant_name.slice(0, 25) + '...' 
                            : up_data.selectedMerchant.merchant_name 
                          }}
                        </span> </p>
                        <a class="rw-change-merchant-tag-1" style="display: none;" href="javascript:void(0)" @click="removeMerchant(index)" v-if="up_data.selectedMerchant && up_data.selectedMerchant.merchant_id">
                        <svg width="24" height="24" viewBox="0 0 74 68" fill="none" xmlns="http://www.w3.org/2000/svg"
                        style="
                          margin-right: 4px;
                          margin-top:8px;
                        "
                        >
                        <!-- First SVG: Black background rectangle -->
                        <rect width="74" height="68" rx="20" fill="black"/>

                        <!-- Second SVG: White arrow-like design, placed on top -->
                        <g transform="translate(12.5, 16)">
                          <path d="M15.8655 0.999909L31.3906 0.999908C37.2127 0.999908 41.8929 5.57437 41.8929 11.265V22.4785L46.4019 18.0714L48.0001 19.6335L40.7515 26.7187L33.5025 19.6335L35.1007 18.0714L39.61 22.4785L39.61 11.265C39.61 6.85791 35.8998 3.23135 31.3907 3.23135L15.8656 3.23135L15.8655 0.999909Z" fill="white"/>
                          <path d="M1.00006 16.3664L8.24903 9.28117L15.498 16.3664L13.8998 17.9285L9.3909 13.5214L9.3905 24.7349C9.3905 29.142 13.1008 32.7686 17.6098 32.7686L33.1349 32.7686V34.9999H17.6098C11.7878 34.9999 7.10749 30.4254 7.10749 24.7348V13.5213L2.59855 17.9284L1.00006 16.3664Z" fill="white"/>
                          <path fill-rule="evenodd" clip-rule="evenodd" d="M15.2655 0.399903L31.3906 0.399902C37.531 0.399902 42.4929 5.23015 42.4929 11.265V21.0531L46.4019 17.2324L48.8584 19.6335L40.7515 27.5578L32.6441 19.6335L35.1007 17.2324L39.01 21.0532L39.01 11.265C39.01 7.20212 35.5814 3.83135 31.3907 3.83135L15.2656 3.83135L15.2655 0.399903ZM39.61 22.4785L35.1007 18.0714L33.5025 19.6335L40.7515 26.7187L48.0001 19.6335L46.4019 18.0714L41.8929 22.4785V11.265C41.8929 5.57437 37.2127 0.999908 31.3906 0.999908L15.8655 0.999909L15.8656 3.23135L31.3907 3.23135C35.8998 3.23135 39.61 6.85791 39.61 11.265L39.61 22.4785ZM0.141602 16.3665L8.24902 8.44217L16.3564 16.3664L13.8998 18.7675L9.99083 14.9468L9.99048 24.7349C9.99048 28.7978 13.4191 32.1686 17.6098 32.1686L33.7349 32.1686V35.5999H17.6098C11.4694 35.5999 6.50747 30.7697 6.50747 24.7348V14.9467L2.59859 18.7673L0.141602 16.3665ZM7.10749 13.5213V24.7348C7.10749 30.4254 11.7878 34.9999 17.6098 34.9999H33.1349V32.7686L17.6098 32.7686C13.1008 32.7686 9.3905 29.142 9.3905 24.7349L9.3909 13.5214L13.8998 17.9285L15.498 16.3664L8.24903 9.28117L1.00006 16.3664L2.59855 17.9284L7.10749 13.5213Z" fill="white"/>
                        </g>
                      </svg>
                      </a>
                        <a class="rw-select-merchant-tag-1" href="javascript:void(0)" @click="changeMerchant(index)" v-else>Select Merchant</a>
                      </div>
                    </div>
                    <!-- <span class="">Select</span> -->
                  </div>
                </div>
                <div class="cp-point-card-bottom p-0">
                    <PriceRange 
                    @change="rangeOnSlide(up_data, $event)"
                    @input="rangeOnSlide(up_data, $event)"
                    v-bind:owned-points="getTotalPoints(up_data)" 
                    v-bind:generic-points="wheelPoints.canpay_points ? parseInt(wheelPoints.canpay_points.reward_point) : 0" 
                    v-bind:merchant-points="up_data.selectedMerchant && up_data.selectedMerchant.reward_point ? parseInt(up_data.selectedMerchant.reward_point) : 0" 
                    v-bind:exchange-rate="wheelPoints.canpay_points ? parseFloat(wheelPoints.canpay_points.exchange_rate): 0"
                    v-bind:minimum-redeem-points="wheelPoints.canpay_points ? parseInt(wheelPoints.canpay_points.minimum_redeem_points) : 0"
                    v-bind:merchant-name="up_data.selectedMerchant && up_data.selectedMerchant.merchant_name ? up_data.selectedMerchant.merchant_name : 'Merchant'" 
                    v-bind:merchant-point-available="up_data.other_points ? up_data.other_points.length : 0" 
                    v-bind:merchant-point-selected="up_data.selectedMerchant && up_data.selectedMerchant.reward_point ? 1 : 0" 
                    v-model="up_data.minimumSpendPoints"
                    />
                </div>
              </div>
            </div>
            <div class="custom-row" style="position:absolute;bottom:10px;width:100%!important" v-if="reasonTypeIncrease(up_data)">
             
                <button  class="reject-btn"  @click="acceptDeclineTransactionAmount(0, up_data.transaction_id, up_data.updated_amount, up_data.tip_amount, index)">
                Reject
                </button>
          
           
                <button  class="approve-btn" @click="acceptDeclineTransactionAmount(1, up_data.transaction_id, up_data.updated_amount, up_data.tip_amount, index)">
                Approve
                </button>
          
            </div>
          <div class="row " v-else>
            <div class="text-center px-3 pb-2" >
              <div class="foot-btn mb-3" >
              <button type="button" style="width:95%;"  class="btn btn-success thnku-btn" @click="updateTransactionRead(up_data.transaction_id, up_data.updated_amount)" >Thanks For Letting Me Know</button>
              </div>

            </div>
          </div>
        </div>
      </div>

      <div class="mx-auto" v-else>
        <enter-tip :transactiondetails="transactiondetails"></enter-tip>
      </div>
      

    </div>
  </div>
  <!-----------------------  MODAL FOR TRANSACTION DECLINED  !------------------------>
  <!--------------------------------------------------------------------------->
  <b-modal
    ref="transaction-modal"
    hide-footer
    v-b-modal.modal-center
    no-close-on-backdrop
    modal-backdrop
    hide-header
    id="transaction-modal"
    centered
    title="BootstrapVue"
  >
    <div class="color">
      <div class="col-12 text-center">
        <svg
          version="1.1"
          id="Layer_1"
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          x="0px"
          y="0px"
          width="120"
          height="120"
          viewBox="0 0 100 125"
          style="enable-background: new 0 0 100 125"
          xml:space="preserve"
          fill="#e14343"
        >
          <path
            d="M96.2,47.5l-22-38c-0.4-0.6-1-1-1.7-1h-44c-0.7,0-1.4,0.4-1.7,1l-22,37.9c-0.4,0.6-0.4,1.4,0,2l22,38.1c0.4,0.6,1,1,1.7,1
h44c0.7,0,1.4-0.4,1.7-1l22-38C96.6,48.9,96.6,48.1,96.2,47.5z M71.3,84.5H29.7L8.8,48.4l20.8-35.9h41.7l20.8,36L71.3,84.5z
M50.5,60.5c1.1,0,2-0.9,2-2v-30c0-1.1-0.9-2-2-2c-1.1,0-2,0.9-2,2v30C48.5,59.6,49.4,60.5,50.5,60.5z M48.4,66.4
c-0.6,0.6-0.9,1.3-0.9,2.1c0,0.8,0.3,1.6,0.9,2.1s1.3,0.9,2.1,0.9c0.8,0,1.6-0.3,2.1-0.9c0.6-0.6,0.9-1.3,0.9-2.1
c0-0.8-0.3-1.6-0.9-2.1C51.5,65.3,49.5,65.3,48.4,66.4z"
          />
        </svg>
      </div>
      <div class="d-block text-center">
        <label class="purchasepower-def-label">Transaction Declined</label>
      </div>
      <label
        class="purchasepower-modal-text text-center"
        style="margin: 10px"
        >{{ transactionModificationMessage }}</label
      >
      <br />
      <br />
      <div class="text-center">
        <button v-if="relink_banking_button == 1"
          type="button"
          class="mx-auto col-10 offset-1 btn-black"
          style="height: 60px"
          v-on:click="$router.push('/banklinking')"
        >
          <label class="purchasepower-modal-ok-label">Relink Banking</label>
        </button>
        <button v-else
          type="button"
          @click="hideModalRedirect('transaction-modal')"
          class="mx-auto col-10 offset-1 btn-black"
          style="height: 60px"
        >
          <label class="purchasepower-modal-ok-label">Close</label>
        </button>
      </div>
    </div>
  </b-modal>
  <!-----------------------  Simple MODAL  !------------------------>
  <b-modal
    ref="show-more-modal"
    hide-footer
    v-b-modal.modal-center
    no-close-on-backdrop
    modal-backdrop
    hide-header
    id="show-more-modal"
    centered
    title="BootstrapVue"
  >
    <div class="color">
      
      <div class="d-block text-center">
        <label class="purchasepower-def-label">Transaction Modification Reason</label>
      </div>
      <label
        class="text-center"
        style="margin: 10px; word-break: break-all;"
        >{{ transactionMoReasonMessage }}</label
      >
      <br />
      <br />
      <div class="text-center">
        <button
          type="button"
          @click="hideModal('show-more-modal')"
          class="mx-auto col-10 offset-1 btn-black"
          style="height: 60px"
        >
          <label class="purchasepower-modal-ok-label">Close</label>
        </button>
      </div>
    </div>
  </b-modal>

  <!-----------------------  MODAL FOR EMAIL AND PHONE NUMBER CHANGE  !----------------->
  <b-modal
    ref="accept-reject-alert-model"
    hide-footer
    v-b-modal.modal-center
    modal-backdrop
    no-close-on-backdrop
    :hide-header="hideModalHeader"
    :title="modalTitle"
    :show-close="false"
    header-class="modalHeader"
    id="accept-reject-alert-model"
    centered
  >
    <div class="color">
      <div class="purchaserpower-modal-text">
        <div class="d-block text-center">
          <label class="update-modal-title"><b>{{ alertTitle }}</b></label>
        </div>
        <div class="row" v-if="tipUpdateShow">
          <div class="col-12">
            <label class="modal-tip">
                <b>Do you want to update your TIP amount?</b>
            </label>
          </div>
          <div class="col-12">
            <input
              @input="tipAmountValidate"
              type="number"
              v-model="tip_amount"
              class="form-control amount_input numberonly"
            />
            <i class="fa fa-dollar fa-sm doller_icon"></i>
          </div>
        </div>
        <div class="row">
            <div class="col-6 pr-1">
              <button
              @click="hideModal('accept-reject-alert-model')"
              class="btn btn-danger btn-md center-block tip-cancel-btn"
              >
                <span class="forgetpassword-ok-label">Cancel</span>
              </button>
            </div>
            <div class="col-6 pl-1">
              <button
              @click="acceptDeclineApiCall"
              class="btn btn-danger btn-md center-block tip-ok-btn w-100"
              >
                <span class="forgetpassword-ok-label">{{
                  sendButtonTitle
                }}</span>
              </button>
            </div>
        </div>
      </div>
    </div>
  </b-modal>
</div>
</template>
<script>
import { db } from "../firebaseConfig.js";
import api from "../api/transactiondetails.js";
import tip from "./Payment/Tip.vue";
import rewardwheelapi from "../api/rewardwheel.js";
import PriceRange from "./Payment/components/PriceRange.vue";
import payment_api from "../api/payment.js";
import account from "../api/account.js";
import Loading from "vue-loading-overlay";
import VueLoadImage from "vue-load-image";
import moment from "moment";
import MerchantPoints from "../components/Payment/components/MerchantPoints.vue";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  name: "UpdatedTransactionDetails",
  data() {
    return {
      isLoading : false,
      selected_index:0,
      fullPage : true,
      showModifyHistory : true,
      transactiondetails: {},
      alertTitle: '',
      tipUpdateShow: false,
      tip_amount: 0,
      sendButtonTitle: '',
      updated_data: [],
      transactionModificationMessage:null,
      transactionMoReasonMessage:null,
      modalTitle: '',
      hideModalHeader: true,
      relink_banking_button:0,
      purchase_power: localStorage.getItem("purchasepower"),
      transactionPointForm: {
        generic_reward_point: null,
        reward_wheel_id: null,
        reward_point: null,
        merchant_id:null
      },
      wheelPoints: {},
      selectedMerchant: {},
      rwState: '',
      showMerchantPoint: false,
    };
  },
  created() {
    this.currentUser = localStorage.getItem("consumer_login_response")
      ? JSON.parse(localStorage.getItem("consumer_login_response"))
      : null;
      
    
    this.beforeGetdata();
    this.otherAction();
  },
  components: {
    "enter-tip": tip,
    "vue-load-image": VueLoadImage,
    Loading,
    CanPayLoader,
    PriceRange,
    MerchantPoints
  },
  methods: {
    redirectBackToDashboard(){
      this.showMerchantPoint = false;
      var app = document.getElementById('nav-drawer');
      app.setAttribute('style', 'overflow-y:scroll !important');
    },
    showModal(data) {
      this.$refs[data].show();
    },
    hideModal(data) {
       this.$refs[data].hide();
    },
    hideModalRedirect(data) {
      this.getModifyDataCheck();
      this.hideModal(data)
    },
    additionalReasonChk(additionalReaso) {
      if(additionalReaso){
        return true
      }else{
        return false;
      }
    },
    reasonVIew(reason, no_of_ch) {
      if(reason.length > no_of_ch){
        return `${reason.slice(0, no_of_ch).trim()}...`;
      }else{
        return reason;
      }
    },
    reasonVIewMoreButton(reason, no_of_ch) {
      return reason.length > no_of_ch ? true : false;
    },
    reasonTypeIncrease(up_data) {
      if( up_data.status == 'Awaiting Consumer Approval'){
        return true;
      }else{
        return false;
      }
    },
    transactiontime(up_data) {
      return moment
        .utc(up_data.transaction_time)
        .local()
        .format(" DD MMM, YYYY | hh:m A");
    },
    showingFullTex(reason) {
      let self = this;
      self.transactionMoReasonMessage = reason;
      self.showModal('show-more-modal');
    },
    beforeGetdata() {
      let self = this;
      var transactionModifiyDetails = window.location.href.split("/");
      transactionModifiyDetails=  transactionModifiyDetails.splice(-1, 1);
      if(transactionModifiyDetails[0].includes('?')){
        var decodeTransaction = transactionModifiyDetails[0].split("?");
        if(decodeTransaction[1].includes('=%3D')){
          decodeTransaction = decodeTransaction[1].split("=%3D");
          decodeTransaction = atob(decodeTransaction[0]);
          self.gettransactionId  = decodeTransaction.split("-")[1];
          self.getmodificationAmount = decodeTransaction.split("-")[3];
        }else{
          decodeTransaction = atob(decodeTransaction[1]);
          self.gettransactionId  = decodeTransaction.split("-")[1];
          self.getmodificationAmount = decodeTransaction.split("-")[3];
        }
        
      }
      self.getdata();
      if (localStorage.getItem("updated_transaction_data") != null) {
        self.updated_data = JSON.parse(localStorage.getItem("updated_transaction_data"));
        localStorage.removeItem("updated_transaction_data");
        self.getWheelPoints();
      }else{
        self.getModifyData();
      }
    },
    getdata() {
      let self = this;
      let ref = db
        .collection("users")
        .doc(String(String(this.currentUser.user_id)));
      ref.get().then((snapshot) => {
        ref.onSnapshot((convo) => {
          let source = convo.metadata.hasPendingWrites ? "Local" : "Server";
          let ref = db
            .collection("users")
            .doc(String(this.currentUser.user_id));
          ref.get().then((snapshot) => {
            if (snapshot.exists) {
              this.users = snapshot.data();
              const containsKey = (obj, key) => Object.keys(obj).includes(key);
              const hasTMName = containsKey(this.users, "transaction_modification");
              const hasTSName = containsKey(this.users, "transaction_successful");
              if (hasTSName == true && this.users.transaction_successful != null) {
                const isDeclined = containsKey(
                  this.users.transaction_successful,
                  "declined"
                );
                if (!isDeclined) {
                  self.setdata('transaction_successful');
                  self.transactiondetails = this.users.transaction_successful;
                  self.showModifyHistory = false;
                  self.isLoading = false;
                }else{
                  this.setdata('transaction_modification');
                  self.getModifyData();
                }
              } else if (hasTMName == true && this.users.transaction_modification != null) {
                this.setdata('transaction_modification');
                self.getModifyData();
              }
            }
          });
        });
      });
    },
    setdata(type) {
      var data = {};
      if(type == 'transaction_modification'){
        data = {
          transaction_modification: null,
        };
      }else if(type == 'transaction_successful'){
        data = {
          transaction_successful: null,
        };
      }else{
        return true;
      }
      var self = this;
      var washingtonRef = db
        .collection("users")
        .doc(String(this.currentUser.user_id));
      // Set the "capital" field of the city 'DC'
      return washingtonRef
        .update(data)
        .then(function () {
          console.log("Document successfully updated!");
        })
        .catch(function (error) {
          // The document probably doesn't exist.
          console.error("Error updating document: ", error);
        });
    },
    getModifyDataCheck() {
      this.updated_data = this.updated_data.filter(object => {
                              return object.transaction_id !== this.transaction_id;
                            });
      this.isLoading = false;
      if(this.updated_data.length == 0){
        this.$router.push("/pay");
      }
    },
    getModifyData() {
      var self = this;
      self.isLoading = true;
      api
        .getMofifiedTransaction()
        .then(function (response) {
          if (response.code == 200) {
            if(response.data.length == 0){
              self.setdata('transaction_modification');
              self.$router.push("/pay");
            }else{
              self.updated_data = response.data;
              self.getWheelPoints();
            }
          }
          
        })
        .catch(function (err) {
          if (err.response.data.message == 401) {
            self.$router.push("/pay");
          } else {
            self.nopurchasepower = true;
            self.purchasepower = err.response.data.message;
          }
        });
    },
    acceptDeclineTransactionAmount(consumer_approve, transactionId, modificationAmount, tip_amount, index)
    {
      let self = this;
      self.consumer_approve = consumer_approve;
      self.transaction_id = transactionId;
      self.updated_amount = modificationAmount;
      self.tip_amount = tip_amount;
      self.tipUpdateShow = false;
      if(consumer_approve == 0){
        self.alertTitle = 'You are rejecting the transaction update request. This will cancel the transaction. Do you want to proceed?';
        self.sendButtonTitle = 'Reject';
        self.modalTitle = "";
        self.hideModalHeader = true;
      }else{
        self.alertTitle = 'I approve this payment increase.';
        self.sendButtonTitle = 'Approve';
        self.modalTitle = "Approve Payment Increase";
        self.hideModalHeader = false;
        self.selected_index = index;
        if(self.tip_amount > 0){
          self.tipUpdateShow = true;
        }
      }
      self.showModal('accept-reject-alert-model');
    },
    updateTransactionRead(transactionId, modificationAmount)
    {
      let self = this;
      self.isLoading = true;
      self.transaction_id = transactionId;
      var request = {
      consumer_approve :2,
      transaction_id: transactionId,
      updated_amount: modificationAmount
      };
        payment_api
        .consumerTransactionApproved(request)
        .then((response) => {
          if (response.code == 200) {
            setTimeout(() => {
              self.getModifyDataCheck();
            }, 500);
          }
        })
        .catch(function (error) {
          self.isLoading = false;
          alert(error.response.data.message);
        });
    },
    acceptDeclineApiCall(){
      let self = this;
      self.hideModal('accept-reject-alert-model');
      self.isLoading = true;
      var transactionPointForm = self.updated_data[self.selected_index].transactionPointForm ? self.updated_data[self.selected_index].transactionPointForm : {};
      var request = {
        consumer_approve : self.consumer_approve,
        transaction_id: self.transaction_id,
        updated_amount: self.updated_amount,
        is_modification:true,
        merchant_id: transactionPointForm ? transactionPointForm.merchant_id : null,
        reward_point: transactionPointForm ? transactionPointForm.reward_point : null,
        reward_wheel_id:null,
        generic_reward_point: transactionPointForm ? transactionPointForm.generic_reward_point : null,
        tip_amount: parseFloat(self.tip_amount)
      };
    
        payment_api
        .consumerTransactionApproved(request)
        .then((response) => {
          if (response.code == 200) {
            // if decline
            if(self.consumer_approve == 0){
              self.isLoading = false;
              self.transactionModificationMessage = response.message;
              setTimeout(() => {
                self.showModal("transaction-modal");
              }, 500);
            }
          }
        })
        .catch(function (error) {
          self.isLoading = false;
          self.transactionModificationMessage = error.response.data.message;
          self.relink_banking_button = error?.response?.data?.data?.relink_banking_button === 1 ? 1 : 0;
          self.showModal("transaction-modal");
          if(self.consumer_approve == 1){
            setTimeout(() => {
              self.setdata('transaction_successful');
            }, 1500);
          }
        });
    },
    otherAction() {
      $(document).on("keypress", ".numberonly", function (e) {
        var keyCode = e.which ? e.which : e.keyCode
        if ((keyCode != 46 || $(this).val().indexOf('.') != -1) && (keyCode < 48 || keyCode > 57)) {
          self.UpAmtMsg = true;
          self.UpAmtErrorMsg = 'Please fill in the required fields.';
          return false;
        } else {
          self.UpAmtMsg = false;
          return true;
        }
      });
    },
    tipAmountValidate(val){

      if(this.tip_amount.indexOf('.') !== -1)
      {
        var afterDot = this.tip_amount.substr( this.tip_amount.indexOf('.') + 1 );

        if(afterDot.toString().length > 2){
          this.tip_amount = parseFloat(this.tip_amount).toFixed(2)
        }else{
          this.tip_amount = this.tip_amount
        }
      }
      
    },
    rangeOnSlide(up_data, val){
      var self = this;
      self.isLoading = true;
      let value = 0;
      if(val > 0){
        value = val;
      }else{
        value = 0;
      }
      up_data.transactionPointForm = {};
      up_data.transactionPointForm.merchant_id = up_data.selectedMerchant?.merchant_id;

      if (up_data.transactionPointForm.merchant_id) {
        up_data.transactionPointForm.reward_point =
          value > up_data.selectedMerchant.reward_point ? up_data.selectedMerchant.reward_point : value;
        up_data.transactionPointForm.generic_reward_point =
          value > up_data.selectedMerchant.reward_point ? value - up_data.selectedMerchant.reward_point : null;
      } else {
        up_data.transactionPointForm.generic_reward_point = value;
      }
      var pointAmount = parseFloat(parseFloat(self.wheelPoints.canpay_points.exchange_rate) * value).toFixed(2);

      // Ensure pointAmount does not exceed updated_amount
      if (parseFloat(pointAmount) > parseFloat(up_data.updated_amount)) {
          pointAmount = up_data.updated_amount; // Limit to updated_amount
      }

      up_data.reward_point_used = pointAmount;
      up_data.bank_posting_amount = parseFloat(parseFloat(up_data.updated_amount) - parseFloat(pointAmount)).toFixed(2);

      self.isLoading = false;
    },
   
    getTotalPoints(up_data){
      var self = this;
      if(up_data.selectedMerchant && up_data.selectedMerchant.reward_point){
        return parseInt(self.wheelPoints.canpay_points.reward_point) + parseInt(up_data.selectedMerchant.reward_point);
      } else{
        return self.wheelPoints.canpay_points ? parseInt(self.wheelPoints.canpay_points.reward_point) : 0;
      }
    },
    getWheelPoints(){
      var self = this;
      self.isLoading = true;
      rewardwheelapi
      .wheelPoints()
      .then((response) => {
        let pointsArr = response.data
        pointsArr.other_points.forEach((other_point, index) => {
          pointsArr.other_points[index]['spend_point'] = 0
          pointsArr.other_points[index]['disabled'] = false
        });
        self.wheelPoints = pointsArr
        self.updated_data.forEach(data => {
            data.minimumSpendPoints = 0;
            data.bank_posting_amount = data.updated_amount;
            data.reward_point_used = parseFloat(0).toFixed(2);
            data.selectedMerchant = self.wheelPoints.other_points.find(m => m.merchant_id === data.merchant_id);
            data.other_points = self.wheelPoints.other_points.filter(m => m.merchant_id === data.merchant_id);
        });
        self.isLoading = false;
      })
      .catch(function (error) {
        self.isLoading = false;
      });
    },
    
    changeMerchant(index){
      var self = this;
      self.isLoading = true;
      self.updated_data[index].selectedMerchant = self.updated_data[index].other_points[0];
      self.isLoading = false;
    },
    removeMerchant(index) {
      var self = this;
      self.isLoading = true;
      delete self.updated_data[index].selectedMerchant;
      self.isLoading = false;
    },
  },
  mounted() {
    var element = document.getElementsByClassName("content-wrap");
    if (element[0]) {
      element[0].style.setProperty("background-color", "#149240");
      element[0].style.height = "114vh";
      if(window.innerWidth>1200){
        element[0].style.height = "121vh";
      }
    }
    this.$root.$emit("loginapp", [""]);
    this.$root.$emit("changeWhiteBackground", [false, true, "common"]);
  },
};
</script>
<style lang="scss" scoped>
.updated-transaction-container{
  height: calc(100vh - 150px);
  padding-top: 15px;
  padding-bottom: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  // flex-flow: column;
}
@media only screen and (max-width: 1300px) {
  .updated-transaction-container{
    height: calc(100vh - 150px);
  }
}
.updated-transaction-slider {
  height: 570px;
  /* line them up horizontally */
  display: flex;

  /* allow for scrolling */
  overflow-x: auto;

  /* make it smooth on iOS */
  -webkit-overflow-scrolling: touch;
  scroll-snap-points-x: repeat(300px);
  scroll-snap-type: mandatory;
}
.updated-transaction-slider > div {
  /* make sure the width is honored */
  flex-shrink: 0;
  width: 340px;
  // height: 300px;
  // line-height: 300px;
  text-align: center;
  background: #ffffff;
  margin-right: 15px;
  border-radius: 5px;
}
.updated-transaction-slider-card{
    height: 100%;
    border-radius: 0 0 6px 6px ;
}
.updated-transaction-slider-card-header{
  background-color: #000;
  padding: 20px 15px;
  border-radius: 6px 6px 0 0 ;
  font-size: 15px;
}
.updated-transaction-slider-card-header h4{
  color: #fff;
  margin-bottom: 0;
  font-size: 19px;
}
.updated-transaction-slider-card-body{
  padding: 15px;
  border-radius: 0 0 6px 6px ;
}
.foot-btn{
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%!important;
}
.modification_text_group{
  margin-bottom: 1.1rem;
}
.modification_text_highlight{
  background-color: #b4b4b426;
  text-align: center;
  border-radius: 5px;
  padding: 10px;
}
.modification_text_highlight .timer{
  font-size: 1.3rem;
}
.modification_text_group p{
  margin-bottom: 0.1rem;
}
.amount_input{
  width: 100%;
  padding: 0px 30px !important;
  display: inline-block;
  border: 1px solid #ccc;
  box-sizing: border-box;
}
.doller_icon{
  position: absolute;
  left: 30px;
  top: 12px;
  color: gray;
}

</style>

<style>
.rw-change-merchant-tag-1{
  position: absolute;
  right: -10px;
  top: 49%; 
}
#accept-reject-alert-model___BV_modal_content_ {
  border-radius: 10px;
  margin: 10px;
  background-color: #ffffff;
}
#accept-reject-alert-model___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#accept-reject-alert-model___BV_modal_body_ .update-modal-title {
  font-size: 1.1rem;
}
#accept-reject-alert-model___BV_modal_body_ .modal-tip {
  font-size: 0.9rem;
}

.tip-cancel-btn {
    width: 100%;
    padding: 10px 10px;
    height: 50px;
    background-color: black !important;
    border-color: black !important;
    display: inline-block;
    vertical-align: top;
}
.thnku-btn{
  color: #fff;
  background-color: #149240;
  border-color: #149240;
  width: 100%;
  height: 50px;
  margin: 0;
  border-radius: 5px;
}
.close {display: none;}
.modalHeader{
  background-color: black;
  color: white;
  justify-content: center;
}
.reject-btn{
  width:90%;
  margin-left:10px;
  margin-right:5px;
  padding:15px;
  border-radius:10px;
  background-color:#000;
  color:#fff;
  font-family: 'Open Sans';
  font-size:21px;
  font-weight:600;
  border:none;
}
.approve-btn{
  width:90%;
  padding:15px;
  margin-right:10px;
   margin-left:5px;
  border-radius:10px;
  background-color:#149240;
  color:#fff;
  font-family: 'Open Sans';
  font-size:21px;
  font-weight:600;
  border:none;
}
.reward-btn{
  width:100%;
  padding:10px;
  border-radius:10px;
  background-color:#149240;
  color:#fff;
  font-family: 'Open Sans';
  font-size:1rem;
  font-weight:600;
  border:none;
}
.select-btn{
  width:100%;
  padding:15px;
  border-radius:10px;
  background-color:#000;
  color:#fff;
  font-family: 'Open Sans';
  font-size:21px;
  font-weight:600;
  border:none;
}
#reward-amount-adjustment-model___BV_modal_content_{
 background-color:#ffffff;
}
#reward-amount-adjustment-model___BV_modal_body_{
  background-color:#ffffff;
  border-radius:12px;
}
.canpay-text{
    bottom:0;
    margin-top:10px;
    font-weight:bolder;
    font-size:25px;
    color:#000000;
    font-family: "Montserrat";
}
.custom-row{
    display:flex;
    justify-content:center;
}
.modification-font-text{
  font-size:13px!important;
}
.modification-alert-header{
  background-color: #EDEDED;
  font-family: "Open Sans";
  font-size:1.05rem;
  font-weight:bolder;
}
.modification-alert-text1{
  font-weight:400;
  font-family: "Open Sans";
  font-size:0.9rem;
}
.modification-alert-text2{
  font-weight:700;
  font-family: "Open Sans";
  font-size:0.9rem;
}
.modification-alert-text3{
  font-weight:700;
  font-family: "Open Sans";
  font-size:1rem;
}
.only-green{
  color:#1B9142;
}
.margin-zero{
  margin:0px!important;
}
.border-radius-modification{
  border-radius:10px;
}
.border-radius-modification-top{
  border-radius: 10px 10px 0px 0px;
}
.merchant-options-card {
  background: #149240;
  position: fixed;
  bottom: -100%;
  opacity:1;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99;
}
.rw-beta-new-tag-1{
    background: #095a26;
    position: absolute;
    padding: 3px 8px;
    border-radius: 50px;
    font-size: 14px;
    margin-left: 2px;
    right: -8px;   
}
.rw-merchant-text{
  font-size: 12px;
  color: #fff;
}
.rw-merchant-text span{
  font-weight: 600;
}

.rw-select-merchant-tag-1{
    background: #095a26;
    padding: 2px 9px;
    border-radius: 7px;
    font-size: 10px;
    color: #fff;
    margin-top: 2px;
    margin-left: 4px;
    position: absolute;
    left: 94px;
 }

@media only screen and ( min-width:280px) and ( max-width:700px) {
  .rw-merchant-text {
    font-size: 8px;
  }
  .merchant-select-btn {
    font-size: 8px;
    padding: 3px 8px; 
  }
}

@media only screen and ( min-width:320px) and ( max-width:700px) {
  .rw-merchant-text {
    font-size: 10px;
  }
  .merchant-select-btn {
    font-size: 9px;
    padding: 3px 8px; 
  }
}

@media only screen and ( min-width:376px) and ( max-width:800px) {
  .rw-merchant-text {
    font-size: 12px;
  }
  .merchant-select-btn{
    font-size: 10px;
    padding: 4px 8px;
  }
}
</style>