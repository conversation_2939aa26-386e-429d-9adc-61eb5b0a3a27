<template>
    <div>
        <div>
            <b-modal
                ref="intermediate-bank-linking"
                hide-footer
                no-close-on-backdrop
                no-close-on-esc
                modal-backdrop
                hide-header
                id="intermediate-bank-linking"
                centered
            >
            <div class="text-center">
                <div>
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="85" height="85" viewBox="0 0 213 186" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H213V186H0V0Z" fill="url(#pattern0_10067_408)"/>
                    <defs>
                        <pattern id="pattern0_10067_408" patternContentUnits="objectBoundingBox" width="1" height="1">
                        <use xlink:href="#image0_10067_408" transform="scale(0.******** 0.********)"/>
                        </pattern>
                        <image id="image0_10067_408" width="213" height="186" xlink:href="data:image/png;base64,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"/>
                    </defs>
                    </svg>
                </div>
                <div>
                 <p style="font-weight:bolder;font-size:17px;font-family:'montserrat';" class="mt-3">
                    We noticed you already have account(s) linked with this bank. Would you like to link a new account or relink your existing account?
                 </p>
                </div>
                <div class="row" style="margin-top:15px;">
                    <div class="col-6">
                        <button class="repair-font" @click="linkNewBank()">
                            Link New Account
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="relink-font" @click="relinkBank()">
                            Relink Account
                        </button>
                    </div>
                </div>
            </div>
            </b-modal>
        </div>  
    </div>
</template>
<script>
export default {
name: "IntermediateBankLinkingModal",
props: {
    directLinkGenerateAPICall:{
        type:Function,
        required: true
    }
},
methods:{
    linkNewBank(){
        let self = this;
        self.hideModal();
        // bank connection is MX, skip_check is 1, widget_mode is verification.
        self.directLinkGenerateAPICall("mx",1,1,"verification");
    },
    relinkBank(){
        let self = this;
        self.hideModal();
        // bank connection is MX, skip_check is 1, widget_mode is verification.
        self.directLinkGenerateAPICall("mx",1,1,"aggregation");
    },
    showModal(){
        let self = this;
        self.$bvModal.show("intermediate-bank-linking");
    },
    hideModal(){
        let self = this;

        self.$bvModal.hide("intermediate-bank-linking");
    }
}
}
</script>
<style scoped>
#intermediate-bank-linking___BV_modal_body_ {
  background-color: #ffffff;
  border-radius: 12px;
  margin: 10px;
}
#intermediate-bank-linking___BV_modal_content_{
    background-color: #ffffff;
}
.repair-font{
    border:none;
    width:100%;
    padding:15px;
    border-radius:5px;
    background-color:#149240;
    font-family:"Montserrat";
    font-size:12px;
    font-weight:bolder;
    color:#ffffff;
}
.relink-font{
    border:none;
    width:100%;
    padding:15px;
    border-radius:5px;
    background-color:#000000;
    font-family:"Montserrat";
    font-size:12px;
    font-weight:bolder;
    color:#ffffff;
}
</style>