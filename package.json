{"name": "project", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "watch": "vue-cli-service build --watch", "fetch-secrets": "node fetch-secrets.js"}, "dependencies": {"@aws-sdk/client-secrets-manager": "3.721.0", "@babel/helper-compilation-targets": "^7.27.1", "@gtm-support/vue2-gtm": "^1.3.0", "@mxenabled/web-widget-sdk": "0.0.11", "@typeform/embed": "^2.16.1", "axios": "^0.26.1", "bootstrap": "^4.6.2", "bootstrap-vue": "^2.23.1", "core-js": "^3.44.0", "dotenv": "^16.6.1", "file-saver": "^2.0.5", "jquery": "^3.7.1", "mobile-device-detect": "^0.3.3", "moment": "^2.29.4", "nativescript-datetimepicker": "^1.2.3", "popper.js": "^1.16.1", "register-service-worker": "^1.7.2", "sass": "^1.89.2", "sass-loader": "^8.0.2", "vue": "^2.7.15", "vue-axios": "^2.1.5", "vue-drawer-layout": "^1.3.0", "vue-firestore": "^0.3.30", "vue-google-autocomplete": "^1.1.4", "vue-intercom": "^1.0.14", "vue-load-image": "^0.1.61", "vue-loading-overlay": "^3.4.2", "vue-long-click": "^0.1.0", "vue-multiselect": "^2.1.9", "vue-online-prop": "^1.0.1", "vue-range-slider": "^0.6.0", "vue-router": "^3.6.5", "vue-select": "^3.20.4", "vue-spinners-css": "^1.1.5", "vue-tabs-with-active-line": "^1.2.6", "vue-the-mask": "^0.11.1", "vue-toasted": "^1.1.28", "vue2-gmap-custom-marker": "^5.6.3", "vue2-google-maps": "^0.10.7", "vuejs-auto-complete": "^0.9.0", "vuejs-countdown-timer": "^2.1.3", "vuejs-datepicker": "^1.6.2", "vuex": "^4.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.5.19", "@vue/cli-plugin-pwa": "^4.5.19", "@vue/cli-plugin-router": "^4.5.19", "@vue/cli-service": "^4.5.19", "babel-loader": "^9.2.1", "vue-template-compiler": "^2.7.15"}}