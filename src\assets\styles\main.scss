/* Variables scss imported */

@import url(https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,600,700,300italic,400italic,600italic);
@import "vue-select/src/scss/vue-select.scss";
@import "variables";

/* Components scss imported */

@import "components/buttons";
@import "components/div";
@import "components/navdrawer";
@import "components/drawer";
@import "components/progress";
@import "components/searchbar";
// /* Modules scss imported */
@import "modules/profile";
@import "modules/app";
@import "modules/tip";
@import "modules/pay";
@import "modules/splash";
@import "modules/login";
@import "modules/registration";
@import "modules/enterotp";
@import "modules/transactiondetails";
@import "modules/registerstep2";
@import "modules/registerstep3";
@import "modules/register5";
@import "modules/navbar";
@import "modules/regstep2";
@import "modules/regStep3";
@import "modules/terms&conditions";
@import "modules/privacypolicy";
@import "modules/forgotpassword";
@import "modules/entermoredetails";
@import "modules/banklinking";
@import "modules/onboarding.scss";
@import "modules/return.scss";
// @import "modules/OnBoardScreeen/upgradecanpaywithemail";
// @import "modules/OnBoardScreeen/emailverificationsuccess";
// @import "modules/OnBoardScreeen/existingphonenumberverification";
// @import "modules/OnBoardScreeen/phonenumberverificationcode";
// @import "modules/OnBoardScreeen/duplicatemobilenumber";
// @import "modules/OnBoardScreeen/registrationprimaryphonenumber";
// @import "modules/OnBoardScreeen/bankaccountregistration";
@import './components/signers.scss';
@import './components/petition.scss';