<template>
<div style="box-shadow: rgb(0, 0, 0) 1px 0px 2px;
    border-radius: 0px 0px 14px 18px;
    padding: 0px 0px 12px;
    position: relative;
    z-index: 5;
    background-color: white;">
  <div class="">
    <div>
      <div class="container">
      <div id="row-header" >
        <div style="display:flex;">
          <div
            class="canpay-crew-position-for-store-header"
          >
            <a href="javascript:void(0)" v-on:click="changeComponent(2)">
              <svg data-v-5a215274="" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 67 56" height="20" width="20" style="" fill="none"><path data-v-5a215274="" d="M2.38113 25.5833L26.6029 1C27.9167 -0.333333 30.0515 -0.333333 31.3652 1C32.6789 2.33333 32.6789 4.5 31.3652 5.83333L12.8909 24.5833H63.6336C65.5221 24.5833 67 26.0833 67 28C67 29.9167 65.5221 31.4167 63.6336 31.4167H12.8909L31.3652 50.1667C32.6789 51.5 32.6789 53.6667 31.3652 55C30.7083 55.6667 29.8873 56 28.9841 56C28.0809 56 27.2598 55.6667 26.6029 55L2.38113 30.4167L0 28L2.38113 25.5833Z" fill="black"></path></svg>
            </a>
          </div>
          <div class="text-left pr-0 canpay-crew-padding-left-none" id="col-header">
            <label class="transaction-history-lable" style="font-size:18px!important;margin-left:28px;">Petition a Store</label>
          </div>
        </div>
      </div>
      </div>
      <div class="container">
      <div>
        <div style="width:100%;">
        <div class="pt-2 pb-2" style="justify-content: center;display: flex;">
            <div class="canpay-crew-search-1">
                <div class="row" style="position:relative;">
                  <input class="canpay-crew-input-search-1" type="text" placeholder="Search" v-model="storeObjChild.store_name">
                  <svg v-on:click="fetchStore(storeObjChild)" style="position:absolute;right:21px;top:5px" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 42 43" fill="none">
                  <path d="M41.303 38.0504L33.7652 30.5642C39.6509 23.0264 39.0313 12.2876 32.268 5.52428C28.7056 1.96189 23.9557 0 18.8961 0C13.8365 0 9.08666 1.96189 5.52428 5.52428C1.96189 9.08666 0 13.8365 0 18.8961C0 23.9557 1.96189 28.7056 5.52428 32.268C9.08666 35.8304 13.8365 37.7923 18.8961 37.7923C23.0781 37.7923 27.2084 36.3983 30.5126 33.7652L37.9988 41.3546C38.4634 41.8193 39.0313 42.0258 39.6509 42.0258C40.2704 42.0258 40.8384 41.7677 41.303 41.3546C42.2323 40.4769 42.2323 38.9797 41.303 38.0504ZM33.1457 18.8961C33.1457 22.7167 31.6484 26.279 28.9637 28.9637C26.279 31.6484 22.665 33.1457 18.8961 33.1457C15.1272 33.1457 11.5132 31.6484 8.82852 28.9637C6.14382 26.279 4.64659 22.665 4.64659 18.8961C4.64659 15.0756 6.14382 11.5132 8.82852 8.82852C11.5132 6.14382 15.1272 4.64659 18.8961 4.64659C22.7167 4.64659 26.279 6.14382 28.9637 8.82852C31.6484 11.5132 33.1457 15.0756 33.1457 18.8961Z" fill="black"/>
                  </svg>
                </div>
            </div>
        </div>
        </div>
      </div>
      </div>
    </div>
  </div>
</div>
</template>
<script>
import DrawerLayout from "vue-drawer-layout";
export default {
  name: "CanPayCrewStoreHeader",
  props:{
    storeObj:{
      type:Object
    },
    fetchStore:{
      type:Function
    },
    changeComponent:{
      type:Function
    }
  },
  data: () => ({
    open: true,
    showmenuicon: true,
    consumer_type: localStorage.getItem("consumer_login_response")
        ? JSON.parse(localStorage.getItem("consumer_login_response")).consumer_type
        : null,
    storeObjChild:{},
  }),
  watch:{
  },
  components: {
    DrawerLayout
  },
  mounted() {
    let self = this;
    self.$root.$on("Menu Drawer Close", function(data) {
      self.showmenuicon = true;
    });

    self.$root.$on("Menu Drawer Open", function(data) {
      setTimeout(function() {
        self.showmenuicon = false;
      }, 30);
    });
    self.storeObjChild = self.storeObj;
  },
  methods: {
    showDrawer() {
      this.showmenuicon = false;
      this.$root.$emit("Menu Drawer", [""]);
    },
    getStoreName(){
      let self = this;
      return self.searchStore;
    }
  }
};
</script>
<style scoped>

</style>
