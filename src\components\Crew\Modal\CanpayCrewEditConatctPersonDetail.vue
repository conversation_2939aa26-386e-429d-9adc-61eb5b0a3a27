<template>
    <div>
        <b-modal
            ref="canpay-crew-edit-conatct-person-detail"
            hide-footer
            v-b-modal.modal-center            
            modal-backdrop
            hide-header
            id="canpay-crew-edit-conatct-person-detail"
            centered
            title="BootstrapVue"
        >
        <div style="font-style:Open Sans">
            <div v-if="stepOFEdit=='canpayCrewEditContactPersonDetail'">
                <div class="row">
                    <div class="col-12 text-right">
                        <svg v-on:click="closeEditContact()" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 45 46" fill="none">
                        <rect width="3.84805" height="59.0035" transform="matrix(-0.707118 0.707095 -0.707118 -0.707095 44.4434 41.7207)" fill="black"/>
                        <rect width="3.84805" height="59.0035" transform="matrix(0.707118 0.707095 -0.707118 0.707096 41.7236 0.907227)" fill="black"/>
                        </svg>
                    </div>
                </div>
                <div class="text-center mt-2">
                    <div class="row">
                        <div class="col-12 canpay-crew-text-font-21 canpay-crew-text-700" style="margin-bottom:24px;">
                            Edit Contact
                        </div>
                    </div>
                </div>
                <div class="row mt-4">
                    <div class="col-12" v-if="contactState == 'primary'">
                        <input type="text"
                         class="canpay-crew-general-input-box
                         canpay-crew-p-margin-bottom-2"
                         maxlength="50"
                         :style="contactDetail.primary_contact_person_firstname == null?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}"
                         placeholder="Store Manager or Employee: First Name" v-model="contactDetail.primary_contact_person_firstname">
                         <p class="text-red-crew" v-if="contactDetail.primary_contact_person_firstname == null">{{storeDataError.error_first_name}}</p>
                        <input type="text"
                         class="canpay-crew-general-input-box
                         canpay-crew-p-margin-bottom-2"
                         maxlength="50"
                         :style="contactDetail.primary_contact_person_lastname == null?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}"
                         placeholder="Store Manager or Employee: Last Name" v-model="contactDetail.primary_contact_person_lastname">
                         <p class="text-red-crew" v-if="contactDetail.primary_contact_person_lastname == null">{{storeDataError.error_last_name}}</p>
                        <input type="text"
                         class="canpay-crew-general-input-box
                         canpay-crew-p-margin-bottom-2"
                         maxlength="50"
                         :style="contactDetail.primary_contact_person_email == null || !isEmailVerifiedForEdit?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}"
                         placeholder="Store Manager or Employee: Email" v-model="contactDetail.primary_contact_person_email">
                         <p class="text-red-crew" v-if="contactDetail.primary_contact_person_email == null && !isEmailVerifiedForEdit">{{storeDataError.error_email}}</p>
                         <p class="text-red-crew" v-else-if="!isEmailVerifiedForEdit">{{storeDataError.error_invalid_email}}</p>
                        <input type="text"
                         class="canpay-crew-general-input-box
                         canpay-crew-p-margin-bottom-2"
                         maxlength="50"
                         :style="contactDetail.primary_contact_person_title == null?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}"
                         placeholder="Store Manager or Employee: Job Title" v-model="contactDetail.primary_contact_person_title">
                         <p class="text-red-crew" v-if="contactDetail.primary_contact_person_title == null">{{storeDataError.error_title}}</p>
                    </div>                    
                    <div class="col-12" v-else>
                        <input type="text"
                         class="canpay-crew-general-input-box canpay-crew-p-margin-bottom-2"
                         maxlength="50"
                         :style="contactDetail.secondary_contact_person_firstname == null?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}"
                         placeholder="Store Manager or Employee: First Name" v-model="contactDetail.secondary_contact_person_firstname">
                         <p class="text-red-crew" v-if="contactDetail.secondary_contact_person_firstname == null">{{additinalContactError.error_first_name}}</p>
                        <input type="text"
                         class="canpay-crew-general-input-box canpay-crew-p-margin-bottom-2"
                         maxlength="50"
                         :style="contactDetail.secondary_contact_person_lastname == null?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}"
                         placeholder="Store Manager or Employee: Last Name" v-model="contactDetail.secondary_contact_person_lastname">
                         <p class="text-red-crew" v-if="contactDetail.secondary_contact_person_lastname == null">{{additinalContactError.error_last_name}}</p>
                        <input type="text"
                         class="canpay-crew-general-input-box canpay-crew-p-margin-bottom-2"
                         maxlength="50"
                         :style="contactDetail.secondary_contact_person_email == null || !isEmailVerifiedForEdit?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}"
                         placeholder="Store Manager or Employee: Email" v-model="contactDetail.secondary_contact_person_email" @input="onEmailInput">
                         <p class="text-red-crew" v-if="contactDetail.secondary_contact_person_email == null && !isEmailVerifiedForEdit">{{additinalContactError.error_email}}</p>
                         <p class="text-red-crew" v-else-if="!isEmailVerifiedForEdit">{{additinalContactError.error_invalid_email}}</p>
                        <input type="text"
                         class="canpay-crew-general-input-box
                         canpay-crew-p-margin-bottom-2"
                         maxlength="50"
                         :style="contactDetail.secondary_contact_person_title == null?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}"
                         placeholder="Store Manager or Employee: Job Title" v-model="contactDetail.secondary_contact_person_title">
                         <p class="text-red-crew" v-if="contactDetail.secondary_contact_person_title == null">{{storeDataError.error_title}}</p>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-12">
                        <button class="canpay-crew-sign-petition-modal-button" v-on:click="mayorDetailsFunction()">
                            Save
                        </button>
                    </div>
                </div>
            </div>
            <div v-if="stepOFEdit == 'CanpayCrewSuccessForMayorDetails'" class="text-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="55" height="55" viewBox="0 0 160 159" fill="none">
                <mask id="path-1-outside-1_16385_131" maskUnits="userSpaceOnUse" x="0" y="0" width="160" height="159" fill="black">
                <rect fill="white" width="160" height="159"/>
                <path d="M159 98.5756C159 91.7189 154.084 85.9171 147.589 84.6864C149.696 82.2251 150.924 79.0605 150.924 75.5442C150.924 67.8085 144.604 61.4793 136.88 61.4793H101.242L104.753 36.8656C106.509 24.5588 98.0822 13.8343 89.3044 9.9664C85.0911 8.03247 81.7556 8.03247 79.1222 9.61478C75.4356 11.9003 74.5578 17.3505 74.3822 24.5588C74.2067 34.9317 68.94 73.4345 48.5756 73.4345H43.1333V63.4132C43.1333 62.0067 41.9044 60.776 40.5 60.776H3.63333C2.22889 60.776 1 62.0067 1 63.4132V155.363C1 156.769 2.22889 158 3.63333 158H40.5C41.9044 158 43.1333 156.769 43.1333 155.363V146.396C45.5911 147.275 48.2244 148.506 50.8578 149.737C59.2844 153.605 68.94 158 81.7556 158H126.171C133.72 158 139.864 151.847 139.864 144.287C139.864 141.122 138.811 138.133 136.88 135.848C144.604 135.848 150.924 129.518 150.924 121.783C150.924 118.267 149.696 115.102 147.589 112.641C154.084 111.234 159 105.432 159 98.5756ZM37.8667 152.726H6.26667V66.0504H37.8667V152.726ZM144.956 107.366H121.08C119.676 107.366 118.447 108.597 118.447 110.003C118.447 111.41 119.676 112.641 121.08 112.641H136.704C141.62 112.641 145.482 116.684 145.482 121.431C145.482 126.354 141.444 130.222 136.704 130.222H118.447C117.042 130.222 115.813 131.452 115.813 132.859C115.813 134.265 117.042 135.496 118.447 135.496H126.171C130.911 135.496 134.598 139.364 134.598 143.935C134.598 148.682 130.736 152.374 126.171 152.374H81.58C69.8178 152.374 60.8644 148.155 52.9644 144.638C49.6289 143.056 46.2933 141.649 42.9578 140.419V78.7088H48.4C77.3667 78.7088 79.4733 25.262 79.4733 24.7346C79.4733 22.449 79.6489 15.4166 81.7556 14.0101C82.6333 13.4826 84.3889 13.6585 86.8467 14.8891C92.4644 17.3505 100.716 25.262 99.3111 36.1624L95.6244 63.7648C95.4489 64.4681 95.8 65.3471 96.3267 65.8746C96.8533 66.402 97.5556 66.7536 98.2578 66.7536H136.88C141.796 66.7536 145.658 70.7973 145.658 75.5442C145.658 80.2912 141.62 84.3348 136.88 84.3348H121.256C119.851 84.3348 118.622 85.5655 118.622 86.972C118.622 88.3785 119.851 89.6092 121.256 89.6092H144.956C149.871 89.6092 153.733 93.6528 153.733 98.3998C153.733 103.498 149.696 107.366 144.956 107.366Z"/>
                <path d="M21.1889 25.4379L33.3022 31.2396L39.0956 43.3707C39.4467 44.2497 40.5 44.953 41.5533 44.953C42.6067 44.953 43.4844 44.4255 44.0111 43.3707L49.8044 31.2396L61.9178 25.4379C62.7956 25.0862 63.4978 24.0314 63.4978 22.9765C63.4978 21.9216 62.9711 21.0426 61.9178 20.5151L49.8044 14.7133L44.0111 2.58231C43.66 1.70325 42.6067 1 41.5533 1C40.5 1 39.6222 1.52743 39.0956 2.58231L33.3022 14.7133L21.1889 20.5151C20.3111 20.8667 19.6089 21.9216 19.6089 22.9765C19.6089 24.0314 20.3111 25.0862 21.1889 25.4379ZM36.4622 19.2844C36.9889 19.1086 37.5156 18.5812 37.6911 18.0538L41.5533 9.9664L45.4156 18.0538C45.5911 18.5812 46.1178 19.1086 46.6444 19.2844L54.72 23.1523L46.6444 26.8443C46.1178 27.0202 45.5911 27.5476 45.4156 28.075L41.5533 36.1624L37.6911 28.075C37.5156 27.5476 36.9889 27.0202 36.4622 26.8443L28.3867 22.9765L36.4622 19.2844Z"/>
                <path d="M121.782 35.1075H127.576V40.9093C127.576 42.3158 128.804 43.5465 130.209 43.5465C131.613 43.5465 132.842 42.3158 132.842 40.9093V35.1075H138.636C140.04 35.1075 141.269 33.8768 141.269 32.4703C141.269 31.0638 140.04 29.8331 138.636 29.8331H132.842V24.0314C132.842 22.6249 131.613 21.3942 130.209 21.3942C128.804 21.3942 127.576 22.6249 127.576 24.0314V29.8331H121.782C120.378 29.8331 119.149 31.0638 119.149 32.4703C119.149 33.8768 120.378 35.1075 121.782 35.1075Z"/>
                </mask>
                <path d="M159 98.5756C159 91.7189 154.084 85.9171 147.589 84.6864C149.696 82.2251 150.924 79.0605 150.924 75.5442C150.924 67.8085 144.604 61.4793 136.88 61.4793H101.242L104.753 36.8656C106.509 24.5588 98.0822 13.8343 89.3044 9.9664C85.0911 8.03247 81.7556 8.03247 79.1222 9.61478C75.4356 11.9003 74.5578 17.3505 74.3822 24.5588C74.2067 34.9317 68.94 73.4345 48.5756 73.4345H43.1333V63.4132C43.1333 62.0067 41.9044 60.776 40.5 60.776H3.63333C2.22889 60.776 1 62.0067 1 63.4132V155.363C1 156.769 2.22889 158 3.63333 158H40.5C41.9044 158 43.1333 156.769 43.1333 155.363V146.396C45.5911 147.275 48.2244 148.506 50.8578 149.737C59.2844 153.605 68.94 158 81.7556 158H126.171C133.72 158 139.864 151.847 139.864 144.287C139.864 141.122 138.811 138.133 136.88 135.848C144.604 135.848 150.924 129.518 150.924 121.783C150.924 118.267 149.696 115.102 147.589 112.641C154.084 111.234 159 105.432 159 98.5756ZM37.8667 152.726H6.26667V66.0504H37.8667V152.726ZM144.956 107.366H121.08C119.676 107.366 118.447 108.597 118.447 110.003C118.447 111.41 119.676 112.641 121.08 112.641H136.704C141.62 112.641 145.482 116.684 145.482 121.431C145.482 126.354 141.444 130.222 136.704 130.222H118.447C117.042 130.222 115.813 131.452 115.813 132.859C115.813 134.265 117.042 135.496 118.447 135.496H126.171C130.911 135.496 134.598 139.364 134.598 143.935C134.598 148.682 130.736 152.374 126.171 152.374H81.58C69.8178 152.374 60.8644 148.155 52.9644 144.638C49.6289 143.056 46.2933 141.649 42.9578 140.419V78.7088H48.4C77.3667 78.7088 79.4733 25.262 79.4733 24.7346C79.4733 22.449 79.6489 15.4166 81.7556 14.0101C82.6333 13.4826 84.3889 13.6585 86.8467 14.8891C92.4644 17.3505 100.716 25.262 99.3111 36.1624L95.6244 63.7648C95.4489 64.4681 95.8 65.3471 96.3267 65.8746C96.8533 66.402 97.5556 66.7536 98.2578 66.7536H136.88C141.796 66.7536 145.658 70.7973 145.658 75.5442C145.658 80.2912 141.62 84.3348 136.88 84.3348H121.256C119.851 84.3348 118.622 85.5655 118.622 86.972C118.622 88.3785 119.851 89.6092 121.256 89.6092H144.956C149.871 89.6092 153.733 93.6528 153.733 98.3998C153.733 103.498 149.696 107.366 144.956 107.366Z" fill="#179346"/>
                <path d="M21.1889 25.4379L33.3022 31.2396L39.0956 43.3707C39.4467 44.2497 40.5 44.953 41.5533 44.953C42.6067 44.953 43.4844 44.4255 44.0111 43.3707L49.8044 31.2396L61.9178 25.4379C62.7956 25.0862 63.4978 24.0314 63.4978 22.9765C63.4978 21.9216 62.9711 21.0426 61.9178 20.5151L49.8044 14.7133L44.0111 2.58231C43.66 1.70325 42.6067 1 41.5533 1C40.5 1 39.6222 1.52743 39.0956 2.58231L33.3022 14.7133L21.1889 20.5151C20.3111 20.8667 19.6089 21.9216 19.6089 22.9765C19.6089 24.0314 20.3111 25.0862 21.1889 25.4379ZM36.4622 19.2844C36.9889 19.1086 37.5156 18.5812 37.6911 18.0538L41.5533 9.9664L45.4156 18.0538C45.5911 18.5812 46.1178 19.1086 46.6444 19.2844L54.72 23.1523L46.6444 26.8443C46.1178 27.0202 45.5911 27.5476 45.4156 28.075L41.5533 36.1624L37.6911 28.075C37.5156 27.5476 36.9889 27.0202 36.4622 26.8443L28.3867 22.9765L36.4622 19.2844Z" fill="#179346"/>
                <path d="M121.782 35.1075H127.576V40.9093C127.576 42.3158 128.804 43.5465 130.209 43.5465C131.613 43.5465 132.842 42.3158 132.842 40.9093V35.1075H138.636C140.04 35.1075 141.269 33.8768 141.269 32.4703C141.269 31.0638 140.04 29.8331 138.636 29.8331H132.842V24.0314C132.842 22.6249 131.613 21.3942 130.209 21.3942C128.804 21.3942 127.576 22.6249 127.576 24.0314V29.8331H121.782C120.378 29.8331 119.149 31.0638 119.149 32.4703C119.149 33.8768 120.378 35.1075 121.782 35.1075Z" fill="#179346"/>
                <path d="M159 98.5756C159 91.7189 154.084 85.9171 147.589 84.6864C149.696 82.2251 150.924 79.0605 150.924 75.5442C150.924 67.8085 144.604 61.4793 136.88 61.4793H101.242L104.753 36.8656C106.509 24.5588 98.0822 13.8343 89.3044 9.9664C85.0911 8.03247 81.7556 8.03247 79.1222 9.61478C75.4356 11.9003 74.5578 17.3505 74.3822 24.5588C74.2067 34.9317 68.94 73.4345 48.5756 73.4345H43.1333V63.4132C43.1333 62.0067 41.9044 60.776 40.5 60.776H3.63333C2.22889 60.776 1 62.0067 1 63.4132V155.363C1 156.769 2.22889 158 3.63333 158H40.5C41.9044 158 43.1333 156.769 43.1333 155.363V146.396C45.5911 147.275 48.2244 148.506 50.8578 149.737C59.2844 153.605 68.94 158 81.7556 158H126.171C133.72 158 139.864 151.847 139.864 144.287C139.864 141.122 138.811 138.133 136.88 135.848C144.604 135.848 150.924 129.518 150.924 121.783C150.924 118.267 149.696 115.102 147.589 112.641C154.084 111.234 159 105.432 159 98.5756ZM37.8667 152.726H6.26667V66.0504H37.8667V152.726ZM144.956 107.366H121.08C119.676 107.366 118.447 108.597 118.447 110.003C118.447 111.41 119.676 112.641 121.08 112.641H136.704C141.62 112.641 145.482 116.684 145.482 121.431C145.482 126.354 141.444 130.222 136.704 130.222H118.447C117.042 130.222 115.813 131.452 115.813 132.859C115.813 134.265 117.042 135.496 118.447 135.496H126.171C130.911 135.496 134.598 139.364 134.598 143.935C134.598 148.682 130.736 152.374 126.171 152.374H81.58C69.8178 152.374 60.8644 148.155 52.9644 144.638C49.6289 143.056 46.2933 141.649 42.9578 140.419V78.7088H48.4C77.3667 78.7088 79.4733 25.262 79.4733 24.7346C79.4733 22.449 79.6489 15.4166 81.7556 14.0101C82.6333 13.4826 84.3889 13.6585 86.8467 14.8891C92.4644 17.3505 100.716 25.262 99.3111 36.1624L95.6244 63.7648C95.4489 64.4681 95.8 65.3471 96.3267 65.8746C96.8533 66.402 97.5556 66.7536 98.2578 66.7536H136.88C141.796 66.7536 145.658 70.7973 145.658 75.5442C145.658 80.2912 141.62 84.3348 136.88 84.3348H121.256C119.851 84.3348 118.622 85.5655 118.622 86.972C118.622 88.3785 119.851 89.6092 121.256 89.6092H144.956C149.871 89.6092 153.733 93.6528 153.733 98.3998C153.733 103.498 149.696 107.366 144.956 107.366Z" stroke="#1B9146" stroke-width="2" mask="url(#path-1-outside-1_16385_131)"/>
                <path d="M21.1889 25.4379L33.3022 31.2396L39.0956 43.3707C39.4467 44.2497 40.5 44.953 41.5533 44.953C42.6067 44.953 43.4844 44.4255 44.0111 43.3707L49.8044 31.2396L61.9178 25.4379C62.7956 25.0862 63.4978 24.0314 63.4978 22.9765C63.4978 21.9216 62.9711 21.0426 61.9178 20.5151L49.8044 14.7133L44.0111 2.58231C43.66 1.70325 42.6067 1 41.5533 1C40.5 1 39.6222 1.52743 39.0956 2.58231L33.3022 14.7133L21.1889 20.5151C20.3111 20.8667 19.6089 21.9216 19.6089 22.9765C19.6089 24.0314 20.3111 25.0862 21.1889 25.4379ZM36.4622 19.2844C36.9889 19.1086 37.5156 18.5812 37.6911 18.0538L41.5533 9.9664L45.4156 18.0538C45.5911 18.5812 46.1178 19.1086 46.6444 19.2844L54.72 23.1523L46.6444 26.8443C46.1178 27.0202 45.5911 27.5476 45.4156 28.075L41.5533 36.1624L37.6911 28.075C37.5156 27.5476 36.9889 27.0202 36.4622 26.8443L28.3867 22.9765L36.4622 19.2844Z" stroke="#1B9146" stroke-width="2" mask="url(#path-1-outside-1_16385_131)"/>
                <path d="M121.782 35.1075H127.576V40.9093C127.576 42.3158 128.804 43.5465 130.209 43.5465C131.613 43.5465 132.842 42.3158 132.842 40.9093V35.1075H138.636C140.04 35.1075 141.269 33.8768 141.269 32.4703C141.269 31.0638 140.04 29.8331 138.636 29.8331H132.842V24.0314C132.842 22.6249 131.613 21.3942 130.209 21.3942C128.804 21.3942 127.576 22.6249 127.576 24.0314V29.8331H121.782C120.378 29.8331 119.149 31.0638 119.149 32.4703C119.149 33.8768 120.378 35.1075 121.782 35.1075Z" stroke="#1B9146" stroke-width="2" mask="url(#path-1-outside-1_16385_131)"/>
                </svg>
                <p class="canpay-crew-text-font-18 canpay-crew-text-700 canpay-crew-p-margin-bottom-1 mt-3">Contact edited sucessfully</p>
            </div>
            <div v-if="stepOFEdit == 'CanpayCrewSuccessShare'" class="text-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="55" height="55" viewBox="0 0 160 159" fill="none">
                <mask id="path-1-outside-1_16385_131" maskUnits="userSpaceOnUse" x="0" y="0" width="160" height="159" fill="black">
                <rect fill="white" width="160" height="159"/>
                <path d="M159 98.5756C159 91.7189 154.084 85.9171 147.589 84.6864C149.696 82.2251 150.924 79.0605 150.924 75.5442C150.924 67.8085 144.604 61.4793 136.88 61.4793H101.242L104.753 36.8656C106.509 24.5588 98.0822 13.8343 89.3044 9.9664C85.0911 8.03247 81.7556 8.03247 79.1222 9.61478C75.4356 11.9003 74.5578 17.3505 74.3822 24.5588C74.2067 34.9317 68.94 73.4345 48.5756 73.4345H43.1333V63.4132C43.1333 62.0067 41.9044 60.776 40.5 60.776H3.63333C2.22889 60.776 1 62.0067 1 63.4132V155.363C1 156.769 2.22889 158 3.63333 158H40.5C41.9044 158 43.1333 156.769 43.1333 155.363V146.396C45.5911 147.275 48.2244 148.506 50.8578 149.737C59.2844 153.605 68.94 158 81.7556 158H126.171C133.72 158 139.864 151.847 139.864 144.287C139.864 141.122 138.811 138.133 136.88 135.848C144.604 135.848 150.924 129.518 150.924 121.783C150.924 118.267 149.696 115.102 147.589 112.641C154.084 111.234 159 105.432 159 98.5756ZM37.8667 152.726H6.26667V66.0504H37.8667V152.726ZM144.956 107.366H121.08C119.676 107.366 118.447 108.597 118.447 110.003C118.447 111.41 119.676 112.641 121.08 112.641H136.704C141.62 112.641 145.482 116.684 145.482 121.431C145.482 126.354 141.444 130.222 136.704 130.222H118.447C117.042 130.222 115.813 131.452 115.813 132.859C115.813 134.265 117.042 135.496 118.447 135.496H126.171C130.911 135.496 134.598 139.364 134.598 143.935C134.598 148.682 130.736 152.374 126.171 152.374H81.58C69.8178 152.374 60.8644 148.155 52.9644 144.638C49.6289 143.056 46.2933 141.649 42.9578 140.419V78.7088H48.4C77.3667 78.7088 79.4733 25.262 79.4733 24.7346C79.4733 22.449 79.6489 15.4166 81.7556 14.0101C82.6333 13.4826 84.3889 13.6585 86.8467 14.8891C92.4644 17.3505 100.716 25.262 99.3111 36.1624L95.6244 63.7648C95.4489 64.4681 95.8 65.3471 96.3267 65.8746C96.8533 66.402 97.5556 66.7536 98.2578 66.7536H136.88C141.796 66.7536 145.658 70.7973 145.658 75.5442C145.658 80.2912 141.62 84.3348 136.88 84.3348H121.256C119.851 84.3348 118.622 85.5655 118.622 86.972C118.622 88.3785 119.851 89.6092 121.256 89.6092H144.956C149.871 89.6092 153.733 93.6528 153.733 98.3998C153.733 103.498 149.696 107.366 144.956 107.366Z"/>
                <path d="M21.1889 25.4379L33.3022 31.2396L39.0956 43.3707C39.4467 44.2497 40.5 44.953 41.5533 44.953C42.6067 44.953 43.4844 44.4255 44.0111 43.3707L49.8044 31.2396L61.9178 25.4379C62.7956 25.0862 63.4978 24.0314 63.4978 22.9765C63.4978 21.9216 62.9711 21.0426 61.9178 20.5151L49.8044 14.7133L44.0111 2.58231C43.66 1.70325 42.6067 1 41.5533 1C40.5 1 39.6222 1.52743 39.0956 2.58231L33.3022 14.7133L21.1889 20.5151C20.3111 20.8667 19.6089 21.9216 19.6089 22.9765C19.6089 24.0314 20.3111 25.0862 21.1889 25.4379ZM36.4622 19.2844C36.9889 19.1086 37.5156 18.5812 37.6911 18.0538L41.5533 9.9664L45.4156 18.0538C45.5911 18.5812 46.1178 19.1086 46.6444 19.2844L54.72 23.1523L46.6444 26.8443C46.1178 27.0202 45.5911 27.5476 45.4156 28.075L41.5533 36.1624L37.6911 28.075C37.5156 27.5476 36.9889 27.0202 36.4622 26.8443L28.3867 22.9765L36.4622 19.2844Z"/>
                <path d="M121.782 35.1075H127.576V40.9093C127.576 42.3158 128.804 43.5465 130.209 43.5465C131.613 43.5465 132.842 42.3158 132.842 40.9093V35.1075H138.636C140.04 35.1075 141.269 33.8768 141.269 32.4703C141.269 31.0638 140.04 29.8331 138.636 29.8331H132.842V24.0314C132.842 22.6249 131.613 21.3942 130.209 21.3942C128.804 21.3942 127.576 22.6249 127.576 24.0314V29.8331H121.782C120.378 29.8331 119.149 31.0638 119.149 32.4703C119.149 33.8768 120.378 35.1075 121.782 35.1075Z"/>
                </mask>
                <path d="M159 98.5756C159 91.7189 154.084 85.9171 147.589 84.6864C149.696 82.2251 150.924 79.0605 150.924 75.5442C150.924 67.8085 144.604 61.4793 136.88 61.4793H101.242L104.753 36.8656C106.509 24.5588 98.0822 13.8343 89.3044 9.9664C85.0911 8.03247 81.7556 8.03247 79.1222 9.61478C75.4356 11.9003 74.5578 17.3505 74.3822 24.5588C74.2067 34.9317 68.94 73.4345 48.5756 73.4345H43.1333V63.4132C43.1333 62.0067 41.9044 60.776 40.5 60.776H3.63333C2.22889 60.776 1 62.0067 1 63.4132V155.363C1 156.769 2.22889 158 3.63333 158H40.5C41.9044 158 43.1333 156.769 43.1333 155.363V146.396C45.5911 147.275 48.2244 148.506 50.8578 149.737C59.2844 153.605 68.94 158 81.7556 158H126.171C133.72 158 139.864 151.847 139.864 144.287C139.864 141.122 138.811 138.133 136.88 135.848C144.604 135.848 150.924 129.518 150.924 121.783C150.924 118.267 149.696 115.102 147.589 112.641C154.084 111.234 159 105.432 159 98.5756ZM37.8667 152.726H6.26667V66.0504H37.8667V152.726ZM144.956 107.366H121.08C119.676 107.366 118.447 108.597 118.447 110.003C118.447 111.41 119.676 112.641 121.08 112.641H136.704C141.62 112.641 145.482 116.684 145.482 121.431C145.482 126.354 141.444 130.222 136.704 130.222H118.447C117.042 130.222 115.813 131.452 115.813 132.859C115.813 134.265 117.042 135.496 118.447 135.496H126.171C130.911 135.496 134.598 139.364 134.598 143.935C134.598 148.682 130.736 152.374 126.171 152.374H81.58C69.8178 152.374 60.8644 148.155 52.9644 144.638C49.6289 143.056 46.2933 141.649 42.9578 140.419V78.7088H48.4C77.3667 78.7088 79.4733 25.262 79.4733 24.7346C79.4733 22.449 79.6489 15.4166 81.7556 14.0101C82.6333 13.4826 84.3889 13.6585 86.8467 14.8891C92.4644 17.3505 100.716 25.262 99.3111 36.1624L95.6244 63.7648C95.4489 64.4681 95.8 65.3471 96.3267 65.8746C96.8533 66.402 97.5556 66.7536 98.2578 66.7536H136.88C141.796 66.7536 145.658 70.7973 145.658 75.5442C145.658 80.2912 141.62 84.3348 136.88 84.3348H121.256C119.851 84.3348 118.622 85.5655 118.622 86.972C118.622 88.3785 119.851 89.6092 121.256 89.6092H144.956C149.871 89.6092 153.733 93.6528 153.733 98.3998C153.733 103.498 149.696 107.366 144.956 107.366Z" fill="#179346"/>
                <path d="M21.1889 25.4379L33.3022 31.2396L39.0956 43.3707C39.4467 44.2497 40.5 44.953 41.5533 44.953C42.6067 44.953 43.4844 44.4255 44.0111 43.3707L49.8044 31.2396L61.9178 25.4379C62.7956 25.0862 63.4978 24.0314 63.4978 22.9765C63.4978 21.9216 62.9711 21.0426 61.9178 20.5151L49.8044 14.7133L44.0111 2.58231C43.66 1.70325 42.6067 1 41.5533 1C40.5 1 39.6222 1.52743 39.0956 2.58231L33.3022 14.7133L21.1889 20.5151C20.3111 20.8667 19.6089 21.9216 19.6089 22.9765C19.6089 24.0314 20.3111 25.0862 21.1889 25.4379ZM36.4622 19.2844C36.9889 19.1086 37.5156 18.5812 37.6911 18.0538L41.5533 9.9664L45.4156 18.0538C45.5911 18.5812 46.1178 19.1086 46.6444 19.2844L54.72 23.1523L46.6444 26.8443C46.1178 27.0202 45.5911 27.5476 45.4156 28.075L41.5533 36.1624L37.6911 28.075C37.5156 27.5476 36.9889 27.0202 36.4622 26.8443L28.3867 22.9765L36.4622 19.2844Z" fill="#179346"/>
                <path d="M121.782 35.1075H127.576V40.9093C127.576 42.3158 128.804 43.5465 130.209 43.5465C131.613 43.5465 132.842 42.3158 132.842 40.9093V35.1075H138.636C140.04 35.1075 141.269 33.8768 141.269 32.4703C141.269 31.0638 140.04 29.8331 138.636 29.8331H132.842V24.0314C132.842 22.6249 131.613 21.3942 130.209 21.3942C128.804 21.3942 127.576 22.6249 127.576 24.0314V29.8331H121.782C120.378 29.8331 119.149 31.0638 119.149 32.4703C119.149 33.8768 120.378 35.1075 121.782 35.1075Z" fill="#179346"/>
                <path d="M159 98.5756C159 91.7189 154.084 85.9171 147.589 84.6864C149.696 82.2251 150.924 79.0605 150.924 75.5442C150.924 67.8085 144.604 61.4793 136.88 61.4793H101.242L104.753 36.8656C106.509 24.5588 98.0822 13.8343 89.3044 9.9664C85.0911 8.03247 81.7556 8.03247 79.1222 9.61478C75.4356 11.9003 74.5578 17.3505 74.3822 24.5588C74.2067 34.9317 68.94 73.4345 48.5756 73.4345H43.1333V63.4132C43.1333 62.0067 41.9044 60.776 40.5 60.776H3.63333C2.22889 60.776 1 62.0067 1 63.4132V155.363C1 156.769 2.22889 158 3.63333 158H40.5C41.9044 158 43.1333 156.769 43.1333 155.363V146.396C45.5911 147.275 48.2244 148.506 50.8578 149.737C59.2844 153.605 68.94 158 81.7556 158H126.171C133.72 158 139.864 151.847 139.864 144.287C139.864 141.122 138.811 138.133 136.88 135.848C144.604 135.848 150.924 129.518 150.924 121.783C150.924 118.267 149.696 115.102 147.589 112.641C154.084 111.234 159 105.432 159 98.5756ZM37.8667 152.726H6.26667V66.0504H37.8667V152.726ZM144.956 107.366H121.08C119.676 107.366 118.447 108.597 118.447 110.003C118.447 111.41 119.676 112.641 121.08 112.641H136.704C141.62 112.641 145.482 116.684 145.482 121.431C145.482 126.354 141.444 130.222 136.704 130.222H118.447C117.042 130.222 115.813 131.452 115.813 132.859C115.813 134.265 117.042 135.496 118.447 135.496H126.171C130.911 135.496 134.598 139.364 134.598 143.935C134.598 148.682 130.736 152.374 126.171 152.374H81.58C69.8178 152.374 60.8644 148.155 52.9644 144.638C49.6289 143.056 46.2933 141.649 42.9578 140.419V78.7088H48.4C77.3667 78.7088 79.4733 25.262 79.4733 24.7346C79.4733 22.449 79.6489 15.4166 81.7556 14.0101C82.6333 13.4826 84.3889 13.6585 86.8467 14.8891C92.4644 17.3505 100.716 25.262 99.3111 36.1624L95.6244 63.7648C95.4489 64.4681 95.8 65.3471 96.3267 65.8746C96.8533 66.402 97.5556 66.7536 98.2578 66.7536H136.88C141.796 66.7536 145.658 70.7973 145.658 75.5442C145.658 80.2912 141.62 84.3348 136.88 84.3348H121.256C119.851 84.3348 118.622 85.5655 118.622 86.972C118.622 88.3785 119.851 89.6092 121.256 89.6092H144.956C149.871 89.6092 153.733 93.6528 153.733 98.3998C153.733 103.498 149.696 107.366 144.956 107.366Z" stroke="#1B9146" stroke-width="2" mask="url(#path-1-outside-1_16385_131)"/>
                <path d="M21.1889 25.4379L33.3022 31.2396L39.0956 43.3707C39.4467 44.2497 40.5 44.953 41.5533 44.953C42.6067 44.953 43.4844 44.4255 44.0111 43.3707L49.8044 31.2396L61.9178 25.4379C62.7956 25.0862 63.4978 24.0314 63.4978 22.9765C63.4978 21.9216 62.9711 21.0426 61.9178 20.5151L49.8044 14.7133L44.0111 2.58231C43.66 1.70325 42.6067 1 41.5533 1C40.5 1 39.6222 1.52743 39.0956 2.58231L33.3022 14.7133L21.1889 20.5151C20.3111 20.8667 19.6089 21.9216 19.6089 22.9765C19.6089 24.0314 20.3111 25.0862 21.1889 25.4379ZM36.4622 19.2844C36.9889 19.1086 37.5156 18.5812 37.6911 18.0538L41.5533 9.9664L45.4156 18.0538C45.5911 18.5812 46.1178 19.1086 46.6444 19.2844L54.72 23.1523L46.6444 26.8443C46.1178 27.0202 45.5911 27.5476 45.4156 28.075L41.5533 36.1624L37.6911 28.075C37.5156 27.5476 36.9889 27.0202 36.4622 26.8443L28.3867 22.9765L36.4622 19.2844Z" stroke="#1B9146" stroke-width="2" mask="url(#path-1-outside-1_16385_131)"/>
                <path d="M121.782 35.1075H127.576V40.9093C127.576 42.3158 128.804 43.5465 130.209 43.5465C131.613 43.5465 132.842 42.3158 132.842 40.9093V35.1075H138.636C140.04 35.1075 141.269 33.8768 141.269 32.4703C141.269 31.0638 140.04 29.8331 138.636 29.8331H132.842V24.0314C132.842 22.6249 131.613 21.3942 130.209 21.3942C128.804 21.3942 127.576 22.6249 127.576 24.0314V29.8331H121.782C120.378 29.8331 119.149 31.0638 119.149 32.4703C119.149 33.8768 120.378 35.1075 121.782 35.1075Z" stroke="#1B9146" stroke-width="2" mask="url(#path-1-outside-1_16385_131)"/>
                </svg>
                <p class="canpay-crew-text-font-18 canpay-crew-text-700 canpay-crew-p-margin-bottom-1 mt-3">Thank you! We sent  {{isEmail ? 'an email' : 'a text'}}</p>
                <p class="canpay-crew-text-font-18 canpay-crew-text-700 canpay-crew-p-margin-bottom-1 mb-3"> {{isEmail ? '' : 'message'}} to your friend.</p>
                 <p class="canpay-crew-text-font-14 canpay-crew-p-margin-bottom-1">Please share with additional friends</p>
                 <p class="canpay-crew-text-font-14 canpay-crew-p-margin-bottom-1">to help convince this store to accept</p>
                 <p class="canpay-crew-text-font-14 canpay-crew-p-margin-bottom-1">CanPay and you both get more </p>
                 <p class="canpay-crew-text-font-14 canpay-crew-p-margin-bottom-1">points.</p>
                 <button class="canpay-crew-sign-petition-modal-button mt-4" v-on:click="openShareModal()">Invite Another Friend</button>
                    <button class="canpay-crew-sign-petition-not-ok-modal-button mt-2 mb-3" v-on:click="closeEditContact()">
                        Not Right Now
                    </button>
            </div>
            <div v-if="stepOFEdit == 'canpayCrewEnterShareDetails'" class="text-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="50" height="90" viewBox="0 0 308 426" fill="none">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M154 101.1C123.444 101.1 98.6747 125.344 98.6747 155.25C98.6747 185.156 123.444 209.4 154 209.4C184.554 209.4 209.325 185.156 209.325 155.25C209.325 125.344 184.554 101.1 154 101.1ZM61.7913 155.25C61.7913 105.406 103.075 65 154 65C204.925 65 246.208 105.406 246.208 155.25C246.208 205.094 204.925 245.5 154 245.5C103.075 245.5 61.7913 205.094 61.7913 155.25Z" fill="black"/>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M154 299.65C90.5152 299.65 57.1426 321.667 40.4938 348.342C34.6998 357.627 36.157 366.383 43.2307 374.428C50.9471 383.203 64.9004 389.9 80.233 389.9H227.767C243.099 389.9 257.052 383.203 264.768 374.428C271.842 366.383 273.299 357.627 267.507 348.342C250.857 321.667 217.484 299.65 154 299.65ZM9.01844 329.523C33.902 289.654 80.9124 263.55 154 263.55C227.088 263.55 274.098 289.654 298.981 329.523C314.426 354.267 308.514 380.019 292.726 397.976C277.578 415.202 253.174 426 227.767 426H80.233C54.8255 426 30.4213 415.202 15.275 397.976C-0.514246 380.019 -6.42536 354.267 9.01844 329.523Z" fill="black"/>
                <path d="M155.231 6C160.388 6.00003 164.568 10.089 164.568 15.1328C164.568 18.4292 162.782 21.317 160.105 22.9229C160.26 23.1595 160.4 23.413 160.523 23.6836L184.26 75.7031L232.125 33.4229C231.613 32.2926 231.326 31.0425 231.326 29.7266C231.326 24.6827 235.506 20.5938 240.663 20.5938C245.82 20.5938 250 24.6827 250 29.7266C250 34.5157 246.231 38.44 241.436 38.8242V125.469C241.436 128.523 238.96 131 235.905 131H71.4961C68.4416 131 65.9658 128.523 65.9658 125.469V38.1377C60.9817 37.9464 57.001 33.9364 57.001 29.0146C57.001 23.971 61.1812 19.8821 66.3379 19.8818C71.4947 19.8818 75.6757 23.9709 75.6758 29.0146C75.6758 30.2798 75.412 31.4845 74.9365 32.5801C74.9604 32.5991 74.9851 32.6172 75.0088 32.6367L126.9 75.3135L150.46 23.6836C150.546 23.4943 150.641 23.3133 150.742 23.1406C147.853 21.5878 145.895 18.5847 145.895 15.1328C145.895 10.089 150.075 6 155.231 6Z" fill="black"/>
                </svg>
                <p class="canpay-crew-text-font-18 canpay-crew-text-700 canpay-crew-p-margin-bottom-1 mt-3">You are now the Mayor of</p>
                 <p class="canpay-crew-text-font-18 canpay-crew-text-700 ">{{contactDetail.store_name}} - {{contactDetail.city}}!</p>

                 <hr style="border:0.5px solid #DFDFDF; margin:16px 0px;"/>
                 <p class="canpay-crew-text-font-14 canpay-crew-text-700 canpay-crew-p-margin-bottom-1">Get 5,000 CanPay Points</p>
                 <p class="canpay-crew-text-font-14 canpay-crew-text-700">For you and your friend</p>

                 <p class="canpay-crew-text-font-14 canpay-crew-p-margin-bottom-1">Share with a friend and if they sign the</p>
                 <p class="canpay-crew-text-font-14 canpay-crew-p-margin-bottom-1">Petition too, you both receive an extra</p>
                 <p class="canpay-crew-text-font-14 canpay-crew-p-margin-bottom-1"><b>5,000</b> CanPay Points when this store</p>
                 <p class="canpay-crew-text-font-14">starts accepting CanPay.</p>

                 <input type="text"
                 maxlength="50"
                 :style="friendEmail == null || !isValidMail?{'outline':'1px solid red'}:{'outline':'1px solid #000000'}"
                  class="canpay-crew-general-input-box-1 canpay-crew-p-margin-bottom-2 mt-5"
                  placeholder="Friend's Email/Phone" v-model="friendEmail" @input="onShareEmailInput">
                  <p v-if="friendEmail == null && !isValidMail" class="text-red-crew">{{storeDataError.error_email_phone}}</p>
                  <p v-else-if="!isValidMail" class="text-red-crew">{{storeDataError.error_invalid_email_phone}}</p>
                 <button class="canpay-crew-sign-petition-modal-button mt-3" v-on:click="sendMayorDetail()">
                    Send
                 </button>
                    <button class="canpay-crew-sign-petition-not-ok-modal-button mt-3 mb-5" v-on:click="closeEditContact()">
                        Not Right Now
                    </button>
            </div>
        </div>
        </b-modal>
    </div>
</template>
<script>
export default {
    name:"CanpayCrewEditConatctPersonDetail",
    props:{
        editContact:{
            type:Function
        },
        shareThePetition:{
            type:Function
        }
    },
    data(){
            return{
                stepOFEdit:"canpayCrewEditContactPersonDetail",
                isEmailVerifiedForEdit:true,
                isEmail:true,
                contactDetail:{
                    'primary_contact_person_title':"",
                    'store_name':"",
                    'street_address':"",
                    'city':"",
                    'type':"mayor",
                    'state':"",
                    'zipcode':"",
                    'primary_contact_person_email':"",
                    'primary_contact_person_lastname':"",
                    'primary_contact_person_firstname':"",
                    'secondary_contact_person_firstname':"",
                    'secondary_contact_person_lastname':"",
                    'secondary_contact_person_email':"",
                    'secondary_contact_person_title':""
                },
                contactState:"",
                storeDataError:{
                    "error_store_name":"Store name is required",
                    "error_street_address":"Store address is required",
                    "error_city":"City is required",
                    "error_state":"State is required",
                    "error_zipcode":"Zipcode is required",
                    "error_first_name":"First name is required",
                    "error_last_name":"Last name is required",
                    "error_email":"Email is required",
                    "error_title":"Title is required",
                    "error_invalid_email":"Provide a valid email",
                    "error_email_phone":"Email/Phone is required",
                    "error_invalid_email_phone":"Provide a valid email/phone"
                },
                additinalContactError:{
                    "error_first_name":"First name is required",
                    "error_last_name":"Last name is required",
                    "error_email":"Email is required",
                    "error_invalid_email":"Provide a valid email"
                },
                friendEmail:"",
                isValidMail:true
            }
    },
    methods:{
        onShareEmailInput(event){
            const liveInput = event.target.value;
            // Check if input is email or phone number
            if(!this.isValidMail) {
                // Try email validation first
                this.isValidMail = this.checkEmailValidation(liveInput) || this.checkPhoneValidation(liveInput);
            }
        },
        onEmailInput(event){
            const liveInput = event.target.value;
            if(!this.isEmailVerifiedForEdit) this.isEmailVerifiedForEdit = this.checkEmailValidation(liveInput);
        },
        checkEmailValidation(email){
            const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return regex.test(email);
        },
        checkPhoneValidation(phone){
            // Basic phone validation - accepts formats like: 1234567890, ************, ************
            const regex = /^\d{10}$|^(\d{3}[-.]){2}\d{4}$/;
            return regex.test(phone);
        },
        closeEditContact(){
             this.$refs['canpay-crew-edit-conatct-person-detail'].hide();
        },
        openSuccessEditModal(){
            this.stepOFEdit = 'CanpayCrewSuccessForMayorDetails';
            this.isValidMail = true;
            this.$refs['canpay-crew-edit-conatct-person-detail'].show();
        },
        openEditContact(item,contactDetailState){
            let self = this;
            self.contactDetail = JSON.parse(JSON.stringify(item));
            self.isEmailVerifiedForEdit = true
            self.isValidMail = true;
            self.contactState = contactDetailState
            this.stepOFEdit="canpayCrewEditContactPersonDetail"
            this.$refs['canpay-crew-edit-conatct-person-detail'].show();
        },
        rectifyContact(){
            let self = this;
            self.contactDetail.primary_contact_person_firstname = self.contactDetail.primary_contact_person_firstname  == "" ? null : self.contactDetail.primary_contact_person_firstname;
            self.contactDetail.primary_contact_person_lastname = self.contactDetail.primary_contact_person_lastname  == "" ? null : self.contactDetail.primary_contact_person_lastname;
            self.contactDetail.primary_contact_person_email = self.contactDetail.primary_contact_person_email  == "" ? null : self.contactDetail.primary_contact_person_email;
            self.contactDetail.primary_contact_person_title = self.contactDetail.primary_contact_person_title == "" ? null : self.contactDetail.primary_contact_person_title;
            self.contactDetail.secondary_contact_person_firstname = self.contactDetail.secondary_contact_person_firstname == "" ? null : self.contactDetail.secondary_contact_person_firstname;
            self.contactDetail.secondary_contact_person_lastname = self.contactDetail.secondary_contact_person_lastname == "" ? null : self.contactDetail.secondary_contact_person_lastname;
            self.contactDetail.secondary_contact_person_email = self.contactDetail.secondary_contact_person_email == "" ? null : self.contactDetail.secondary_contact_person_email;
            self.contactDetail.secondary_contact_person_title = self.contactDetail.secondary_contact_person_title == "" ? null : self.contactDetail.secondary_contact_person_title;
        },
        validateContact(){
            let self = this;
            console.log(self.contactState)
            if(self.contactState == 'primary'){
                if(self.contactDetail.primary_contact_person_firstname == null || self.contactDetail.primary_contact_person_lastname == null || self.contactDetail.primary_contact_person_email == null || self.contactDetail.primary_contact_person_title == null){
                    return false
                }
            }else{
                if(self.contactDetail.secondary_contact_person_email == null || self.contactDetail.secondary_contact_person_firstname == null || self.contactDetail.secondary_contact_person_lastname == null || self.contactDetail.secondary_contact_person_title == null){
                    return false;
                }
            }
            return true
        },

        preparePayload(){
            let payload = {};
            let self = this;
            if(self.contactState == 'primary'){
                payload =  {
                    petition_id : self.contactDetail.id,
                    primary : 1,
                    firstname : self.contactDetail.primary_contact_person_firstname,
                    lastname: self.contactDetail.primary_contact_person_lastname,
                    email: self.contactDetail.primary_contact_person_email,
                    title: self.contactDetail.primary_contact_person_title
                }
            }else{
                payload =  {
                    petition_id : self.contactDetail.id,
                    primary : 0,
                    firstname : self.contactDetail.secondary_contact_person_firstname,
                    lastname: self.contactDetail.secondary_contact_person_lastname,
                    email: self.contactDetail.secondary_contact_person_email,
                    title: self.contactDetail.secondary_contact_person_title
                }
            }
            return payload;
        },
        mayorDetailsFunction(){
            let self = this;
            self.rectifyContact();
            this.isEmailVerifiedForEdit = true;
            if(self.contactState == 'primary'){
                this.isEmailVerifiedForEdit = self.checkEmailValidation(self.contactDetail.primary_contact_person_email);
            }else{
                this.isEmailVerifiedForEdit = self.checkEmailValidation(self.contactDetail.secondary_contact_person_email);
            }
            if(this.isEmailVerifiedForEdit){
                if(self.validateContact()){
                    const payload = self.preparePayload();
                    self.editContact(payload)
                }
            }


        },
        sendMayorDetail(){
            this.isValidMail = this.checkEmailValidation(this.friendEmail) || this.checkPhoneValidation(this.friendEmail);
            if(this.isValidMail){
                this.isEmail = self.checkEmailValidation(self.petitionEmail);
                const payload = {
                    petition_id: this.contactDetail.id,
                    email: this.friendEmail,
                    type: this.isEmail ? 'email' : 'phone'
                }
                try{
                    this.shareThePetition(payload,this.contactDetail);
                    this.stepOFEdit = 'CanpayCrewSuccessShare' 
                }catch(err){
                    
                }

            }
        },
        openShareModal(){
            this.stepOFEdit = "canpayCrewEnterShareDetails";
        }

    }
}
</script>
