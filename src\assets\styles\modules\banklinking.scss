.bank-linking-text {
    font-size: 13px;
    color: $cp-white;
    font-family: $cp-font;
    display: block;
    font-weight: 100;
}

.camera-text {
    margin-top: 4px !important;
}

#uploading-modal___BV_modal_content_ {
    border-radius: 10px !important;
    background-color: transparent !important;
    border-color: transparent !important;
}

#uploading-modal___BV_modal_body_ {
    background-color: transparent !important;
    border-radius: 12px;
    margin: 10px;
}

#overlay-modal___BV_modal_content_ {
    border-radius: 10px !important;
    background-color: transparent !important;
    border-color: transparent !important;
}

#overlay-modal___BV_modal_body_ {
    background-color: transparent !important;
    border-radius: 12px;
    margin: 10px;
}

#overlay-modal {
    background-color: #0c0c0ca8;
}

#upload-success-modal___BV_modal_content_ {
    border-radius: 10px;
    margin: 10px;
    background-color: #ffffff;
}

#upload-success-modal___BV_modal_body_ {
    background-color: #ffffff;
    border-radius: 12px;
    margin: 10px;
}

.text-upload {
    margin-top: -8px;
}

.modal-text {
    font-size: 13px;
    color: $cp-black;
    font-family: $cp-font;
    display: block;
    font-weight: 800;
}

.warning-icon-space {
    margin-top: 90px;
}

.success-text {
    font-size: 18px;
    color: $cp-white;
    font-family: $cp-font;
    display: block;
    font-weight: 600;
}

.success-sub-text {
    font-size: 15px;
    color: $cp-white;
    font-family: $cp-font;
    display: block;
    font-weight: 100;
}

.manual-text {
    font-size: 15px;
    color: $cp-white;
    font-family: $cp-font;
    display: block;
    font-weight: 100;
}

.capture-text {
    font-size: 17px;
    color: $cp-white;
    font-family: $cp-font;
    display: block;
    font-weight: 100;
    margin-bottom: 60px;
}

.btn-upload {
    height: 50px !important;
    border-radius: 8px !important;
    width: 100% !important;
    border-color: transparent !important;
    background-color: $cp-black !important;
    font-family: $cp-font-secondary;
    font-size: 14px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: center;
    color: #ffffff;
    margin-top: 5px !important;
    cursor: pointer;
}

@media only screen and (max-height: 600px) {
    .success-btn-space {
        margin-top: 80px !important;
    }
}

.success-btn-space {
    margin-top: 100px;
}

.row-space {
    margin-top: 10px;
}

.icon-space {
    margin-top: 20px;
}

.padding {
    margin-left: 12px;
    margin-right: 12px;
}

.btn-ok {
    height: 50px !important;
    width: 147px;
    border-radius: 6px !important;
    border-color: transparent !important;
    background-color: $cp-black !important;
    font-family: $cp-font-secondary;
    font-size: 14px;
    font-weight: bold;
    color: white;
}

.btn-row-space {
    margin-top: 40px !important;
}

.btn-width {
    height: 67px !important;
    font-size: 13px;
    font-family: $cp-font;
}

.connect-bank-text {
    font-size: 15px;
    color: $cp-white;
    font-family: $cp-font;
}

.manual-btn {
    background-color: $cp-secondary;
    border-color: $cp-secondary;
    width: 100%;
    color: $cp-white;
    border-radius: 8px !important;
    border-color: transparent !important;
}

.upload-document {
    margin: auto;
    width: 301px;
    height: 213px;
    background-color: $cp-secondary !important;
    border-color: $cp-secondary !important;
    border-radius: 7px;
}

.btn-upload {
    width: 301px !important;
}

.preview {
    margin: auto;
    width: 301px;
    height: 213px;
    border-radius: 7px;
}

.pdf-preview {
    margin: auto;
    width: 213px;
    height: 213px;
    border-radius: 7px;
}

.routing-top-space {
    margin-top: 80px !important;
}

.routing-bottom-space {
    margin-top: 80px !important;
}

@media only screen and (min-width: 360px) and (max-height: 740px) {
    .routing-top-space {
        margin-top: 30px !important;
    }
    .routing-bottom-space {
        margin-top: 40px !important;
    }
}

@media only screen and (min-width: 300px) and (max-width: 320px) {
    .routing-top-space {
        margin-top: 12px !important;
    }
    .routing-bottom-space {
        margin-top: 30px !important;
    }
    .icon-space {
        margin-top: 10px;
    }
    .btn-width {
        height: 45px !important;
        font-size: 13px;
        font-family: $cp-font;
    }
    .btn-row-space {
        margin-top: 20px;
    }
    .warning-icon-space {
        margin-top: 40px;
    }
    .manual-text {
        font-size: 11px;
        color: $cp-white;
        font-family: $cp-font;
        display: block;
        font-weight: 100;
    }
    .capture-text {
        font-size: 15px;
        color: $cp-white;
        font-family: $cp-font;
        display: block;
        font-weight: 500;
        margin-bottom: 60px;
    }
    .upload-document {
        margin: auto;
        width: 243px;
        height: 168px;
        background-color: $cp-secondary !important;
        border-color: $cp-secondary !important;
        border-radius: 7px;
    }
    .btn-upload {
        width: 243px !important;
    }
    .preview {
        margin: auto;
        width: 243px;
        height: 168px;
        border-radius: 7px;
    }
    .pdf-preview {
        margin: auto;
        width: 168px;
        height: 168px;
        border-radius: 7px;
    }
}

@media only screen and ( min-width:270px) and ( max-width:414px) {
    .bank-linking-text {
        font-size: 11px;
        color: $cp-white;
        font-family: $cp-font;
        display: block;
        font-weight: 100;
    }
    .capture-text {
        font-size: 15px;
        color: $cp-white;
        font-family: $cp-font;
        display: block;
        font-weight: 500;
        margin-bottom: 60px;
    }
}

@media only screen and ( min-width:360px) and ( max-height:667px) {
    .success-text {
        font-size: 20px;
        color: $cp-white;
        font-family: $cp-font;
        display: block;
        font-weight: 600;
    }
    .success-sub-text {
        font-size: 18px;
        color: $cp-white;
        font-family: $cp-font;
        display: block;
        font-weight: 100;
    }
    .routing-top-space {
        margin-top: 20px !important;
    }
    .routing-bottom-space {
        margin-top: 40px !important;
    }
    .warning-icon-space {
        margin-top: 40px;
    }
    .manual-text {
        font-size: 13px;
        color: $cp-white;
        font-family: $cp-font;
        display: block;
        font-weight: 100;
    }
    .upload-document {
        margin: auto;
        width: 279px;
        height: 190px;
        background-color: $cp-secondary !important;
        border-color: $cp-secondary !important;
        border-radius: 7px;
    }
    .btn-upload {
        width: 279px !important;
    }
    .preview {
        margin: auto;
        width: 279px;
        height: 190px;
        border-radius: 7px;
    }
    .pdf-preview {
        margin: auto;
        width: 190px;
        height: 190px;
        border-radius: 7px;
    }
}

@media only screen and ( min-width:411px) and ( max-width:414px) {
    .routing-top-space {
        margin-top: 40px !important;
    }
    .routing-bottom-space {
        margin-top: 40px !important;
    }
}

.visibility {
    display: none !important;
}

.font-white {
    color: $cp-white;
}

.bank-name-list {
    height: 4rem;
    background-color: $cp-gray;
    border-radius: 0.5rem;
}
.bank-name-list-disabled {
    height: 4rem;
    background-color: $cp-gray;
    border-radius: 0.5rem;
}
.remove-padding {
    padding-left: unset !important;
    padding-right: unset !important;
}

.bank-list {
    max-height: calc(100vh - 320px);
    overflow-y: scroll;
    overflow-x: hidden;
}

  /* width */
  .bank-list::-webkit-scrollbar {
    width: 0px;
  }
  
  /* Track */
  .bank-list::-webkit-scrollbar-track {
    background: #f1f1f1; 
  }
   
  /* Handle */
  .bank-list::-webkit-scrollbar-thumb {
    background: #f1f1f1; 
  }
  
  /* Handle on hover */
  .bank-list::-webkit-scrollbar-thumb:hover {
    background: #f1f1f1; 
  }

.bl-heading {
    font-size: 1.22em;
    font-weight: 600;
}

.bl-f-weight {
    font-weight: 900;
}

.bl-bank-name {
    font-size: 1.2em;
}

.bl-f-color {
    color: $cp-primary;
}

.bl-footer-text {
    font-size: 1em;
    font-weight: 600;
}

.radio-custom {
    opacity: 0;
    position: absolute;
}

.radio-custom,
.radio-custom-label {
    display: inline-block;
    vertical-align: middle;
    margin: 5px;
    cursor: pointer;
}

.radio-custom-label {
    position: relative;
}

.radio-custom+.radio-custom-label:before {
    content: '';
    background: $cp-darkgary;
    border: 1px solid $cp-darkgary;
    display: inline-block;
    vertical-align: middle;
    width: 25px;
    height: 25px;
    padding: 0px;
    text-align: center;
}

.radio-custom+.radio-custom-label:before {
    border-radius: 50%;
}

.radio-custom:checked+.radio-custom-label:before {
    content: "\f00c";
    font-family: 'FontAwesome';
    color: $cp-white;
    background: $cp-primary;
    border: 1px solid $cp-primary;
    width: 25px;
    height: 25px;
    padding: 0px;
}

.bank-highlight {
    border: 1px solid #f8f9fa;
    box-shadow: 1px -1px 20px 8px #dee2e6;
    border-radius: 2px;
}

#update-bank-modal___BV_modal_body_ {
    background-color: #ffffff;
    border-radius: 12px;
    margin: 10px;
}

.delete-bank-account {
    content: "\f00d";
    font-family: 'FontAwesome';
    color: $cp-white;
    background: $cp-primary;
    border: 1px solid $cp-primary;
    width: 25px;
    height: 25px;
    padding: 0px;
}

.delete-bank-label {
    display: inline-block;
    vertical-align: middle;
    margin: 5px;
    cursor: pointer;
}

.delete-bank-label {
    position: relative;
}

.delete-bank-label:before {
    content: '';
    background: $cp-darkgary;
    border: 1px solid $cp-darkgary;
    display: inline-block;
    vertical-align: middle;
    width: 25px;
    height: 25px;
    padding: 0px;
    text-align: center;
}

.delete-bank-label:before {
    border-radius: 50%;
}

.delete-bank-label:before {
    content: "\f00d";
    font-family: 'FontAwesome';
    color: $cp-white;
    background: $cp-red;
    border: 1px solid $cp-red;
    width: 25px;
    height: 25px;
    padding: 0px;
}

// problematic account start
.problematic-account-bank-label {
    display: inline-block;
    vertical-align: middle;
    margin: 5px;
    cursor: pointer;
}

.problematic-account-bank-label {
    position: relative;
}

.problematic-account-bank-label:before {
    content: '!';
    background: $cp-darkgary;
    border: 1px solid $cp-darkgary;
    display: inline-block;
    vertical-align: middle;
    width: 25px;
    height: 25px;
    padding: 0px;
    text-align: center;
    animation: boxShadowAnimation 900ms linear alternate infinite;
}

.problematic-account-bank-label:before {
    border-radius: 50%;
}

.problematic-account-bank-label:before {
    content: "!";
    font-family: 'FontAwesome';
    color: $cp-white;
    font-size: 14px;
    background: $cp-red;
    border: 1px solid $cp-red;
    width: 21px;
    height: 21px;
    padding: 0px;
}
@keyframes boxShadowAnimation {
    0% {
      box-shadow: 1px 0 1px rgba(255, 0, 0, 0.802);
      width: 21px;
      height: 21px;
    }
    50% {
      box-shadow: 1px 0 3px rgba(255, 0, 0, 0.889);
      width: 20px;
      height: 20px;
    }
    100% {
      box-shadow: 1px 0 1px rgb(255, 0, 0);
      width: 21px;
      height: 21px;
    }
  }
// problematic account end

.enable-bank-label {
    display: inline-block;
    vertical-align: middle;
    margin: 5px;
    cursor: pointer;
}

.enable-bank-label {
    position: relative;
}

.enable-bank-label:before {
    content: '';
    background: $cp-primary;
    border: 1px solid $cp-primary;
    display: inline-block;
    vertical-align: middle;
    width: 25px;
    height: 25px;
    padding: 0px;
    text-align: center;
}

.enable-bank-label:before {
    border-radius: 50%;
}

.enable-bank-label:before {
    content: "\f00c";
    font-family: 'FontAwesome';
    color: $cp-white;
    background: $cp-primary;
    border: 1px solid $cp-primary;
    width: 25px;
    height: 25px;
    padding: 0px;
}


// problematic account start
.fix-label-bank {
    display: inline-block;
    vertical-align: middle;
    margin: 5px;
    cursor: pointer;
}

.fix-label-bank {
    position: relative;
}

.fix-label-bank:before {
    content: '';
    background: $cp-darkgary;
    border: 1px solid $cp-darkgary;
    display: inline-block;
    vertical-align: middle;
    width: 25px;
    height: 25px;
    padding: 0px;
    text-align: center;
}

.fix-label-bank:before {
    border-radius: 50%;
}

.fix-label-bank:before {
    content: "\1F6E0";
    font-family: 'FontAwesome';
    color: $cp-white;
    background: #149240;
    border: 1px solid #149240;
    width: 25px;
    height: 25px;
    padding: 0px;
}

// problematic account end